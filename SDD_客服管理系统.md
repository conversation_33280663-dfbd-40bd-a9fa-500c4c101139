# 客服管理系统软件设计文档（SDD）

## 1. 架构设计
### 1.1 技术选型
- 前端：Vue.js 2.x、Element-UI、ECharts、Axios
- 移动端：Vue.js 2.x、Vux、Mint-UI
- 后端：Spring Boot 2.x、MyBatis-Plus、Redis、Swagger2
- 数据库：MySQL 5.7+
- 其他：JPush极光推送、短信平台、UC统一认证

### 1.2 系统架构
- 前后端分离，RESTful API通信。
- 多端入口：PC端（客服管理）、移动端（业主/员工）、微信端。
- 统一认证与权限管理，支持单点登录。
- Redis用于Session共享、分布式锁、缓存用户信息等。

### 1.3 目录结构
- `customer_service/`：后端服务
- `customer_service_web/`：PC端前端
- `csm_mobile/`：移动端前端

## 2. 详细模块设计（极致细化）
### 2.1 用户与权限
- 用户、角色、权限、菜单表结构详见数据库脚本。
- UC同步接口，支持手动/自动同步。
- 权限校验在每个接口前置，未授权请求直接返回401。

### 2.2 报事工单流程
- 工单主表、流程节点表、状态机详见SRS表结构。
- 流程服务（BaseProcessServiceImpl）实现所有流转、回退、升级、特殊关闭等业务逻辑。
- 所有状态变更均有日志记录，支持追溯。

### 2.3 统计报表
- 报表服务（ReportFormService）支持多维度统计，所有统计接口支持导出。
- 报表导出工具（ExcelUtil）支持多Sheet、格式自定义。

### 2.4 数据模型
- 详见SRS表结构，所有表字段、索引、约束、注释均需完整。
- 主要实体关系：工单与流程节点、业主与房屋、项目与区域/城市、标签与客户、公告与用户等。

### 2.5 接口设计（极致细化）
- 所有接口均采用RESTful风格，参数、返回值、错误码、分页、排序、筛选等需标准化。
- Swagger2自动生成接口文档，所有接口需注解说明。
- 典型接口参数/返回值详见SRS。

### 2.6 缓存与分布式
- Redis用于Session共享、分布式锁、缓存用户信息等。
- 关键业务操作加分布式锁，防止并发冲突。

### 2.7 安全与异常处理
- 全局异常处理，接口返回统一格式（code/msg/data）。
- 登录、权限校验，防止未授权访问。
- 所有敏感操作需日志记录。

## 3. 数据流程与时序
- 详见SRS业务流与数据流。
- 典型时序图：工单提交→分派→处理→回访→关闭。

## 4. 部署与运维
- 支持多环境配置（dev/prod），详见`application.properties`。
- 日志、监控、接口文档（Swagger）等配套完善。
- 支持高并发、分布式部署，所有服务支持横向扩展。

## 5. ER图与数据关系
- 详见`wiki/客服一体化v1.pdm`，可用PowerDesigner等工具打开查看详细ER图。
- 主要实体关系：工单与流程节点、业主与房屋、项目与区域/城市、标签与客户、公告与用户等。

## 6. 代码规范与扩展
- 所有代码需注释完整，变量/方法/类命名规范。
- 所有接口、表结构、前端页面均预留扩展字段。
- 代码分层清晰，Controller- Service- Dao- Model。 