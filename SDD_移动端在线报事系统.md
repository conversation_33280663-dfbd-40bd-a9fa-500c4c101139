# 移动端在线报事系统软件设计文档（SDD）

## 1. 架构设计

### 1.1 前端整体架构
- 框架：Vue.js 2.x
- UI组件库：Vux、Mint-UI
- 图表库：ECharts
- 状态管理：Vue自带（无Vuex）
- 路由管理：vue-router，history模式，base为/app/
- 网络请求：Axios，统一封装
- 样式：SCSS

### 1.2 目录结构说明
- `src/pages/`：页面组件（如重大投诉、报事统计、详情等）
- `src/components/`：通用组件（如TabBar、tab、dialog、lineTable等）
- `src/api/`：API接口封装
- `src/axios/`：Axios封装
- `src/assets/`：静态资源（图片、样式、字体等）
- `src/router/`：路由配置
- `src/filter/`：全局过滤器

## 2. 主要模块设计

### 2.1 TabBar导航
- 组件：`TabBar.vue`
- 功能：底部导航，动态渲染菜单项，支持路由高亮
- 数据来源：`navList`（由router/index.js导出）
- 交互：点击切换页面，当前路由高亮

### 2.2 重大投诉列表
- 页面：`newsReportList/index.vue` + `components/singleNewsReport.vue`
- 功能：展示重大投诉，支持下拉加载更多，点击进入详情
- 交互：
  - 下拉触发`load`方法，分页加载数据
  - 点击条目跳转到详情页
  - 弹窗说明重大投诉定义
- 数据流：调用`getInfoList`接口，分页获取数据

### 2.3 报事统计
- 页面：`newsReportCount/index.vue`
- 功能：统计报事总量、分类、升级、满意度等，支持切换客户/员工/供应商、时间筛选
- 交互：
  - Tab切换维度
  - Picker切换时间、分类
  - 图表展示统计数据
- 数据流：调用`getNewsReportCountCus`、`getNewsReportLvUpCount`、`getSatisfactionRateCount`等接口

### 2.4 报事详情
- 页面：`newsReportDetail/index.vue`（含baseInfo、reportorInfo、followRecord子组件）
- 功能：展示报事基本信息、客户诉求、处理进度
- 数据流：调用`getInfoDetail`接口，渲染详情与进度

### 2.5 分类统计详情、员工统计、事故统计
- 页面：`newsReportCount/classifyDetail/index.vue`、`reportingDetail/index.vue`、`accidentDetail/index.vue`
- 功能：展示各类统计明细，支持图表、列表、筛选
- 数据流：调用`getClassifyDetail`、`getReportingDetail`、`getComplaintDetail`等接口

## 3. 接口设计

### 3.1 API基础
- 所有接口基础路径：`/api/mreport`
- 统一通过`src/axios/index.js`封装的`service`对象请求
- 请求超时：100s
- POST默认`Content-Type`：`application/x-www-form-urlencoded`
- 请求拦截器：自动添加时间戳参数
- 响应拦截器：统一处理状态码与错误

### 3.2 主要接口
- `GET /api/mreport/complaint/list`：获取投诉列表
  - 参数：`pageNum`、`pageSize`
  - 返回：`{ code, data: { records: [...] } }`
- `GET /api/mreport/complaint/detail`：获取投诉详情
  - 参数：`id`
  - 返回：`{ code, data: { mcomplaintVo, processList } }`
- `GET /api/mreport/customer/statistics/report`：报事统计
  - 参数：`range`
  - 返回：统计数据
- `GET /api/mreport/customer/statistics/reportUp`：升级统计
  - 参数：`range`、`region`、`type`
  - 返回：升级统计数据
- `GET /api/mreport/customer/statistics/satisficing`：满意度
  - 参数：`range`
  - 返回：满意度数据
- `GET /api/mreport/customer/statistics/reportNext`：分类详情
  - 参数：`range`、`firstSortCode`
  - 返回：分类统计明细

## 4. 数据流程
- 页面组件通过API模块请求数据，Axios拦截器统一处理请求/响应。
- 数据流动：页面 -> API方法 -> Axios封装 -> 后端接口 -> 返回数据 -> 页面渲染
- 图表数据由接口返回后，传递给ECharts实例渲染。
- TabBar、Tab等组件通过props和事件与父组件通信。

## 5. 关键实现细节

### 5.1 TabBar
- 动态渲染菜单项，支持路由高亮，适配移动端安全区

### 5.2 图表渲染
- 通过ECharts渲染饼图、柱状图，支持动态数据、颜色配置

### 5.3 下拉加载
- 重大投诉列表使用Mint-UI的Loadmore组件，支持分页、无数据提示、加载动画

### 5.4 Axios封装
- 统一拦截请求与响应，自动加时间戳，处理错误

## 6. 扩展性与维护性设计
- 组件化开发，页面与通用组件分离，便于维护与复用
- API接口集中管理，便于后续扩展
- 路由配置集中，支持页面扩展与权限控制
- 样式采用SCSS，支持主题定制
- 代码结构清晰，便于多人协作与持续集成

--- 