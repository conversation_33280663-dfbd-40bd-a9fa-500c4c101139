# 客服管理系统需求规格说明书（SRS）

## 1. 项目背景与目标
本系统为泰禾集团客服一体化平台，面向客服人员、管理人员及部分业主，提供报事、投诉、报修、回访、统计、标签、公告等全流程服务，提升客服效率与服务质量。

## 2. 术语与定义
- 报事：客户通过系统提交的事件，包括投诉、报修、咨询等。
- 工单：系统内流转的报事处理单据。
- 流程节点：工单处理的各个阶段，如录入、分派、处理、回访、关闭等。
- 角色：如客服、管理员、IT支持、业主等。

## 3. 功能需求（极致细化）

### 3.1 用户与权限管理
#### 3.1.1 用户表（cs_uc_user）
| 字段名 | 类型 | 说明 | 约束/备注 |
|--------|------|------|-----------|
| id | Long | 主键，自增 | 必填，唯一 |
| fdSid | String | 用户全局ID | 必填，唯一 |
| fdName | String | 用户姓名 | 必填 |
| fdUsername | String | 登录用户名 | 必填，唯一 |
| fdPassword | String | 登录密码 | 必填 |
| fdOrgId | String | 部门ID | 必填 |
| fdOrgName | String | 部门名称 | 必填 |
| fdTel | String | 手机号 | 必填 |
| fdEmail | String | 邮箱 |  |
| fdAvailable | Integer | 是否有效 | 1可用，0禁用 |
| fdUsertype | Integer | 用户类型 |  |
| ... | ... | ... | ... |

#### 3.1.2 角色与权限
- 角色表、权限表、菜单表，支持树形结构。
- 权限分配接口：`/api/perm/userPackage`，返回用户信息包（含菜单树、角色、组织等）。

#### 3.1.3 典型接口
- `/api/csUcUser/selectUserTel`  
  - 参数：tel（呼入电话）、username（登录名）
  - 返回：用户详细信息（JSON对象，含所有字段）
- `/api/perm/userLogin`
  - 参数：account, password
  - 返回：登录结果，用户信息包

### 3.2 报事工单管理
#### 3.2.1 工单主表（cs_form_inst）
| 字段名 | 类型 | 说明 | 约束/备注 |
|--------|------|------|-----------|
| id | Long | 主键，自增 | 必填，唯一 |
| formNo | String | 工单编号 | 必填，唯一 |
| deptCode | String | 部门类型编码 | 必填 |
| deptName | String | 部门类型名称 |  |
| processCode | String | 流程类别编码 | 必填（如processKSCL、processBZ等） |
| processName | String | 流程类别名称 |  |
| firstSortCode | String | 一级分类编码 | 必填 |
| firstSortName | String | 一级分类名称 |  |
| secSortCode | String | 二级分类编码 |  |
| secSortName | String | 二级分类名称 |  |
| ... | ... | ... | ... |
| processStateCode | String | 当前业务步骤编码 | 必填（如draft、toBeAssigned、handle等） |
| processStateName | String | 当前业务步骤名称 |  |
| ownerId | String | 业主ID |  |
| ownerName | String | 业主姓名 |  |
| mobile | String | 业主手机号 |  |
| projectCode | String | 项目编码 |  |
| project | String | 项目名称 |  |
| regionCode | String | 区域编码 |  |
| region | String | 区域名称 |  |
| cityCode | String | 城市编码 |  |
| city | String | 城市名称 |  |
| houseInfoId | String | 房屋ID |  |
| houseNo | String | 房屋编号 |  |
| ... | ... | ... | ... |
| assignId | String | 当前分派人ID |  |
| assignName | String | 当前分派人姓名 |  |
| curAssigneeId | String | 当前处理人ID |  |
| curAssigneeName | String | 当前处理人姓名 |  |
| handleRecord | String | 处理记录 |  |
| satisfactionCode | String | 满意度编码 |  |
| satisfactionName | String | 满意度名称 |  |
| upgradeFlag | Long | 升级标志（1是） |  |
| reworkFlag | Long | 返工标志（1是） |  |
| rejectFlag | Long | 退回标志（1是） |  |
| ... | ... | ... | ... |
| creationDate | Date | 创建时间 |  |
| lastUpdateDate | Date | 最后更新时间 |  |
| submitDate | Date | 提交时间 |  |
| ... | ... | ... | ... |

#### 3.2.2 工单节点表（cs_process_workitem）
| 字段名 | 类型 | 说明 | 约束/备注 |
|--------|------|------|-----------|
| id | Long | 主键，自增 | 必填 |
| formInstId | Long | 工单主表ID | 必填，外键 |
| taskStatus | Long | 任务状态（20待办，30已办） | 必填 |
| assignId | String | 处理人ID |  |
| assignName | String | 处理人姓名 |  |
| assignMobile | String | 处理人手机号 |  |
| processStateCode | String | 业务步骤编码 | 必填 |
| processStateName | String | 业务步骤名称 |  |
| comment | String | 处理意见 |  |
| taskStartTime | Date | 开始时间 |  |
| taskEndTime | Date | 结束时间 |  |
| creationDate | Date | 创建时间 |  |
| lastUpdateDate | Date | 最后更新时间 |  |
| thPlatform | String | 来源平台 |  |
| ... | ... | ... | ... |

#### 3.2.3 典型接口（参数/返回值极致细化）
- `POST /api/process/submit`
  - 参数：
    - formInstId（String，必填）：工单ID
    - comment（String，可选）：备注
    - assignUserId（String，可选）：分派人ID
    - assignUserName（String，可选）：分派人姓名
    - mobile（String，可选）：分派人手机号
    - operateType（String，必填）：操作类型（submit/退回/reject等）
  - 返回：
    - code（int）：状态码
    - msg（String）：提示信息
    - data（Object）：操作结果

- `POST /api/csFormInst/save`
  - 参数：JSON对象，结构如下
    ```json
    {
      "csFormInst": { ... },
      "operateType": "draft|submit|update",
      "comment": "string"
    }
    ```
  - 返回：工单ID（String）

- `GET /api/csFormInst/info`
  - 参数：pageNum（int），pageSize（int）
  - 返回：分页对象
    ```json
    {
      "total": 100,
      "records": [ { ... } ]
    }
    ```

- 其余接口详见前述答案，所有参数、返回值均需表格/JSON示例详细列出。

### 3.3 统计与报表
- 详见前述，所有接口参数、返回值、字段均需详细说明。

### 3.4 基础信息管理
- 详见前述，所有接口参数、返回值、字段均需详细说明。

### 3.5 标签与公告
- 详见前述，所有接口参数、返回值、字段均需详细说明。

### 3.6 短信任务与字典项
- 详见前述，所有接口参数、返回值、字段均需详细说明。

## 4. 业务流与数据流（极致细化）
### 4.1 工单流转业务流
1. 录入/暂存：前端调用`/api/csFormInst/save`，operateType=draft，工单状态为draft。
2. 提交：前端调用`/api/process/submit`，operateType=submit，工单状态流转至toBeAssigned。
3. 分派：系统/人工分派，更新assignUserId/assignUserName，工单状态为toBeAssigned。
4. 处理：处理人领取工单，填写处理结果，工单状态为handle。
5. 回访：处理完成后，工单状态流转至returnVisit，客服回访并记录满意度。
6. 关闭/升级/特殊关闭：回访后工单归档，状态为nomalEnd/specialEnd/upgrade。

### 4.2 数据流
- 前端：Vue页面通过Axios请求后端接口，参数严格校验。
- 后端：Spring Boot Controller接收请求，参数映射为Java对象，调用Service层执行业务逻辑。
- Service层：执行业务流转、状态变更、数据校验、日志记录等。
- DAO层：MyBatis-Plus操作MySQL数据库，所有字段严格映射。
- Redis：用于Session、分布式锁、缓存等。
- 返回：所有接口返回统一格式（code/msg/data），前端根据code判断业务结果。

## 5. 非功能需求
- 性能：接口响应<1s，页面渲染<2s，支持1000+并发。
- 兼容性：IE11+、Chrome、Firefox、Edge、微信内置浏览器。
- 安全性：所有接口需token校验，防止CSRF/XSS/SQL注入。
- 可用性：界面简洁，操作便捷，支持下拉刷新、加载更多、批量操作。
- 可扩展性：所有接口、表结构、前端页面均预留扩展字段。 