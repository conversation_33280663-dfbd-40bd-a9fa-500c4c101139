# 移动端在线报事系统需求规格说明书（SRS）

## 1. 项目背景与目标

本系统为客户服务移动端（csm_mobile），主要面向微信公众号用户，提供在线报事、投诉、统计等功能，提升客户服务效率和用户体验。

## 2. 术语与定义
- 报事：客户通过系统提交的事件，包括投诉、报修等。
- 重大投诉：满足特定条件（如70天未解决、群诉、媒体曝光）的投诉。
- 用户：指通过微信公众号访问本系统的客户。
- 管理员/客服：后台处理报事的工作人员。

## 3. 总体描述
### 3.1 用户角色
- 普通用户：提交报事、查看报事进度与详情。
- 客服/管理员：后台处理，不在本系统范围。

### 3.2 业务流程
1. 用户关注公众号，点击菜单进入H5页面。
2. 用户浏览重大投诉、报事统计等页面。
3. 用户可查看报事详情、进度、统计数据。

### 3.3 系统环境
- 前端：Vue.js 2.x + Vux + Axios
- 运行环境：微信内置浏览器/主流手机浏览器
- 后端API：RESTful接口，基于`/api/mreport`路径

### 3.4 假设与依赖
- 用户已通过微信授权，系统可获取openid等身份信息。
- 后端API已部署并可用。

## 4. 功能需求

### 4.1 页面与模块
#### 4.1.1 主页面（/mainPage）
- 提供TabBar导航，入口包括“重大投诉”、“报事统计”。

#### 4.1.2 重大投诉列表（/mainPage/important）
- 展示重大投诉列表，支持下拉加载更多。
- 每条投诉显示标题、类别、报事人、当前处理人、处理时长、所属项目。
- 点击可进入报事详情。
- 提供“关于重大投诉”说明弹窗。

#### 4.1.3 报事统计（/mainPage/newsReport）
- 展示报事统计数据，包括报事总量、分类统计、升级统计、满意度等。
- 支持按客户/员工/供应商切换（供应商暂不可用）。
- 支持按时间（全部/本年/本月）筛选。
- 分类统计、升级统计、满意度均以图表和列表形式展示。
- 可进入分类详情、员工统计、事故统计等子页面。

#### 4.1.4 报事详情（/newsReportDetail）
- 展示报事基本信息、客户诉求、最新处理现状、处理进度（跟进记录）。
- 进度以流程节点方式展示。

#### 4.1.5 分类统计详情（/classifyDetail）
- 展示某一分类下的详细统计数据。

#### 4.1.6 员工报事统计详情（/reportingDetail）
- 展示员工维度的报事统计数据，支持图表展示。

#### 4.1.7 员工投诉事故统计详情（/accidentDetail）
- 展示员工投诉/事故的详细数据，支持按时间、类型筛选。

### 4.2 操作流程
- 用户通过TabBar切换页面。
- 列表页面支持下拉加载、点击进入详情。
- 统计页面支持切换维度、筛选时间、分类。

### 4.3 数据流
- 前端通过Axios请求后端API，接口基础路径为`/api/mreport`。
- 主要API包括：
  - `/complaint/list`：获取投诉列表
  - `/complaint/detail`：获取投诉详情
  - `/customer/statistics/report`：获取报事统计
  - `/customer/statistics/reportUp`：获取升级统计
  - `/customer/statistics/satisficing`：获取满意度
  - `/customer/statistics/reportNext`：获取分类详情

## 5. 非功能需求
- 性能：页面响应时间<2s，图表渲染流畅。
- 兼容性：适配主流手机浏览器及微信内置浏览器。
- 安全性：接口需校验用户身份，防止未授权访问。
- 可用性：界面简洁，操作便捷，支持下拉刷新、加载更多。

## 6. 其他需求
- 支持后续扩展供应商报事统计。
- 支持多语言（如有需求）。
- 前端需支持埋点统计与异常上报。

--- 