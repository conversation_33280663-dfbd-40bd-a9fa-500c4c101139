<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>客服管理</title>
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
<script type="text/javascript">

      var strcookie = document.cookie;//获取cookie字符串
      var arrcookie = strcookie.split("; ");//分割
      //if(!getCookie('LtpaToken')) window.location.href= 'http://oa.tahoecndemo.com:8080/ekp/login.jsp?THRedirectUrl=http://m.tahoecndemo.com:8080';//本地
      if(!getCookie('LtpaToken')) window.location.href= 'http://oa.tahoecndemo.com:8080/ekp/login.jsp?THRedirectUrl=http://'+location.host;//测试
      function getCookie(c_name) {
					if(document.cookie.length > 0) {
						c_start = document.cookie.indexOf(c_name + "=");//获取字符串的起点
						if(c_start != -1) {
							c_start = c_start + c_name.length + 1;//获取值的起点
							c_end = document.cookie.indexOf(";", c_start);//获取结尾处
							if(c_end == -1) c_end = document.cookie.length;//如果是最后一个，结尾就是cookie字符串的结尾
							return decodeURI(document.cookie.substring(c_start, c_end));//截取字符串返回
						}
					}
					return "";
			}


  !(function(win) {
    var doc = win.document;
    var docEl = doc.documentElement;
    var tid;

    function refreshRem() {
      var width = docEl.getBoundingClientRect().width;

      // var rem = width /4.14; // 将屏幕宽度分成10份， 1份为1rem

      var rem = width / 3.75; // 将屏幕宽度分成10份， 1份为1rem
      docEl.style.fontSize = rem + 'px';
      //alert(docEl.style.fontSize);
      doc.body.style.height = docEl.clientHeight + 'px';
    }
    win.addEventListener('resize', function() {
      clearTimeout(tid);
      tid = setTimeout(refreshRem, 300);
    }, false);
    win.addEventListener('pageshow', function(e) {
      if (e.persisted) {
        clearTimeout(tid);
        tid = setTimeout(refreshRem, 300);
      }
    }, false);
    refreshRem();
  })(window);
</script>
