<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App',
  // watch: {
  //   $route: {
  //     handler: function(val, oldVal){
  //       if(val.name == 'newsReport'){
  //         document.getElementById('cusSysTitle').style = "box-shadow:none";
  //       }else{
  //         if(document.getElementById('cusSysTitle')){
  //           document.getElementById('cusSysTitle').style = "";
  //         }
  //       }
  //     },
  //     deep: true
  //   }
  // },
  mounted(){
    // if(this.$route.name == 'newsReport'){
    //   document.getElementById('cusSysTitle').style = "box-shadow:none";
    // }else{
    //   if(document.getElementById('cusSysTitle')){
    //     document.getElementById('cusSysTitle').style = "";
    //   }
    // }
  }
}
</script>

<style>
#app {
  /*font-family: 'Avenir', Helvetica, Arial, sans-serif;*/
  /*-webkit-font-smoothing: antialiased;*/
  /*-moz-osx-font-smoothing: grayscale;*/
  /*text-align: center;*/
  /*color: #2c3e50;*/
  /*margin-top: 60px;*/
}
</style>
