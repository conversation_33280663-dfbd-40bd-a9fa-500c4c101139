import axios from "@/axios/index.js"

let api = "/api/mreport"

export function getInfoList(params) {//获取首页数据列表
    return axios.service({
        url: api + `/complaint/list`,
        method: 'get',
        params
    })
}


export function getInfoDetail(params) {//根据ID获取数据详情
    return axios.service({
        url: api + `/complaint/detail`,
        method: 'get',
        params
    })
}

export function getNewsReportCountCus(params) {//获取报事统计-客户
    return axios.service({
        url: api + `/customer/statistics/report`,
        method: 'get',
        params
    })
}

export function getNewsReportLvUpCount(params) {//报事升级统计-客户
    return axios.service({
        url: api + `/customer/statistics/reportUp`,
        method: 'get',
        params
    })
}

export function getNewsReportLvUpCountNext(params) {//报事升级统计下级-客户
    return axios.service({
        url: api + `/customer/statistics/reportUpNext`,
        method: 'get',
        params
    })
}

export function getSatisfactionRateCount(params) {//报事满意度统计-客户
    return axios.service({
        url: api + `/customer/statistics/satisficing`,
        method: 'get',
        params
    })
}

export function getClassifyDetail(params) {
    return axios.service({
        url:api + '/customer/statistics/reportNext',
        method:'get',
        params
    })
}

