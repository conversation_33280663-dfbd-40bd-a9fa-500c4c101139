import axios from "@/axios/index.js"

let api = "/api/mreport"

export function getNewspaperTrend(params) {//获取报事趋势统计
    return axios.service({
        url: api + `/staff/statistics/dayOfMonthReport`,
        method: 'get',
        params
    })
}

export function getNewspaperStatistics(params) {//获取报事统计
    return axios.service({
        url: api + `/staff/statistics/itReport`,
        method: 'get',
        params
    })
}

export function getComplaintsStatistics(params) {//获取投诉/事故统计
    return axios.service({
        url: api + `/staff/statistics/itAccidentReport`,
        method: 'get',
        params
    })
}

export function getReportingDetail(params) {//获取报事统计详情
    return axios.service({
        url: api + `/staff/statistics/itReportNext`,
        method: 'get',
        params
    })
}

export function getComplaintDetail(params) {//获取报事投诉/事故详情
    return axios.service({
        url: api + `/staff/statistics/itAccidentReportNext`,
        method: 'get',
        params
    })
}

export function getComplaintDetailTime(params) {//获取报事投诉/事故详情(时间查询)
    return axios.service({
        url: api + `/staff/statistics/itAccidentReportNextQuery`,
        method: 'get',
        params
    })
}
