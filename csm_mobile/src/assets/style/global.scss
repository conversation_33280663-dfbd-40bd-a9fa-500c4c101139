@font-face {
    font-family: 'PingFangSC-Regular';
    src: url('./PingFangSC.ttf');
    font-weight: normal;
    font-style: normal;
}

html,
body {
    margin: 0;
    padding: 0;
    font-family: 'PingFangSC-Regular, sans-serif';
}

.wryh {
    font-family: "Microsoft YaHei" !important;
}

#app {
    overflow-x: hidden;
    font-size: 0.12rem;
    background-color: #fbfbfb;
    min-height: 100%;
}

.vux-header {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: fixed !important;
    background-color: #fff;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 999;
}

.router-link-active {
    color: #E53636 !important;
}

.icon-grey {
    color: #9B9B9B;
}

.page-title {
    font-size: 0.2rem;
    font-weight: bold;
    color: #000000;
    display: inline-block;
    padding-bottom: 0.1rem;
}

.single-info-container {
    background-color: #ffffff;
    box-shadow: 0 0.02rem 0.12rem 0 rgba(0, 0, 0, 0.1);
    border-radius: 0.1rem;
    padding: 0.13rem 0.1rem;
    margin-bottom: 0.1rem;
}

.single-info-gray {
    display: block;
    padding: 0.1rem;
    border-radius: 0.04rem;
    background-color: #F5F5F5;
    border: solid 1px #E6E6E6;
    font-size: 0.14rem;
    color: #000000;
    line-height: 0.2rem;
}

.title-16 {
    font-size: 0.16rem;
    color: #0D0E10;
    font-weight: bold;
}

.mint-loadmore {
    padding: 1.05rem 0;
    padding-top: 0.2rem;
    background-color: #f8f8f8;
}