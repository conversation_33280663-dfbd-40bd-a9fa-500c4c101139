<template>
  <div id="tabbar">
    <nav>
      <div v-for="(item,index) in menuList" :key="index">
        <router-link class="nav-item" :to="item.path">
          <img :src="routePath.indexOf(item.path)==-1?item.icondefault:item.iconactive">
          <p>{{item.text}}</p>
        </router-link>
      </div>
    </nav>
  </div>
</template>

<script>
  import {navList} from '@/router'
    export default {
        watch: {
          $route: {
            handler: function(val, oldVal){
              this.routePath = val.fullPath;
            },
            deep: true
          }
        },
        data(){
          return{
            menuList:[],
            routePath:null,
          }
        },
        methods:{

        },
        created(){
          this.menuList = navList[0].children
          this.routePath = this.$route.fullPath
        }
    }
</script>

<style lang="scss" scoped>
#tabbar{
  position:fixed;
  bottom:0;
  width:100%;
}
nav{
  width:100%;
  height:0.49rem;
  background-color:#ffffff;
  box-shadow:0 -2px 4px 0 rgba(0,0,0,0.10);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom:constant(safe-area-inset-bottom);
  padding-bottom:env(safe-area-inset-bottom);
  .nav-item{
    display:flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-decoration:none;
    color: #000000;
    img{
      display: block;
      width:0.22rem;
      height:0.22rem;
    }
    p{
      font-size:0.12rem;
      margin: 0;
      padding: 0;
    }
  }
  .nav-item,.nav-item:hover,.nav-item:active,.nav-item:visited,.nav-item:link,.nav-item:focus{
    -webkit-tap-highlight-color:rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    outline:none;
    background: none;
    text-decoration: none;
  }

}
</style>
