<template>
    <div>
        <div class="m-dialog" v-if="isShow">
            <div class="dialog-item">
                <p class="dialog-title">{{title}}</p>
                <div class="dialog-content" v-html="content"></div>
                <div class="dialog-button-container">
                    <button @click="event">{{buttonText}}</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default{
    props:[
        'title',
        'content',
        'buttonText',
        'dialogVisible'
    ],
    watch:{
        dialogVisible(){
            this.isShow = this.dialogVisible;
        }
    },
    data(){
        return{
            isShow:false
        }
    },
    methods:{
        event(){
            this.$emit('btnEvent');
        }
    },
    created(){
        this.isShow = this.dialogVisible;
    }
}
</script>
<style lang="scss" scoped="scoped">
.m-dialog{
    width:100vw;
    height:100vh;
    background-color:rgba(0,0,0,0.5);
    position:fixed;
    top:0;
    left:0;
    bottom:0;
    right:0;
    display:flex;
    flex-direction:row;
    justify-content:center;
    align-items:center;
    z-index:9999;
    .dialog-item{
        width:2.6rem;
        box-shadow: 0 0.02rem 0.12rem 0 rgba(0,0,0,0.1);
        border-radius:0.1rem;
        background-color:#ffffff;
        .dialog-title{
            font-size:0.16rem;
            height:0.22rem;
            color:#000000;
            padding:0.15rem 0;
            text-align:center;
            margin-top:0.05rem;
            margin-bottom:0;
        }
        .dialog-content{
           width:2.2rem;
           text-indent: 2em;
           margin:0 auto;
           color:#9b9b9b;
           font-size:0.14rem; 
        }
        .dialog-button-container{
            padding:0.22rem 0;
            text-align:center;
            button{
                height:0.24rem;
                line-height:0.24rem;
                border:none;
                outline:none;
                padding:0 0.44rem;
                background-color:#e53636;
                color:#ffffff;
                border-radius:1rem;
            }
        }
    }
}
</style>
