<template>
    <div :class="border=='top'?'borderInTop':border=='bottom'?'borderInBottom':'no-border'" style="padding-right:0.15rem;">
      <span class="t-title">{{title}}</span>
      <span class="t-content">{{content}}</span>
    </div>
</template>

<script>
    export default {
        props:[
          'border',
          'title',
          'content'
        ],
        data(){
          return{

          }
        }
    }
</script>

<style lang="scss" scoped="scoped">
   .borderInTop{
     border-top:solid 1px #f5f5f5;
     padding:0.14rem 0rem;
     display:flex;
     justify-content: space-between;
     //align-items: center;
   }
   .borderInBottom{
     border-bottom:solid 1px #f5f5f5;
     padding:0.14rem 0rem;
     display:flex;
     justify-content: space-between;
     align-items: center;
   }
   .no-border{
     padding:0.14rem 0rem;
     display:flex;
     justify-content: space-between;
     align-items: center;
   }
  .t-title{
    display:inline-block;
    color: #262626;
    text-align: left;
    font-size:0.16rem;
    vertical-align:top;
  }
  .t-content{
    display:inline-block;
    color: #9B9B9B;
    max-width:2.4rem;
    text-align:right;
    font-size:0.16rem;
    vertical-align:top;
  }
</style>
