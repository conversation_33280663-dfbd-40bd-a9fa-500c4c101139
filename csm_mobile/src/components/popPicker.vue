<template>
    <div class="pop-picker">
      <div class="occur-selector" @click="showSelectedCard">
          {{labelDisplay}}
      </div>
      <div class="pop-picker-mask" v-if="isShow" @click="isShow = false">
        <div class="picker-item-container" ref="selectContainer">
            <div class="picker-item" v-for="(item,index) in pickerlist" :key="index" @click="selectedValue(item)">{{item.label}}</div>
            <!-- <div class="close-pop-picker" @click="isShow = false">取消</div> -->
            <div style="width:100%;height:0.34rem;background-color:#ffffff;"></div>
        </div>
      </div>
    </div>
</template>

<script>
    export default {
        props:[
            'data'
        ],
        watch:{
            data(){
                this.pickerlist = this.data;
                this.labelDisplay = this.pickerlist[0].label;
            },
            selected(){
                this.$emit('change',this.selected);
            }
        },
        data(){
          return{
             pickerlist:[],
             isShow:false,
             labelDisplay:null,
             selected:1,
          }
        },
        methods:{
            selectedValue(item){
                this.selected = item.value;
                this.labelDisplay = item.label;
                this.isShow = false;
            },
            showSelectedCard(){
                this.isShow = true;
                setTimeout(() => {
                    let h = Number(this.$refs.selectContainer.offsetHeight);
                    this.$refs.selectContainer.style.bottom = -h +'px';
                    let that = this;
                    function _interval(){
                       setTimeout(() => {
                            if(-h < 0){
                                h -= 5;
                                that.$refs.selectContainer.style.bottom = -h +'px';
                                _interval();
                            }else{
                                that.$refs.selectContainer.style.bottom = 0 +'px';
                            }
                        }, 4); 
                    }
                    _interval();
                    
                },50);
            }
        },
        created(){
            this.pickerlist = this.data;
            this.labelDisplay = this.pickerlist[0].label;
            this.pickerlist.map((item,index) => {
                if(item.default){
                    this.labelDisplay = this.pickerlist[index].label;
                }
            })
        },
    }
</script>

<style lang="scss" scoped="scoped">
.occur-selector{
    display:inline-block;
    width:0.89rem;
    height:0.23rem;
    line-height:0.23rem;
    text-align: center;
    border-radius:0.14rem;
    background-color:#ebebeb;
    font-size:0.12rem;
    color:#0D0E10;
    position:relative;
    background-image: url(../assets/images/common/select.png);
    background-size:100% 100%;
}
.pop-picker-mask{
    position:fixed;
    width:100vw;
    height:100vh;
    top:0;
    left:0;
    right:0;
    bottom:0;
    background-color:rgba(0,0,0,0.5);
    z-index:9999;
    .picker-item-container{
        position:absolute;
        bottom:-1000px;
        width:100%;
        .picker-item,.close-pop-picker{
            width:100%;
            height:0.5rem;
            background-color:#ffffff;
            border-bottom:solid 0.5px #DCDCDC;
            line-height:0.5rem;
            text-align:center;
            font-size:0.18rem;
            color:#0D0E10;
        }
    }
}
</style>
