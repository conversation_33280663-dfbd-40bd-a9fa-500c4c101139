<template>
    <div class="tab-change-bar">
        <div class="tab-change-container">
          <div :class="active==item.value?'tab-item-active':'tab-item'" v-for="(item,index) in tabList" :key="index" @click="tabChange(item)">
            <span>{{item.label}}</span>
            <span class="tab-step"></span>
          </div>
        </div>
    </div>
</template>

<script>
import { Toast } from 'vux'
    export default {
        props:[
          'tabList',
          'defaultActive'
        ],
        watch:{
          default(){
            this.active = this.defaultActive
          },
          active(){
            this.$emit('change',this.active);
          }
        },
        components:{
          Toast
        },
        data(){
          return{
            active:'',
          }
        },
        methods:{
          tabChange(val){
            if(val.disabled){
              if(val.message){
                this.$vux.toast.text(val.message, 'default');
              }
            }else{
              this.active = val.value;
            }
          }
        },
        created(){
          this.active = this.defaultActive
        }
    }
</script>

<style lang="scss" scoped="scoped">
  .tab-change-container{
    height:0.52rem;
    width:100%;
    background-color:#ffffff;
    display:flex;
    justify-content: space-around;
    align-items: flex-end;
    box-shadow: 0 0.02rem 0.12rem 0 rgba(0,0,0,0.1);
    position:fixed;
    top:0;
    z-index:99;
    .tab-item-active{
      height:0.32rem;
      font-size:0.16rem;
      min-width:0.5rem;
      display: flex;
      color: #E53636;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      .tab-step{
        display:block;
        width:0.2rem;
        height:0.02rem;
        background-color: #E53636;
      }
    }
    .tab-item{
      height:0.32rem;
      font-size:0.16rem;
      min-width:0.5rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      color:#9B9B9B; 
      .tab-step{
        display:block;
        width:0.2rem;
        height:0.02rem;
        background-color: #fff;
      }
    }
  }
  .tab-item,.tab-item:hover,.tab-item:active,.tab-item:visited,.tab-item:link,.tab-item:focus{
    -webkit-tap-highlight-color:rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    outline:none;
    background: none;
    text-decoration: none;
  }
  .tab-item-active,.tab-item-active:hover,.tab-item-active:active,.tab-item-active:visited,.tab-item-active:link,.tab-item-active:focus{
    -webkit-tap-highlight-color:rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    outline:none;
    background: none;
    text-decoration: none;
  }
</style>
