import Vue from 'vue'

Vue.prototype.formatDate = function(time,type){
    if(time){
        let _date = new Date(time);
        let y = _date.getFullYear();
        let m = Number(_date.getMonth()) + 1 < 10 ? '0' + Number(_date.getMonth()) + 1 : Number(_date.getMonth()) + 1;
        let d = _date.getDate() < 10 ? '0' + _date.getDate() : _date.getDate();
        let hh = _date.getHours() < 10 ? '0' + _date.getHours() : _date.getHours();
        let mm = _date.getMinutes() < 10 ? '0' + _date.getMinutes() : _date.getMinutes();
        let ss = _date.getSeconds() < 10 ? '0' + _date.getSeconds() : _date.getSeconds();
        if (type == 'yyyy-MM-dd') {
            return (y + '-' + m + '-' + d).toString();
        } else if (type == 'yyyy-MM-dd hh:mm:ss') {
            return (y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss).toString();
        } else if (type == 'yyyy-MM-dd hh:mm'){
            return (y + '-' + m + '-' + d + ' ' + hh + ':' + mm).toString();
        }
    }else{
        return null
    }
}