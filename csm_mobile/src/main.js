// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import '@/assets/style/global.scss'
import My<PERSON>hart from 'echarts'
import Loadmore from 'mint-ui'
import '@/filter'
import {
  ToastPlugin,
} from 'vux'
Vue.prototype.$mycharts = MyChart
Vue.use(ToastPlugin)
Vue.use(Loadmore)
Vue.config.productionTip = false
/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  components: { App },
  template: '<App/>'
})
