<template>
  <div id="mainPage">
    <!-- <x-header title="客服系统" id="cusSysTitle">
      <template slot="right">关闭</template>
    </x-header> -->
    <router-view/>
    <v-nav/>
  </div>
</template>

<script>
  import tabbar from '@/components/TabBar.vue'
    export default {
        components:{
          'v-nav':tabbar
        },
        data(){
            return{
              name:'页面主入口',
            }
        },
        methods:{

        },
        created(){

        },
        mounted(){

        }
    }
</script>

<style scoped>

</style>
