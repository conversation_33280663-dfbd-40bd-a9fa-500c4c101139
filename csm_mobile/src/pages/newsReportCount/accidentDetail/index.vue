<template>
  <div id="accidentDetails">
    <v-tab :tabList="tabList" :defaultActive="$route.params.type" @change="getTabChange"/>
    <div class="wrap">
      <!-- $route.params.selectedTime  0全部3当日2本月1本年-->
      <v-screentimes
        :selectedTime="$route.params.selectedTime"
        @getTime="getTime"
        :type="type"
        :sortCode="this.$route.params.regionCode"
        :dataList.sync="dataList"
      ></v-screentimes>
      <div class="list" v-for="(item, idx) in dataList" :key="idx">
        <div style="color: #414141;padding-left: .14rem;">{{item.firstSortName}}</div>
        <div class="content">
          <div>
            <div style="float: left;">
              <span class="font-color">报事人</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <span style="color: #070707">{{item.ownerName}}</span>
            </div>
            <div style="float: right;">
              <span class="font-color">处理人</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <span
                style="color: #070707"
              >{{item.curAssigneeName}}</span>
            </div>
          </div>
          <div style="clear: both;"></div>
          <div>
            <div style="padding: .05rem 0;">
              <span class="font-color">客户诉求</span>
            </div>
            <div style="color: #070707;word-break: break-all;">{{item.customerDemand}}</div>
          </div>
        </div>
      </div>
      <load-more :show-loading="false" tip="暂无数据" v-if="dataList.length==0"></load-more>
    </div>
  </div>
</template>

<script>
import Tab from "@/components/tab.vue";
import Screentimes from "./screenTime";
import { getComplaintDetail, getComplaintDetailTime } from "@/api/staff";
import { LoadMore } from "vux";
export default {
  name: "accidentDetails",
  components: {
    "v-tab": Tab,
    "v-screentimes": Screentimes,
    LoadMore
  },
  data() {
    return {
      tabList: [{ label: "事故", value: "1" }, { label: "投诉", value: "2" }],
      dataList: [],
      times: "",
      type: this.$route.params.type,
      firstSortCode: this.$route.params.regionCode,
      times2: ""
    };
  },
  methods: {
    getTime(data) {
      this.times = data;
      this.times2 = data;
    },
    getTabChange(val) {
      this.type = val;
      console.log(val);
      if (this.times == "") {
        this.complaintDetail();
      } else {
        console.log(this.times);
        if (
          this.$route.params.selectedTime == 0 ||
          this.$route.params.selectedTime == 1
        ) {
          let year = "";
          var date = new Date();
          year = date.getFullYear();
          if (this.times2.length > 2) {
            this.times = this.times2;
          } else {
            this.times = year.toString() + "-" + this.times2;
          }
        } else if (this.$route.params.selectedTime == 2) {
          let year1 = "";
          let month1 = "";
          var date1 = new Date();
          year1 = date1.getFullYear();
          month1 = (date1.getMonth() + 1).toString();
          if (month1.length == 1) {
            month1 = "0" + month1;
          }
          this.times = year1.toString() + "-" + month1 + "-" + this.times2;
          console.log(this.times);
        }
        let params = {
          range: this.$route.params.selectedTime,
          type: val,
          firstSortCode: this.$route.params.regionCode,
          time: this.times
        };
        getComplaintDetailTime(params).then(res => {
          if (res.code == 200) {
            this.dataList = res.data;
          }
        });
      }
    },
    complaintDetail() {
      let params = {
        range: this.$route.params.selectedTime,
        type: this.type,
        firstSortCode: this.$route.params.regionCode
      };
      getComplaintDetail(params).then(res => {
        if (res.code == 200) {
          this.dataList = res.data;
        }
      });
    }
  },
  created() {
    // this.complaintDetail();
  }
};
</script>

<style lang='scss' scoped>
#accidentDetails {
  min-height: 100%;
  padding-top: 0.68rem;
  .wrap {
    margin: 0 0.15rem;
    .list {
      background-color: #ffffff;
      box-shadow: 0 0.02rem 0.12rem 0 rgba(0, 0, 0, 0.1);
      border-radius: 0.1rem;
      padding: 0.1rem 0.15rem;
      margin-bottom: 0.2rem;
      .content {
        background-color: #eee;
        margin-top: 0.1rem;
        padding: 0.08rem 0.14rem 0.14rem;
        border-radius: 0.05rem;
        .font-color {
          color: #9a9a9a;
        }
      }
    }
    .list:nth-child(1) {
      margin-top: 0.05rem;
    }
  }
}
</style>
