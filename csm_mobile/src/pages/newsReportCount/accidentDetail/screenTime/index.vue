<template>
  <div id="screenTime">
    <div class="wrap">
      <div v-if="showTime" class="box">
        <Group>
          <date-time
            v-model="value1"
            :format="format"
            @on-change="change"
            @on-cancel="log('cancel')"
            @on-confirm="onConfirm"
            @on-hide="log('hide', $event)"
          ></date-time>
        </Group>
      </div>
    </div>
  </div>
</template>

<script>
import { Datetime, Group } from "vux";
import { getComplaintDetail, getComplaintDetailTime } from "@/api/staff";
export default {
  name: "screenTime",
  props: ["selectedTime", "type", "sortCode", "dataList"],
  components: {
    "date-time": Datetime,
    Group
  },
  data() {
    return {
      showTime: true,
      value1: "",
      format: ""
    };
  },
  methods: {
    change(value) {
      console.log("change", value);
    },
    log(str1, str2 = "") {
      console.log(str1, str2);
    },
    onConfirm(value) {
      console.log("on-confirm arg", value);
      console.log("current value", this.value1);
      this.$emit("getTime", value);
      let times = "";
      if (this.selectedTime == 1) {
        var date = new Date();
        var year = date.getFullYear();
        times = year.toString() + "-" + value;
      } else if (this.selectedTime == 2) {
        var date = new Date();
        var year1 = date.getFullYear();
        var month1 = (date.getMonth() + 1).toString();
        if (month1.length == 1) {
          month1 = "0" + month1;
        }
        times = year1.toString() + "-" + month1.toString() + "-" + value;
      } else if (this.selectedTime == 0) {
        times = value;
      }
      console.log(times, "==");
      let params = {
        range: this.selectedTime,
        type: this.type,
        firstSortCode: this.sortCode,
        time: times
      };
      getComplaintDetailTime(params).then(res => {
        if (res.code == 200) {
          this.$emit("update:dataList", res.data);
        }
      });
      let str = "";
      let str1 = "";
      let month = "";
      month = "月";
      let val = value;
      // 0全部3当日2本月1本年
      if (this.selectedTime == "0") {
        if (value.indexOf("-") != -1) {
          value = value.replace("-", "年");
          str = value.substr(value.length - 2);
          str1 = str.charAt(0);
          if (str1 == "0") {
            str = str.replace("0", "");
          }
          this.value1 = value.substring(0, 5) + str + month;
        }
      } else if (this.selectedTime == "1") {
        if (value.length == 2) {
          if (value.charAt(0) == "0") {
            value = value.replace("0", "");
            var date = new Date();
            var year = date.getFullYear();
            this.value1 = year.toString() + "年" + value + "月";
          } else {
            var date = new Date();
            var year = date.getFullYear();
            this.value1 = year.toString() + "年" + value + "月";
          }
        }
      } else if (this.selectedTime == "2") {
        if (value.length == 2) {
          if (value.charAt(0) == "0") {
            value = value.replace("0", "");
            var date = new Date();
            var month1 = date.getMonth() + 1;
            this.value1 = month1.toString() + "月" + value + "日";
          } else {
            var date = new Date();
            var month1 = date.getMonth() + 1;
            this.value1 = month1.toString() + "月" + value + "日";
          }
        }
      }
    },
    filterTime() {
      // 0全部3当日2本月1本年
      if (this.selectedTime == 0) {
        this.showTime = true;
        this.format = "YYYY-MM";
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        // month = month < 10 ? "0" + month : month;
        var mydate = year.toString() + "年" + month.toString() + "月";
        this.value1 = mydate;
      } else if (this.selectedTime == 1) {
        this.showTime = true;
        this.format = "MM";
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        // month = month < 10 ? "0" + month : month;
        var mydate = year.toString() + "年" + month.toString() + "月";
        this.value1 = mydate;
      } else if (this.selectedTime == 2) {
        this.showTime = true;
        this.format = "DD";
        var date = new Date();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        // month = month < 10 ? "0" + month : month;
        var mydate = month.toString() + "月" + day.toString() + "日";
        this.value1 = mydate;
      } else if (this.selectedTime == 3) {
        this.showTime = false;
      }
    }
  },
  created() {
    this.filterTime();
  },
  mounted() {
  }
};
</script>

<style lang="scss">
.dp-left {
  color: #9b9b9b !important;
  font-size: 0.16rem;
  font-family: "PingFangSC-Regular, sans-serif";
}
.dp-right {
  color: #e53636 !important;
  font-size: 0.16rem;
  font-family: "PingFangSC-Regular, sans-serif";
}
.weui-cell__ft::after {
  content: "";
  display: inline-block!important;
  height: 0px!important;
  width: 0px!important;
  border-width: 0 !important;
  border-color: #ebebeb !important;
  border-style: solid!important;
  -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position: relative!important;
  top: 0px!important;
  position: absolute!important;
  top: 0%!important;
  margin-top: 0px!important;
  right: 0px!important;
}
</style>
<style lang='scss' scoped>
#screenTime .wrap {
  height: 0.35rem;
  // line-height: .35rem;
  // margin-bottom: 0.13rem;/deep/ .vux-cell-primary
}
#screenTime .box /deep/ .vux-datetime {
  float: right;
  border-radius: .14rem;
}
#screenTime .box /deep/ .vux-cell-value {
  float: right;
  display: inline-block;
  width: 1.1rem;
  height: 0.23rem;
  line-height: 0.23rem;
  text-align: center;
  border-radius: 0.14rem;
  background-color: #ebebeb;
  font-size: 0.12rem;
  color: #0d0e10;
  position: relative;
  background-image: url(../../../../assets/images/common/select.png);
  background-size: 100% 100%;
}
#screenTime .box /deep/ .weui-cell {
  padding: 0;
}
#screenTime .box /deep/ .weui-cells {
  position: inherit;
  background-color: #fbfbfb;
}
#screenTime .box /deep/ .weui-cells:before {
  border-top: 0;
}
#screenTime .box /deep/ .weui-cells:after {
  border-bottom: 0;
}
#screenTime .box /deep/ .weui-cell__ft {
  padding-right: 0;
}
#screenTime .box {
  // float: right;
  // width: 1.1rem;
  // height: 0.23rem;
  // line-height: 0.23rem;
}
</style>
