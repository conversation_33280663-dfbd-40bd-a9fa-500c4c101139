<template>
  <div class="area-city-count">
    <div
      class="ac-count-container"
      v-for="(item,index) in countList"
      :key="index"
      :style="index==countList.length-1?'border:none':''"
    >
      <div class="acc-item" @click="toDetail(item)">
        <div class="acc-item-title wryh">{{item.regionName}}</div>
        <div class="acc-item-num">{{item.regionCount}}/{{item.regionAllCount}}</div>
        <div class="acc-item-bar">
          <div
            class="acc-item-finish"
            :style="'width:'+(item.regionCount/item.regionAllCount)*100+'%'"
          ></div>
          <div class="acc-item-total"></div>
        </div>
        <img class="acc-item-turn" :src="require('@/assets/images/common/<EMAIL>')">
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ["data", "tabIsShow", "selectedTime1"],
  watch: {
    data() {
      this.countList = this.data;
    }
  },
  data() {
    return {
      countList: [],
    };
  },
  methods: {
    toDetail(val) {
      console.log(this.tabIsShow);
      console.log(this.$parent.selectedTime1, "+++");
      if (this.tabIsShow == "kh") {
        this.$router.push({
          name: "lvupDetail",
          params: {
            regionName: val.regionName,
            type: this.$parent.lvUpType,
            region: this.$parent.aoc,
            range: this.$parent.selectedTime
          }
        });
      } else if (this.tabIsShow == "yg") {
        this.$router.push({
          name: "accidentDetail",
          params: {
            regionCode: val.code,
            type: this.$parent.lvUpType,
            region: this.$parent.aoc,
            selectedTime: this.$parent.selectedTime1
          }
        });
      }
    }
  },
  created() {
    this.countList = this.data;
  }
};
</script>
<style lang="scss" scoped="scoped">
.ac-count-container {
  padding: 0.15rem 0.15rem 0.15rem 0;
  border-bottom: solid 0.5px #e6e6e6;
  .acc-item {
    height: 0.2rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .acc-item-bar {
      width: 1.31rem;
      .acc-item-total,
      .acc-item-finish {
        height: 0.1rem;
        border-top-right-radius: 0.06rem;
        border-bottom-right-radius: 0.06rem;
      }
      .acc-item-total {
        width: 100%;
        background-color: #e6e6e6;
      }
      .acc-item-finish {
        background-color: #fbdca8;
      }
    }
    .acc-item-num {
      width: 0.9rem;
      font-size: 0.14rem;
      color: #9b9b9b;
    }
    .acc-item-turn {
      display: inline-block;
      width: 0.08rem;
      height: 0.14rem;
    }
    .acc-item-title {
      width: 0.6rem;
      text-align: center;
      font-size: 0.14rem;
      color: #000000;
    }
  }
}
</style>
