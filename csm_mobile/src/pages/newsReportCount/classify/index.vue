<template>
  <div class="single-classify">
    <div
      class="classify-item"
      v-for="(item,index) in dataList"
      :key="index"
      :style="index == dataList.length-1?'border:none':''"
      @click="toDetail(item)"
    >
      <div class="line-container" :style="detail?'width:100%':ident==1?'width:100%':''">
        <div class="line-one">
          <div>
            <span class="circle-mark" v-if="!detail" :style="'background-color:'+item.bgColor"></span>
            <span v-if="item.secSortName == null">
              <span
                v-if="detail"
                class="classify-name"
                :style="detail?'margin-left:0;':''"
              >{{item.firstSortName}}</span>
              <span v-else class="classify-name">{{item.firstSortName}}</span>
            </span>
            <span v-else>
              <span
                v-if="detail"
                class="classify-name"
                :style="detail?'margin-left:0;':''"
              >{{item.firstSortName+'-'+item.secSortName}}</span>
              <span v-else class="classify-name">{{item.firstSortName}}</span>
            </span>
          </div>
          <div>
            <span class="num-span">{{item.finishCount}}</span>已完成
          </div>
        </div>
        <div class="line-two" :style="detail?'padding-left:0':''">
          <div v-if="detail">
            占比
            <span>{{((item.finishCount/item.groupCount)*100).toFixed(0)+'%'}}</span>
          </div>
          <div v-else>
            占比
            <span>{{((item.groupCount/item.allCount)*100).toFixed(0)+'%'}}</span>
          </div>
          <div>
            <span class="num-span">{{item.groupCount}}</span>总报事
          </div>
        </div>
      </div>
      <img
        v-if="tabIsShow =='kh' && !detail"
        class="right-icon"
        :src="require('@/assets/images/common/<EMAIL>')"
      >
      <img
        v-if="tabIsShow == 'yg' && ident!=1"
        class="right-icon"
        :src="require('@/assets/images/common/<EMAIL>')"
      >
    </div>
  </div>
</template>
<script>
export default {
  props: ["data", "detail", "tabIsShow", "ident"],
  watch: {
    data() {
      this.dataList = this.data;
    }
  },
  data() {
    return {
      dataList: Array
      // aaa: String
    };
  },
  methods: {
    toDetail(val) {
      console.log(val, "===", this.tabIsShow);
      let params = val;
      params.type = 1;
      if (this.tabIsShow == "kh") {
        this.$router.push({
          name: "classifyDetail",
          params: params
        });
      } else if (this.tabIsShow == "yg") {
        params.aoc = this.$parent.aoc;
        params.selectedTime = this.$parent.selectedTime1;
        this.$router.push({
          name: "reportingDetail",
          params: params
        });
      }
    }
  },
  created() {
    this.dataList = this.data;
  }
};
</script>
<style lang="scss" scoped="scoped">
.classify-item {
  padding: 0.1rem 0;
  border-bottom: solid 0.5px #dcdcdc;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-right: 0.15rem;
  .line-two {
    padding-left: 0.22rem;
    margin-top: 0.05rem;
  }
  .line-one,
  .line-two {
    height: 0.22rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    div {
      line-height: 0.22rem;
      font-size: 0.16rem;
      color: #9b9b9b;
      display:flex;
      flex-direction:row;
      justify-content: space-between;
      align-items:center;
    }
  }
  .circle-mark {
    display: inline-block;
    width: 0.08rem;
    height: 0.08rem;
    border-radius: 0.06rem;
    vertical-align: middle;
  }
  .classify-name {
    display: -webkit-box!important;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 1!important;
    overflow:hidden!important;
    color: #262626;
    font-size: 0.16rem;
    margin-left: 0.1rem;
    vertical-align: middle;
    max-width: 2.0rem!important;
    // white-space: nowrap;  /*强制span不换行*/
    // display: inline-block;  /*将span当做块级元素对待*/
    // overflow: hidden;  /*超出宽度部分隐藏*/
    // text-overflow: ellipsis;  /*超出部分以点号代替*/
  }
  .num-span {
    display: inline-block;
    padding-right: 0.06rem;
  }
  .right-icon {
    width: 0.08rem;
    height: 0.14rem;
  }
  .line-container {
    width: 2.97rem;
  }
}
</style>

