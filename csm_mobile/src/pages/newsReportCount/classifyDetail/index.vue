<template>
    <div class="classifyDetail">
        <v-tab :tabList="tabList" :defaultActive="$route.params.type"/>
        <div class="single-info-container" style="margin:0 0.15rem;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0.15rem;">
            <v-classify :data="dataList" :detail="true"/>
            <!-- <line-item v-for="(item,index) in dataList" :key="index" :title="item.firstSortName+'-'+item.secSortName" :content="item.groupCount+'个'" :border="index != dataList.length-1?'bottom':'none'"></line-item> -->
        </div>
        <load-more :show-loading="false" tip="暂无数据" v-if="showNoData"></load-more>
    </div>
</template>
<script>
import Tab from '@/components/tab.vue'
import LineTable from '@/components/lineTable.vue'
import classify from './../classify'
import { LoadMore } from 'vux'
import { Toast } from 'vux'
import {getClassifyDetail} from '@/api'
    export default{
        components:{
            'v-tab':Tab,
            'line-item':LineTable,
            'v-classify':classify,
            LoadMore,
            Toast
        },
        data(){
            return{
                tabList:[],
                defaultType:null,
                dataList:Array,
                showNoData:false,
            }
        },
        methods:{
            getPageData(){
                let params = {
                   range:this.$route.params.range,
                   firstSortCode:this.$route.params.firstSortCode 
                }
                getClassifyDetail(params).then((res) => {
                    if(res.code == 200){
                        this.dataList = res.data
                    }else{
                        this.$vux.toast.text(res.message, 'error');
                    }
                })
            }
        },
        created(){
            this.getPageData();
            this.tabList = [
                {label:this.$route.params.firstSortName,value:'1'},
            ]
        }
    }
</script>
<style lang="scss" scoped="scoped">
.classifyDetail{
    min-height:100%;
    padding-top:0.68rem;
}
.page-title{
   font-family: 'PingFangSC-Semibold'!important; 
}
</style>