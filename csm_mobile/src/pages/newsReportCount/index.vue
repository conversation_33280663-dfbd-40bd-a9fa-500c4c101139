<template>
  <div class="body-container">
    <v-tab :tabList="tabList" defaultActive="kh" @change="getTabChange"/>
    <div class="news-report-count" v-show="tabIsShow =='kh'">
      <span class="title-16">报事统计</span>
      <v-picker :data="timeStage" style="float:right;" @change="getSelectedTime"/>
      <div class="echarts-pie-container" id="myChartOne">
        
      </div>
      <div class="count-single-card" style="padding-top:0;padding-bottom:0">
        <div class="report-total-count">
          <div class="report-total-count-title wryh" style="color:#000000;">
            报事总量
          </div>
          <div class="report-total-count-info">
            <div class="count-num"><span>{{newsReportCountData.finishCount}}</span><span>{{newsReportCountData.allCount}}</span></div>
            <div class="count-bar">
              <div class="count-bar-finish" :style="'width:'+newsReportCountData.finishCount/newsReportCountData.allCount*100+'%'"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="count-single-card" style="margin-top:0.1rem;padding-top:0;padding-bottom:0;padding-right:0;">
        <v-classify :data="classifyData"  :tabIsShow="tabIsShow"/>
      </div>
      <div class="clasify-control">
        <span class="title-16">投诉/报修 升级统计</span>
        <v-picker :data="classifyOption" style="float:right;" @change="getSelectedClassify"/>
        <div style="clear:both;"></div>
        <div class="classify-btn-control">
          <div> 
            <span :class="aoc==2?'diy-btn-active':'diy-btn'" @click="changeAreaAndCity(2)">区域</span><span :class="aoc==1?'diy-btn-active':'diy-btn'" @click="changeAreaAndCity(1)">城市</span>
          </div>
          <div>
            <div style="height:0.16rem;line-height:0.16rem;color:#000000;">
              <span class="wryh" style="display:inline-block;width:0.08rem;height:0.08rem;border-radius:0.06rem;background-color:#FBDCA8"></span>
              升级&nbsp; / &nbsp;
              <span class="wryh" style="display:inline-block;width:0.08rem;height:0.08rem;border-radius:0.06rem;background-color:#E6E6E6"></span>
              全部
            </div>
          </div>
        </div>
      </div>
      <div class="count-single-card" style="padding-right:0;padding-top:0;padding-bottom:0">
        <v-acc :data="lvUpData" :tabIsShow="tabIsShow"/>
      </div>
      <span class="title-16" style="display:inline-block;margin-top:0.2rem;margin-bottom:0.1rem;">报事满意度</span>
      <div class="count-single-card" style="padding-right:0;padding-bottom:0;">
        <div class="echarts-pie-area" id="SatisfactionCharts">

        </div>
        <v-rank :data="rankList"/>
      </div>
    </div>
    <v-staff ref='staff' v-show="tabIsShow=='yg'" :tabIsShow="tabIsShow"></v-staff>
  </div>
</template>

<script>
  import {getNewsReportCountCus,getNewsReportLvUpCount,getSatisfactionRateCount} from '@/api'
  import Tab from '@/components/tab'
  import PopPicker from '@/components/popPicker'
  import classify from './classify'
  import cityArea from './areaOrCity'
  import rank from './rank'
  import staff from './staff'
  export default {
    components:{
      'v-tab':Tab,
      'v-picker':PopPicker,
      'v-classify':classify,
      'v-acc':cityArea,
      'v-rank':rank,
      'v-staff':staff
    },
    watch:{
      aoc(){
        this.getLvUpData(this.selectedTime,this.aoc,this.lvUpType);
      }
    },
    data(){
      return{
        tabList:[
          {label:'客户',value:'kh'},
          {label:'员工',value:'yg',},
          {label:'供应商',value:'gys',disabled:true,message:'暂时没有供应商报事数据'}
        ],
        classifyOption:[
          {label:'投诉',value:'1'},
          {label:'报修',value:'2'},
        ],
        timeStage:[
          {label:'全部',value:0},
          {label:'本年',value:1,default:true},
          {label:'本月',value:2},
        ],
        rankList:[],
        newsReportCountData:Object,
        classifyData:Array,
        selectedTime:1,
        lvUpData:Array,
        aoc:2,//城市、区域按钮控制
        lvUpType:1,//选择升级统计类型
        //colorPickerArray:['#609CF7','#ff9f7f','#F0D342','#9d96f5'],
        colorPickerArray:['#837ce6','#f29200','#ee4e40','#01afd2'],
        tabIsShow: "kh"
      }
    },
    methods:{
      getSelectedTime(val){
        this.selectedTime = val;
        this.getCountData(val);
        this.getSatisficing(val);
        this.getLvUpData(val,this.aoc,this.lvUpType);
      },
      getSelectedClassify(val){
        this.lvUpType = val;
        this.getLvUpData(this.selectedTime,this.aoc,val);
      },
      getTabChange(val){
        this.tabIsShow = val;
        console.log('changeTab',val);
      },
      changeAreaAndCity(type){
        this.aoc = type;
      },
      getCountData(tim){//获取统计数据
        let params = {
          range:tim
        }
        getNewsReportCountCus(params).then((res) => {
          this.newsReportCountData = res.data;
          res.data.statisticsGroupList.map((item,index) => {
            if(index <= this.colorPickerArray.length-1){
              item.bgColor = this.colorPickerArray[index];
            }else{
              item.bgColor = this.buildRandowColr();
            }
            item.allCount = res.data.allCount;
          })
          this.classifyData = res.data.statisticsGroupList;
          this.classifyData.map((item) => {
            item.range = tim;
          })
          this.renderCountChart(this.classifyData);
        })
      },
      getLvUpData(range,region,type){//获取报事升级数据---range时间（本年，本月），region区域（城市，区域），type类型（投诉，报修）
        let params = {
          range:range,
          region:region,
          type:type
        }
        getNewsReportLvUpCount(params).then((res) => {
          let total = {regionName:'合计',regionAllCount:res.data.allCount,regionCount:res.data.upCount,isCount:true};
          res.data.statisticsUpGroupList = res.data.statisticsUpGroupList?res.data.statisticsUpGroupList:new Array();
          res.data.statisticsUpGroupList.unshift(total);
          this.lvUpData = res.data.statisticsUpGroupList;
        })
      },
      getSatisficing(tim){//获取满意度数据
        let params = {
          range:tim
        }
        getSatisfactionRateCount(params).then((res) => {
          this.renderSatisfactionChart(50);
          setTimeout(() => {
            this.renderSatisfactionChart(res.data.satisfactionProportion);
          }, 300); 
          res.data.satisficingGroupVoList.map((item,index) => {
            if(index == 0 && item.satisfactionProportion<res.data.thirdProportion){
              res.data.satisficingGroupVoList.unshift({regionName:'第三方机构评分',type:'groupavr',satisfactionProportion:res.data.thirdProportion,seq:''});
            }else if(item.satisfactionProportion<res.data.thirdProportion && res.data.satisficingGroupVoList[index-1].satisfactionProportion >= res.data.thirdProportion && res.data.satisficingGroupVoList[index-1].type != 'groupavr'){
              res.data.satisficingGroupVoList.splice(index,0,{regionName:'第三方机构评分',type:'groupavr',satisfactionProportion:res.data.thirdProportion,seq:''});
            }
          })
          let thirdObj = {regionName:res.data.scoreName,type:'avg',satisfactionProportion:res.data.thirdProportion,seq:''}
          res.data.satisficingGroupVoList.unshift(thirdObj);
          this.rankList = res.data.satisficingGroupVoList;

        })
      },
      renderSatisfactionChart(data){//渲染报事满意度ECharts图
        let myChart = this.$mycharts.init(document.getElementById('SatisfactionCharts'));

        let satisfaction = data?data:data==0?data:100;
        let noSatisfaction = 100 - satisfaction;

        let option = {
            series: [
                {
                    name:'报事满意度',
                    type:'pie',
                    radius: ['55%', '65%'],
                    avoidLabelOverlap: false,
                    hoverAnimation:false,
                    label: {
                        normal: {
                            show: false,
                            position: 'center',
                            formatter: "{d|d}\n{b|b}"
                        },
                        emphasis: {
                            show: true,
                            formatter: (data) => {
                              if(data.percent == satisfaction)
                                return "{percent|"+data.percent.toFixed(1)+'}\n{name|' + data.name +"}";
                              else
                                return "{percent|"+(100 - data.percent).toFixed(1)+'}\n{name|' + data.name +"}";
                                // return "{percent|"+data.percent.toFixed(0)+'}\n{name|' + data.name +"}";
                            },
                            rich:{
                                percent:{fontSize: 20,color:'#000000'},
                                name:{fontSize: 12,color:'#9B9B9B'}    
                            }
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data:[
                        {value:satisfaction, name:'总满意度',itemStyle:{color:'#FBDCA8'},emphasis:{itemStyle:{color:'#FBDCA8'}}},
                        {value:noSatisfaction, name:'总满意度',itemStyle:{color:'#E6E6E6'},emphasis:{itemStyle:{color:'#E6E6E6'}}},
                    ]
                }
            ]
        }
        myChart.setOption(option)
        this.defaultSet(myChart,option);
      },
      defaultSet(myChart,option){
        myChart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: 0
        });

        //记录上次高亮的索引
        let lastMouseOverIndex=null;
        // mouseover事件，记录当前数据索引并取消其他高亮，over在out之后
        myChart.on('mouseover', function (params) {
            var dataLen = option.series[0].data.length;
            lastMouseOverIndex = params.dataIndex;
            for(var i=0;i<dataLen;i++){
                if(i!= params.dataIndex){
                    myChart.dispatchAction({
                        type: 'downplay',
                        seriesIndex: 0,
                        dataIndex: i
                    })
                }
            }
        });
        // mouseout事件，将上次的高亮
        myChart.on('mouseout', function (params) {
            myChart.dispatchAction({
                type: 'highlight',
                seriesIndex: 0,
                dataIndex: 0//lastMouseOverIndex
            })
        });
      },
      renderCountChart(data){//渲染报事统计ECharts图
        let myChart = this.$mycharts.init(document.getElementById('myChartOne'));
        let renderData = new Array();
        data.map((item) => {
          let obj  = new Object();
          obj.value = item.groupCount;
          obj.name = item.firstSortName+'-'+item.secSortName;
          obj.itemStyle = {color:item.bgColor};
          renderData.push(obj);
        })
        let option = {
          series:[
            {
              name:'报事统计',
              type:'pie',
              radius:['34%','66%'],
              avoidLabelOverlap: false,
              hoverAnimation:false,
              label: {
                normal: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    show: false,
                    textStyle: {
                        fontSize: '30',
                        fontWeight: 'bold'
                    }
                }
              },
              data:renderData,
            }
          ],
        }
        myChart.setOption(option,true)
      },
      buildRandowColr(){
        let r=Math.floor(Math.random()*256);
        let g=Math.floor(Math.random()*256);
        let b=Math.floor(Math.random()*256);
        return "rgb("+r+','+g+','+b+")";
        //return '#'+Math.floor(Math.random()*256).toString(10);
        //return '#'+Math.floor(Math.random()*0xffffff).toString(16);
      }
    },
    mounted() {
      this.getCountData(1);
      this.getLvUpData(1,2,1);
      this.getSatisficing(1);
    },
  }
</script>

<style lang="scss" scoped>
.body-container{
  background-color:#fbfbfb;
}
.news-report-count{
  padding:0.72rem 0.15rem 1.05rem 0.15rem;
}
.echarts-pie-container{
  height:1.9rem;
}
.count-single-card{
  background-color:#ffffff;
  box-shadow:0 0.02rem 0.12rem 0 rgba(0,0,0,0.1);
  border-radius: 0.1rem;
  padding:0.1rem 0.15rem;
  .echarts-pie-area{
    padding-right:0.15rem;
    height:1.86rem;
    padding-bottom:0.1rem;
  }
}
.report-total-count{
  padding:0.07rem 0 0.08rem 0.05rem;
  display:flex;
  flex-direction: row;
  justify-content: space-between;
  .report-total-count-title{
    width:0.76rem;
    text-align:left;
    color:#000000;
    height:0.35rem;
    line-height:0.35rem;
    font-size:0.14rem;
  }
  .report-total-count-info{
    width:2.34rem;
    height:0.35rem;
    float:right;
    display:flex;
    flex-direction:column;
    justify-content: space-between;
    .count-num{
      display:flex;
      flex-direction:row;
      justify-content:space-between;
      span{
        font-size:0.14rem;
        color:#9B9B9B;
      }
    }
    .count-bar{
      width:2.34rem;
      height:.1rem;
      background-color:#e6e6e6;
      border-top-right-radius:0.06rem;
      border-bottom-right-radius: 0.06rem;
      .count-bar-finish{
        height:100%;
        background-color:#FBDCA8;
        border-top-right-radius:0.06rem;
        border-bottom-right-radius: 0.06rem;
      }
    }
  }
}
.clasify-control{
    padding-top:0.2rem;
    padding-bottom:0.2rem;
    .classify-btn-control{
      height:0.33rem;
      display:flex;
      flex-direction:row;
      justify-content:space-between;
      align-items: flex-end;
      .diy-btn{
        display:inline-block;
        width:0.54rem;
        height:0.23rem;
        line-height:0.23rem;
        border:solid 1px #9B9B9B;
        border-radius:0.04rem;
        text-align:center;
        background-color:#ffffff;
        font-size:0.12rem;
        color:#9B9B9B;
        margin-right:0.1rem;
      }
      .diy-btn-active{
        display:inline-block;
        width:0.54rem;
        height:0.23rem;
        line-height:0.23rem;
        border:solid 1px #E53636;
        border-radius:0.04rem;
        text-align:center;
        background-color:#ffffff;
        font-size:0.12rem;
        color:#E53636;
        margin-right:0.1rem;
      }
    }
}
.title-16{
  font-family: 'PingFangSC-Medium'!important;
}
</style>
