<template>
    <div class="rank-count">
        <div class="rank-item" v-for="(item,index) in rankList" :key="index" :style="item.type=='groupavr'?'height:0.3rem;':''">
            <div v-if="item.type!='groupavr'" class="rank-item-icon">
                <img v-if="item.type!='avg'" :src="item.change==0?require('@/assets/images/common/<EMAIL>'):item.change==1?require('@/assets/images/common/<EMAIL>'):item.change==-1?require('@/assets/images/common/<EMAIL>'):''">
            </div>
            <div v-if="item.type!='groupavr'" class="rank-item-info" :style="index == rankList.length-1 || rankList[index+1].type=='groupavr'?'border:none':''">
                <div class="rank-sort-num">{{item.type=='avg'?'-':Number(item.seq)+1}}</div>
                <div class="rank-title-rate">
                    <div class="rank-item-title">{{item.regionName}}</div>
                    <div class="rank-item-total">
                        <div class="rank-item-finish" :style="item.type=='avg'?'background-color:#88B2FF;width:'+item.satisfactionProportion+'%':'width:'+item.satisfactionProportion+'%'"></div>
                    </div>
                </div>
                <div class="rank-item-score">
                    {{item.satisfactionProportion}}
                </div>
            </div>
            <div class="avrage-rank" v-if="item.type=='groupavr'"> 
                <div>-----------------------------------------------------------------------------------</div>
                <div style="width:1.5rem;font-size:0.14rem;text-align:center;">{{'第三方机构评分 '+item.satisfactionProportion}}</div>
                <div>-----------------------------------------------------------------------------------</div>
            </div>
        </div>
    </div>
</template>
<script>
    export default{
        props:[
            'data'
        ],
        watch:{
            data(){
                this.rankList = this.data;
            }
        },
        data(){
            return{
                rankList:[],
            }
        },
        created(){
            this.rankList = this.data;
        }
    }
</script>
<style lang="scss" scoped="scoped">
.avrage-rank{
    height:0.3rem;
    display:flex;
    flex-direction:row;
    justify-content:space-between;
    width:100%;
    padding-right:0.15rem;
    div{
        width:0.85rem;
        line-height:0.3rem;
        color:#E53636;
        overflow:hidden;
    }
}
    .rank-item{
       height:0.69rem;
       display:flex;
       flex-direction:row;
       justify-content:space-between;
       align-items:center;
       .rank-item-icon{
           width:0.1rem;
           height:0.1rem;
           padding-right:0.1rem;
           img{
               width:100%;
               height:100%;
               vertical-align:top;
           }
       }
       .rank-item-info{
           width:3.1rem;
           height:100%;
           border-bottom:solid 0.5px #E6E6E6;
           padding:0 0.15rem 0 0;
           display:flex;
           flex-direction:row;
           justify-content:space-between;
           align-items:center;
           .rank-sort-num{
               width:0.22rem;
               height:0.22rem;
               border:solid 1px #E6E6E6;
               border-radius:0.11rem;
               background-color:#F5F5F5;
               text-align:center;
               line-height:0.22rem;
               color:#000000;
           }
           .rank-title-rate{
               width:2.30rem;
               height:0.37rem;
               display:flex;
               flex-direction:column;
               justify-content:space-between;
               .rank-item-title{
                  width:100%;
                  height:0.22rem;
                  font-size:0.16rem;
                  color:#000000; 
               }
               .rank-item-total{
                   width:100%;
                   height:0.1rem;
                   background-color:#E6E6E6;
                   border-top-right-radius: 0.06rem;
                   border-bottom-right-radius: 0.06rem;
                   .rank-item-finish{
                        height:0.1rem;
                        background-color:#FBDCA8;
                        border-top-right-radius: 0.06rem;
                        border-bottom-right-radius: 0.06rem; 
                   }
               }
           }
           .rank-item-score{
               width:0.22rem;
               height:0.37rem;
               font-size:0.14rem;
               line-height:0.63rem;
               color:#000000;  
           }
       } 
    }
</style>

