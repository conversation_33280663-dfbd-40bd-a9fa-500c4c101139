<template>
  <div id="reportingDetail">
    <v-tab :tabList="tabList" :defaultActive="$route.params.type"/>
    <div class="echarts-pie-container" id="myChartOne"></div>
    <div class="count-single-card" style="margin: 0px 0.15rem; padding: 0px 0.15rem;">
      <div class="report-total-count">
        <div class="report-total-count-title wryh" style="color:#000000;">报事总量</div>
        <div class="report-total-count-info">
          <div class="count-num">
            <span>{{sumData.finishCount}}</span>
            <span>{{sumData.allCount}}</span>
          </div>
          <div class="count-bar">
            <div class="count-bar-finish" :style="'width:'+sumData.finishCount/sumData.allCount*100+'%'"></div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="single-info-container"
      style="margin:0.15rem 0.15rem;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0.15rem;"
    >
      <v-classify :data="dataList" ident="1"/>
      <!-- <line-item v-for="(item,index) in dataList" :key="index" :title="item.firstSortName+'-'+item.secSortName" :content="item.groupCount+'个'" :border="index != dataList.length-1?'bottom':'none'"></line-item> -->
    </div>
  </div>
</template>

<script>
import Tab from "@/components/tab.vue";
import classify from "./../classify";
import { getReportingDetail } from "@/api/staff";
export default {
  name: "reportingDetail",
  components: {
    "v-tab": Tab,
    "v-classify": classify
  },
  data() {
    return {
      tabList: [],
      dataList: Array,
      sumData: [],
      colorPickerArray: ["#837ce6", "#f29200", "#ee4e40", "#01afd2"],
    };
  },
  methods: {
    reportingDetail() {
      let params = {
        range: this.$route.params.selectedTime,
        firstSortCode: this.$route.params.firstSortCode,
        type: this.$route.params.aoc
      };
      getReportingDetail(params).then(res => {
        if (res.code == 200) {
          this.sumData = res.data;
          res.data.statisticsGroupList.map((item, index) => {
            if (index <= this.colorPickerArray.length - 1) {
              item.bgColor = this.colorPickerArray[index];
            } else {
              item.bgColor = this.buildRandowColr();
            }
            item.allCount = res.data.allCount;
          });
          this.dataList = res.data.statisticsGroupList;
          this.renderCountChart(this.dataList);
        } else {
          this.$vux.toast.text(res.message, "error");
        }
      });
    },
    buildRandowColr() {
      let r = Math.floor(Math.random() * 256);
      let g = Math.floor(Math.random() * 256);
      let b = Math.floor(Math.random() * 256);
      return "rgb(" + r + "," + g + "," + b + ")";
      //return '#'+Math.floor(Math.random()*256).toString(10);
      //return '#'+Math.floor(Math.random()*0xffffff).toString(16);
    },
    renderCountChart(data) {
      //渲染报事统计ECharts图
      let myChart = this.$mycharts.init(document.getElementById("myChartOne"));
      let renderData = new Array();
        data.map(item => {
          let obj = new Object();
          obj.value = item.groupCount;
          obj.name = item.firstSortName + "-" + item.secSortName;
          obj.itemStyle = { color: item.bgColor };
          renderData.push(obj);
        });
      let option = {
        series: [
          {
            name: "报事统计",
            type: "pie",
            radius: ["34%", "66%"],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              normal: {
                show: false,
                position: "center"
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: "30",
                  fontWeight: "bold"
                }
              }
            },
            data: renderData
          }
        ]
      };
      myChart.setOption(option, true);
    }
  },
  mounted() {
    this.reportingDetail();
  },
  created() {
    this.tabList = [{ label: this.$route.params.firstSortName, value: "1" }];
  }
};
</script>

<style lang="scss" scoped>
#reportingDetail {
  min-height: 100%;
  padding-top: 0.68rem;
}
#reportingDetail .echarts-pie-container {
  height: 1.9rem;
}
.count-single-card {
  background-color: #ffffff;
  box-shadow: 0 0.02rem 0.12rem 0 rgba(0, 0, 0, 0.1);
  border-radius: 0.1rem;
  padding: 0.1rem 0.15rem;
  .echarts-pie-area {
    padding-right: 0.15rem;
    height: 1.86rem;
    padding-bottom: 0.1rem;
  }
}
.report-total-count {
  padding: 0.07rem 0 0.08rem 0.05rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .report-total-count-title {
    width: 0.76rem;
    text-align: left;
    color: #000000;
    height: 0.35rem;
    line-height: 0.35rem;
    font-size: 0.14rem;
  }
  .report-total-count-info {
    width: 2.34rem;
    height: 0.35rem;
    float: right;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .count-num {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      span {
        font-size: 0.14rem;
        color: #9b9b9b;
      }
    }
    .count-bar {
      width: 2.34rem;
      height: 0.1rem;
      background-color: #e6e6e6;
      border-top-right-radius: 0.06rem;
      border-bottom-right-radius: 0.06rem;
      .count-bar-finish {
        height: 100%;
        background-color: #fbdca8;
        border-top-right-radius: 0.06rem;
        border-bottom-right-radius: 0.06rem;
      }
    }
  }
}
</style>
