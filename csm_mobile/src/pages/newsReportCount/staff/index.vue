<template>
  <div id="staff">
    <span class="title-16">报事趋势统计（日均报事量）</span>
    <div id="myChartsOne"></div>
    <div class="newspaper-statistics">
      <span class="title-16">报事统计</span>
      <div class="statistics">
        <div class="statistics-title">
          <span :class="aoc==1?'diy-btn-active':'diy-btn'" @click="changeAreaAndCity(1)">员工</span>
          <span :class="aoc==2?'diy-btn-active':'diy-btn'" @click="changeAreaAndCity(2)">VIP</span>
          <v-picker :data="timeStage" style="float:right;" @change="getSelectedTime"/>
        </div>
        <div id="myChartsTwo"></div>
        <div class="count-single-card" style="padding-top:0;padding-bottom:0">
          <div class="report-total-count">
            <div class="report-total-count-title wryh" style="color:#000000;">报事总量</div>
            <div class="report-total-count-info">
              <div class="count-num">
                <span>{{newspaperStatisticsData.finishCount}}</span>
                <span>{{newspaperStatisticsData.allCount}}</span>
              </div>
              <div class="count-bar">
                <div
                  class="count-bar-finish"
                  :style="'width:'+newspaperStatisticsData.finishCount/newspaperStatisticsData.allCount*100+'%'"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="count-single-card"
          style="margin-top:0.1rem;padding-top:0;padding-bottom:0;padding-right:0;"
        >
          <v-classify :data="classifyData" :tabIsShow="tabIsShow"/>
        </div>
      </div>
    </div>
    <div class="complaint">
      <div class="clasify-control" style="padding: .2rem 0;">
        <span class="title-16">投诉/事故统计</span>
        <div style="height:0.16rem;line-height:0.16rem;color:#000000;padding-top:.1rem;">
          <span
            class="wryh"
            style="display:inline-block;width:0.08rem;height:0.08rem;border-radius:0.06rem;background-color:#FBDCA8"
          ></span>
          事故&nbsp; / &nbsp;
          <span
            class="wryh"
            style="display:inline-block;width:0.08rem;height:0.08rem;border-radius:0.06rem;background-color:#E6E6E6"
          ></span>
          全部
          <v-picker :data="classifyOption" style="float:right;" @change="getSelectedClassify"/>
        </div>
      </div>

      <div class="count-single-card" style="padding-right:0;padding-top:0;padding-bottom:0">
        <v-acc :data="lvUpData" :tabIsShow="tabIsShow" :selectedTime1="selectedTime1"/>
      </div>
    </div>
  </div>
</template>

<script>
import { getNewsReportLvUpCount } from "@/api";
import {
  getNewspaperTrend,
  getNewspaperStatistics,
  getComplaintsStatistics
} from "@/api/staff";
import PopPicker from "@/components/popPicker";
import classify from "../classify";
import cityArea from "../areaOrCity";
import { setTimeout, setInterval } from "timers";
export default {
  name: "staff",
  components: {
    "v-picker": PopPicker,
    "v-classify": classify,
    "v-acc": cityArea
  },
  props: ["tabIsShow"],
  watch: {
    tabIsShow() {
      this.newspaperTrend();
      this.newspaperStatistics(this.selectedTime1, this.aoc);
    }
  },
  data() {
    return {
      aoc: 1, //员工、vip按钮控制
      timeStage: [
        { label: "全部", value: 0 },
        { label: "当日", value: 3 },
        { label: "本月", value: 2 },
        { label: "本年", value: 1, default: true }
      ],
      selectedTime1: 1,
      classifyData: Array,
      colorPickerArray: ["#837ce6", "#f29200", "#ee4e40", "#01afd2"],
      classifyOption: [
        { label: "事故", value: "1" },
        { label: "投诉", value: "2" }
      ],
      lvUpType: 1, //选择事故统计类型
      lvUpData: Array,
      newspaperStatisticsData: Array // 报事统计数据
    };
  },
  methods: {
    // 报事趋势统计
    newspaperTrend() {
      getNewspaperTrend().then(res => {
        // this.renderNewspaperTrendChart([{ month: 0, avgOfMonth: 0 }]);
        // setTimeout(() => {
        this.renderNewspaperTrendChart(res.data);
        // }, 300);
        // if (res.code == 200) {
        // }
      });
    },
    renderNewspaperTrendChart(data) {
      let renderTime = new Array();
      let reportedVolume = new Array();

      data.map(res => {
        renderTime.push(res.month);
        reportedVolume.push(res.avgOfMonth);
      });
      
      renderTime.reverse();
      reportedVolume.reverse();

      var parent = document.getElementById("myChartsOne");
      parent.innerHTML = "";
      let divMap = "map";
      let mapD = document.getElementById(divMap);
      var div = document.createElement("div");
      div.setAttribute("id", divMap);
      div.style.height = "1.8rem";
      parent.appendChild(div);
      // var chart = ECharts.init(document.getElementById(divMap));
      let myChart = this.$mycharts.init(document.getElementById(divMap));

      // chart.setOption({ 图表设置 });

      // let myChart = this.$mycharts.init(document.getElementById("myChartsOne"));
      let option = {
        tooltip: {
          trigger: "axis",
          formatter: function() {
            return "";
          },
          // backgroundColor: 'red',//背景颜色（此时为默认色）
          // triggerOn: "click"
        },
        series: [
          {
            data: reportedVolume,
            type: "line",
            smooth: true,
            itemStyle: {
              normal: {
                color: "#f1ad59",
                lineStyle: {
                  color: "#f1ad59"
                },
                label: { show: true, color: "#121212" }
              }
            },
            symbolSize: 9
          }
        ],
        grid: {
          left: "0%",
          right: "6%",
          top: "16%",
          bottom: "5%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: renderTime,
          // axis: { show: false }
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            formatter: function(value, idx) {
              // var date = new Date(value);
              // return idx === 0
              //   ? value
              //   : [date.getMonth() + 1, date.getDate()].join("-");
              if (value.length == 1) {
                return 0 + value + "月";
              } else {
                return value + "月";
              }
            },
            textStyle: {
              color: "#121212"
            }
          },
          splitLine: {
            show: true,
            // 改变轴线颜色
            lineStyle: {
              // 使用深浅的间隔色
              type: "dashed",
              color: ["#dedede"],
              width: 1
            }
          },
        },
        yAxis: {
          type: "value",
          // splitLine: {
          //   show: false
          // }
          show: false,
        }
      };
      myChart.setOption(option, true);
    },
    // 员工、vip
    changeAreaAndCity(type) {
      this.aoc = type;
      this.newspaperStatistics(this.selectedTime1, this.aoc);
    },
    getSelectedTime(val) {
      this.selectedTime1 = val;
      // this.getCountData(val);
      // this.getSatisficing(val);
      this.newspaperStatistics(this.selectedTime1, this.aoc);
      this.accidentStatistics(this.selectedTime1, this.lvUpType);
    },
    // 报事统计
    newspaperStatistics(selectedTime, aoc) {
      let params = {
        range: selectedTime,
        type: aoc
      };
      getNewspaperStatistics(params).then(res => {
        if (res.code == 200) {
          this.newspaperStatisticsData = res.data;
          res.data.statisticsGroupList.map((item, index) => {
            if (index <= this.colorPickerArray.length - 1) {
              item.bgColor = this.colorPickerArray[index];
            } else {
              item.bgColor = this.buildRandowColr();
            }
            item.allCount = res.data.allCount;
          });
          this.classifyData = res.data.statisticsGroupList;
          this.renderStatisticsChart(this.classifyData);
        }
      });
    },
    buildRandowColr() {
      let r = Math.floor(Math.random() * 256);
      let g = Math.floor(Math.random() * 256);
      let b = Math.floor(Math.random() * 256);
      return "rgb(" + r + "," + g + "," + b + ")";
      //return '#'+Math.floor(Math.random()*256).toString(10);
      //return '#'+Math.floor(Math.random()*0xffffff).toString(16);
    },
    renderStatisticsChart(data) {
      var parent1 = document.getElementById("myChartsTwo");
      parent1.innerHTML = "";
      let divMap1 = "map1";
      let mapD = document.getElementById(divMap1);
      var div1 = document.createElement("div");
      div1.setAttribute("id", divMap1);
      div1.style.height = "1.9rem";
      parent1.appendChild(div1);
      // var chart = ECharts.init(document.getElementById(divMap));
      // let myChart = this.$mycharts.init(document.getElementById(divMap));

      let myChart = this.$mycharts.init(document.getElementById(divMap1));
      let renderData = new Array();
      data.map(item => {
        let obj = new Object();
        obj.value = item.groupCount;
        obj.name = item.firstSortName + "-" + item.secSortName;
        obj.itemStyle = { color: item.bgColor };
        renderData.push(obj);
      });
      let option = {
        series: [
          {
            name: "报事统计",
            type: "pie",
            radius: ["34%", "66%"],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              normal: {
                show: false,
                position: "center"
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: "30",
                  fontWeight: "bold"
                }
              }
            },
            data: renderData
          }
        ]
      };
      myChart.setOption(option, true);
    },

    getSelectedClassify(val) {
      this.lvUpType = val;
      this.accidentStatistics(this.selectedTime1, val);
    },
    // 投诉/事故统计
    accidentStatistics(range, type) {
      let params = {
        range: range,
        type: type
      };
      getComplaintsStatistics(params).then(res => {
        if (res.code == 200) {
          res.data.accidentGroupVoList = res.data.accidentGroupVoList
            ? res.data.accidentGroupVoList
            : new Array();
          res.data.accidentGroupVoList.map(v => {
            v.regionName = v.codeName;
            v.regionAllCount = v.codeAllCount;
            v.regionCount = v.codeCount;
          });
          let total = {
            regionName: "合计",
            regionAllCount: res.data.allCount,
            regionCount: res.data.accidentCount,
            isCount: true
          };
          res.data.accidentGroupVoList.unshift(total);
          this.lvUpData = res.data.accidentGroupVoList;
        }
      });
    }
  },
  mounted() {
    this.newspaperTrend();
    this.newspaperStatistics(1, 1);
    this.accidentStatistics(1, 1);
  }
};
</script>

<style lang="scss" scoped>
#staff {
  padding: 0.72rem 0.15rem 1.05rem 0.15rem;
}
#myChartsOne {
  height: 1.8rem;
  background-color: #f8f8f8;
  margin: 0.1rem 0;
}
#myChartsTwo {
  height: 1.9rem;
}
#staff .statistics-title {
  padding-top: 0.1rem;
}
.diy-btn {
  display: inline-block;
  width: 0.54rem;
  height: 0.23rem;
  line-height: 0.23rem;
  border: solid 1px #9b9b9b;
  border-radius: 0.04rem;
  text-align: center;
  background-color: #ffffff;
  font-size: 0.12rem;
  color: #9b9b9b;
  margin-right: 0.1rem;
}
.diy-btn-active {
  display: inline-block;
  width: 0.54rem;
  height: 0.23rem;
  line-height: 0.23rem;
  border: solid 1px #e53636;
  border-radius: 0.04rem;
  text-align: center;
  background-color: #ffffff;
  font-size: 0.12rem;
  color: #e53636;
  margin-right: 0.1rem;
}
.count-single-card {
  background-color: #ffffff;
  box-shadow: 0 0.02rem 0.12rem 0 rgba(0, 0, 0, 0.1);
  border-radius: 0.1rem;
  padding: 0.1rem 0.15rem;
  .echarts-pie-area {
    padding-right: 0.15rem;
    height: 1.86rem;
    padding-bottom: 0.1rem;
  }
}
.report-total-count {
  padding: 0.07rem 0 0.08rem 0.05rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .report-total-count-title {
    width: 0.76rem;
    text-align: left;
    color: #000000;
    height: 0.35rem;
    line-height: 0.35rem;
    font-size: 0.14rem;
  }
  .report-total-count-info {
    width: 2.34rem;
    height: 0.35rem;
    float: right;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .count-num {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      span {
        font-size: 0.14rem;
        color: #9b9b9b;
      }
    }
    .count-bar {
      width: 2.34rem;
      height: 0.1rem;
      background-color: #e6e6e6;
      border-top-right-radius: 0.06rem;
      border-bottom-right-radius: 0.06rem;
      .count-bar-finish {
        height: 100%;
        background-color: #fbdca8;
        border-top-right-radius: 0.06rem;
        border-bottom-right-radius: 0.06rem;
      }
    }
  }
}
</style>
