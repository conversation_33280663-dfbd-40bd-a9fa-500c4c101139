<template>
    <div class="baseinfo-container">
      <span class="page-title">基本信息</span>
      <div class="single-info-container">
        <span class="second-title" style="font-family:'PingFangSC-Semibold'">{{pageData.complaintHeadlines}}</span>
      </div>
      <div class="single-info-container" style="padding-bottom:0;padding-left:0.15rem;padding-right:0;padding-left:0.1rem;">
          <span class="second-title" style="color:#262626;font-weight:normal;margin-bottom:0.13rem;">客户诉求</span>
          <div class="single-info-gray" style="margin-right:0.1rem">
            {{pageData.customerDemand}}
          </div>
          <div class="line"></div>
          <span class="second-title" style="color:#262626;font-weight:normal;margin-bottom:0.13rem;">最新处理现状</span>
          <div class="single-info-gray" style="margin-bottom:0.1rem;margin-right:0.1rem;">
            {{pageData.comment}}
          </div>
          <div style="padding-left:0.05rem;">
            <line-item v-if="pageData.lastUpdateDate" title="最新处理时间" :content="pageData.lastUpdateDate" border="top"></line-item>
            <line-item title="报事类别" :content="pageData.secSortName+'-'+pageData.thirdSortName" border="top"></line-item>
            <line-item v-if="pageData.mainResUnit" title="内部责任部门" :content="pageData.mainResUnit" border="top"></line-item>
            <line-item v-if="pageData.curAssigneeName" title="当前处理人" :content="pageData.curAssigneeName" border="top"></line-item>
            <line-item v-if="pageData.doTime" title="处理时长" :content="pageData.doTime+'天'" border="top"></line-item>
          </div>
      </div>
    </div>
</template>

<script>
  import lineTable from '@/components/lineTable.vue'
    export default {
        props:[
          'data'
        ],
        watch:{
          data(){
            this.pageData = this.data
          }
        },
        components:{
          'line-item':lineTable
        },
        data(){
          return{
              name:'baseInfo',
              pageData:{
                title:'办理期限过长问题投诉(标题)',
                customAppeal:'办证期限过长问题投诉，一、二手房交易资金监管是二手房转移登记过程中的一项业务，嵌于转移确权和不动产登记全过程中，监管期限是由二手房转移登记时间决定的，并未单独计算工作时限。',
                newStatus:'正在解决处理办证问题，目前还需要协同解决'
              },
          }
        },
        created(){
          this.pageData = this.data
        }
    }
</script>

<style lang="scss" scoped>
  .baseinfo-container{
    padding:0.6rem 0.15rem 0.1rem 0.15rem;
    padding-top:0.2rem;
    background-color:#fbfbfb;
  }
  .second-title{
    font-size:0.16rem;
    font-weight:bold;
    color: #000;
    display:inline-block;
    padding: 0 0.05rem;
  }
  .line{
    margin-left:0.03rem;
    border-top:solid 1px #F5F5F5;
    margin-top:0.1rem;
    margin-bottom:0.13rem;
  }
  .single-info-gray{
    min-height:0.3rem;
  }
  .page-title{
    font-family: 'PingFangSC-Semibold'!important;
  }
</style>
