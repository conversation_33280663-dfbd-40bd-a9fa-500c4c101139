<template>
  <div class="follow-record">
    <span class="page-title">跟进记录</span>
    <div class="single-record" v-for="(item,index) in processData" :key="index">
      <img class="status-icon" :src="item.taskStatus==30?require('@/assets/images/processNode/<EMAIL>'):item.taskStatus==20?require('@/assets/images/processNode/<EMAIL>'):require('@/assets/images/processNode/<EMAIL>')">
      <div class="process-name-date">
        <span class="process-item-name">{{item.processStateName}}</span>
        <span v-if="item.taskStatus!=10">{{item.lastUpdateDate.substring(0,16)}}</span>
      </div>
      <div class="process-item-info" :style="index == processData.length-1?'border:none':''">
        <div class="process-item-detail" v-if="item.taskStatus!=10">
            <div class="message-box"><span class="p-title">操作人:</span><span class="p-info">{{item.assignName}}</span></div>
            <div style="width:100%;height:0.1rem;"></div>
            <div class="message-box"><span class="p-title" style="vertical-align:top;">操 作:</span><span class="p-info" style="vertical-align:top;">{{item.comment}}</span></div>
        </div>
        <img class="next-step-icon" v-if="item.taskStatus == 30" :src="require('@/assets/images/common/<EMAIL>')">
      </div>
    </div>
  </div>
</template>

<script>
    export default {
        props:[
          'data'
        ],
        watch:{
          data(){
            this.processData = this.data;
          }
        },
        data(){
          return{
            processData:[],
          }
        },
        created(){
          this.processData = this.data;
        }
    }
</script>

<style lang="scss" scoped="scoped">
  .follow-record{
    padding:0.03rem 0.15rem 0.7rem 0.15rem;
    background-color:#fbfbfb;
  }
  .single-record{
    position:relative;
    padding-left:0.09rem;
    .status-icon{
      position: absolute;
      left:0;
      top:0;
      display:inline-block;
      width:0.18rem;
      min-height:0.18rem;
    }
    .process-name-date{
      display:flex;
      justify-content: space-between;
      align-items: center;
      height:0.18rem;
      font-size:0.12rem;
      color: #9B9B9B;
      .process-item-name{
        padding-left:0.24rem;
      }
    }
    .process-item-info{
      border-left:solid 1px #E6E6E6;
      padding-left:0.23rem;
      padding-top:0.1rem;
      padding-bottom:0.2rem;
      position:relative;
      .process-item-detail{
        background-color: #ffffff;
        border-radius:0.1rem;
        padding-top:0.1rem;
        padding-bottom:0.1rem;
        box-shadow:0 0.02rem 0.12rem 0 rgba(0,0,0,0.1);
        .message-box{
          min-height:0.2rem;
          margin-left:0.15rem;
          .p-title{
            font-size:0.14rem;
            color: #9B9B9B;
            display:inline-block;
            width:0.56rem;
            text-align:left;
            word-spacing:0.1rem;
          }
          .p-info{
            font-size:0.14rem;
            color: #000;
            display:inline-block;
            width:2.3rem;
          }
        }
      }
      .next-step-icon{
        position:absolute;
        width:0.09rem;
        height:0.09rem;
        left:-0.05rem;
        top:0.4rem;
      }
    }
  }
  .page-title{
    font-family: 'PingFangSC-Semibold'!important;
  }
</style>
