<template>
    <div class="body-container">
      <v-baseInfo :data="baseInfo"/>
      <v-reportorInfo :data="baseInfo"/>
      <v-record :data="recordInfo"/>
    </div>
</template>

<script>
import BaseInfo from './baseInfo'
import ReportInfo from './reportorInfo'
import FollowRecord from './followRecord'
import { getInfoDetail } from '@/api/index.js' 
    export default {
        components:{
          'v-baseInfo':BaseInfo,
          'v-reportorInfo':ReportInfo,
          'v-record':FollowRecord,
        },
        data(){
          return{
            name:'报事详情',
            baseInfo:Object,
            recordInfo:Array,
          }
        },
        methods:{
            getDetail(val){
              getInfoDetail(val).then((res) => {
                
                this.baseInfo = res.data.mcomplaintVo;
                res.data.processList.reverse();
                if(res.data.processList[res.data.processList.length-1].processStateName == '报事录入'){
                  res.data.processList.push(
                    {processStateName:'报事分派',taskStatus:10},
                    {processStateName:'报事处理',taskStatus:10},
                    {processStateName:'报事回访',taskStatus:10},
                    {processStateName:'报事关闭',taskStatus:10},
                  )
                }else if(res.data.processList[res.data.processList.length-1].processStateName == '报事分派'){
                  res.data.processList.push(
                    {processStateName:'报事处理',taskStatus:10},
                    {processStateName:'报事回访',taskStatus:10},
                    {processStateName:'报事关闭',taskStatus:10},
                  )
                }else if(res.data.processList[res.data.processList.length-1].processStateName == '报事处理'){
                  res.data.processList.push(
                    {processStateName:'报事回访',taskStatus:10},
                    {processStateName:'报事关闭',taskStatus:10},
                  )
                }else if(res.data.processList[res.data.processList.length-1].processStateName == '报事回访'){
                  res.data.processList.push(
                    {processStateName:'报事关闭',taskStatus:10},
                  )
                }else if(res.data.processList[res.data.processList.length-1].processStateName == '报事升级'){
                  res.data.processList.push(
                    {processStateName:'报事处理',taskStatus:10},
                    {processStateName:'报事回访',taskStatus:10},
                    {processStateName:'报事关闭',taskStatus:10},
                  )
                }
                this.recordInfo = res.data.processList;
              })
            }
        },
        created(){
          let params = {
            id:this.$route.params.newsId
          }
          this.getDetail(params);          
        }
    }
</script>

<style scoped>

</style>
