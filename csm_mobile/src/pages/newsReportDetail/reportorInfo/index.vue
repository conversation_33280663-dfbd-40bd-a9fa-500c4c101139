<template>
  <div class="reportInfo-container">
    <span class="page-title">报事人信息</span>
    <div class="single-info-container" style="padding-top:0;padding-bottom:0;padding-right:0;padding-left:0.15rem;">
      <line-item v-if="pageData.ownerName" title="客户姓名" :content="pageData.ownerName"></line-item>
      <line-item v-if="pageData.mobile" title="移动电话" :content="pageData.mobile" border="top"></line-item>
      <line-item v-if="pageData.eMail" title="电子邮件" :content="pageData.eMail" border="top"></line-item>
      <line-item title="项目" :content="pageData.region+'-'+pageData.city+'-'+pageData.project" border="top"></line-item>
      <line-item v-if="handleHouseNo(pageData.buildingNo,pageData.buildingUnit,pageData.roomNo)" title="房间号" :content="handleHouseNo(pageData.buildingNo,pageData.buildingUnit,pageData.roomNo)" border="top"></line-item>
      <line-item v-if="pageData.housekeeperName" title="管家姓名" :content="pageData.housekeeperName" border="top"></line-item>
      <line-item v-if="pageData.housekeeperTel" title="管家电话" :content="pageData.housekeeperTel" border="top"></line-item>
      <line-item title="特殊客户" :content="pageData.specialUser==1?'是':'否'" border="top"></line-item>
    </div>
  </div>
</template>

<script>
  import LineTable from '@/components/lineTable.vue'
    export default {
        props:[
          'data'
        ],
        watch:{
          data(){
            this.pageData = this.data;
          }
        },
        components:{
          'line-item':LineTable
        },
        data(){
          return{
            pageData:[],
          }
        },
        methods:{
          handleHouseNo(area, build, house){
            let a,b,h;
            if(area == null || area == '' ){
              a = '';
            }else{
              a = area+'-';
            }
            if(build == null || build == ''){
              b = '';
            }else{
              b = build+'-'
            }
            if(house == null || house == ''){
              h = ''
            }else{
              h = house
            }
            return a+b+h;
          }
        },
        created(){
          this.pageData = this.data;
        }
    }
</script>

<style lang="scss" scoped="scoped">
.reportInfo-container{
  padding:0 0.15rem;
  background-color:#fbfbfb;
}
.page-title{
    font-family: 'PingFangSC-Semibold'!important;
}
</style>
