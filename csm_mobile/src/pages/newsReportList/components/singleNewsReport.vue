<template>
<mt-loadmore :bottom-method="load" :bottom-all-loaded="allLoaded" ref="loadmore">
  <div style="text-align:right;padding-right:0.15rem;"><span style="color:#4787FF;font-size:0.12rem;" @click="showTip">关于重大投诉?</span></div>
  <v-dialog 
    title="关于重大投诉的定义" 
    content="<p style='text-indent:0'>满足以下任意一点即为重大投诉：</p><p>① 客户投诉70天未解决；</p><p>② 5 人以上（包含 5 人）的群诉或媒体曝光的投诉。</p>"
    buttonText="知道了"
    :dialogVisible="isShowDialog"
    @btnEvent="closeTip"
  />
  <div class="single-report">
    <div class="container-info" v-for="(item,index) in list" :key="index" @click="toDetail(item.id)">
      <div style="padding:0.14rem 0.14rem;width:3.15rem;position:relative;">
        <p class="info-title">{{item.complaintHeadlines}}</p>
        <img class="icon-right icon-grey" :src="require('@/assets/images/common/<EMAIL>')">
      </div>
      <div class="info-message" @click="toDetail(item.id)">
        <ul>
          <li><span class="message-title w65">办事类别</span><span class="message-content break-row">{{item.secSortName+'-'+item.thirdSortName}}</span></li>
          <li><span class="message-title">报事人</span><span class="message-content">{{item.ownerName}}</span></li>
          <li><span class="message-title">当前处理人</span><span class="message-content">{{item.curAssigneeName}}</span></li>
          <li><span class="message-title">处理时长</span><span class="message-content" :style="item.doTime>=100?'color:#E53636':''">{{item.doTime+'天'}}</span></li>
          <li><span class="message-title w65">所属项目</span><span class="message-content break-row">{{item.region+'-'+item.city+'-'+item.project}}</span></li>
        </ul>
      </div>
    </div>
  </div>
  <div slot="bottom" class="mint-loadmore-bottom">
      <span v-if="showLoading"><img class="loading-img" :src="require('@/assets/images/common/loading.gif')">正在加载</span>
      <span v-if="showNomore">这已经是我的底线了</span>
      <span v-if="showNoData">暂无数据</span>
  </div>  
</mt-loadmore>
</template>
<script>
import { getInfoList } from '@/api/index.js'
import { Loadmore } from 'mint-ui'
import dialog from '@/components/dialog'
    export default {
        components: {
          'mt-loadmore':Loadmore,
          'v-dialog':dialog
        },
        data(){
          return{
            list:[],
            allLoaded:false,
            showLoading:false,
            showNomore:false,
            showNoData:false,
            pageNum:1,
            isShowDialog:false,
          }
        },
        methods:{
          showTip(){
            this.isShowDialog = true;
          },
          closeTip(){
            this.isShowDialog = false;
          },
          load(){
            if(this.showLoading == false){
              this.pageNum += 1;
              this.showLoading = true;
              this.showNomore = false;
              this.showNoData = false;
              setTimeout(() => {
                this.loadMoreData(this.pageNum);
              }, 500);
            }
          },
          toDetail(nid){
              this.$router.push({
                name:'newsReportDetail',
                params:{
                  newsId:nid
                }
              })
          },
          loadMoreData(pageNum){
            let params = {
              pageNum:pageNum,
              pageSize:10
            }
            getInfoList(params).then((res) => {
              res.data.records.map((item) => {
                this.list.push(item);
              })
              this.showLoading = false;
              if(res.data.records.length==0 && this.pageNum != 1){
                this.showNomore = true;
                this.allLoaded = true;
              }
              if(this.$refs.loadmore.onBottomLoaded()){
                  this.$refs.loadmore.onBottomLoaded();
              }
            })
          },
          getList(pageNum){
            let params = {
              pageNum:pageNum,
              pageSize:10
            }
            getInfoList(params).then((res) => {
              this.list = res.data.records;
              this.showLoading = false;
              if(res.data.records.length==0 && this.pageNum != 1){
                this.showNoData = true;
                this.allLoaded = true;
              }
            })
          },
        },
        created(){
          this.getList(1);
        },
        mounted(){
          
        }
    }
</script>

<style lang="scss" scoped>
  .single-report{
    //padding:1.05rem 0;
    //padding-top:0.2rem;
    background-color: #f8f8f8;
  }
  .container-info{
    width:3.45rem;
    box-shadow:0 0.02rem 0.12rem 0 rgba(0,0,0,0.1);
    border-radius:0.1rem;
    margin:0 auto;
    margin-top:0.1rem;
    padding-bottom:0.1rem;
    background-color:#ffffff;
  }
  .info-title{
    color:#262626;
    font-size:0.16rem;
    margin:0;
    width:2.9rem;
    display: -webkit-box;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 2;
    overflow:hidden;
    font-family:'PingFangSC-Medium'!important; 
  }
  .icon-right{
    display:inline-block;
    height:0.14rem;
  }
  .info-message{
    width:3.05rem;
    margin:0 auto;
    ul,li{
      margin:0;
      padding:0;
      list-style:none;
      height:0.3rem;
      line-height:0.3rem;
      font-size:0.14rem;
    }
    ul{
      height:1.5rem;
    }
    background-color: #F5F5F5;
    border:solid 1px #E6E6E6;
    border-radius:0.04rem;
    padding:0 0.1rem;
  }
  .message-title{
    display:block;
    float:left;
    width:0.8rem;
    text-align:left;
    color: #9B9B9B ;
    font-size:0.14rem;
  }
  .message-content{
    display:block;
    float: right;
    color: #000000;
    text-align:right;
  }
  .icon-grey{
    position: absolute;
    right:0.1rem;
    top:35%;
  }
  .w65{
    max-width:0.65rem!important;
  }
  .break-row{
    max-width:2.4rem!important;
    display: -webkit-box!important;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 1!important;
    overflow:hidden!important;
  }
  .mint-loadmore-bottom{
    text-align:center;
    height:0.42rem;
    line-height:0.42rem;
    color:#9B9B9B;
    .loading-img{
      width:0.16rem;
      height:0.16rem;
      vertical-align:middle;
      margin-right:0.05rem;
    }
  }
</style>
