<template>
    <div class="lvUpDetail">
        <v-tab :tabList="tabList" :defaultActive="$route.params.type" @change="getTabChange"/>
        <div class="page-title" style="padding-top:0.72rem;padding-bottom:0.1rem;padding-left:0.15rem;">
            {{$route.params.regionName}}
        </div>
        <div class="single-info-container" style="margin:0 0.15rem;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0.15rem;">
             <line-item v-for="(item,index) in dataList" :key="index" :title="item.firstSortName+'-'+item.secSortName" :content="item.groupCount+'个'" :border="index != dataList.length-1?'bottom':'none'"></line-item>
        </div>
        <load-more :show-loading="false" tip="暂无数据" v-if="showNoData"></load-more>
    </div>
</template>
<script>
import Tab from '@/components/tab.vue'
import LineTable from '@/components/lineTable.vue'
import { LoadMore } from 'vux'
import {getNewsReportLvUpCountNext} from '@/api'
    export default{
        components:{
            'v-tab':Tab,
            'line-item':LineTable,
            LoadMore
        },
        data(){
            return{
                tabList:[
                    {label:'投诉',value:'1'},
                    {label:'报修',value:'2'}
                ],
                defaultType:null,
                dataList:Array,
                showNoData:false,
            }
        },
        methods:{
            getTabChange(val){
                this.getPageData(val);
            },
            getPageData(type){
                let params = {
                    type:type,
                    region:this.$route.params.region,
                    range:this.$route.params.range,
                }
                if(this.$route.params.regionName != '合计'){
                    params.regionName = this.$route.params.regionName;
                }
                getNewsReportLvUpCountNext(params).then((res) => {
                    this.dataList = res.data.reportUpNextGroupList;
                    if(this.dataList == null || this.dataList.length == 0){
                        this.showNoData = true;
                    }else{
                        this.showNoData = false;
                    }
                })
            }
        },
        created(){
            //console.log(this.$route.params);
            this.getPageData(this.$route.params.type);
            
        }
    }
</script>
<style lang="scss" scoped="scoped">
.lvUpDetail{
    min-height:100%;
}
.page-title{
   font-family: 'PingFangSC-Semibold'!important; 
}
</style>

