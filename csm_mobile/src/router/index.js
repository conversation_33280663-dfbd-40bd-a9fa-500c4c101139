import Vue from 'vue'
import Router from 'vue-router'
import MainPage from '@/pages'//主入口

import NewsReportList from '@/pages/newsReportList/index.vue'
import NewsReportCount from '@/pages/newsReportCount'
import NewsReportDetail from '@/pages/newsReportDetail'
import ReportLvUpDetail from '@/pages/reportLevUpDetail'
import classifyDetail from '@/pages/newsReportCount/classifyDetail'
import reportingDetail from '@/pages/newsReportCount/reportingDetail' // 员工报事统计详情
import accidentDetail from '@/pages/newsReportCount/accidentDetail' // 员工投诉事故统计详情

Vue.use(Router)

export const navList = [
  {
    path: '/mainPage',
    name: 'mainPage',
    component: MainPage,
    children:[
      {
        path:'/mainPage/important',
        name:'important',
        component:NewsReportList,
        text:'重大投诉',
        icondefault:require('@/assets/images/nav/important.png'),
        iconactive:require('@/assets/images/nav/important_active.png')
      },
      {
        path:'/mainPage/newsReport',
        name:'newsReport',
        component:NewsReportCount,
        text:'报事统计',
        icondefault:require('@/assets/images/nav/re_thing.png'),
        iconactive:require('@/assets/images/nav/re_thing_active.png')
      }
    ]
  },
  {
    path:'/newsReportDetail',
    name:'newsReportDetail',
    component:NewsReportDetail
  },
  {
    path:'/lvupDetail',
    name:'lvupDetail',
    component: ReportLvUpDetail
  },
  {
    path:'/classifyDetail',
    name:'classifyDetail',
    component: classifyDetail
  },
  {
    path:'/reportingDetail',
    name:'reportingDetail',
    component: reportingDetail
  },
  {
    path:'/accidentDetail',
    name:'accidentDetail',
    component: accidentDetail
  },
  {
    path:'/',
    redirect:'/mainPage/important'
  },
]

export default new Router({
  mode: 'history',
  base: '/app/',
  routes: navList
})
