<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.tahoecn</groupId>
	<artifactId>customerservice</artifactId>
	<version>0.0.1</version>
	<packaging>jar</packaging>

	<name>customerservice</name>
	<description>customerservice for Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.0.3.RELEASE</version>
	</parent>

	<properties>
		<customerservice.build.sourceEncoding>UTF-8</customerservice.build.sourceEncoding>
		<customerservice.reporting.outputEncoding>UTF-8</customerservice.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<fastjson.version>1.2.34</fastjson.version>
	</properties>


	<dependencies>


		<!-- 启用JSP支持，如果是动静分离，请注销tomcat-embed-jasper与jstl -->
		<!-- <dependency> <groupId>org.apache.tomcat.embed</groupId> <artifactId>tomcat-embed-jasper</artifactId> 
			</dependency> <dependency> <groupId>javax.servlet</groupId> <artifactId>jstl</artifactId> 
			</dependency> -->
		<dependency>
			<groupId>com.landray</groupId>
			<artifactId>EKP-SSO-client-java</artifactId>
			<version>1.0</version>
		</dependency>
		
		<dependency>
		    <groupId>com.tahoecn</groupId>
		    <artifactId>uc-sso</artifactId>
		    <version>1.0.0-release</version>
		</dependency>
		
		<dependency>
		    <groupId>io.jsonwebtoken</groupId>
		    <artifactId>jjwt</artifactId>
		    <version>0.7.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<version>${parent.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-zipkin</artifactId>
			<version>2.0.1.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.10</version>
		</dependency>

		<!-- mybatis-plus -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>2.3</version>
			<exclusions>
				<exclusion>
					<artifactId>tomcat-jdbc</artifactId>
					<groupId>org.apache.tomcat</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.46</version>
		</dependency>
		<!-- jdbc驱动 -->
		<dependency>
			<groupId>net.sourceforge.jtds</groupId>
			<artifactId>jtds</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/cn.afterturn/easypoi -->
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-base</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-annotation</artifactId>
			<version>3.2.0</version>
		</dependency>

		<!-- swagger2 -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.8.0</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.8.0</version>
		</dependency>

		<!-- 泰禾公共服务包，包含org.apache.commons.httpclient4.5.6、 包含com.fasterxml.jackson2.9.6 -->
		<dependency>
			<groupId>com.tahoecn</groupId>
			<artifactId>pmo-tool-all</artifactId>
			<version>1.0.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>19.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.6</version>
		</dependency>

		<!-- 仅用于mybatis plus 生成代码使用 -->
		<!-- https://mvnrepository.com/artifact/org.apache.velocity/velocity -->
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity</artifactId>
			<version>1.7</version>
			<scope>test</scope>
		</dependency>
		<!-- Hutool包引入，用于短信 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>4.1.19</version>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity</artifactId>
			<version>1.7</version>
		</dependency>
		
		
		<!-- 极光 APP推送消息 -->
		<dependency>
		    <groupId>cn.jpush.api</groupId>
		    <artifactId>jpush-client</artifactId>
		    <version>3.3.10</version>
		</dependency>
	    <dependency>
	        <groupId>cn.jpush.api</groupId>
	        <artifactId>jiguang-common</artifactId>
	        <version>1.1.4</version>
	    </dependency>
	    <dependency>
	        <groupId>io.netty</groupId>
	        <artifactId>netty-all</artifactId>
	        <version>4.1.6.Final</version>
	        <scope>compile</scope>
	    </dependency>
	</dependencies>


	<profiles>
		<profile>
			<id>develop</id>
			<properties>
				<package.environment>develop</package.environment>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>released</id>
			<properties>
				<package.environment>released</package.environment>
			</properties>
		</profile>
		<profile>
			<id>production</id>
			<properties>
				<package.environment>production</package.environment>
			</properties>
		</profile>
	</profiles>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources/${package.environment}</directory>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/mapper*/xml/*.xml</include>
				</includes>
			</resource>

		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<failOnMissingWebXml>false</failOnMissingWebXml>
				</configuration>
			</plugin>
		</plugins>
		<defaultGoal>compile</defaultGoal>
	</build>
	<repositories>
		<repository>
			<id>maven-tahoecn</id>
			<name>Maven tahoecn</name>
			<url>http://mvn.tahoecn.com/repository/maven-public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>maven-tahoecn</id>
			<name>Maven tahoecn</name>
			<url>http://mvn.tahoecn.com/repository/maven-public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
