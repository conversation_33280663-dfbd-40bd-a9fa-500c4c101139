/**
 * 
 */
package com.tahoecn.customerservice.common.ekp;

import javax.jws.WebService;

/**
 * @ClassName ISysNotifyTodoWebService
 * <AUTHOR>
 * @date 2018年12月12日
 */
@WebService(targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", name = "ISysNotifyTodoWebService")
public interface ISysNotifyTodoWebService {

	/**
	 * 发送待办消息
	 * 
	 * @param context
	 * @return
	 */
	public NotifyTodoAppResult sendTodo(NotifyTodoSendContext context);

	/**
	 * 发送已办消息
	 * 
	 * @param context
	 * @return
	 */
	public NotifyTodoAppResult setTodoDone(NotifyTodoRemoveContext context);

}
