/**
 * 
 */
package com.tahoecn.customerservice.common.ekp;

/**
 * @ClassName RemoveContext
 * <AUTHOR>
 * @date 2018年12月12日
 */
public class NotifyTodoRemoveContext {
	protected String appName;
	protected String key;
	protected String modelId;
	protected String modelName;
	protected int optType;
	protected String param1;
	protected String param2;
	protected String targets;

	public NotifyTodoRemoveContext() {
		super();
	}

	public NotifyTodoRemoveContext(String appName, String modelId) {
		super();
		this.modelId = modelId;
		this.appName = appName;
		this.modelName = "客服系统待办";
		this.optType = 1;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getModelId() {
		return modelId;
	}

	public void setModelId(String modelId) {
		this.modelId = modelId;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public int getOptType() {
		return optType;
	}

	public void setOptType(int optType) {
		this.optType = optType;
	}

	public String getParam1() {
		return param1;
	}

	public void setParam1(String param1) {
		this.param1 = param1;
	}

	public String getParam2() {
		return param2;
	}

	public void setParam2(String param2) {
		this.param2 = param2;
	}

	public String getTargets() {
		return targets;
	}

	public void setTargets(String targets) {
		this.targets = targets;
	}
}
