/**
 * 
 */
package com.tahoecn.customerservice.common.ekp;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName SendContext
 * <AUTHOR>
 * @date 2018年12月12日
 */
public class NotifyTodoSendContext {
	protected String appName;
	protected String createTime;
	protected String key;
	protected String link;
	protected String modelId;
	protected String modelName;
	protected String param1;
	protected String param2;
	protected String subject;
	protected String targets;
	protected int type;

	public NotifyTodoSendContext() {

	}

	public NotifyTodoSendContext(String appName, String link, String modelId, String subject, String targets) {
		super();
		this.link = link;
		this.modelId = modelId;
		this.subject = subject;
		this.targets = targets;
		this.appName = appName;
		this.modelName = "客服系统待办";
		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		this.createTime = sf.format(new Date());
		this.type = 1;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public String getModelId() {
		return modelId;
	}

	public void setModelId(String modelId) {
		this.modelId = modelId;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public String getParam1() {
		return param1;
	}

	public void setParam1(String param1) {
		this.param1 = param1;
	}

	public String getParam2() {
		return param2;
	}

	public void setParam2(String param2) {
		this.param2 = param2;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getTargets() {
		return targets;
	}

	public void setTargets(String targets) {
		this.targets = targets;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
}
