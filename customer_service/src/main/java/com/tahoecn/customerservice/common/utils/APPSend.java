/**
 * 
 */
package com.tahoecn.customerservice.common.utils;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.tahoecn.customerservice.model.CsMessage;
import com.tahoecn.customerservice.service.CsMessageService;

import cn.jiguang.common.ClientConfig;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.audience.AudienceTarget;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;

/**
 * @ClassName OAUtils
 * <AUTHOR>
 * @date 2018年12月12日
 */
@Component
public class APPSend {

	private Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${APP_KEY}")
	private String APP_KEY;

	@Value("${MASTER_SECRET}")
	private String MASTER_SECRET;

	@Autowired
	private CsMessageService csMessageService;
	/**
	 * 手机APP推送消息
	 * @param formId 工单id
	 * @param itemId 流程id
	 * @param msgContent 推送内容
	 * @param loginName 推送人
	 */
	public void sendTodo(String formId, String itemId, String msgContent, String loginName) {

        try {
        	if(loginName != null && !"".equals(loginName)){
        		//1.记录消息
        		String messId = messageRecord(formId, msgContent, loginName);
        		//2.推送消息
        		JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY, null, ClientConfig.getInstance());
        		PushPayload payload = buildPushObject_ios_audienceMore_messageWithExtras(messId,formId, msgContent, loginName);
        		System.out.println(payload);
        		PushResult result = jpushClient.sendPush(payload);
        		System.out.println(result);
        		logger.info("",result);
        	}else{
        		logger.info("[formId:"+formId+";itemId:"+itemId+";msgContent:"+msgContent
        				+";loginName:"+loginName+"]:推送人为空,不推送消息！！！！！！！！！！！！");
        	}
        } catch (Exception e) {
        	e.printStackTrace();
        }
	}

	public static PushPayload buildPushObject_ios_audienceMore_messageWithExtras(String messId, String formId, String msgContent, String loginName) {
		return PushPayload.newBuilder()
                .setPlatform(Platform.android_ios())
                .setAudience(Audience.newBuilder()
//                        .addAudienceTarget(AudienceTarget.tag("tag1","tag2"))
                        .addAudienceTarget(AudienceTarget.alias(loginName))
                        .build())
                .setNotification(Notification.newBuilder()
                        .addPlatformNotification(AndroidNotification.newBuilder()
                                .addExtra("type", "infomation")
                                .addExtra("messId", messId)
                                .addExtra("formId", formId)
                                .setAlert(msgContent)
                                .build())
                        .addPlatformNotification(IosNotification.newBuilder()
                                .addExtra("type", "infomation")
                                .addExtra("messId", messId)
                                .addExtra("formId", formId)
                                .setAlert(msgContent)
                                .build())
                        .build())
                .setOptions(Options.newBuilder()
    					.setApnsProduction(true)
    					.build())
                .build();
    }
	
	/**
	 * 消息记录
	 * @param formId
	 * @param title
	 * @param loginName
	 */
	private String messageRecord(String formId, String msgContent, String loginName) {
		CsMessage message = new CsMessage();
		message.setFormId(formId);
		message.setMessage(msgContent);
		message.setState("1");
		message.setCreateDate(new Date());
		message.setUpdate(new Date());
		message.setLoginName(loginName);
		csMessageService.insert(message);
		return message.getId().toString();
	}
}