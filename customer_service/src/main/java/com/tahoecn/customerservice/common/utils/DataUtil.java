package com.tahoecn.customerservice.common.utils;

import org.apache.commons.lang.StringUtils;

public class DataUtil {

	/**
	 * 手机号加密
	 * @param phone
	 * @return
	 */
	public static String phoneDec(String phone) {
		String phoneDec = "";
		if(StringUtils.isNotBlank(phone) && phone.length() > 6) {
			phoneDec += phone.substring(0,3);
			phoneDec += "****";
			if(phone.length() > 7) {
				phoneDec += phone.substring(7, phone.length());
			}
		}else {
			phoneDec = phone;
		}
		return phoneDec;
	}
	
	/**
	 * 手机号加密
	 * @param phoneArr
	 * @return
	 */
	public static String phoneDec(String[] phoneArr) {
		String phoneDec = "";
		if(null != phoneArr && phoneArr.length != 0) {
			for(String phone : phoneArr) {
				phoneDec += phoneDec(phone);
				phoneDec += ",";
			}
		}
		return phoneDec.substring(0,phoneDec.length()-1);
	}
	
	/**
	 * 身份证后四位加密
	 * @param idCard 身份证号
	 * @return
	 */
	public static String idCardDec (String idCard) {
		String idCardDec = "";
		if(StringUtils.isNotBlank(idCard) && idCard.length() > 4) {
			idCardDec += idCard.substring(0, idCard.length()-4);
			idCardDec += "****";
		}else {
			idCardDec = idCard;
		}
		return idCardDec;
	}
}
