package com.tahoecn.customerservice.common.utils;

import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.tahoecn.core.json.JSONResult;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.ExcelExportService;
import cn.afterturn.easypoi.util.PoiPublicUtil;

public class ExcelUtil {

	public static void listToExcel(List<?> list, String title, OutputStream outputStream) throws Exception {
		if (list != null) {
			if (list.size() != 0) {
				ExportParams exportParams = new ExportParams(title, "SheetData");
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, list.get(0).getClass(), list);
				workbook.write(outputStream);
			}
		}
	}

	public static void listToExcel(List<?> list, String title, String sheetName, OutputStream outputStream)
			throws Exception {
		if (list != null) {
			if (list.size() != 0) {
				// 得到所有字段
				List<ExcelExportEntity> excelParams = new ArrayList<ExcelExportEntity>();
				Class<?> clazz = list.get(0).getClass();
				Field[] fileds = PoiPublicUtil.getClassFields(clazz);
				ExcelTarget etarget = clazz.getAnnotation(ExcelTarget.class);
				String targetId = etarget == null ? null : etarget.value();
				ExcelExportService es = new ExcelExportService();
				es.getAllExcelField(null, targetId, fileds, excelParams, clazz, null, null);

				Workbook wb = new SXSSFWorkbook(100);
				// 工作表对象
				Sheet sheet = null;
				// 行对象
				Row nRow = null;
				// 列对象
				Cell nCell = null;
				// 总行号/页行号
				int rowNo = 0;
				int pageRowNo = 1;
				Iterator<?> ir = list.iterator();
				while (ir.hasNext()) {
					// 打印300000条后切换到下个工作表，可根据需要自行拓展，2百万，3百万...数据一样操作，只要不超过1048576就可以
					if (rowNo % 300000 == 0) {
						System.out.println("Current Sheet:" + rowNo / 300000);
						// 建立新的sheet对象
						sheet = wb.createSheet(sheetName + pageRowNo);
						// 动态指定当前的工作表
						sheet = wb.getSheetAt(rowNo / 300000);
						// 每当新建了工作表就将当前工作表的行号重置为0
						pageRowNo = 1;
						// 新建行对象
						nRow = sheet.createRow(0);
						for (int i = 0; i < excelParams.size(); i++) {
							nCell = nRow.createCell(i);
							nCell.setCellValue(excelParams.get(i).getName());
						}
						// 休息一下，防止对CPU占用，其实影响不大
						Thread.sleep(10);
					}
					rowNo++;

					nRow = sheet.createRow(pageRowNo++);
					Object o = ir.next();
					for (int i = 0; i < excelParams.size(); i++) {
						nCell = nRow.createCell(i);
						Object object = es.getCellValue(excelParams.get(i), o);
						nCell.setCellValue(String.valueOf(object));
					}

				}
				wb.write(outputStream);
			}
		}
	}

	public static void listToExcel(List<ExcelExportEntity> entity, List<?> list, String title,
			OutputStream outputStream) throws Exception {
		if (list != null) {
			if (list.size() != 0) {
				Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(title, "SheetData"), entity, list);
				workbook.write(outputStream);
			}
		}
	}

	/**
	 * 模板导出
	 * 
	 * @param exportParamUrl
	 *            模板路径 eq:WEB-INF/doc/专项支出用款申请书_map.xls
	 * @param map
	 * @param outputStream
	 * @throws Exception
	 */
	public static void listToExcel(String exportParamUrl, Map<String, Object> map, OutputStream outputStream)
			throws Exception {
		if (map != null && StringUtils.isNotBlank(exportParamUrl)) {
			TemplateExportParams params = new TemplateExportParams(exportParamUrl);
			Workbook workbook = ExcelExportUtil.exportExcel(params, map);
			workbook.write(outputStream);
		}
	}

	public static JSONResult excelToList(InputStream inputStream, Class<?> cls, int titleRows, int headRows)
			throws Exception {
		ImportParams importParams = new ImportParams();
		importParams.setTitleRows(titleRows);
		importParams.setHeadRows(headRows);

		List<Object> list = ExcelImportUtil.importExcel(inputStream, cls, importParams);
		long index = titleRows + headRows;

		List<String> errors = new ArrayList();
		for (Object obj : list) {
			index++;
			List<String> rowErrors = ModelValidatorUtil.modelValidator(obj);
			if (rowErrors.size() != 0) {
				errors.add("第" + index + "行" + rowErrors);
			}
		}

		JSONResult jsonResult = new JSONResult();

		// 有一个错误整个EXCEL都不能导入到LIST中
		if (errors.size() != 0) {
			jsonResult.setCode(-1);
			jsonResult.setMsg("Excel读取失败！");
			jsonResult.setData(errors);
		} else {
			jsonResult.setCode(0);
			jsonResult.setMsg("Excel读取成功！");
			jsonResult.setData(list);
		}

		return jsonResult;
	}

}