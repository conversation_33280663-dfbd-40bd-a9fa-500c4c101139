package com.tahoecn.customerservice.common.utils;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by gaara on 2018/11/24.
 */

@Retention(RetentionPolicy.RUNTIME)
@Target( { java.lang.annotation.ElementType.FIELD })
public @interface NoteAttribute {
    /**
     * 字段名字
     */
    public abstract String name();
}
