/**
 * 
 */
package com.tahoecn.customerservice.common.utils;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.tahoecn.customerservice.common.ekp.ISysNotifyTodoWebService;
import com.tahoecn.customerservice.common.ekp.NotifyTodoAppResult;
import com.tahoecn.customerservice.common.ekp.NotifyTodoRemoveContext;
import com.tahoecn.customerservice.common.ekp.NotifyTodoSendContext;

/**
 * @ClassName OAUtils
 * <AUTHOR>
 * @date 2018年12月12日
 */
@Component
public class OASend {

	private Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${uc_sysId}")
	private String appName;

	@Value("${ekp_url}")
	private String ekpUrl;

	@Value("${ekp_link_url}")
	private String linkUrl;

	/**
	 * 发送待办接口
	 * 
	 * @param formId
	 * 
	 * @param modelId
	 *            唯一标识
	 * @param subject
	 *            标题
	 * @param targets
	 *            推送人 "{\"LoginName\":\"wanghongxin\"}"
	 * @return
	 * @throws MalformedURLException
	 */
	public NotifyTodoAppResult sendTodo(Long formId, String modelId, String subject, String targets) {
		try {
			String link = linkUrl + "?isOa=ture&title=报事处理&id=" + formId;
			NotifyTodoSendContext context = new NotifyTodoSendContext(appName, link, modelId, subject, targets);
			NotifyTodoAppResult result = service().sendTodo(context);
			logger.info("NotifyTodoAppResult __ returnState:{} __ message:{} ", result.getMessage(),
					result.getReturnState());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 设为已办接口
	 * 
	 * @param modelId
	 *            唯一标识
	 * @return
	 * @throws MalformedURLException
	 */
	public NotifyTodoAppResult setTodoDone(String modelId) {
		try {
			NotifyTodoRemoveContext context = new NotifyTodoRemoveContext(appName, modelId);
			NotifyTodoAppResult result = service().setTodoDone(context);
			logger.info("NotifyTodoAppResult __ returnState:{} __ message:{} ", result.getMessage(),
					result.getReturnState());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取wsdl 服务
	 * 
	 * @return
	 * @throws MalformedURLException
	 */
	private ISysNotifyTodoWebService service() throws MalformedURLException {
		URL url = new URL(ekpUrl);

		// 创建服务名称
		// 1.namespaceURI - 命名空间地址 (wsdl文档中的targetNamespace)
		// 2.localPart - 服务视图名 (wsdl文档中服务名称，例如<wsdl:service name="MobileCodeWS">)
		QName qname = new QName("http://webservice.notify.sys.kmss.landray.com/", "ISysNotifyTodoWebServiceService");

		// 创建服务视图
		// 参数解释：
		// 1.wsdlDocumentLocation - wsdl地址
		// 2.serviceName - 服务名称
		Service service = Service.create(url, qname);

		// 获取服务实现类
		// 参数解释:serviceEndpointInterface - 服务端口(wsdl文档中服务端口的name属性，例如<wsdl:port
		// name="MobileCodeWSSoap" binding="tns:MobileCodeWSSoap">)
		return service.getPort(ISysNotifyTodoWebService.class);

	}
}