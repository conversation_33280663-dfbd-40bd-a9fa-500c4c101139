/**
 * 
 */
package com.tahoecn.customerservice.common.utils;

import com.tahoecn.customerservice.model.CsUcUser;

/**
 * <AUTHOR>
 * @date 2018年9月5日 下午5:18:08
 * @desc
 */
public class ThreadLocalUtils {

	private static final ThreadLocal<CsUcUser> LOCAL = new ThreadLocal<CsUcUser>();
	private static final ThreadLocal<String> TH_PLATFORM = new ThreadLocal<String>();

	public static void setUser(CsUcUser csUcUser) {
		LOCAL.set(csUcUser);
	}

	/**
	 * 获取对象
	 * 
	 * @return
	 */
	public static CsUcUser get() {
		return LOCAL.get();
	}

	/**
	 * 获取当前登陆用户名
	 * 
	 * @return
	 */
	public static String getUserName() {
		if (LOCAL.get() == null)
			return null;
		return LOCAL.get().getFdUsername();
	}

	/**
	 * 获取当前登陆用户中文名
	 * 
	 * @return
	 */
	public static String getRealName() {
		if (LOCAL.get() == null)
			return null;
		return LOCAL.get().getFdName();
	}

	/**
	 * 获取当前登陆用户ID
	 * 
	 * @return
	 */
	public static String getUserId() {
		if (LOCAL.get() == null)
			return null;
		return LOCAL.get().getFdSid();
	}

	/**
	 * 清理线程
	 */
	public static void remove() {
		LOCAL.remove();
	}

	public static void setPlatform(String thPlatform) {
		if (thPlatform != null && !"".equals(thPlatform))
			TH_PLATFORM.set(thPlatform);
	}

	public static String getPlatform() {
		return TH_PLATFORM.get();
	}

	public static void removePlatform() {
		TH_PLATFORM.remove();
	}

}
