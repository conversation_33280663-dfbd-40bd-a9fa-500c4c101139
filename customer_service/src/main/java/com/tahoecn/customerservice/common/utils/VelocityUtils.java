package com.tahoecn.customerservice.common.utils;

import java.io.StringWriter;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

public class VelocityUtils {
	public static String getStrByVelocity(VelocityContext context, String str) {
		Velocity.init();
        StringWriter stringWriter = new StringWriter();
        Velocity.evaluate(context, stringWriter, "mystring", str);
        return stringWriter.toString();
	}
}
