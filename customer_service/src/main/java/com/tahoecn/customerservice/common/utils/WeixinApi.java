/**
 * 
 */
package com.tahoecn.customerservice.common.utils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;
import com.tahoecn.http.HttpClient;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

/**
 * <AUTHOR>
 *
 */
/**
 * @ClassName <PERSON>xinApi
 * <AUTHOR>
 * @date 2019年5月10日
 */
@SuppressWarnings({ "rawtypes", "unchecked" })
@Component
public class WeixinApi {
	private static final Log log = LogFactory.get();

	private final static String API = "https://api.weixin.qq.com";

//	private final static String APP_ID = "wxe74d07c39a45b592";
//
//	private final static String SECRET = "c44a3d8ae434e60cd74587ca976d8381";
//	
//	private final static String TEMPLATE_ID = "MlPrdDPFSeZEDvOg4K1CgT9czjFQeqAeJ_LBynIB_j0";
	
	@Value("${WX_APP_ID}")
	private String APP_ID;
	
	@Value("${WX_SECRET}")
	private String SECRET;
	
	@Value("${WX_TEMPLATE_ID}")
	private String TEMPLATE_ID;

	@Autowired
	RedisTemplate redisTemplate;
	
	/**
	 * 推送微信模板
	 * 
	 * @param openId
	 * @param formId
	 */
	public void sendTemplate(String openId,String formId,String key1,String key2 ) {
		String accessToken = getAccessToken();
		
		String url = String.format(API + "/cgi-bin/message/template/send?access_token=%s", accessToken);
		log.info("request url = {}", url);
		Map<String, Object> map = new HashMap<>();
		map.put("touser", openId);
		map.put("template_id", TEMPLATE_ID);
		map.put("url", "http://comfort.tahoecn.com/#/orderDetail?formId="+formId);
		

		LinkedHashMap<String, Object> dataMap = new LinkedHashMap<>();
		
		Map<String, Object> first = new HashMap<>();
		first.put("value", "尊敬的客户，您所申报的问题已处理完毕，请进入详情页面确认！\r\n");
		dataMap.put("first", first);
		
		Map<String, Object> keyword1 = new HashMap<>();
		keyword1.put("value", key1);
		dataMap.put("keyword1", keyword1);
		
		Map<String, Object> keyword2 = new HashMap<>();
		keyword2.put("value", key2);
		dataMap.put("keyword2", keyword2);
		
		Map<String, Object> remark = new HashMap<>();
		remark.put("value", "\r\n邀请您为本次报事服务进行评价，您的评价将是我们改善服务源泉。\r\n感谢您对泰禾集团服务的一贯支持");
		remark.put("color", "#173177");
		dataMap.put("remark", remark);
		
		JSONObject json = new JSONObject();
		json.putAll(dataMap);
		
		map.put("data", json);
		
		RestTemplate restTemplate = new RestTemplate();
	    HttpHeaders headers = new HttpHeaders();
	    headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
	    HttpEntity<Map<String, Object>> request = new HttpEntity<>(map, headers);
	    ResponseEntity<String> response = restTemplate.postForEntity( url, request , String.class );
	    
		log.info("send json = {},result = {}", json.toJSONString(), response.getBody());
	}

	/**
	 * 获取access_token
	 * 
	 * @return
	 */
	private String getAccessToken() {
		// 从缓存里去获取token
		Object weixinAccessToken = redisTemplate == null ?null :redisTemplate.opsForValue().get("weixinAccessToken");
		if (weixinAccessToken == null) {

			String url = String.format(API + "/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", APP_ID,
					SECRET);
			log.info("request url = {}", url);
			String result = HttpClient.httpGet(url);
			log.info("userinfo result = {}", result);

			JSONObject json = JSONObject.parseObject(result);
			if (json.containsKey("access_token")) {
				if (json.get("access_token") != null && !json.get("access_token").equals("")) {
					weixinAccessToken = json.get("access_token");
					if(redisTemplate != null) {
						redisTemplate.opsForValue().set("weixinAccessToken", weixinAccessToken, 6000, TimeUnit.SECONDS);
					}
				}
			}
		}
		return weixinAccessToken.toString();
	}

	public static void main(String[] args) {
		WeixinApi api = new WeixinApi();
		api.sendTemplate("oDC5h5rurdrhExeoGjX-LMr896X8", "21143", "是顶顶顶顶顶顶顶顶顶顶顶顶顶顶", "2018-02-01");
	}
}
