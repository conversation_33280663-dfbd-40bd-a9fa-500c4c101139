package com.tahoecn.customerservice.common.web;

import java.io.Serializable;

public class ResponseMessage implements Serializable {
    /**
     * 是否成功  true false
     */
    private boolean sucess;

    /**
     * 反馈数据
     */
    private Object data;

    /**
     * 反馈信息
     */
    private String message;

    /**
     * 状态码
     */
    private int code;
    
    public ResponseMessage(){
        
    }

    protected ResponseMessage(boolean success, Object data) {
        this.code = success ? 200 : 500;
        this.data = data;
        this.sucess = success;
    }

    protected ResponseMessage(String message) {
        this.code = 500;
        this.message = message;
        this.sucess = false;
    }

    public static ResponseMessage ok(Object data) {
        return new ResponseMessage(true, data);
    }

    public static ResponseMessage okm(String message) {
        ResponseMessage responseMessage = new ResponseMessage(true, null);
        responseMessage.setCode(200);
        responseMessage.setMessage(message);
        return responseMessage;
    }

    public static ResponseMessage error(String message) {

        return new ResponseMessage(message);
    }

    public boolean isSucess() {
        return sucess;
    }

    public void setSucess(boolean sucess) {
        this.sucess = sucess;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


}
