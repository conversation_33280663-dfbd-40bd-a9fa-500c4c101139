package com.tahoecn.customerservice.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.spring.MybatisMapperRefresh;
import com.baomidou.mybatisplus.spring.MybatisSqlSessionFactoryBean;

@Configuration
@MapperScan(basePackages = "com.tahoecn.customerservice.mapper", sqlSessionFactoryRef = "sessionFactory")
public class DataSourceConfig {

	private static final String MAPPER_LOCATION = "classpath*:com/tahoecn/customerservice/mapper/xml/*.xml";

	@Value("${spring.datasource.core.driver-class-name}")
	private String driverClassName;

	@Value("${spring.datasource.core.url}")
	private String url;

	@Value("${spring.datasource.core.username}")
	private String username;

	@Value("${spring.datasource.core.password}")
	private String password;

	@Value("${spring.datasource.druid.initial-size}")
	private int initialSize;

	@Value("${spring.datasource.druid.min-idle}")
	private int minIdle;

	@Value("${spring.datasource.druid.max-active}")
	private int maxActive;

	// 初始化数据库连接
	@Bean(name = "dataSource")
	@Primary
	public DataSource dataSource() {
		DruidDataSource dataSource = new DruidDataSource();
		dataSource.setDriverClassName(driverClassName);
		dataSource.setUrl(url);
		dataSource.setUsername(username);
		dataSource.setPassword(password);
		return dataSource;
	}

	// 数据源事务管理器
	@Bean(name = "transactionManager")
	@Primary
	public DataSourceTransactionManager transactionManager() {
		DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
		dataSourceTransactionManager.setDataSource(dataSource());
		return dataSourceTransactionManager;
	}

	// 创建Session
	@Bean(name = "sessionFactory")
	@Primary
	public MybatisSqlSessionFactoryBean sessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
		final MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
		sqlSessionFactoryBean.setDataSource(dataSource);
		Resource[] resource = new PathMatchingResourcePatternResolver().getResources(DataSourceConfig.MAPPER_LOCATION);
		sqlSessionFactoryBean.setMapperLocations(resource);
		sqlSessionFactoryBean.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:MybatisConfig.xml"));
		return sqlSessionFactoryBean;
	}
	
	// XML文件热加载
	@Bean
	public MybatisMapperRefresh mybatisMapperRefresh(@Qualifier("sessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
		Resource[] resource = new PathMatchingResourcePatternResolver().getResources(DataSourceConfig.MAPPER_LOCATION);
		return new MybatisMapperRefresh(resource, sqlSessionFactory, true);
	}

}
