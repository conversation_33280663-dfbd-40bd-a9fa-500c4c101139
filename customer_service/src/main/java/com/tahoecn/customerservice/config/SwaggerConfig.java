package com.tahoecn.customerservice.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    @Bean
    public Docket createRestApi() {
    	ParameterBuilder parameterBuilder = new ParameterBuilder();
        List<Parameter> parameters = new ArrayList();
        parameterBuilder.name("userName").defaultValue("wanghongxin").modelRef(new ModelRef("string")).parameterType("header").build();
        parameters.add(parameterBuilder.build());
        
        parameterBuilder = new ParameterBuilder();
        parameterBuilder.name("random").defaultValue("123456").modelRef(new ModelRef("string")).parameterType("header").build();
        parameters.add(parameterBuilder.build());
        
        parameterBuilder = new ParameterBuilder();
        parameterBuilder.name("key").defaultValue("34a97375014fc5a5b75330582f885c78").modelRef(new ModelRef("string")).parameterType("header").build();
        parameters.add(parameterBuilder.build());
        
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.tahoecn.customerservice.controller"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(parameters);
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("400通用框架标题")
                .description("400通用框架描述")
                .termsOfServiceUrl("400通用框架服务地址")
                .version("1.0")
                .build();
    }
}
