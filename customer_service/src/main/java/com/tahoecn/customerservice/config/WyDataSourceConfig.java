//package com.tahoecn.customerservice.config;
//
//import javax.sql.DataSource;
//
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.Resource;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import com.baomidou.mybatisplus.mapper.ISqlInjector;
//import com.baomidou.mybatisplus.mapper.LogicSqlInjector;
//import com.baomidou.mybatisplus.spring.MybatisSqlSessionFactoryBean;
//
//@Configuration
//@MapperScan(basePackages = "com.tahoecn.customerservice.mapperwy", sqlSessionFactoryRef = "wySqlSessionFactory")
//public class WyDataSourceConfig {
//
//	private static final String MAPPER_LOCATION = "classpath*:com/tahoecn/customerservice/mapperwy/xml/*.xml";
//
//	@Value("${spring.datasource.wy.driver-class-name}")
//	private String driverClassName;
//
//	@Value("${spring.datasource.wy.url}")
//	private String url;
//
//	@Value("${spring.datasource.wy.username}")
//	private String username;
//
//	@Value("${spring.datasource.wy.password}")
//	private String password;
//
//	@Value("${spring.datasource.druid.initial-size}")
//	private int initialSize;
//
//	@Value("${spring.datasource.druid.min-idle}")
//	private int minIdle;
//
//	@Value("${spring.datasource.druid.max-active}")
//	private int maxActive;
//
//	// 初始化数据库连接
//	@Bean(name = "wyDataSource")
//	public DataSource wyDataSource() {
//		DruidDataSource dataSource = new DruidDataSource();
//		dataSource.setDriverClassName(driverClassName);
//		dataSource.setUrl(url);
//		dataSource.setUsername(username);
//		dataSource.setPassword(password);
//		dataSource.setInitialSize(initialSize);
//		dataSource.setMinIdle(minIdle);
//		dataSource.setMaxActive(maxActive);
//		dataSource.setValidationQuery("SELECT GETDATE()");
//		return dataSource;
//	}
//
//	// 数据源事务管理器
//	@Bean(name = "wyTransactionManager")
//	public DataSourceTransactionManager wyDataSourceTransactionManager() {
//		DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
//		dataSourceTransactionManager.setDataSource(wyDataSource());
//		return dataSourceTransactionManager;
//	}
//
//	// 创建Session
//	@Bean(name = "wySqlSessionFactory")
//	public SqlSessionFactory wySqlSessionFactory(@Qualifier("wyDataSource") DataSource wyDataSource) throws Exception {
//		final MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
//		sqlSessionFactoryBean.setDataSource(wyDataSource);
//		Resource[] resource = new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION);
//		sqlSessionFactoryBean.setMapperLocations(resource);
//		sqlSessionFactoryBean.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:MybatisConfig.xml"));
//		return sqlSessionFactoryBean.getObject();
//	}
//
////	@Bean
//	public ISqlInjector sqlInjector() {
//		return new LogicSqlInjector();
//	}
//
//}