package com.tahoecn.customerservice.controller;


import com.landray.sso.client.EKPSSOContext;
import org.springframework.stereotype.Component;


/**
 * <p>
 * 分支 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-10-10
 */
@Component
public class BaseController extends TahoeBaseController {
   

    public String getUsername() {
        EKPSSOContext context = EKPSSOContext.getInstance();
        if (context == null) {
            return null;
        }
        return context.getCurrentUsername();
    }

  
}

