package com.tahoecn.customerservice.controller;


import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsAnnouncement;
import com.tahoecn.customerservice.service.CsAnnouncementService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-22
 */
@Api(tags = "公告接口", value = "公告接口")
@Controller
@RequestMapping("/api/csAnnouncement")
public class CsAnnouncementController {

	@Autowired
	private CsAnnouncementService csAnnouncementService;
	
    @ApiOperation(value = "公告内容", notes = "公告内容")
	@RequestMapping(value = "/announcement", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage announcement() {
        try {
        	Wrapper<CsAnnouncement> wrapper = new EntityWrapper<CsAnnouncement>();
//        	wrapper.where("state = {0}", "1");
        	List<CsAnnouncement> list = csAnnouncementService.selectList(wrapper);
        	if(list != null && list.size() != 0){
        		return ResponseMessage.ok(list.get(0));
        	}else{
        		return ResponseMessage.ok("");
        	}
        } catch (Exception e) {
        	e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    
    @ApiOperation(value = "保存公告内容", notes = "保存公告内容")
	@RequestMapping(value = "/saveAnnouncement", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage saveAnnouncement(@RequestBody CsAnnouncement ann) {
        try {
        	Wrapper<CsAnnouncement> wrapper = new EntityWrapper<CsAnnouncement>();
        	List<CsAnnouncement> list = csAnnouncementService.selectList(wrapper);
        	if(list != null && list.size() != 0){
        		csAnnouncementService.updateById(ann);
        	}else{
        		csAnnouncementService.insert(ann);
        	}
        	return ResponseMessage.ok("修改成功");
        } catch (Exception e) {
        	e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
}

