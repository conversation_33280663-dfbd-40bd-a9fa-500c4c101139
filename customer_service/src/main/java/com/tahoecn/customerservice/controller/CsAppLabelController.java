package com.tahoecn.customerservice.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsLabel;
import com.tahoecn.customerservice.model.dto.CsLabelTreeDto;
import com.tahoecn.customerservice.service.CsLabelService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * <p>
 * 标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-09
 */
@Api(tags = "APP标签接口",value="APP标签接口")
@Controller
@RequestMapping("/weChatApi/csLabel")
public class CsAppLabelController {
	
	@Autowired
	private CsLabelService csLabelService;
	
	@ApiOperation(value = "查询客户标签列表", notes = "查询标签列表，树形结构返回")
	@ApiImplicitParams({@ApiImplicitParam(name = "flag", value = "客户活动标志cust客户activity活动", dataType = "String")})
	/*	@ApiImplicitParam(name = "itemName", value = "标签名称", dataType = "String"),
		@ApiImplicitParam(name = "collection", value = "收集渠道", dataType = "String"),
		@ApiImplicitParam(name = "score", value = "分值", dataType = "String"),
		})*/
	@RequestMapping(value = "/selCustLabel", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage selCustLabel(String flag) {
		try{
			Map<String, Object> map = new HashMap<>();
			//查询条件
			map.put("flag", flag);//查询客户、活动标志
			/*map.put("itemName", itemName);
			map.put("collection", collection);
			map.put("score", score);*/
			//查询列表
			List<CsLabelTreeDto> list = csLabelService.getLabelTree(map);
			return ResponseMessage.ok(list);
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【查询客户标签列表】系统出错");
		}
	}
}

