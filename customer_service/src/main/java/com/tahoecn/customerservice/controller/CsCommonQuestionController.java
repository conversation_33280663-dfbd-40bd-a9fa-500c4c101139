package com.tahoecn.customerservice.controller;


import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsCommonQuestion;
import com.tahoecn.customerservice.service.CsCommonQuestionService;

import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 常见问题表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Controller
@RequestMapping("/api/csCommonQuestion")
public class CsCommonQuestionController extends BaseController{

    @Autowired
    CsCommonQuestionService csCommonQuestionService;

    /**
     * 常见问题自定义
     */
    @ApiOperation(value = "自定义个人常见问题", notes = "自定义个人常见问题")
    @RequestMapping(value = "/addCommonQuestion",method = RequestMethod.POST)
    @ResponseBody
    public Object addCommonQuestion(String questionTitle,String questionDesc, Long id){
        try {
            if(StringUtils.isBlank(questionTitle)||
                    StringUtils.isBlank(questionDesc)){
                return ResponseMessage.error("请补全数据");
            }
            else{
                CsCommonQuestion cq = new CsCommonQuestion();
                cq.setUserName(getUsername());
                cq.setQuestionTitle(questionTitle);
                cq.setQuestionDesc(questionDesc);
                cq.setCreateDate(new Date());
                cq.setLastUpdateDate(new Date());
                if(null == id) {
                	id = csCommonQuestionService.addCommonQuestion(cq);
                	return ResponseMessage.ok(id);
                }else {
                	Wrapper<CsCommonQuestion> wrapper = new EntityWrapper<>();
                	wrapper.eq("id", id);
					boolean updateFlag = csCommonQuestionService.update(cq,wrapper);
					if(updateFlag) {
						return ResponseMessage.ok(id);
					}else {
						return ResponseMessage.error("fail");
					}
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseMessage.error("系统错误，请联系管理员");
    }

    /**
     * 常见问题查询(通用问题+属于自己的自定义问题)
     */
    @ApiOperation(value = "常见问题按登陆人查询", notes = "常见问题按登陆人查询")
    @RequestMapping(value = "/selectQuestionByUserId",method = RequestMethod.GET)
    @ResponseBody
    public Object selectQuestionByUserId(){
        try {
            List<CsCommonQuestion> list = csCommonQuestionService.selectCommQuestionAndPersonal(getUsername());
            return ResponseMessage.ok(list);
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseMessage.error("系统错误，请联系管理员");
    }

    /**
     * id查询问题
     */
    @ApiOperation(value = "常见问题按主键查询", notes = "常见问题按主键查询")
    @RequestMapping(value = "/selectQuestionById",method = RequestMethod.GET)
    @ResponseBody
    public Object selectQuestionById(Long id){
        try {
            if(null!=id){
                CsCommonQuestion cq = csCommonQuestionService.selectQuestionById(id);
                return ResponseMessage.ok(cq);
            }else {
                return ResponseMessage.error("无查询条件");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseMessage.error("系统错误，请联系管理员");
    }

    @ApiOperation(value = "常见问题删除", notes = "常见问题删除")
    @RequestMapping(value = "/deleteById",method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage deleteById(Long id) {
    	if(null == id) {
    		return ResponseMessage.error("fail");
    	}
    	CsCommonQuestion cq = new CsCommonQuestion();
    	cq.setIsDelete(1);
    	Wrapper<CsCommonQuestion> wrapper = new EntityWrapper<>();
    	wrapper.eq("id", id);
    	try {
			boolean deleteFlag = csCommonQuestionService.update(cq,wrapper);
			if(deleteFlag) {
				return ResponseMessage.okm("success");
			}
		} catch (Exception e) {
		}
    	return ResponseMessage.error("fail");
    }
}

