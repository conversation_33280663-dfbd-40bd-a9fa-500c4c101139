package com.tahoecn.customerservice.controller;

import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsCustFamily;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.service.CsCustFamilyService;

import cn.hutool.core.lang.UUID;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 客户家庭成员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Controller
@RequestMapping("/api/csCustFamily")
public class CsCustFamilyController {
	@Autowired
	private CsCustFamilyService csCustFamilyService;

	@ApiOperation(value = "家庭人员", notes = "根据业主id查询家庭成员")
	@ApiImplicitParams({ @ApiImplicitParam(name = "custId", value = "业主ID", required = false, dataType = "String"),
		@ApiImplicitParam(name = "formId", value = "工单ID,传入则查工单相关家庭成员信息", required = false, dataType = "String")})
    @RequestMapping(value = "/selectByCustId", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage selectByCustId(String custId, String formId) {
		CsCustFamily csCustFamily = new CsCustFamily();
		if (StringUtils.isNotBlank(formId)) {
			csCustFamily.setFormInstId(Long.valueOf(formId));
			return ResponseMessage.ok(csCustFamilyService.selectList(new EntityWrapper<CsCustFamily>(csCustFamily)));
		}else if (StringUtils.isNotBlank(custId)){
			EntityWrapper<CsCustFamily> wrapper = new EntityWrapper<CsCustFamily>();
			wrapper.eq("cust_id", custId);
			wrapper.and().isNull("form_inst_id");
			return ResponseMessage.ok(csCustFamilyService.selectList(wrapper));
		}
		return ResponseMessage.error("参数异常!!!");
	}
	
	@ApiOperation(value = "修改家庭成员", notes = "修改家庭成员")
	@RequestMapping(value = "/save", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage save(@RequestBody CsCustFamily csCustFamily) {
		if(StringUtils.isBlank(csCustFamily.getCustId())) {
			return ResponseMessage.error("参数异常");
		}
		csCustFamily.setUpdateTime(new Date());
		if(csCustFamily.getId() == null) {
			csCustFamily.setCreateTime(new Date());
			csCustFamily.setMemberId(UUID.randomUUID().toString());
		}
		csCustFamilyService.insertOrUpdate(csCustFamily);
		return ResponseMessage.ok(csCustFamily.getCustId());
	}
	
}
