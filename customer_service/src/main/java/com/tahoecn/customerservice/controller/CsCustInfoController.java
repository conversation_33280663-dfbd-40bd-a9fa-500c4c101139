package com.tahoecn.customerservice.controller;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.common.utils.ExcelUtil;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.dto.CsCustInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;
import com.tahoecn.customerservice.service.CsCustInfoService;
import com.tahoecn.customerservice.service.CsSyncCustService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 客户信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Controller
@RequestMapping("/api/csCustInfo")
public class CsCustInfoController {

	@Autowired
	CsCustInfoService csCustInfoService;
	
	@Autowired
	CsSyncCustService csSyncCustService;
	
	@ApiOperation(value = "房人工单 联查接口", notes = "房人工单 联查接口")
	@RequestMapping(value = "/hcfInfo", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage hcfInfo(HcfInfoDto dto) {
		if (!dto.paramsIsNotNull()) {
			return ResponseMessage.ok(null);
		}
		return ResponseMessage.ok(csCustInfoService.getByHcfInfoDto(dto));
	}

	@ApiOperation(value = "查询人员列表", notes = "查询人员列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),			
			@ApiImplicitParam(name = "sex", value = "性别", dataType = "String"),
			@ApiImplicitParam(name = "email", value = "电子邮件", dataType = "String"),
			@ApiImplicitParam(name = "stewardName", value = "管家姓名", dataType = "String"),
			@ApiImplicitParam(name = "national", value = "国籍", dataType = "String"),			
			@ApiImplicitParam(name = "sHouseNum", value = "房屋编号", dataType = "String"),
			@ApiImplicitParam(name = "custName", value = "客户姓名", dataType = "String"),
			@ApiImplicitParam(name = "telephone", value = "电话", dataType = "String"),
			@ApiImplicitParam(name = "certificateNum", value = "证件号", dataType = "String"),
			@ApiImplicitParam(name = "belong", value = "个人/单位", dataType = "String"),
			@ApiImplicitParam(name = "regionCode", value = "区域编码", dataType = "String"),
			@ApiImplicitParam(name = "cityCompanyCode", value = "城市公司编码", dataType = "String"),
			@ApiImplicitParam(name = "cityCode", value = "城市编码", dataType = "String"),
			@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
			@ApiImplicitParam(name = "isHasMoreHouse", value = "是否有多套房", dataType = "String"),
			@ApiImplicitParam(name = "cusIdentity", value = "客户身份", dataType = "String"),
			@ApiImplicitParam(name = "isAdmin", value = "是否是管理员", dataType = "String", defaultValue = "0"),
			@ApiImplicitParam(name = "bStartDate", value = "出生日期开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "bEndDate", value = "出生日期结束时间", dataType = "Date") })
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage list(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "20") Integer pageSize, String houseNum,String sHouseNum,String sHouseName, String custName, String telephone,
			String certificateNum, String belong, String isAdmin, Date bStartDate, Date bEndDate, String regionCode,String cityCompanyCode, 
			String cityCode, String projectCode, String isHasMoreHouse, String cusIdentity,String sex,String email,String stewardName,String national) {
//		Wrapper<CsCustInfo> wrapper = new EntityWrapper<>();
//		if (StringUtils.isNotBlank(houseNum)) {
//			wrapper.eq("house_num", houseNum);
//		}
//		if (StringUtils.isNotBlank(sHouseNum)) {
//			wrapper.like("house_num", sHouseNum);
//		}
//		if (StringUtils.isNotBlank(custName)) {
//			wrapper.like("cust_name", custName);
//		}
//		if (StringUtils.isNotBlank(telephone)) {
//			wrapper.like("telephone", telephone);
//		}
//		if (StringUtils.isNotBlank(certificateNum)) {
//			wrapper.like("certificate_num", certificateNum);
//		}
//		if (StringUtils.isNotBlank(belong)) {
//			wrapper.eq("belong", belong);
//		}
//		if (bStartDate != null) {
//			wrapper.ge("birthday", bStartDate);
//		}
//		if (bEndDate != null) {
//			wrapper.le("birthday", bEndDate);
//		}
//		if (pageSize != 0) {
//			// wrapper.groupBy("cust_id");
//			Page<CsCustInfo> selectPage = csCustInfoService.selectPage(new Page<>(pageNum, pageSize), wrapper);
//			//chenyy 20190108
//			/*if("0".equals(isManager)) {//不是管理员则进行加密
//				if(null != selectPage && null != selectPage.getRecords() && selectPage.getRecords().size() > 0) {
//					for(CsCustInfo csCustInfo : selectPage.getRecords()) {
//						String telephoneStr = csCustInfo.getTelephone();
//						String[] phoneArr = telephoneStr.split(",");
//						csCustInfo.setTelephone(DataUtil.phoneDec(phoneArr));//手机号加密
//						csCustInfo.setCertificateNum(DataUtil.idCardDec(csCustInfo.getCertificateNum()));//身份证号加密
//					}
//				}
//			}*/
//			return ResponseMessage.ok(selectPage);
//		} else {
//			List<CsCustInfo> selectList = csCustInfoService.selectList(wrapper);
//			//chenyy 20190108
//			/*if("0".equals(isManager)) {//不是管理员则进行加密
//				if(selectList.size() > 0) {
//					for(CsCustInfo csCustInfo : selectList) {
//						String telephoneStr = csCustInfo.getTelephone();
//						String[] phoneArr = telephoneStr.split(",");
//						csCustInfo.setTelephone(DataUtil.phoneDec(phoneArr));//手机号加密
//						csCustInfo.setCertificateNum(DataUtil.idCardDec(csCustInfo.getCertificateNum()));//身份证号加密
//					}
//				}
//			}*/
//			return ResponseMessage.ok(selectList);
//		}
		
		
		if (pageSize == 0) {//房屋信息详情关联客户查询
			if(StringUtils.isBlank(houseNum)) {
				return ResponseMessage.ok(new ArrayList<>());
			}
			Wrapper<CsCustInfo> wrapper = new EntityWrapper<>();
			wrapper.eq("house_num", houseNum);
			List<CsCustInfo> selectList = csCustInfoService.selectList(wrapper);
			return ResponseMessage.ok(selectList);
		}
		
		//update by chenyy   2019年1月9日15:48:10   业主姓名后拼接房屋数量
		Map<String, Object> map = new HashMap<>();
		Page<CsCustInfoDto> page = new Page<CsCustInfoDto>(pageNum, pageSize);
		
		map.put("sex", sex);
		map.put("email", email);
		map.put("stewardName", stewardName);
		map.put("national", national);
		map.put("houseNum", houseNum);
		map.put("sHouseNum", sHouseNum);
		map.put("sHouseName", sHouseName);
		map.put("custName", custName);
		map.put("telephone", telephone);
		map.put("certificateNum", certificateNum);
		map.put("belong", belong);
		map.put("bStartDate", bStartDate);
		map.put("bEndDate", bEndDate);
		map.put("isAdmin", isAdmin);
		map.put("regionCode", regionCode);
		map.put("cityCompanyCode", cityCompanyCode);
		map.put("cityCode", cityCode);
		map.put("projectCode", projectCode);
		map.put("isHasMoreHouse", isHasMoreHouse);
		map.put("cusIdentity", cusIdentity);
		map.put("count", (pageNum - 1) * pageSize);
		map.put("pageSize", pageSize);
		List<CsCustInfoDto> resultList = csCustInfoService.selectCustInfoList(map);
		
		page.setRecords(resultList);
		Integer total = csCustInfoService.getListCount(map);
		page.setTotal(total);
		return ResponseMessage.ok(page);
		
	}
	
	
	/**
	 * 导出人员列表
	 * update by chenyy
	 * insert time: 2019年1月9日16:53:39
	 * @param response
	 */
	
	@ApiOperation(value = "导出人员列表", notes = "导出人员列表")
	@ApiImplicitParams({			
		@ApiImplicitParam(name = "sex", value = "性别", dataType = "String"),
		@ApiImplicitParam(name = "email", value = "电子邮件", dataType = "String"),
		@ApiImplicitParam(name = "stewardName", value = "管家姓名", dataType = "String"),
		@ApiImplicitParam(name = "national", value = "国籍", dataType = "String"),			
		@ApiImplicitParam(name = "sHouseNum", value = "房屋编号", dataType = "String"),
		@ApiImplicitParam(name = "custName", value = "客户姓名", dataType = "String"),
		@ApiImplicitParam(name = "telephone", value = "电话", dataType = "String"),
		@ApiImplicitParam(name = "certificateNum", value = "证件号", dataType = "String"),
		@ApiImplicitParam(name = "belong", value = "个人/单位", dataType = "String"),
		@ApiImplicitParam(name = "regionCode", value = "区域编码", dataType = "String"),
		@ApiImplicitParam(name = "cityCompanyCode", value = "城市公司编码", dataType = "String"),
		@ApiImplicitParam(name = "cityCode", value = "城市编码", dataType = "String"),
		@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
		@ApiImplicitParam(name = "isHasMoreHouse", value = "是否有多套房", dataType = "String"),
		@ApiImplicitParam(name = "cusIdentity", value = "客户身份", dataType = "String"),
		@ApiImplicitParam(name = "isAdmin", value = "是否是管理员", dataType = "String", defaultValue = "0"),
		@ApiImplicitParam(name = "bStartDate", value = "出生日期开始时间", dataType = "Date"),
		@ApiImplicitParam(name = "bEndDate", value = "出生日期结束时间", dataType = "Date")})
	@RequestMapping(value = "/exportCustInfo", method = RequestMethod.GET)
	public void exportITExcel(HttpServletResponse response,String sHouseNum,String sHouseName, String custName, String telephone,
			String certificateNum, String belong, String isAdmin, Date bStartDate, Date bEndDate, String regionCode,String cityCompanyCode, 
			String cityCode, String projectCode, String isHasMoreHouse, String cusIdentity,String sex,String email,String stewardName,String national) {
		
		try {
			Map<String, Object> map = new HashMap<>();
			
			response.setHeader("content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode("客服管理系统客户信息明细.xlsx", "utf-8"));
			
//			if (setUserDataPriv(map)) {
//				ExcelUtil.listToExcel(new ArrayList<ExpForm>(), "客服管理系统客户信息明细", response.getOutputStream());
//				return;
//			}
			
			map.put("sex", sex);
			map.put("email", email);
			map.put("stewardName", stewardName);
			map.put("national", national);
			map.put("sHouseNum", sHouseNum);
			map.put("sHouseName", sHouseName);
			map.put("custName", custName);
			map.put("telephone", telephone);
			map.put("certificateNum", certificateNum);
			map.put("belong", belong);
			map.put("bStartDate", bStartDate);
			map.put("bEndDate", bEndDate);
			map.put("isAdmin", isAdmin);
			map.put("regionCode", regionCode);
			map.put("cityCompanyCode", cityCompanyCode);
			map.put("cityCode", cityCode);
			map.put("projectCode", projectCode);
			map.put("isHasMoreHouse", isHasMoreHouse);
			map.put("cusIdentity", cusIdentity);
			/*List<CsCustInfoDto> list = csCustInfoService.selectCustInfoList(map);*/
			List<CsHouseInfoCustInfoFrom> list = csCustInfoService.selectCsHouseInfoCustInfoFrom(map);
			
			ExcelUtil.listToExcel(list, "客服管理系统客户信息明细","客户信息明细", response.getOutputStream());
//			ExcelUtil.listToExcelMoreSheet(list, "客服管理系统客户信息明细", "客户信息明细", response.getOutputStream());
		} catch (Exception e) {
			System.out.println("===================================");
		}
	}
	

	@ApiOperation(value = "根据业主编码查询业主信息", notes = "根据业主编码查询业主信息")
	@RequestMapping(value = "/selecthcfInfoByNum", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage selecthcfInfoByNum(String id) {
		return ResponseMessage.ok(csCustInfoService.selectOne((new EntityWrapper<CsCustInfo>()).eq("cust_id", id)));
	}
	
	@ApiOperation(value = "修改业主信息", notes = "修改业主信息")
	@RequestMapping(value = "/save", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage save(@RequestBody CsCustInfo custInfo) {
		if(StringUtils.isBlank(custInfo.getCustId())) {
			return ResponseMessage.error("参数异常");
		}
		custInfo.setId(null);
		custInfo.setHouseNum(null);
		Wrapper<CsCustInfo> wrapper = new EntityWrapper<>();
		wrapper.eq("cust_id", custInfo.getCustId());
		csCustInfoService.update(custInfo, wrapper);
		csSyncCustService.updateCust(custInfo);
		return ResponseMessage.ok(custInfo.getCustId());
	}
}
