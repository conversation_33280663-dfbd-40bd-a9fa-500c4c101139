package com.tahoecn.customerservice.controller;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsDictItem;
import com.tahoecn.customerservice.service.CsDictItemService;

import io.swagger.annotations.ApiOperation;

/**
 * 字典表
 */
@Controller
@RequestMapping("/api/csDictItem")
public class CsDictItemController extends BaseController {

	@Autowired
	private CsDictItemService csDictItemService;

	@ApiOperation(value = "字典项全部", notes = "字典项全部")
	@RequestMapping(value = "/selectAll", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage selectAll() {
		EntityWrapper<CsDictItem> wrapper = new EntityWrapper<>();
		wrapper.orderBy("dict_code,display_order");
		wrapper.eq("status", "1");
		return ResponseMessage.ok(csDictItemService.selectList(wrapper));
	}

	/**
	 * 字典项查询
	 */
	@ApiOperation(value = "字典项查询", notes = "字典项查询")
	@RequestMapping(value = "/selectCsDictItemByItemCode", method = RequestMethod.GET)
	@ResponseBody
	public Object selectCsDictItemByItemCode(String code) {
		try {
			if (StringUtils.isNotBlank(code)) {
				code = URLDecoder.decode(code, "UTF-8");
				CsDictItem cs = csDictItemService.selectCsDictItemByItemCode(code);
				return ResponseMessage.ok(cs);
			} else {
				return ResponseMessage.ok("请输入查询内容");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 字典组查询
	 */
	@ApiOperation(value = "字典组查询", notes = "字典组查询")
	@RequestMapping(value = "/selectAllCsDictItemByDictCode", method = RequestMethod.GET)
	@ResponseBody
	public Object selectAllCsDictItemByDictCode(String code) {
		try {
			if (StringUtils.isNotBlank(code)) {
				code = URLDecoder.decode(code, "UTF-8");
				List<CsDictItem> list = csDictItemService.selectAllCsDictItemByDictCode(code);
				return ResponseMessage.ok(list);
			} else {
				return ResponseMessage.ok("请输入查询内容");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 查询不需联动一些初始字典
	 */
	@ApiOperation(value = "查询不需联动一些初始字典", notes = "查询不需联动一些初始字典")
	@RequestMapping(value = "/selectCommDict", method = RequestMethod.GET)
	@ResponseBody
	public Object selectCommDict() {
		try {
			List<CsDictItem> list = csDictItemService.selectAllCsDictItem();
			Map<String, List<Map<String, String>>> result = new HashMap<>();
			Set<String> set = new LinkedHashSet<>();
			for (CsDictItem di : list) {
				set.add(di.getDictCode());
			}
			for (String dc : set) {
				List<Map<String, String>> re = new ArrayList<>();
				result.put(dc, re);
			}
			Set<String> keySet = result.keySet();
			for (CsDictItem ci : list) {
				for (String dictCode : keySet) {
					if (ci.getDictCode().equals(dictCode)) {
						Map<String, String> itemKeyValue = new HashMap<>();
						itemKeyValue.put("itemCode", ci.getItemCode());
						itemKeyValue.put("itemValue", ci.getItemValue());
						result.get(dictCode).add(itemKeyValue);
						continue;
					}
				}
			}
			return ResponseMessage.ok(result);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}


	/**
	 * <AUTHOR>
	 * @Description //TODO
	 * @Date 16:01 2018/12/20
	 * @Param
	 * @return
	 **/
	@ApiOperation(value = "报事类型管理列表查询", notes = "报事类型管理列表查询")
	@RequestMapping(value = "/treeListQuery", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage treeListQuery() {
		try {
			ResponseMessage message = csDictItemService.selectTreeDictItem();
			return message;
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}


	/**
	 * <AUTHOR>
	 * @Description //TODO
	 * @Date 18:19 2018/12/20
	 * @Param
	 * @return
	 **/
	@ApiOperation(value = "报事类型管理编辑页面回显", notes = "报事类型管理编辑页面回显")
	@RequestMapping(value = "/updateEchoInfo", method = RequestMethod.GET)
	@ApiImplicitParams({
                @ApiImplicitParam(name = "itemCode", value = "字典项编码", required = true, dataType = "String")
	})
	@ResponseBody
	public ResponseMessage updateEchoInfo(String itemCode) {
		if (itemCode == null || itemCode.equals("")){
			return ResponseMessage.error("itemCode 为空, 请检查参数");
		}
		try {
			ResponseMessage message = csDictItemService.selectOneDictItem(itemCode);
			return message;
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}


	/**
	 * <AUTHOR>
	 * @Description //TODO
	 * @Date 9:20 2018/12/21
	 * @Param
	 * @return
	 **/
	@ApiOperation(value = "报事类型管理编辑提交保存", notes = "报事类型管理编辑提交保存")
        @RequestMapping(value = "/dictItemInfoSave", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage updateDictItemInfo(@RequestBody Map<String , Object> pamam) {
		try {
			String type = pamam.get("type").toString();
			if (type == null || type.equals("")){
				return ResponseMessage.error("请填写参数 : type");
			}
			String itemCode = pamam.get("itemCode").toString();
			if (itemCode == null || itemCode.equals("")){
				return  ResponseMessage.error("请填写参数 : itemCode");
			}
			String itemValue = pamam.get("itemValue").toString();
			if (itemValue == null || itemValue.equals("")){
				return ResponseMessage.error("请填写参数 : itemValue");
			}
			ResponseMessage message = csDictItemService.updateDictItemInfo(pamam);
			return message;
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}

	@ApiOperation(value = "IT报事类型管理列表查询", notes = "IT报事类型管理列表查询")
	@RequestMapping(value = "/itTreeListQuery", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage itTreeListQuery() {
		try {
			List<Map<String, Object>> list = csDictItemService.selectItTreeDictItem();
			return ResponseMessage.ok(list);
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}
	
	@ApiOperation(value = "IT报事类型管理编辑保存", notes = "IT报事类型管理编辑保存")
    @RequestMapping(value = "/updateItDictItem", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage updateItDictItem(@RequestBody Map<String , Object> pamam) {
		try {
			return csDictItemService.updateItDictItem(pamam);
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}
	
	@ApiOperation(value = "IT报事类型管理添加子级", notes = "IT报事类型管理添加子级")
	@RequestMapping(value = "/insertItDictItem", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage insertItDictItem(@RequestBody Map<String , Object> pamam) {
		try {
			return csDictItemService.insertItDictItem(pamam);
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}
	
	@ApiOperation(value = "IT报事类型管理删除", notes = "IT报事类型管理删除")
	@RequestMapping(value = "/deleteItDictItem", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage deleteItDictItem(String itemCode) {
		try {
			return csDictItemService.deleteItDictItem(itemCode);
		} catch (Exception e){
			e.getMessage();
			return ResponseMessage.error("系统异常,请联系管理员");
		}
	}
	
}
