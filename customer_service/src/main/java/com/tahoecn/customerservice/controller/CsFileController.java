package com.tahoecn.customerservice.controller;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFile;
import com.tahoecn.customerservice.service.CsFileService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

/**
 * 附件表 前端控制器
 */
@Controller
@RequestMapping("/api/csFile")
public class CsFileController extends BaseController {

	private static final Log log = LogFactory.get();
	@Autowired
	private CsFileService csFileService;

	@Value("${root_path}")
	private String rootPath;

	/**
	 * 附件上传
	 */
	@RequestMapping(value = "/fileUpdate", method = RequestMethod.POST)
	@ResponseBody
	public Object fileUpdate(@RequestParam("localfile") List<MultipartFile> files, String clientPath) {
		try {
			if (files == null || (files != null && files.size() == 0)) {
				return ResponseMessage.error("上传文件不能为空");
			}
			List<Long> list = new ArrayList<>();
			List<CsFile> csFileList = new ArrayList<>();
			for (MultipartFile file : files) {
				if (file.isEmpty()) {
					continue;
				}
				Long id = csFileService.saveUploadFile(file, clientPath);
				list.add(id);
			}
			return ResponseMessage.ok(list);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 附件工单关联
	 */
	@RequestMapping(value = "/relationFormAndFiles", method = RequestMethod.POST)
	@ResponseBody
	public Object relationFormAndFiles(Long id, String... ids) {
		try {
			if ((ids != null && ids.length > 0) && (id != null)) {
				List<Long> idList = new ArrayList<>();
				for (String idd : ids) {
					idd = idd.replace("[", "");
					idd = idd.replace("]", "");
					if (StringUtils.isNotBlank(idd)) {
						Long iddd = Long.parseLong(idd);
						idList.add(iddd);
					}
				}
				Long row = csFileService.relationFormAndFiles(id, idList);
				if (row > 0) {
					return ResponseMessage.ok("关联成功");
				}
				return ResponseMessage.error("系统错误，附件丢失");
			} else {
				return ResponseMessage.error("条件有误");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 多附件Id查询
	 */
	@RequestMapping(value = "/selectCsFileByIdList", method = RequestMethod.GET)
	@ResponseBody
	public Object selectCsFileByIdList(Long... idList) {
		try {
			if (idList != null && idList.length > 0) {
				List<Long> ids = new ArrayList<>();
				for (Long id : idList) {
					ids.add(id);
				}
				List<CsFile> list = csFileService.selectCsFileByIdList(ids);
				return ResponseMessage.ok(list);
			} else {
				return ResponseMessage.error("无查询条件");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 附件下载
	 */
	@RequestMapping(value = "/fileDownload", method = RequestMethod.GET)
	public Object fileDownload(Long id, final HttpServletResponse response, HttpServletRequest request) {
		try {
			CsFile csFile = csFileService.selectCsFileById(id);
			if (csFile == null) {
				return ResponseMessage.error("无法查询到数据");
			}
			String downLoadPath = rootPath + csFile.getServerPath();
			BufferedInputStream bis = null;
			BufferedOutputStream bos = null;
			try {
				String fileName = URLEncoder.encode(csFile.getFileName(), "UTF-8");
				long fileLength = new File(downLoadPath).length();
				response.setHeader("Content-disposition", "attachment; filename=" + fileName);
				response.setContentType("multipart/form-data; charset=utf-8");
				response.setHeader("Content-Length", String.valueOf(fileLength));
				bis = new BufferedInputStream(new FileInputStream(downLoadPath));
				bos = new BufferedOutputStream(response.getOutputStream());
				byte[] buff = new byte[2048];
				int bytesRead;
				while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
					bos.write(buff, 0, bytesRead);
				}
			} catch (Exception e) {
				log.error(e, "attach download fail! {}", e.getMessage());
			} finally {
				try {
					if (bis != null)
						bis.close();
					if (bos != null)
						bos.close();
				} catch (IOException e) {
					log.error(e, "attach download fail! {}", e.getMessage());
				}
			}
			return ResponseMessage.ok("");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 附件删除
	 */
	@RequestMapping(value = "/fileDelete", method = RequestMethod.GET)
	@ResponseBody
	public Object fileDelete(String formNo, Long id) {
		try {
			if (StringUtils.isNotBlank(formNo) || id != null) {
				csFileService.deleteCsFile(formNo, id);
				return ResponseMessage.ok("删除成功");
			}
			return ResponseMessage.error("请输入参数");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 附件初始展示
	 */
	@RequestMapping(value = "/selectFileByFormNo", method = RequestMethod.GET)
	@ResponseBody
	public Object selectFileByFormNo(String formNo) {
		try {
			if (StringUtils.isNotBlank(formNo)) {
				List<CsFile> list = csFileService.selectFileByFormNo(formNo);
				return ResponseMessage.ok(list);
			} else {
				return ResponseMessage.error("无工单编号");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * id查询单个附件
	 */
	@RequestMapping(value = "/selectFileById", method = RequestMethod.GET)
	@ResponseBody
	public Object selectFileById(Long id) {
		try {
			if (id != null) {
				CsFile cf = csFileService.selectFileById(id);
				return ResponseMessage.ok(cf);
			} else {
				return ResponseMessage.error("无查询条件");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

}
