package com.tahoecn.customerservice.controller;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Maps;
import com.tahoecn.customerservice.common.utils.NoteAttribute;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.dto.CsFormInstDto;
import com.tahoecn.customerservice.model.dto.FamilyListDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.vo.CompareVo;
import com.tahoecn.customerservice.service.CsCustFamilyService;
import com.tahoecn.customerservice.service.CsCustInfoService;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsProjectInfoService;
import com.tahoecn.customerservice.service.CsSyncWyService;
import com.tahoecn.customerservice.service.CsUcUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Api(tags = "报事工单 处理接口", value = "报事工单 处理接口")
@Controller
@RequestMapping("/api/csFormInst")
public class CsFormInstController extends BaseController {

	@Autowired
	CsFormInstService csFormInstService;
	@Autowired
	CsCustInfoService csCustInfoService;
	@Autowired
	CsHouseInfoService csHouseInfoService;
	@Autowired
	CsProjectInfoService csProjectInfoService;
	@Autowired
	private CsCustFamilyService csCustFamilyService;
	@Autowired
	private CsProcessWorkitemService csProcessWorkitemService;
	@Autowired
	CsUcUserService csUcUserService;
	
	@Autowired
	CsSyncWyService csSyncWyService;

	@ApiOperation(value = "接口名称", notes = "接口描述")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20") })
	@RequestMapping(value = "/info", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage info(Integer pageNum, Integer pageSize) {
		return ResponseMessage.ok(csFormInstService.selectPage(new Page<CsFormInst>(pageNum, pageSize), null));
	}
	
	@ApiOperation(value = "接口名称", notes = "接口描述")
	@ApiImplicitParams({ @ApiImplicitParam(name = "firstType", value = "一级分类", dataType = "String", required = true) })
	@RequestMapping(value = "/getFormNo", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage getFormNo(String firstType) {
		return ResponseMessage.ok(csFormInstService.getNo(firstType));
	}
	@ApiOperation(value = "微信报事-取消", notes = "根据工单id，正常关闭")
	@ApiImplicitParams({@ApiImplicitParam(name = "formInstId", value = "工单id", dataType = "String"),
		@ApiImplicitParam(name = "cancelReason", value = "取消原因", dataType = "String")})
	@RequestMapping(value = "/weChatCancel", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage weChatCancel(String formInstId,String cancelReason) {
		try{
			if(StringUtils.isBlank(formInstId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			csFormInstService.setWeChatFormCancel(formInstId,cancelReason);
			return ResponseMessage.ok("提交成功");
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【微信报事-取消】系统出错");
		}
	}
	
	@ApiOperation(value = "存储工单信息", notes = "存储工单信息")
	@RequestMapping(value = "/save", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage save(@RequestBody FamilyListDto familyListDto) {
		if(StringUtils.isBlank(familyListDto.getCsFormInst().getDeptCode()) ||
				StringUtils.isBlank(familyListDto.getCsFormInst().getProcessCode()) ||
				StringUtils.isBlank(familyListDto.getCsFormInst().getFirstSortCode())||
				StringUtils.isBlank(familyListDto.getCsFormInst().getProjectCode())
					) {
			return ResponseMessage.error("参数录入不完整，请检查参数信息");
		}

		CompareVo saveBefore = null;
		if(familyListDto.getCsFormInst().getId()!=null) {
			saveBefore = csFormInstService.getCompareField(familyListDto.getCsFormInst().getId());
		}
		// 过滤物业数据
		if("deptWY".equals(familyListDto.getCsFormInst().getDeptCode())) {
			Map<String, Object> map = Maps.newHashMap();
			map.put("project_code", familyListDto.getCsFormInst().getProjectCode());
			map.put("project_source", "1");
			if(csProjectInfoService.selectByMap(map).size() == 0) {
				return ResponseMessage.error("项目不属于物业，请检查录入信息");
			}
			if(!"1".equals(familyListDto.getCsFormInst().getPublicArea())) {
				if(familyListDto.getCsFormInst().getOwnerId() == null || "".equals(familyListDto.getCsFormInst().getOwnerId())){
					return ResponseMessage.error("业主未选择，请在筛选界面选择业主后进行报事录入！");
				}
				if(familyListDto.getCsFormInst().getHouseInfoId() == null || "".equals(familyListDto.getCsFormInst().getHouseInfoId())){
					return ResponseMessage.error("房屋未选择，请在筛选界面选择房屋后进行报事录入！");
				}
				if(!csSyncWyService.checkCust(familyListDto.getCsFormInst().getProjectCode(), familyListDto.getCsFormInst().getOwnerId())) {
					return ResponseMessage.error("业主不属于物业，请检查录入信息");
				}
				if(!csSyncWyService.checkHouse(familyListDto.getCsFormInst().getProjectCode(), familyListDto.getCsFormInst().getHouseInfoId())) {
					return ResponseMessage.error("房屋不属于物业，请检查录入信息");
				}
			}
			
		}
		
		Long custId = csFormInstService.saveOrUpate(familyListDto);
		CompareVo saveAfter = csFormInstService.getCompareField(familyListDto.getCsFormInst().getId());

		String operateType = "";
		if(familyListDto.getOperateType()==null || "".equals(familyListDto.getOperateType())){
			operateType = "draft";
		}

		String text = this.compareObj(saveBefore,saveAfter,operateType);
		if(familyListDto.getOperateType()==null || "".equals(familyListDto.getOperateType()) || "draft".equals(familyListDto.getOperateType())){
			familyListDto.setOperateType("draft");
			familyListDto.setComment("暂存");
		}else if(familyListDto.getComment()!=null && !"".equals(familyListDto.getComment())){
			//familyListDto.setComment(familyListDto.getComment());
		}else{
			familyListDto.setComment(familyListDto.getCsFormInst().getHandleRecord() + text);
		}
		CsFormInst form = familyListDto.getCsFormInst();
		if(!"".equals(text)){
			form.setChangeRecord(text);
			csFormInstService.updateById(form);
		}

		String processStateCode = familyListDto.getCsFormInst().getProcessStateCode();

		Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
		wrapper.where("form_inst_id={0}",custId).and("process_state_code={0}","draft").and("task_status={0}",new Long(20));
		CsProcessWorkitem item = csProcessWorkitemService.selectOne(wrapper);
		if(item==null && !"deptWY".equals(familyListDto.getCsFormInst().getDeptCode()) && !"processKSCL".equals(familyListDto.getCsFormInst().getProcessCode()) && !"submit".equals(familyListDto.getOperateType())) {
			csProcessWorkitemService.addWorkitem(custId, familyListDto.getComment(), familyListDto.getOperateType(),form);
		}
		return ResponseMessage.ok(custId.toString());
	}

	@ApiOperation(value = "通过ID删除工单，且删除相应的其他数据", notes = "通过ID删除工单，且删除相应的其他数据")
	@ApiImplicitParams({ @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long") })
	@RequestMapping(value = "/deleteId", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage deleteId(Long id) {
		return ResponseMessage.ok(csFormInstService.deleteById(id));
	}

	@ApiOperation(value = "报事列表-暂存列表", notes = "报事列表-暂存列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCompanyCode", value = "城市编码", dataType = "String"),
			@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
			@ApiImplicitParam(name = "buildingNoCode", value = "楼号", dataType = "String"),
			@ApiImplicitParam(name = "mobile", value = "电话", dataType = "String"),
			@ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date") })
	@RequestMapping(value = "/temporaryList", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage temporaryList(String regionCode, String cityCompanyCode, String projectCode, String buildingNoCode,
										 String mobile, String startDate, String endDate, @RequestParam(defaultValue="1")Integer pageNum, @RequestParam(defaultValue="20")Integer pageSize) {

		Map<String, Object> map = new HashMap<>();
		map.put("regionCode",regionCode);
		map.put("cityCode", cityCompanyCode);
		map.put("projectCode", projectCode);
		map.put("buildingNoCode", buildingNoCode);
		map.put("mobile", StringUtils.isBlank(mobile) ? null : mobile.trim());
		map.put("endDate",  StringUtils.isBlank(endDate) ? null : endDate);
		map.put("startDate", startDate);
		map.put("curAssigneeId", ThreadLocalUtils.getUserName());
		Page<CsFormInst> page = new Page<>(pageNum,pageSize);
		List<CsFormInst> list = csFormInstService.findTemporaryList(map, page);
		return ResponseMessage.ok(page.setRecords(list));
	}

	@ApiOperation(value = "报事列表-回访列表", notes = "报事列表-回访列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分费编码", dataType = "String"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCompanyCode", value = "城市编码", dataType = "String"),
			@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
			@ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String"),
			@ApiImplicitParam(name = "ownerName", value = "业主名称", dataType = "String"),
			@ApiImplicitParam(name = "rVStartTimeBegin", value = "报事回访开始时间从", dataType = "String"),
			@ApiImplicitParam(name = "rVStartTimeEnd", value = "报事回访开始时间至", dataType = "String"),
			@ApiImplicitParam(name = "mobile", value = "报事人移动电话", dataType = "String"),
            @ApiImplicitParam(name = "orderBy", value = "排序规则", dataType = "String")
	})
	@RequestMapping(value = "/returnVisitList", method = {RequestMethod.POST})
	@ResponseBody
	public ResponseMessage returnVisitList(@RequestParam(required=false)String formNo, @RequestParam(required=false)String regionCode, 
			@RequestParam(required=false)String cityCompanyCode, @RequestParam(required=false)String projectCode,
			@RequestParam(required=false)String firstSortCode, @RequestParam(required=false)String startDate, @RequestParam(required=false)String endDate, 
			@RequestParam(required=false)String ownerName, @RequestParam(required=false)String mobile, String rVStartTimeBegin, String rVStartTimeEnd,
			@RequestParam(defaultValue="1")Integer pageNum, @RequestParam(defaultValue="20")Integer pageSize,@RequestParam(required=false)String orderBy) {

		//查询条件
		Map<String, Object> map = new HashMap<>();
		map.put("formNo",StringUtils.isBlank(formNo) ? null : formNo.trim());
		// 新增字段
		map.put("firstSortCode", firstSortCode);
		map.put("regionCode",regionCode);
		map.put("cityCode", cityCompanyCode);
		map.put("projectCode", projectCode);
		map.put("ownerName",StringUtils.isBlank(ownerName)? null : ownerName.trim() );
		map.put("mobile", StringUtils.isBlank(mobile)? null : mobile.trim());
		map.put("processStateCode","returnVisit"); 
		map.put("startDate", startDate);
		map.put("endDate",  StringUtils.isBlank(endDate) ? null : endDate);
		map.put("rVStartTimeBegin",  StringUtils.isBlank(rVStartTimeBegin) ? null : rVStartTimeBegin);
		map.put("rVStartTimeEnd",  StringUtils.isBlank(rVStartTimeEnd) ? null : rVStartTimeEnd);
        map.put("orderBy",orderBy);
		Page<CsFormInstDto> page = new Page<>(pageNum,pageSize);
		List<CsFormInstDto> list = csFormInstService.findReturnVisitList(map, page);
		return ResponseMessage.ok(page.setRecords(list));
	}

	@ApiOperation(value = "报事列表-回访定时锁查询", notes = "报事回访列表页面跳转到详情时调用,返回status为0时,调用报事回访加锁接口,然后跳转")
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "主键", dataType = "String")})
	@RequestMapping(value = "/returnVisitLockQuery", method = {RequestMethod.POST})
	@ResponseBody
	public ResponseMessage returnVisitLockQuery(String id) {

		Map<String, Object> result = new HashMap<>();

		//查询当前工单
		CsFormInst csFormInst = csFormInstService.selectById(id);
		if (csFormInst == null) {
			result.put("status", "1");
			result.put("msg", "当前工单不存在");
			return ResponseMessage.ok(result);
		}

		//回访人
		if (StringUtils.isBlank(csFormInst.getLockUserId())) {
			result.put("status", "0");
			return ResponseMessage.ok(result);
		}
		
		String[] name = csFormInst.getLockUserId().split("_");
		if(name.length == 2) {
			if (name[1].equals(ThreadLocalUtils.getUserName())) {
				result.put("status", "0");
				return ResponseMessage.ok(result);
			}
		} else {
			result.put("status", "2");
			result.put("msg", "当前工单正在回访");
			return ResponseMessage.ok(result);
		}

		//锁定时间比较当前时间
		if (csFormInst.getLockTime() == null) {
			result.put("status", "0");
			return ResponseMessage.ok(result);
		}
		Date lockTime = csFormInst.getLockTime();
		Date currentTime = new Date();
		long abs = Math.abs((currentTime.getTime() - lockTime.getTime()) / 1000);
		if (abs > Long.parseLong("180")) {
			result.put("status", "0");
			return ResponseMessage.ok(result);
		}

		result.put("status", "2");
		result.put("msg", "当前工单 " + name[0] + "正在回访");
		return ResponseMessage.ok(result);
	}

	@ApiOperation(value = "报事列表-报事回访加锁", notes = "报事回访列表详情页面每2分钟调用本接口给报事单加锁")
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "主键", dataType = "String")})
	@RequestMapping(value = "/returnVisitLock", method = {RequestMethod.POST})
	@ResponseBody
	public ResponseMessage returnVisitLock(String id) {
		CsFormInst csFormInst = csFormInstService.selectById(id);
		csFormInst.setLockUserId(ThreadLocalUtils.getRealName()+"_"+ThreadLocalUtils.getUserName());
		csFormInst.setLockTime(new Date());
		return ResponseMessage.ok(csFormInstService.updateById(csFormInst));
	}

	@ApiOperation(value = "获取工单信息", notes = "获取工单信息")
	@RequestMapping(value = "/selectCsformInst", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage selectCsformInst(String id,String source) {
		FamilyListDto familyListDto = new FamilyListDto();
		if(StringUtils.isNotBlank(id)) {
			CsFormInst csFormInst = csFormInstService.selectById(id);
//			Map<String, Object> map = Maps.newHashMap();
//			map.put("form_inst_id", id);
			familyListDto.setCsFormInst(csFormInst);
			//familyListDto.setFormCustFamilies(csCustFamilyService.selectByMap(map));
			familyListDto.setProcessWorkitem(csProcessWorkitemService.getWorkItemList(id));
		}
        familyListDto.setSource(source);
		return ResponseMessage.ok(familyListDto);
	}

	@ApiOperation(value = "报事分配列表查询", notes = "报事分配列表查询")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
			@ApiImplicitParam(name = "processCode", value = "流程类别", dataType = "String"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "curAssigneeName", value = "当前处理人", dataType = "String"),
			@ApiImplicitParam(name = "processStateCode", value = "业务步骤", dataType = "String"),
            @ApiImplicitParam(name = "orderBy", value = "排序", dataType = "String")
	})
	@RequestMapping(value = "/getFormAssignList", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage getFormAssignList(CsFormInst csFormInst, @RequestParam(defaultValue="1")Integer pageNum, @RequestParam(defaultValue="20")Integer pageSize,String orderBy){
		Map<String, Object> map = new HashMap<>();
		try {
			map.put("formNo",StringUtils.isBlank(csFormInst.getFormNo())? null : csFormInst.getFormNo().trim());
			map.put("processCode",csFormInst.getProcessCode());
			map.put("firstSortCode",csFormInst.getFirstSortCode());
			// 默认 toBeAssigned 业务步骤为待分派的
			map.put("processStateCode",StringUtils.isBlank(csFormInst.getProcessStateCode()) ? "toBeAssigned" : csFormInst.getProcessStateCode());
			map.put("curAssigneeName", StringUtils.isBlank(csFormInst.getCurAssigneeName()) ? null : csFormInst.getCurAssigneeName().trim());
			map.put("curAssigneeId", ThreadLocalUtils.getUserName());
			map.put("orderBy", orderBy);
			return csFormInstService.selectFormAssignPageList(map,pageNum,pageSize);
		}catch (Exception e){
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	@ApiOperation(value = "报事处理列表查询", notes = "报事处理列表查询")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
			@ApiImplicitParam(name = "deptCode", value = "部门", dataType = "String"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "createUserName", value = "创建人", dataType = "String"),
			@ApiImplicitParam(name = "assignName", value = "分派人", dataType = "String"),
			@ApiImplicitParam(name = "processStateCode", value = "业务步骤", dataType = "String"),
			@ApiImplicitParam(name = "startDate", value = "分派开始时间", dataType = "String"),
			@ApiImplicitParam(name = "endDate", value = "分派结束时间", dataType = "String"),
			@ApiImplicitParam(name = "ownerName", value = "业主名称", dataType = "String"),
			@ApiImplicitParam(name = "mobile", value = "报事人移动电话", dataType = "String"),
			@ApiImplicitParam(name = "roomNo", value = "房间号", dataType = "String"),
            @ApiImplicitParam(name = "orderBy", value = "排序", dataType = "String")
	})
	@RequestMapping(value = "/getFormHandlingList", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage getFormHandlingList(CsFormInst csFormInst, String startDate, String endDate,@RequestParam(defaultValue="0")Integer pageNum, @RequestParam(defaultValue="20")Integer pageSize,String orderBy){
		Map<String, Object> map = new HashMap<>();
		try {
			map.put("formNo",StringUtils.isBlank(csFormInst.getFormNo())? null : csFormInst.getFormNo().trim());
			map.put("deptCode",csFormInst.getDeptCode());
			map.put("firstSortCode",csFormInst.getFirstSortCode());
			// 新增分派时间
			map.put("startDate", startDate);
			map.put("endDate",  StringUtils.isBlank(endDate) ? null : endDate);
			// 新增业主信息
			map.put("ownerName",StringUtils.isBlank(csFormInst.getOwnerName())? null : csFormInst.getOwnerName().trim() );
			map.put("mobile", StringUtils.isBlank(csFormInst.getMobile())? null : csFormInst.getMobile().trim());
			map.put("roomNo", StringUtils.isBlank(csFormInst.getRoomNo())? null : csFormInst.getRoomNo().trim());
			// 默认显示待处理的数据    handle  已分派
			map.put("processStateCode",StringUtils.isBlank(csFormInst.getProcessStateCode()) ? "handle" : csFormInst.getProcessStateCode() );
			map.put("curAssigneeId", ThreadLocalUtils.getUserName());
			map.put("createUserId", ThreadLocalUtils.getUserName());
			map.put("assignId", ThreadLocalUtils.getUserName());
			map.put("orderBy", orderBy);
			return csFormInstService.selectFormHandlingPageList(map,pageNum,pageSize);
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
	}

	@ApiOperation(value = "房人工单 联查接口", notes = "房人工单 联查接口")
	@RequestMapping(value = "/hcfInfo", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage hcfInfo(HcfInfoDto dto) {
		if(!dto.paramsIsNotNull()) {
			return ResponseMessage.ok(null);
		}
		dto.setCurAssigneeId(getUsername());
		return ResponseMessage.ok(csFormInstService.getByHcfInfoDto(dto));
	}

	private String compareObj(CompareVo obj1, CompareVo obj2,String operateType) {

			if(obj1==null || obj2 ==null)
				return "";

			StringBuffer text = new StringBuffer();
			try {
				Class clazz = obj1.getClass();
				Field[] fields = obj1.getClass().getDeclaredFields();
				for (Field field : fields) {
					PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
					Method getMethod = pd.getReadMethod();
					Object o1 = getMethod.invoke(obj1);
					Object o2 = getMethod.invoke(obj2);
					if((o1==null || o2==null || o1 instanceof String == false|| o2 instanceof String == false) && ("darft").equals(operateType))
						continue;
					String s1 = o1 == null ? "" : o1.toString();
					String s2 = o2 == null ? "" : o2.toString();
					if (!s1.equals(s2)) {
					    if(s1.equals("-1"))
					        s1 = "否";
					    if(s1.equals("1"))
					        s1 = "是";
                        if(s2.equals("-1"))
                            s2 = "否";
                        if(s2.equals("1"))
                            s2 = "是";

						text.append("<br>变更：" + field.getAnnotation(NoteAttribute.class).name() +"  "+  s1 + " > " +s2);
					}
				}
			} catch (Exception e) {
				System.out.println(e.getMessage());
			}
			return text.toString();
	}
	
	@ApiOperation(value = "表单正常关闭流程同步", notes = "表单正常关闭流程同步")
    @RequestMapping(value = "/updateFormProcessState", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateFormProcessState(@RequestBody Map<String, Object> param) {
		if(null != param.get("formInstId")) {
			Long formInstId = Long.valueOf(param.get("formInstId").toString());
			Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<>();
			wrapper.eq("form_inst_id", formInstId).and().eq("process_state_code", "nomalEnd");
			//查询表单流程表中是否有正常关闭的记录
			if(csProcessWorkitemService.selectCount(wrapper) > 0) {
				//更新表单表中流程状态
				Wrapper<CsFormInst> formwrapper = new EntityWrapper<>();
				formwrapper.eq("id", formInstId);
				
				CsFormInst formInst = new CsFormInst();
				formInst.setId(formInstId);
				formInst.setProcessStateCode("nomalEnd");
				formInst.setProcessStateName("正常关闭");
				csFormInstService.update(formInst, formwrapper);
			}
		}
		return ResponseMessage.okm("");
    }
}
