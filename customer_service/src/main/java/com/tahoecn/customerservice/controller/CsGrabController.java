package com.tahoecn.customerservice.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.service.CsGrabService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 抢单配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-21
 */
@Controller
@RequestMapping("/api/csGrab")
public class CsGrabController {

	@Autowired
	CsGrabService csGrabService;

	@ApiOperation(value = "项目列表", notes = "项目列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "itemCode", value = "类型编码", dataType = "String") })
	@RequestMapping(value = "/projectList", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage projectList(String itemCode) {
		if (StringUtils.isBlank(itemCode))
			return ResponseMessage.ok(Lists.newArrayList());
		return ResponseMessage.ok(csGrabService.selectProjectByCode(itemCode));
	}

	@ApiOperation(value = "删除抢单配置", notes = "删除抢单配置")
	@ApiImplicitParams({ @ApiImplicitParam(name = "itemCode", value = "类型编码", dataType = "String") })
	@RequestMapping(value = "/delectCode", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage delectCode(String itemCode) {
		if (StringUtils.isNotBlank(itemCode))
			csGrabService.deleteByCode(itemCode);
		return ResponseMessage.ok("删除成功");
	}

	@ApiOperation(value = "项目列表", notes = "项目列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "itemCode", value = "类型编码", dataType = "String"),
			@ApiImplicitParam(name = "projectCodes", value = "项目编码 ','号分割", dataType = "String") })
	@RequestMapping(value = "/insertCode", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage insertCode(String itemCode, String projectCodes) {
		if (StringUtils.isBlank(itemCode))
			return ResponseMessage.error("类型编码不能为空");
		csGrabService.insertGrab(itemCode, projectCodes);
		return ResponseMessage.ok("添加成功");
	}

}
