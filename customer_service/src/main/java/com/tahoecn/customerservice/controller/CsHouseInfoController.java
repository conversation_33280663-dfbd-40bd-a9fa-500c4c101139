package com.tahoecn.customerservice.controller;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.common.utils.ExcelUtil;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.CsSyncHouse;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;
import com.tahoecn.customerservice.model.excelDTO.DeliverDto;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsSyncHouseService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 房屋信息表
 */
@Api(tags = "房屋信息接口", value = "房屋信息接口")
@Controller
@RequestMapping("/api/csHouseInfo")
public class CsHouseInfoController extends BaseController {

	@Autowired
	private CsHouseInfoService csHouseInfoService;

	@Autowired
	private CsSyncHouseService csSyncHouseService;

	@ApiOperation(value = "房人工单 联查接口", notes = "房人工单 联查接口")
	@RequestMapping(value = "/hcfInfo", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage hcfInfo(HcfInfoDto dto) {
		if (!dto.paramsIsNotNull()) {
			return ResponseMessage.ok(null);
		}
		return ResponseMessage.ok(csHouseInfoService.getByHcfInfoDto(dto));
	}

	/**
	 * 楼栋、单元号、房间号联动查询
	 */

	@ApiOperation(value = "获取下拉条件", notes = "获取下拉条件")
	@RequestMapping(value = "/getSelect", method = RequestMethod.GET)
	@ResponseBody
	public Object getSelect() {
		Wrapper<CsHouseInfo> wrapper = new EntityWrapper<CsHouseInfo>();
		wrapper.setSqlSelect("distinct use_property");
		wrapper.isNotNull("use_property");
		List<CsHouseInfo> d = csHouseInfoService.selectList(wrapper);

		wrapper = new EntityWrapper<CsHouseInfo>();
		wrapper.setSqlSelect("distinct fitment");
		wrapper.isNotNull("fitment");
		List<CsHouseInfo> f = csHouseInfoService.selectList(wrapper);
		Map<String, List<CsHouseInfo>> map = new HashMap<String, List<CsHouseInfo>>();
		map.put("useProperty", d);
		map.put("fitment", f);

		return ResponseMessage.ok(map);

	}

	@ApiOperation(value = "楼栋、单元号、房间号联动查询", notes = "楼栋、单元号、房间号联动查询")
	@RequestMapping(value = "/selectHouseInfoRelation", method = RequestMethod.GET)
	@ResponseBody
	public Object selectHouseInfoRelation(CsHouseInfo ci) {
		try {
			if (StringUtils.isNotBlank(ci.getProject())) {
				ci.setProject(URLDecoder.decode(ci.getProject(), "UTF-8"));
			}
			if (StringUtils.isNotBlank(ci.getProjectId())) {
				ci.setProjectId(URLDecoder.decode(ci.getProjectId(), "UTF-8"));
			} else {
				return ResponseMessage.error("无查询条件");
			}
			if (StringUtils.isNotBlank(ci.getBuilding())) {
				ci.setBuilding(URLDecoder.decode(ci.getBuilding(), "UTF-8"));
			}
			if (StringUtils.isNotBlank(ci.getUnit())) {
				ci.setUnit(URLDecoder.decode(ci.getUnit(), "UTF-8"));
			}
			if (StringUtils.isNotBlank(ci.getRoomNum())) {
				ci.setRoomNum(URLDecoder.decode(ci.getRoomNum(), "UTF-8"));
			}
			List<CsHouseInfo> list = csHouseInfoService.selectHouseInfoRelation(ci);
			return ResponseMessage.ok(list);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	@ApiOperation(value = "根据房屋编码查询房屋信息", notes = "根据房屋编码查询房屋信息")
	@RequestMapping(value = "/selectHouseInfoById", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage selectHouseInfoById(String id) {
		return ResponseMessage.ok(csHouseInfoService.selectPHouse(id));
	}

	@ApiOperation(value = "房屋信息列表", notes = "房屋信息列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "custName", value = "客户姓名", dataType = "String"),
			@ApiImplicitParam(name = "building", value = "楼栋", dataType = "String"),
			@ApiImplicitParam(name = "fitment", value = "房屋装修类型（精装情况）", dataType = "String"),
			@ApiImplicitParam(name = "useProperty", value = "产品类型（使用性质）", dataType = "String"),
			/* @ApiImplicitParam(name = "signDate", value = "签约时间", dataType = "String"), */
			@ApiImplicitParam(name = "focusStartDate", value = "集中交房时间从", dataType = "Date"),
			@ApiImplicitParam(name = "focusEndDate", value = "集中交房时间到", dataType = "Date"),
			/*
			 * @ApiImplicitParam(name = "actualDeliveryDate", value = "实际交房时间", dataType =
			 * "Date"),
			 */
			/*
			 * @ApiImplicitParam(name = "obtainTime", value = "取证时间", dataType = "String"),
			 */
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCompanyCode", value = "城市公司", dataType = "String"),
			@ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "String"),
			@ApiImplicitParam(name = "deliveryStatus", value = "状态", dataType = "String"),
			@ApiImplicitParam(name = "dStartDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "dEndDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "oStartDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "oEndDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "obtainStartDate", value = "取证开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "obtainEndDate", value = "取证结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "aStartDate", value = "实际交房开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "aEndDate", value = "实际交房结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "sStartDate", value = "签约开始时间", dataType = "String"),
			@ApiImplicitParam(name = "sEndDate", value = "签约结束时间", dataType = "String") })
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage list(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "20") Integer pageSize, String custName, String building, String fitment,
			String useProperty, Date focusStartDate, Date focusEndDate, String houseNum, String houseName, String projectId,
			String cityCompanyCode, String regionCode, Date dStartDate, Date dEndDate, Date oStartDate, Date oEndDate,
			String deliveryStatus, Date obtainStartDate, Date obtainEndDate, Date aStartDate, Date aEndDate,
			String sStartDate, String sEndDate) {
		Page<CsHouseInfo> page = new Page<>(pageNum, pageSize);
		Map<String, Object> map = new HashMap<>();
		map.put("custName", custName);
		map.put("building", building);
		map.put("fitment", fitment);
		map.put("useProperty", useProperty);
		/* map.put("signDate", signDate); */
		map.put("focusStartDate", focusStartDate);
		map.put("focusEndDate", focusEndDate);
		/*
		 * map.put("actualDeliveryDate", actualDeliveryDate); map.put("obtainTime",
		 * obtainTime);
		 */
		map.put("houseNum", houseNum);
		map.put("houseName", houseName);
		map.put("projectId", projectId);
		map.put("cityCompanyCode", cityCompanyCode);
		map.put("regionCode", regionCode);
		map.put("dStartDate", dStartDate);
		map.put("dEndDate", dEndDate);
		map.put("oStartDate", oStartDate);
		map.put("oEndDate", oEndDate);
		map.put("obtainStartDate", obtainStartDate);
		map.put("obtainEndDate", obtainEndDate);
		map.put("aStartDate", aStartDate);
		map.put("aEndDate", aEndDate);
		map.put("sStartDate", sStartDate);
		map.put("sEndDate", sEndDate);
		map.put("deliveryStatus", deliveryStatus);
		List<CsHouseInfo> list = csHouseInfoService.selectListPage(map, page);
		page.setRecords(list);

		return ResponseMessage.ok(page);
	}

	/**
	 * 导出房屋信息列表 update by chenyy insert time: 2019年1月9日16:53:39
	 * 
	 * @param response
	 */
	@ApiOperation(value = "导出房屋信息列表", notes = "导出房屋信息列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "custName", value = "客户姓名", dataType = "String"),
			@ApiImplicitParam(name = "building", value = "楼栋", dataType = "String"),
			@ApiImplicitParam(name = "fitment", value = "房屋装修类型（精装情况）", dataType = "String"),
			@ApiImplicitParam(name = "useProperty", value = "产品类型（使用性质）", dataType = "String"),
			/* @ApiImplicitParam(name = "signDate", value = "签约时间", dataType = "String"), */
			@ApiImplicitParam(name = "focusStartDate", value = "集中交房时间从", dataType = "Date"),
			@ApiImplicitParam(name = "focusEndDate", value = "集中交房时间到", dataType = "Date"),
			/*
			 * @ApiImplicitParam(name = "actualDeliveryDate", value = "实际交房时间", dataType =
			 * "Date"),
			 */
			/*
			 * @ApiImplicitParam(name = "obtainTime", value = "取证时间", dataType = "String"),
			 */
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCompanyCode", value = "城市公司", dataType = "String"),
			@ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "String"),
			@ApiImplicitParam(name = "deliveryStatus", value = "状态", dataType = "String"),
			@ApiImplicitParam(name = "dStartDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "dEndDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "oStartDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "oEndDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "obtainStartDate", value = "取证开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "obtainEndDate", value = "取证结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "aStartDate", value = "实际交房开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "aEndDate", value = "实际交房结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "sStartDate", value = "签约开始时间", dataType = "String"),
			@ApiImplicitParam(name = "sEndDate", value = "签约结束时间", dataType = "String") })
	@RequestMapping(value = "/exportHouseInfo", method = RequestMethod.GET)
	public void exportITExcel(HttpServletResponse response, String custName, String building, String fitment,
			String useProperty, Date focusStartDate, Date focusEndDate, String houseNum, String houseName, String projectId,
			String cityCompanyCode, String regionCode, Date dStartDate, Date dEndDate, Date oStartDate, Date oEndDate,
			String deliveryStatus, Date obtainStartDate, Date obtainEndDate, Date aStartDate, Date aEndDate,
			String sStartDate, String sEndDate) {

		try {
			Map<String, Object> map = new HashMap<>();

			response.setHeader("content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode("客服管理系统房屋信息明细.xlsx", "utf-8"));

			// if (setUserDataPriv(map)) {
			// ExcelUtil.listToExcel(new ArrayList<ExpForm>(), "客服管理系统客户信息明细",
			// response.getOutputStream());
			// return;
			// }

			map.put("custName", custName);
			map.put("building", building);
			map.put("fitment", fitment);
			map.put("useProperty", useProperty);
			/* map.put("signDate", signDate); */
			map.put("focusStartDate", focusStartDate);
			map.put("focusEndDate", focusEndDate);
			/*
			 * map.put("actualDeliveryDate", actualDeliveryDate); map.put("obtainTime",
			 * obtainTime);
			 */
			map.put("houseNum", houseNum);
			map.put("houseName", houseName);
			map.put("projectId", projectId);
			map.put("cityCompanyCode", cityCompanyCode);
			map.put("regionCode", regionCode);
			map.put("dStartDate", dStartDate);
			map.put("dEndDate", dEndDate);
			map.put("oStartDate", oStartDate);
			map.put("oEndDate", oEndDate);
			map.put("obtainStartDate", obtainStartDate);
			map.put("obtainEndDate", obtainEndDate);
			map.put("aStartDate", aStartDate);
			map.put("aEndDate", aEndDate);
			map.put("sStartDate", sStartDate);
			map.put("sEndDate", sEndDate);
			map.put("deliveryStatus", deliveryStatus);
			/* List<CsHouseInfo> list = csHouseInfoService.selectListPage(map, null); */
			List<CsHouseInfoCustInfoFrom> list = csHouseInfoService.selectCsHouseInfoCustInfoFrom(map);

			ExcelUtil.listToExcel(list, "客服管理系统房屋信息明细", "房屋信息明细", response.getOutputStream());

			// ExcelUtil.listToExcelMoreSheet(list, "客服管理系统客户信息明细", "客户信息明细",
			// response.getOutputStream());
		} catch (Exception e) {
			System.out.println("===================================");
		}
	}

	@ApiOperation(value = "获取交付列表", notes = "获取交付列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20") })
	@RequestMapping(value = "/selectDelivery", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage selectDelivery(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "20") Integer pageSize, CsSyncHouse csSyncHouse) {
		return ResponseMessage.ok(csSyncHouseService.selectDelivery(pageNum, pageSize, csSyncHouse));
	}

	@ApiOperation(value = "变更交付状态", notes = "变更交付状态")
	@RequestMapping(value = "/updateDelivery", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage updateDelivery(String projectId, String building, Integer deliveryStatus) {
		if (StringUtils.isBlank(projectId) || StringUtils.isBlank(building) || deliveryStatus == null) {
			return ResponseMessage.error("参数异常");
		}
		csSyncHouseService.updateDelivery(projectId, building, deliveryStatus);
		return ResponseMessage.ok("修改成功");
	}

	/**
	 * 导出房屋交付信息列表
	 * 
	 * @param response
	 * @param csSyncHouse
	 */
	@ApiOperation(value = "导出房屋信息列表", notes = "导出房屋信息列表")
	@RequestMapping(value = "/expDeliver", method = RequestMethod.GET)
	public void expDeliver(HttpServletResponse response, CsSyncHouse csSyncHouse) {

		try {
			response.setHeader("content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode("客服导出资源资料.xlsx", "utf-8"));

			List<DeliverDto> list = csSyncHouseService.expDeliver(csSyncHouse);

			ExcelUtil.listToExcel(list==null?new ArrayList<DeliverDto>():list, "客服导出资源资料", "房产、业主资料", response.getOutputStream());
		} catch (Exception e) {
			System.out.println("===================================");
		}
	}
	
	@ApiOperation(value = "导出房屋信息列表-new", notes = "导出房屋信息列表-new")
	@RequestMapping(value = "/expDeliverNew", method = RequestMethod.GET)
	public void expDeliverNew(HttpServletResponse response, String projectIds, String buildings) {

		try {
			response.setHeader("content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode("客服导出资源资料.xlsx", "utf-8"));

			List<DeliverDto> list = csSyncHouseService.expDeliver(projectIds,buildings);

			ExcelUtil.listToExcel(list==null?new ArrayList<DeliverDto>():list, "客服导出资源资料", "房产、业主资料", response.getOutputStream());
		} catch (Exception e) {
			System.out.println("===================================");
		}
	}
}
