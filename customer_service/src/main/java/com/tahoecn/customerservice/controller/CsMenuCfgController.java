package com.tahoecn.customerservice.controller;


import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.dto.MenuDO;
import com.tahoecn.customerservice.model.dto.Tree;
import com.tahoecn.customerservice.service.CsMenuCfgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 菜单URL配置表
 */
@Controller
@RequestMapping("/api/csMenuCfg")
public class CsMenuCfgController extends BaseController {

    @Autowired
    private CsMenuCfgService csMenuCfgService;

    /**
     * 返回当前用户菜单树结构
     */
    @RequestMapping(value = "/menuTree", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage menuTree() {
        try {
            List<Tree<MenuDO>> trees = csMenuCfgService.listMenuTreeByUser();
            return ResponseMessage.ok(trees);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseMessage.error("系统错误，请联系管理员");
    }

}

