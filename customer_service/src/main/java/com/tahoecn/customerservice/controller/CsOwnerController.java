package com.tahoecn.customerservice.controller;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsCustFamily;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.dto.OwnerFormDto;
import com.tahoecn.customerservice.service.CsCustFamilyService;
import com.tahoecn.customerservice.service.CsCustInfoService;
import com.tahoecn.customerservice.service.CsFileService;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.ProcessService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "业主接口",value="业主接口")
@Controller
@RequestMapping("/weChatApi/csOwner")
public class CsOwnerController extends BaseController{

	@Autowired
	private CsFormInstService csFormInstService;
	@Autowired
	private CsCustInfoService csCustInfoService;
	@Autowired
	private CsHouseInfoService csHouseInfoService;
	@Autowired
	private CsProcessWorkitemService csProcessWorkitemService;
	@Autowired
	private CsCustFamilyService csCustFamilyService;
	@Autowired
	CsUcUserService csUcUserService;
	@Autowired
	private CsFileService csFileService;

    @Resource(name = "sComplaintService")
    ProcessService sComplaintService;

    @Resource(name = "sRepairService")
    ProcessService sRepairService;

    @Resource(name = "processService")
    ProcessService processService;
	
	@ApiOperation(value = "业主认证", notes = "业主认证手机、身份证号，并关联微信号")
	@ApiImplicitParams({ @ApiImplicitParam(name = "certificateNum", value = "身份证号", dataType = "String"),
			@ApiImplicitParam(name = "telephone", value = "移动电话", dataType = "String"),
			@ApiImplicitParam(name = "weChat", value = "微信号", dataType = "String"),	
			@ApiImplicitParam(name = "openId", value = "openId", dataType = "String")})
	@RequestMapping(value = "/certification", method = { RequestMethod.GET})
	@ResponseBody
	public ResponseMessage certification(String certificateNum, String telephone, String weChat,String openId) {
		if(StringUtils.isBlank(certificateNum) ||
				StringUtils.isBlank(telephone) ||
//				StringUtils.isBlank(weChat) ||
				StringUtils.isBlank(openId)){
			return ResponseMessage.error("参数录入不完整，请检查参数信息");
		}
		try{
			//1.验证业主是否存在,需验证业主和家庭成员是否都不存在
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("certificateNum", certificateNum);
			map.put("telephone", telephone);
			List<CsCustInfo> csCustInfo = csCustInfoService.certification(map);
			List<CsCustFamily> csCustFamily = csCustFamilyService.certification(map);
			//2.不存在，返回验证失败信息
			if((csCustInfo == null || csCustInfo.size() <= 0)
					&& (csCustFamily == null || csCustFamily.size() <= 0)){
				return ResponseMessage.error("验证失败");
			}else{//存在，关联微信
				Map<String,Object> custMap = new HashMap<String,Object>(); 
				String custIds = "";
				String custFamilyIds = "";
				//业主
				if(csCustInfo != null && csCustInfo.size() != 0 ){
					custIds += csCustInfoService.updateWeChat(csCustInfo, weChat,openId);
				}
				//家庭成员不回写openid
				if(csCustFamily != null && csCustFamily.size() != 0 ){
					custFamilyIds += csCustFamilyService.updateWeChat(csCustFamily, weChat,openId);
//					for(HashMap<String,Object> mapcust : custFamilyIds ){
//						List<CsCustInfo> listcust = csCustInfoService.selectByCustId(mapcust.get("custId").toString());
//						if(listcust.size()>0){
//							mapcust.put("custName", listcust.get(0).getCustName());
//							custFamilyIdsresult.add(mapcust);
//						}
//					}
				}
				/*//去重
				String[] custId= custIds.split(",");
				Set<String> set=new HashSet<String>(Arrays.asList(custId));
				custId = set.toArray(new String[set.size()]);
				
				Map<String, Object> custIDMap = new HashMap<String, Object>();
		        for (String str : custId) {
		        	custIDMap.put(str, str);
		        }
		        //返回一个包含所有对象的指定类型的数组
		        String[] newArrStr =  custIDMap.keySet().toArray(new String[1]);
		        System.out.println(Arrays.toString(newArrStr));
				
				return ResponseMessage.ok(Arrays.toString(newArrStr));*/
				custMap.put("custId", custIds);
				custMap.put("custFamilyIds", custFamilyIds);
				return ResponseMessage.ok(custMap);
			}
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【业主认证】系统出错");
		}
	}
	
	@ApiOperation(value = "业主房产信息", notes = "根据业主id查询所有的房产信息")
	@ApiImplicitParams({ @ApiImplicitParam(name = "custId", value = "业主id", dataType = "String")})
	@RequestMapping(value = "/custHouse", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage custHouse(String custId) {
		if(StringUtils.isBlank(custId)){
			return ResponseMessage.error("参数录入不完整，请检查参数信息");
		}
		try{
			return ResponseMessage.ok(csHouseInfoService.selectCustHouseByCustId(custId));
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【查询业主房产信息】系统出错");
		}
	}
	
	@ApiOperation(value = "业主报事接口", notes = "保存业主报事记录信息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userId", value = "", dataType = "String"),
		@ApiImplicitParam(name = "customerDemand", value = "客户需求", dataType = "String"),
		@ApiImplicitParam(name = "houseName", value = "房屋名称", dataType = "String"),
		@ApiImplicitParam(name = "houseNum", value = "房屋编号", dataType = "String"),
		@ApiImplicitParam(name = "mobile", value = "电话", dataType = "String"),
		@ApiImplicitParam(name = "ownerName", value = "业主名称", dataType = "String"),
		@ApiImplicitParam(name = "project", value = "项目", dataType = "String"),
		@ApiImplicitParam(name = "projectId", value = "项目id", dataType = "String"),
		@ApiImplicitParam(name = "openId", value = "openid", dataType = "String")
	})
	@RequestMapping(value = "/ownerSave", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage ownerSave(String userId,String customerDemand,
			String houseName,String houseNum,String mobile,String ownerName,String project,
			String projectId, String pictureUrl,String openId) {
		try{
			if(StringUtils.isBlank(houseName) || StringUtils.isBlank(houseNum)
					|| StringUtils.isBlank(project)
					|| StringUtils.isBlank(projectId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("pictureUrl", pictureUrl);
			map.put("userId", userId);
			map.put("customerDemand", customerDemand);
			map.put("houseName", houseName);
			map.put("houseNum", houseNum);
			map.put("mobile", mobile);
			map.put("ownerName", ownerName);
			map.put("project", project);
			map.put("projectId", projectId);
			map.put("openId", openId);
			//1.生成报事信息
			CsFormInst csformInst = csFormInstService.saveFormInst(map);
			//2.生成待办信息【业主报事+报事录入，两条待办】
			csProcessWorkitemService.setOwnerProcess(csformInst.getId(), "提交","",csformInst);
			//3.关联图片信息
			if (pictureUrl != null && !"".equals(pictureUrl)) {
				String[] picUrl = pictureUrl.split(",");
				List<String> urls = new ArrayList<>();
				for (String url : picUrl) {
					if (StringUtils.isNotBlank(url)) {
						urls.add(url);
					}
				}
				csFileService.saveFile(csformInst.getId(), urls);
			}
			return ResponseMessage.ok(csformInst.getId());
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【业主报事接口】系统出错");
		}
	}
	
	@ApiOperation(value = "微信报事回访", notes = "微信报事回访")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "formId", value = "", dataType = "String"),
		@ApiImplicitParam(name = "sNum", value = "sNum", dataType = "String"),
		@ApiImplicitParam(name = "openId", value = "openid", dataType = "String"),
		@ApiImplicitParam(name = "name", value = "name", dataType = "String")
	})
	@RequestMapping(value = "/satisfaction", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage satisfaction(String formId, String sNum, String openId, String name) {
		try{
			Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
	        formWrapper.where("id={0} and open_id={1}",formId,openId);
	        CsFormInst form = csFormInstService.selectOne(formWrapper);
	        if( form == null) {
	        	return ResponseMessage.error("数据异常");
	        }
	        Map<String, String> map = new HashMap<>();
	        map.put("5","5分 非常满意");
	        map.put("4","4分 比较满意");
	        map.put("3","3分 一般");
	        map.put("2","2分 不满意");
	        map.put("1","1分 非常不满意");
	        
	        form.setSatisfactionCode(sNum);
	        form.setSatisfactionName(map.get(sNum));
			csFormInstService.updateById(form);

	        String firstSortCode = form.getFirstSortCode();
	        String deptCode = form.getDeptCode();
	        String processStateCode = form.getProcessStateCode();
	        String handleRecord = form.getHandleRecord();
			
			ProcessService processService = this.getService(form.getDeptCode(),form.getProcessCode(),form.getFirstSortCode());
	        processService.process(form,formId,firstSortCode,deptCode,processStateCode,handleRecord,null,form.getOpenId(),"业主：" + name,null,"submit");
	        
			return ResponseMessage.ok("");
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【业主报事接口】系统出错");
		}
	}
	
	public ProcessService getService(String deptCode, String processCode,String firstsortCode){
    	if (processCode.equals("processBZ")) {
        	if (firstsortCode.equals("coTS")) {//投诉
                return this.sComplaintService;
            } else if (firstsortCode.equals("coBX")) {//报修
                return this.sRepairService;
            }
        }
        return this.processService;
    }
	
	
	@ApiOperation(value = "项目列表", notes = "根据业主id查询所有的房产对应的项目列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "custId", value = "业主id", dataType = "String")})
	@RequestMapping(value = "/custHouseProject", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage custHouseProject(String custId) {
		if(StringUtils.isBlank(custId)){
			return ResponseMessage.error("参数录入不完整，请检查参数信息");
		}
		try{
			return ResponseMessage.ok(csHouseInfoService.selectCustHouseProjectByCustId(custId));
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【查询业主房产项目信息】系统出错");
		}
	}
	
	@ApiOperation(value = "区域列表", notes = "根据业主id查询所有的房产对应的项目的区域列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "custId", value = "业主id", dataType = "String")})
	@RequestMapping(value = "/custHouseRegion", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage custHouseRegion(String custId) {
		if(StringUtils.isBlank(custId)){
			return ResponseMessage.error("参数录入不完整，请检查参数信息");
		}
		try{
			return ResponseMessage.ok(csHouseInfoService.selectCustHouseRegionByCustId(custId));
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【查询业主房产区域信息】系统出错");
		}
	}
	
	@ApiOperation(value = "业主报事列表", notes = "根据业主id查询所有的房产对应的项目的区域列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
		@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
		@ApiImplicitParam(name = "regionCode", value = "区域编码", dataType = "String"),
		@ApiImplicitParam(name = "cityCode", value = "城市编码", dataType = "String"),
		@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
		@ApiImplicitParam(name = "mobile", value = "移动电话", dataType = "String"),
		@ApiImplicitParam(name = "creationStartDate", value = "报事提交开始时间", dataType = "Date"),
		@ApiImplicitParam(name = "creationEndDate", value = "报事提交结束时间", dataType = "Date"),
		@ApiImplicitParam(name = "orderBy", value = "排序规则", dataType = "String")
		})
	@RequestMapping(value = "/ownerList", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage ownerList(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "20") Integer pageSize,
			String regionCode, String cityCode, String projectCode,
			String mobile, String creationStartDate, String creationEndDate,@RequestParam(defaultValue = "DESC") String orderBy) {
		try{
			Map<String, Object> map = new HashMap<>();
			Page<OwnerFormDto> page = new Page<OwnerFormDto>(pageNum, pageSize);
			//查询条件
			map.put("regionCode", regionCode);
			map.put("cityCode", cityCode);
			map.put("projectCode", projectCode);
			map.put("mobile", mobile);
			map.put("creationStartDate", StringUtils.isBlank(creationStartDate) ? null : creationStartDate);
			map.put("creationEndDate", StringUtils.isBlank(creationEndDate) ? null : creationEndDate);
			map.put("orderBy",orderBy);
			List<OwnerFormDto> list = csFormInstService.getOwnerForm(page, map);
			page.setRecords(list);
			return ResponseMessage.ok(page);
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【查询业主报事列表】系统出错");
		}
	}
	
	@ApiOperation(value = "微信报事-取消", notes = "根据工单id，正常关闭")
	@ApiImplicitParams({@ApiImplicitParam(name = "formInstId", value = "工单id", dataType = "String"),
		@ApiImplicitParam(name = "cancelReason", value = "取消原因", dataType = "String")})
	@RequestMapping(value = "/weChatCancel", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage weChatCancel(String formInstId, String cancelReason) {
		try{
			if(StringUtils.isBlank(formInstId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			csFormInstService.setWeChatFormCancel(formInstId,cancelReason);
			return ResponseMessage.ok("提交成功");
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【微信报事-取消】系统出错");
		}
	}
	
	@ApiOperation(value = "报事进度查询", notes = "根据用户id，查询所有的报事列表")
	@ApiImplicitParams({@ApiImplicitParam(name = "custId", value = "用户ID", dataType = "String"),
		@ApiImplicitParam(name = "openId", value = "openID", dataType = "String")})
	@RequestMapping(value = "/weChatFormList", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage weChatFormList(String custId,String openId) {
		try{
			if(StringUtils.isBlank(custId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			Map<String,String> map = new HashMap<String,String>();
			map.put("custId", custId);
			map.put("openId", openId);
			return ResponseMessage.ok(csFormInstService.getWeChatFormList(map));
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【微信公众号报事进度查询】系统出错");
		}
	}
	
	@ApiOperation(value = "报事进度详情", notes = "根据报事id，查询报事详情")
	@ApiImplicitParams({@ApiImplicitParam(name = "custId", value = "业主id", dataType = "String"),
		@ApiImplicitParam(name = "formInstId", value = "工单id", dataType = "String"),
		@ApiImplicitParam(name = "openId", value = "openid", dataType = "String")})
	@RequestMapping(value = "/weChatFormDetails", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage weChatFormDetails(String custId, String formInstId,String openId) {
		try{
			if(StringUtils.isBlank(formInstId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			Map<String,String> map = new HashMap<String,String>();
			map.put("custId", custId);
			map.put("formInstId", formInstId);
			map.put("openId", openId);
			return ResponseMessage.ok(csFormInstService.setWeChatFormDetails(map));
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("【微信公众号报事进度详情】系统出错");
		}
	}

    /**
     * 时间转换
     */
    public Date getDate(String date) {
        if (date == null) {
            date = "";
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(date, pos);
        return strtodate;
    }
}