package com.tahoecn.customerservice.controller;


import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * 待办表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Controller
@RequestMapping("/api/csProcessWorkitem")
public class CsProcessWorkitemController {

    @Autowired
    CsProcessWorkitemService csProcessWorkitemService;

    @RequestMapping(value = "/getProgressList", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getProgressList(String formInstId){
        List list =  csProcessWorkitemService.getProgressWorkItemList(formInstId);
        return ResponseMessage.ok(list);
    }
}

