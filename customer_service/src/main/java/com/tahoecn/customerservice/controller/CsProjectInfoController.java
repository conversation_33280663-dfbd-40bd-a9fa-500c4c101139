package com.tahoecn.customerservice.controller;

import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.dto.DataDto;
import com.tahoecn.customerservice.model.dto.MatterStatisticalDto;
import com.tahoecn.customerservice.model.dto.MatterStatisticalParentDto;
import com.tahoecn.customerservice.model.dto.StatisticalDto;
import com.tahoecn.customerservice.model.vo.StatisticalVo;
import com.tahoecn.customerservice.service.CsProjectInfoService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 初始化项目信息表
 */
@Controller
@RequestMapping("/api/csProjectInfo")
public class CsProjectInfoController extends BaseController {


    @Autowired
    private CsProjectInfoService csProjectInfoService;

    @Value("${max_give_score}")
    private int maxScore;

    /**
     * 项目信息表(区域、城市、项目)
     */
    @ApiOperation(value = "项目基础信息", notes = "区域、城市、项目联查接口")
    @RequestMapping(value = "/selectAll",method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage selectAll(){
        return ResponseMessage.ok(csProjectInfoService.selectList(null));
    }




    /**
     * 项目信息表(区域、城市、项目)
     */
    @ApiOperation(value = "区域、城市、项目联查接口", notes = "区域、城市、项目联查接口")
    @RequestMapping(value = "/selectProjectInfoRelation",method = RequestMethod.GET)
    @ResponseBody
    public Object selectProjectInfoRelation(CsProjectInfo pi){
        try {
            if(StringUtils.isNotBlank(pi.getRegionCode())){
                pi.setRegionCode(URLDecoder.decode(pi.getRegionCode(), "UTF-8"));
            }
            if(StringUtils.isNotBlank(pi.getRegion())){
                pi.setRegion(URLDecoder.decode(pi.getRegion(), "UTF-8"));
            }
            if(StringUtils.isNotBlank(pi.getCityCompanyCode())){
                pi.setCityCompanyCode(URLDecoder.decode(pi.getCityCompanyCode(), "UTF-8"));
            }
            if(StringUtils.isNotBlank(pi.getCityCompany())){
                pi.setCityCompany(URLDecoder.decode(pi.getCityCompany(), "UTF-8"));
            }
            if(StringUtils.isNotBlank(pi.getCityCode())){
                pi.setCityCode(URLDecoder.decode(pi.getCityCode(), "UTF-8"));
            }
            if(StringUtils.isNotBlank(pi.getCity())){
                pi.setCity(URLDecoder.decode(pi.getCity(), "UTF-8"));
            }
            if(StringUtils.isNotBlank(pi.getProjectCode())){
                pi.setProjectCode(URLDecoder.decode(pi.getProjectCode(), "UTF-8"));
            }
            List<CsProjectInfo> list = csProjectInfoService.selectProjectInfoRelation(pi);
            List<CsProjectInfo> newList = new ArrayList<>();
            //查询所有区域
            if((pi.getRegionCode()==null||pi.getRegionCode()=="")&&
                    (pi.getRegion()==null||pi.getRegion()=="")){
                Set<String> setCode = new LinkedHashSet<>();
                Set<String> setValue = new LinkedHashSet<>();
                for(CsProjectInfo info : list){
                    String code = info.getRegionCode();
                    String value = info.getRegion();
                    setCode.add(code);
                    setValue.add(value);
                }
                for(String code : setCode){
                    CsProjectInfo info = new CsProjectInfo();
                    info.setRegionCode(code);
                    newList.add(info);
                }
                int i = 0;
                for (String value : setValue){
                    newList.get(i).setRegion(value);
                    i++;
                }
                return ResponseMessage.ok(newList);
            }
            //查询所有城市公司
            if((pi.getCityCompanyCode()==null||pi.getCityCompanyCode()=="")
                    &&(pi.getCityCompany()==null||pi.getCityCompany()=="")){
                Set<String> setCode = new LinkedHashSet<>();
                Set<String> setValue = new LinkedHashSet<>();
                for(CsProjectInfo info : list){
                    String code = info.getCityCompanyCode();
                    String value = info.getCityCompany();
                    setCode.add(code);
                    setValue.add(value);
                }
                for(String code : setCode){
                    CsProjectInfo info = new CsProjectInfo();
                    info.setCityCompanyCode(code);
                    newList.add(info);
                }
                int i = 0;
                for (String value : setValue){
                    newList.get(i).setCityCompany(value);
                    i++;
                }
                return ResponseMessage.ok(newList);
            }
            //查询所有项目
            return ResponseMessage.ok(list);
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseMessage.error("系统错误，请联系管理员");
    }

    @RequestMapping(value = "/cascadingList", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage cascadingList(CsProjectInfo pi) {
        Wrapper<CsProjectInfo> wrapper = null;
        if(StringUtils.isNotBlank(pi.getCityCode())) {
            wrapper = new EntityWrapper<CsProjectInfo>(pi, "project_code,project");
            wrapper.eq("city_code", pi.getCityCode());
        } else if(StringUtils.isNotBlank(pi.getRegionCode())) {
            wrapper = new EntityWrapper<CsProjectInfo>(pi, "city_code,city");
            wrapper.eq("region_code", pi.getRegionCode());
        } else {
            wrapper = new EntityWrapper<CsProjectInfo>(pi, "region_cod,region");
        }
        return ResponseMessage.ok(csProjectInfoService.selectList(wrapper));
    }




    @RequestMapping(value = "/allMasterSlaveList", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "首页—区域/城市/项目报事", notes = "首页—区域/城市/项目报事")//总量、完成个数、正在处理、。。。。升级率
    @ResponseBody   //
    public ResponseMessage allMasterSlaveList(
            @RequestParam(value="condition",required=false)String condition,
           /* @RequestParam(value="type",required=false)String type,*/
            @RequestParam(value="organName",required=false)String organName,
            @RequestParam(value="cityName",required=false)String cityName,
            @RequestParam(value="startDate")String startDate,
            @RequestParam(value="endDate")String endDate
    ){
    	String limitTimeStart=startDate;
    	String limitTimeEnd=endDate;
    	/*String format="yyyy-MM-DD";
    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
    	if(!StringUtils.isBlank(endDate) && endDate !=null){
    		limitTimeEnd = simpleDateFormat.format(endDate);
    	}
    	if(!StringUtils.isBlank(startDate) && startDate !=null){
    		limitTimeStart = simpleDateFormat.format(startDate);
    	}*/
    	/*if(!"1".equals(type)){
    	String format="yyyy";
        Date date = new Date();
        if("3".equals(type)){
        	format+="-MM";	
        }
    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
    	limitTime=simpleDateFormat.format(date);
    	} */  	
//        try {
//            if(!StringUtils.isEmpty(year)){//年份条件是获取当年区域报事信息
//                Integer.parseInt(year);
//                List<Object> data = csProjectInfoService.getData(year);
//                return ResponseMessage.ok(data);
//            }
//        } catch (Exception e) {
//            // TODO: handle exception
//            return ResponseMessage.error("错误的年限");
//        }
        List<MatterStatisticalParentDto> recursiveGet=null;
        if(!StringUtils.isEmpty(condition)){
            if("consult".equals(condition)){condition="coZX";}
            else if("complain".equals(condition)){condition="coTS";}
            else if("repair".equals(condition))condition="coBX";
        }
        //null null null 查区域   XXX,null,null查城市，XXX XXX ，null查项目
        String[] indexName=new String[] {organName,cityName,null};

        int i= StringUtils.isNotBlank(cityName)?2:StringUtils.isNotBlank(organName)?1:0;
        try {
            recursiveGet = csProjectInfoService.recursiveGet(indexName,i, condition,limitTimeStart,limitTimeEnd);

        } catch (Exception e) {
            e.printStackTrace();
            // TODO: handle exception
            return ResponseMessage.error("错误信息录入或系统异常");
        }
        return ResponseMessage.ok(recursiveGet);
    }
    
    @RequestMapping(value = "/thridPicture", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "首页第三个图形数据", notes = "首页—区域/城市/项目报事")//总量、完成个数、正在处理、。。。。升级率
    @ResponseBody  
    public ResponseMessage thridPicture(
            /*@RequestParam(value="type",required=false)String type ,*/
            @RequestParam(value="startDate")String startDate,
            @RequestParam(value="endDate")String endDate   		
    		){
    	String limitTimeStart=startDate;
    	String limitTimeEnd=endDate;
/*    	String format="yyyy-MM-DD";
    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);*/
/*    	if(!StringUtils.isBlank(endDate) && endDate !=null){
    		limitTimeEnd = simpleDateFormat.format(endDate);
    	}
    	if(!StringUtils.isBlank(startDate) && startDate !=null){
    		limitTimeStart = simpleDateFormat.format(startDate);
    	}*/
    	/*if(!"1".equals(type)){
    	String format="yyyy";
        Date date = new Date();
        if("3".equals(type)){
        	format+="-MM";	
        }
    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
    	limitTime=simpleDateFormat.format(date);
    	} */
      try {
    	  List<Object> data = csProjectInfoService.getData(limitTimeStart,limitTimeEnd);
          return ResponseMessage.ok(data);
	} catch (Exception e) {
         return ResponseMessage.ok("查询失败");

	}
     
    }

    @RequestMapping(value = "/firstPageInfo", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "首页图形展示", notes = "首页图形展示")
    @ResponseBody
    public ResponseMessage firstPageInfo(
            /*@RequestParam(value="type")String type,*/
            @RequestParam(value="startDate")String startDate,
            @RequestParam(value="endDate")String endDate
    ){
    	String limitTimeStart=startDate;
    	String limitTimeEnd=endDate;
    	/*String format="yyyy-MM-DD";
    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
    	if(!StringUtils.isBlank(endDate) && endDate !=null){
    		limitTimeEnd = simpleDateFormat.format(endDate);
    	}
    	if(!StringUtils.isBlank(startDate) && startDate !=null){
    		limitTimeStart = simpleDateFormat.format(startDate);
    	}*/
    	/*if(!"1".equals(type)){
    	String format="yyyy";
        Date date = new Date();
        if("3".equals(type)){
        	format+="-MM";	
        }
    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
    	limitTime=simpleDateFormat.format(date);
    	} */
        int score=0;
        double sum=0.0,count=0.0,rate=0;
        List<Integer> conditionsNum = csProjectInfoService.getConditionsNum(limitTimeStart,limitTimeEnd);
        List<StatisticalVo> statisticalDtos = csProjectInfoService.satisfaction(limitTimeStart,limitTimeEnd);
        System.err.println(statisticalDtos.size());
        if(statisticalDtos.size()>0&&statisticalDtos.get(0)!=null){
            for (StatisticalVo statisticalDto : statisticalDtos) {
                if(statisticalDto.getStatisticalDo()==null)score=0;
                else score=Integer.parseInt(statisticalDto.getStatisticalDo());
                sum+=statisticalDto.getNum()*score;
                count+=statisticalDto.getNum();
            }
            rate=	sum/count*100/maxScore;
        }
        DataDto dataDto=new DataDto();
        dataDto.setRepair(conditionsNum.get(0));
        dataDto.setConsult(conditionsNum.get(1));
        dataDto.setAdviceAndpraise(conditionsNum.get(2));
        dataDto.setComplaints(conditionsNum.get(3));
        dataDto.setFinshNum(csProjectInfoService.getCountNum(limitTimeStart,limitTimeEnd,2));//2,3,4分别对应完成条件，正在做条件和升级条件
        dataDto.setDoingNum(csProjectInfoService.getCountNum(limitTimeStart,limitTimeEnd,3));
        dataDto.setSatisfaction(String.format("%.1f", rate));
        dataDto.setLevelUpNum(csProjectInfoService.getCountNum(limitTimeStart,limitTimeEnd,4));
        return ResponseMessage.ok(dataDto);

    }
//	@RequestMapping(value = "/stepGetData", method = {RequestMethod.POST, RequestMethod.GET})
//	  @ApiOperation(value = "分批获取首页数据", notes = "分批获取首页数据")
//	  @ResponseBody
//		public ResponseMessage stepGetData(
//				@RequestParam(value="region",required=false)String region,
//				@RequestParam(value="city",required=false)String city,
//				@RequestParam(value="condition",required=false)String condition,
//				@RequestParam(value="year",required=false)String year
//				){
//		List<Object> data = csProjectInfoService.getData(region,city,condition,year);
//		return ResponseMessage.ok(data);
//
//	}
    
    @ApiOperation(value = "查询项目信息", notes = "查询项目信息")
	@ApiImplicitParams({ @ApiImplicitParam(name = "code", value = "项目code", required = true, dataType = "String"),
						 @ApiImplicitParam(name = "codeType", value = "code类型", required = true, dataType = "String") })
	@RequestMapping(value = "/getProDetailByCode", method = { RequestMethod.GET })
	@ResponseBody
    public ResponseMessage getOrgDetailByCode(String code, String codeType) {
    	if(StringUtils.isEmpty(code) || StringUtils.isEmpty(codeType)) {
    		return ResponseMessage.error("Fail");
    	}
    	Map<String, Object> resultMap= csProjectInfoService.getProDetailByCode(code,codeType);
    	return ResponseMessage.ok(resultMap);
    }
}
