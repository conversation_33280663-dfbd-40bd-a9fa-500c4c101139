package com.tahoecn.customerservice.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.service.CsSendSmsLogService;

import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * <p>
 * 期初发送短信通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-29
 */
@Controller
@RequestMapping("/api/csSendtoall")
public class CsSendtoallController {
	@Autowired
	private CsSendSmsLogService csSendSmsLogService;
	/*@ApiOperation(value = "期初短信接口", notes = "上线通知给业主发短信")
    @RequestMapping(value = "/sendToAll", method = {RequestMethod.GET })
	@ResponseBody
    public ResponseMessage sendToAll() {
		try {
			csSendSmsLogService.sendToAll();
			return ResponseMessage.okm("发送成功");
		} catch (Exception e) {
			return ResponseMessage.error("系统错误，请联系管理员");
		}
       
    }*/
}

