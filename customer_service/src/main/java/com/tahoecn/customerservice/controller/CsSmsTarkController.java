package com.tahoecn.customerservice.controller;

import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsSmsTark;
import com.tahoecn.customerservice.schedule.SmsSendTask;
import com.tahoecn.customerservice.service.CsSmsTarkService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 短信推送任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-17
 */
@Api(tags = "短信任务接口", value = "短信任务接口")
@Controller
@RequestMapping("/api/csSmsTark")
public class CsSmsTarkController {

	@Autowired
	CsSmsTarkService csSmsTarkService;
	@Autowired
	SmsSendTask smsSendTask;

	@ApiOperation(value = "分页列表", notes = "分页列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "当前页", required = true, dataType = "Integer"),
			@ApiImplicitParam(name = "pageSize", value = "每页显示条数", required = true, dataType = "Integer") })
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage list(Integer pageNum, Integer pageSize) {
		Page<CsSmsTark> page = new Page<CsSmsTark>(pageNum, pageSize);
		// EntityWrapper<CsSmsTark> wrapper = new EntityWrapper<CsSmsTark>();
		return ResponseMessage.ok(csSmsTarkService.selectPage(page));
	}

	@ApiOperation(value = "保存", notes = "保存")
	@ApiImplicitParams({ @ApiImplicitParam(name = "tel", value = "电话", required = true, dataType = "runtime"),
			@ApiImplicitParam(name = "msg", value = "消息", required = true, dataType = "String"),
			@ApiImplicitParam(name = "runtime", value = "执行时间", dataType = "Date") })
	@RequestMapping(value = "/save", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage save(String tel, String msg, Date runtime) {
		if(StringUtils.isBlank(tel) || StringUtils.isBlank(msg)) {
			return ResponseMessage.error("参数异常！");
		}
		CsSmsTark csSmsTark = new CsSmsTark();
		csSmsTark.setTel(tel);
		csSmsTark.setMsg(msg);
		if(runtime == null) {
			csSmsTark.setType("实时");
			csSmsTark.setRuntime(new Date());
		}else {
			csSmsTark.setType("定时");
			csSmsTark.setRuntime(runtime);
		}
		csSmsTark.setStatus("待发送");
		csSmsTark.setCreateUserName(ThreadLocalUtils.getUserName());
		csSmsTark.setCreateRealName(ThreadLocalUtils.getRealName());
		csSmsTark.setCreationDate(new Date());
		csSmsTarkService.insert(csSmsTark);
		if(runtime == null) {
			smsSendTask.runSend(csSmsTark);
		}
		return ResponseMessage.ok(csSmsTark.getId());
	}
	
	@ApiOperation(value = "删除", notes = "删除")
	@ApiImplicitParams({ @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String") })
	@RequestMapping(value = "/delete", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage delete(String id) {
		return ResponseMessage.ok(csSmsTarkService.deleteById(Long.valueOf(id)));
	}

}
