package com.tahoecn.customerservice.controller;


import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsSupplierInfo;
import com.tahoecn.customerservice.service.CsSupplierInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.net.URLDecoder;
import java.util.List;

/**
 * <p>
 * 供方数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-18
 */
@Controller
@RequestMapping("/api/csSupplierInfo")
public class CsSupplierInfoController {

    @Autowired
    private CsSupplierInfoService csSupplierInfoService;

    /**
     * 根据区域查询责任单位
     */
    @RequestMapping(value = "/selectNameByArea",method = RequestMethod.GET)
    @ResponseBody
    public Object selectNameByArea(CsSupplierInfo si){
        try {
            if(StringUtils.isNotBlank(si.getSupplierAreas())){
                si.setSupplierAreas(URLDecoder.decode(si.getSupplierAreas(), "UTF-8"));
            }
            List<CsSupplierInfo> list = csSupplierInfoService.selectNameByArea(si);
            return ResponseMessage.ok(list);
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseMessage.error("系统错误，请联系管理员");
    }

}

