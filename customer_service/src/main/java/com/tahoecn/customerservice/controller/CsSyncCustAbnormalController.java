package com.tahoecn.customerservice.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsSyncCustAbnormal;
import com.tahoecn.customerservice.service.CsSyncCustAbnormalService;
import com.tahoecn.customerservice.service.CsSyncCustService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 客户信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-15
 */
@Controller
@RequestMapping("/api/csSyncCustAbnormal")
public class CsSyncCustAbnormalController {

	@Autowired
	private CsSyncCustAbnormalService abnormalService;
	@Autowired
	private CsSyncCustService csSyncCustService;

	@ApiOperation(value = "查询异常列表", notes = "异常人员列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20") })
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage list(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "20") Integer pageSize, CsSyncCustAbnormal abnormal) {

		return ResponseMessage.ok(abnormalService.selectDtoList(pageSize, pageNum, abnormal));
	}

	@ApiOperation(value = "查询异常详情", notes = "异常人员详情")
	@RequestMapping(value = "/get", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage get(String id) {
		return ResponseMessage.ok(abnormalService.selectById(id));
	}

	@ApiOperation(value = "获取其他相关信息", notes = "获取其他相关信息")
	@RequestMapping(value = "/otherCode", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage otherCode(String id, String certificateNum) {
		return ResponseMessage.ok(abnormalService.abnormal(id, certificateNum));
	}

	@ApiOperation(value = "保留人员信息", notes = "保留人员信息")
	@RequestMapping(value = "/retain", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage retain(String id, String certificateNum) {
		csSyncCustService.retainCustById(id);
		abnormalService.removeByCode(certificateNum);
		return ResponseMessage.ok("处理成功");
	}

}
