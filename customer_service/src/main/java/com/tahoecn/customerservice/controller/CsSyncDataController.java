//package com.tahoecn.customerservice.controller;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import com.tahoecn.customerservice.common.web.ResponseMessage;
//import com.tahoecn.customerservice.model.CsSyncCustAbnormal;
//import com.tahoecn.customerservice.schedule.SyncData;
//import com.tahoecn.customerservice.service.CsSyncCustAbnormalService;
//import com.tahoecn.customerservice.service.CsSyncCustService;
//
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
//import io.swagger.annotations.ApiOperation;
//
///**
// * <p>
// * 客户信息 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2019-07-15
// */
//@Controller
//@RequestMapping("/api/csSyncData")
//public class CsSyncDataController {
//
//	@Autowired
//	private SyncData syncData;
//
//	@ApiOperation(value = "手动同步数据", notes = "手动同步数据")
//	@RequestMapping(value = "/syncData", method = { RequestMethod.GET })
//	@ResponseBody
//	public ResponseMessage syncData() {
//		try {
//			syncData.syncData();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return ResponseMessage.ok("执行完成");
//	}
//
//}
