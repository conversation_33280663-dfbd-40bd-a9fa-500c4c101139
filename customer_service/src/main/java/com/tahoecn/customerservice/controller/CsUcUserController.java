package com.tahoecn.customerservice.controller;

import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.core.date.DateUtil;
import com.tahoecn.core.util.JsonUtil;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.dto.ResponseDto;
import com.tahoecn.customerservice.model.dto.UserInfoDto;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.http.HttpClient;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * UC用户信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Api(tags = "UC用户信息", value = "UC用户信息")
@Controller
@RequestMapping("/api/csUcUser")
public class CsUcUserController {

	@Autowired
	CsUcUserService csUcUserService;

	@ApiOperation(value = "用户信息", notes = "通过条件查用户")
	@ApiImplicitParams({ @ApiImplicitParam(name = "tel", value = "呼入电话", dataType = "String"),
			@ApiImplicitParam(name = "username", value = "登陆名", dataType = "String") })
	@RequestMapping(value = "/selectUserTel", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage selectUserTel(String tel, String username) {
		if (StringUtils.isBlank(tel) && StringUtils.isBlank(username)) {
			return ResponseMessage.ok(null);
		}
		Wrapper<CsUcUser> wrapper = new EntityWrapper<>();
		if (StringUtils.isNotBlank(tel)) {
			wrapper.eq("fd_tel", tel);
		}
		if (StringUtils.isNotBlank(username)) {
			wrapper.eq("fd_username", username);
		}
		return ResponseMessage.ok(csUcUserService.selectOne(wrapper));
	}

	@ApiOperation(value = "用户工单联动", notes = "用户工单联动")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "tel", value = "呼入电话", dataType = "String"),
			@ApiImplicitParam(name = "username", value = "登陆名", dataType = "String"),
			@ApiImplicitParam(name = "project", value = "项目", dataType = "String"),
			@ApiImplicitParam(name = "processStateCode", value = "业务状态", dataType = "String"),
			@ApiImplicitParam(name = "createDateStart", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "createDateEnd", value = "结束时间", dataType = "Date") })
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage list(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "20") Integer pageSize, String tel, String username, String project,
			String processStateCode, Date createDateStart, Date createDateEnd) {
		Page<CsUcUser> page = new Page<>(pageNum, pageSize);
		if (StringUtils.isNotBlank(tel) || StringUtils.isNotBlank(username) || StringUtils.isNotBlank(project)
				|| StringUtils.isNotBlank(processStateCode) || createDateStart != null || createDateEnd != null) {
			Map<String, Object> map = new HashMap<>();
			map.put("tel", tel);
			map.put("username", username);
			map.put("project", project);
			map.put("processStateCode", processStateCode);
			map.put("createDateStart", createDateStart);
			map.put("createDateEnd", createDateEnd);
			Boolean flg = StringUtils.isNotBlank(project) || StringUtils.isNotBlank(processStateCode)
					|| createDateStart != null || createDateEnd != null;
			map.put("isFrom", flg);
			return ResponseMessage.ok(csUcUserService.userForm(map, page));
		}
		return ResponseMessage.ok(page);
	}

	@Value("${uc_api_url}")
	private String ucUrl;
	@Value("${uc_sysId}")
	private String sysId;
	@Value("${uc_priv_key}")
	private String privKey;

	@ApiOperation(value = "手动同步单个uc用户", notes = "手动同步单个uc用户")
	@ApiImplicitParams({ @ApiImplicitParam(name = "userName", value = "用户登陆名", required = true, dataType = "String") })
	@RequestMapping(value = "/synUser", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage synUser(String userName) {
		if (StringUtils.isBlank(userName)) {
			return ResponseMessage.ok(null);
		}

		Long timestamp = System.currentTimeMillis() / 1000;
		String token = SecureUtil.md5(timestamp + privKey);
		String url = String.format(ucUrl + "/v1/user/info?sysId=%s&timestamp=%d&token=%s&userName=%s", sysId, timestamp,
				token, userName);
		JSONObject json = new JSONObject(HttpClient.httpGet(url));
		if ((Integer) json.get("code") == 0) {
			CsUcUser csUcUser = JsonUtil.convertJsonToBean(String.valueOf(json.get("result")), CsUcUser.class);
			if (StringUtils.isNotBlank(csUcUser.getFdUsername())) {
				csUcUserService.synUserInfo(csUcUser);
				return ResponseMessage.okm(String.format("同步%s完成", userName));
			}
			return ResponseMessage.error(String.format("UC接口请求用户信息为空，请检查用户 '%s' 是否存在！", userName));
		} else {
			return ResponseMessage.error(String.valueOf(json.get("msg")));
		}
	}

	@ApiOperation(value = "手动同步uc用户", notes = "手动同步uc用户")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "fromTime", value = "开始时间：yyyy-MM-dd HH:mm:ss  默认：2000-01-01 00:00:00", dataType = "String") })
	@RequestMapping(value = "/synAllUser", method = { RequestMethod.GET })
	@ResponseBody
	public ResponseMessage synAllUser(String fromTime) {
		if (StringUtils.isBlank(fromTime)) {
			fromTime = "2000-01-01 00:00:00";
		}
		synUserInfo(fromTime);
		return ResponseMessage.ok("同步开始，请等待后台执行");
	}

	@SuppressWarnings("unchecked")
	@Async
	public void synUserInfo(String fromTime) {
		try {
			Long timestamp = System.currentTimeMillis() / 1000;
			String token = SecureUtil.md5(timestamp + privKey);
			String toTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
			Integer pageSize = 50;
			Integer pageNo = 1;
			Integer pageTotal = pageNo;
			while (pageNo < pageTotal + 1) {
				String url = String.format(
						ucUrl + "/v3/user/list?sysId=%s&timestamp=%d&token=%s"
								+ "&fromTime=%s&toTime=%s&pageNo=%s&pageSize=%s",
						sysId, timestamp, token, URLEncoder.encode(fromTime, "utf-8"),
						URLEncoder.encode(toTime, "utf-8"), pageNo, pageSize);
				String result = HttpClient.httpGet(url);
				if (StringUtils.isNotBlank(result)) {
					ResponseDto<List<UserInfoDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
					if (responseDto != null && responseDto.getCode() == 0) {
						pageTotal = responseDto.getTotalPages();
						if (responseDto.getResult() != null) {
							for (Object str : responseDto.getResult()) {
								String info = JsonUtil.convertObjectToJson(str);
								UserInfoDto userInfoDto = JsonUtil.convertJsonToBean(info, UserInfoDto.class);
								CsUcUser userInfo = convert(userInfoDto);
								csUcUserService.synUserInfo(userInfo);
							}
						}
					}
				}
				Thread.sleep(1000);
				pageNo++;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private CsUcUser convert(UserInfoDto userInfoDto) {
		CsUcUser userInfo = new CsUcUser();
		BeanUtils.copyProperties(userInfoDto, userInfo);
		userInfo.setCreateTime(new Date());
		userInfo.setUpdateTime(new Date());
		return userInfo;
	}

}
