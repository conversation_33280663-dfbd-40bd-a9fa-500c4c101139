package com.tahoecn.customerservice.controller;


import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.dto.ProjectRoleDO;
import com.tahoecn.customerservice.model.dto.TreeExt;
import com.tahoecn.customerservice.service.CsDictItemService;
import com.tahoecn.customerservice.service.CsUpgradeCfgService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 升级配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Controller
@RequestMapping("/api/csUpgradeCfg")
public class CsUpgradeCfgController {

    @Autowired
    private CsUpgradeCfgService csUpgradeCfgService;

    @ApiOperation(value = "报事升级配置列表", notes = "报事升级配置列表")
    @RequestMapping(value = "/tree/list", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage treeList() {
        return csUpgradeCfgService.selectTreeUpgradeCfg();
    }

    @ApiOperation(value = "报事升级配置编辑回显", notes = "报事升级配置编辑回显")
    @RequestMapping(value = "/upgradeCfgEcho", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage upgradeCfgEcho(String type, String code) {
        if (type == null || type.equals("")){
            return ResponseMessage.error("类型[type]参数为空,请进行检查传值");
        }
        if (code == null || code.equals("")){
            return ResponseMessage.error("报事项目编号[code]参数为空,请进行检查传值");
        }
        return csUpgradeCfgService.selectOneUpgradeCfg(type,code);
    }

    @ApiOperation(value = "报事升级配置编辑", notes = "报事升级配置编辑")
    @RequestMapping(value = "/updateSaveUpgradeCfg", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseMessage updateSaveUpgradeCfg(@RequestBody Map<String, Object> pamam) {

        try {
            //对前台传来的值进行非空判断
            String type = pamam.get("type").toString();
            if (type == null || type.equals("")){
                return ResponseMessage.error("类型参数为空,请进行检查传值");
            }
            String code = pamam.get("code").toString();
            if (code == null || code.equals("")){
                return ResponseMessage.error("报事项目编号[sortCode]参数为空,请进行检查传值");
            }
            String regionDays = pamam.get("regionDays").toString();
            if (regionDays == null || regionDays.equals("")){
                //return ResponseMessage.error("区域升级天数[regionDays]参数为空,请进行检查传值");
            }
            String groupDasy = pamam.get("groupDasy").toString();
            if (groupDasy == null || groupDasy.equals("")){
                //return ResponseMessage.error("集团升级天数[groupDasy]参数为空,请进行检查传值");
            }
            return csUpgradeCfgService.saveUpgradeCfg(pamam);
        }catch (Exception e){
            e.getMessage();
            return ResponseMessage.error("系统错误,请联系管理员");
        }
    }

}

