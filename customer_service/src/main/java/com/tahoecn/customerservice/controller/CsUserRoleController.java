package com.tahoecn.customerservice.controller;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.dto.ProjectRoleAttrDO;
import com.tahoecn.customerservice.model.dto.ProjectRoleDO;
import com.tahoecn.customerservice.model.dto.Tree;
import com.tahoecn.customerservice.model.dto.TreeExt;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.CsUserRoleService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-16
 */
@Controller
@RequestMapping("/api/csUserRole")
public class CsUserRoleController {

    private static final Log log = LogFactory.get();

    @Autowired
    private CsUserRoleService csUserRoleService;

    @Autowired
    private CsUcUserService csUcUserService;

    @ApiOperation(value = "用户项目角色列表", notes = "用户项目角色列表")
    @RequestMapping(value = "/tree/list", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage treeList() {
        List<TreeExt<ProjectRoleDO>> treeList = csUserRoleService.getTreeList();
        return ResponseMessage.ok(treeList);
    }

    @ApiOperation(value = "用户项目角色编辑", notes = "用户项目角色编辑")
    @RequestMapping(value = "/tree/edit", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage treeEdit(String type, String typeName, String treePath) {
        ProjectRoleAttrDO projectRoleAttrDO = csUserRoleService.getByType(type, typeName,null);
        if (projectRoleAttrDO != null) {
            ProjectRoleDO projectRoleDO = new ProjectRoleDO();
            projectRoleDO.setTreePath(treePath);
            projectRoleDO.setType(type);
            projectRoleDO.setTypeName(typeName);
            projectRoleDO.setProjectRoleAttrDO(projectRoleAttrDO);
            return ResponseMessage.ok(projectRoleDO);
        }
        return ResponseMessage.error("查询不到数据!");
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 客服管理项目编辑保存
     * @Date 16:01 2018/12/17
     * @Param
     **/
    @ApiOperation(value = "客服管理项目编辑", notes = "客服管理项目编辑")
    @RequestMapping(value = "/projectEditing", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage projectEditing(@RequestBody Map<String, Object> pamam) {
        try {
            //获取前台数据
            //进行数据空值判断
            String type = pamam.get("type").toString();
            if (type == null || type.equals("")){
                return ResponseMessage.error("请填写参数 : type");
            }
            //根据前台数据进行添加
            ResponseMessage responseMessage = csUserRoleService.updateUserRole(pamam);
            return responseMessage;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }

    /**
     * 组织数
     */
    @ApiOperation(value = "组织树", notes = "组织数")
    @RequestMapping(value = "/orgTree", method = {
            RequestMethod.GET})
    @ResponseBody
    public ResponseMessage orgTree() {
        return csUserRoleService.orgTree();
    }

    @RequestMapping(value = "findUserByOrgId", method = {RequestMethod.GET})
    @ApiOperation("通过组织获取用户信息")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "String", name = "orgId", value = "组织 Sid ")})
    public ResponseMessage findUserByOrgId(String orgId) {
        return csUcUserService.findUserByOrgId(orgId);
    }


    /**
     * 查询用户信息，实现 添加管理员选人功能
     *
     * @param username 用户名或帐号
     * @return
     */
    @RequestMapping(value = "findUsersByNameOrCode", method = {RequestMethod.POST,RequestMethod.GET})
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "String", name = "username", value = "用户名或帐号")})
    public ResponseMessage findUsersByNameOrCode(String username) {
        return csUcUserService.findUsersByNameOrCode(username);
    }

    @ApiOperation(value = "IT项目人员查询", notes = "IT项目人员查询")
    @RequestMapping(value = "selectItUser", method = {RequestMethod.POST,RequestMethod.GET})
    @ResponseBody
    public ResponseMessage selectItUser() {
        return csUserRoleService.selectItUser();
    }

    @ApiOperation(value = "IT项目人员编辑", notes = "IT项目人员编辑")
    @RequestMapping(value = "/saveItUser", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage saveItUser(@RequestBody Map<String, Object> pamam) {
        try {
            //根据前台数据进行添加
            ResponseMessage responseMessage = csUserRoleService.saveItUser(pamam);
            return responseMessage;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
}

