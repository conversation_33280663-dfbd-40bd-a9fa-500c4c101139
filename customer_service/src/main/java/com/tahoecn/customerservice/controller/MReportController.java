package com.tahoecn.customerservice.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.vo.*;
import com.tahoecn.customerservice.service.CsFormInstService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * Created by zhanghw on 2018/12/3.
 * 移动报表
 */
@Controller
@RequestMapping("/api/mreport")
@Api(tags = "移动端报表", value = "移动端报表")
public class MReportController extends BaseController {

    @Autowired
    private CsFormInstService csFormInstService;

    @ApiOperation(value = "重大投诉列表", notes = "重大投诉列表")
    @RequestMapping(value = "/complaint/list", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage complaintList(@RequestParam(defaultValue = "0") Integer pageNum, @RequestParam(defaultValue = "20") Integer pageSize) {
        Page<McomplaintVo> list = csFormInstService.findComplaintPageList(pageNum, pageSize, null);
        return ResponseMessage.ok(list);
    }

    @ApiOperation(value = "重大投诉详情", notes = "重大投诉详情")
    @RequestMapping(value = "/complaint/detail", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage complaintDetail(@RequestParam(defaultValue = "0") Long id) {
        McomplaintDetailVo mcomplaintDetailVo = csFormInstService.findComplaintDetail(id);
        return ResponseMessage.ok(mcomplaintDetailVo);
    }

    @ApiOperation(value = "报事统计-客户", notes = "报事统计-客户")
    @RequestMapping(value = "/customer/statistics/report", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部 1:按年 2：按月")})
    @ResponseBody
    public ResponseMessage customerStatisticsReport(@RequestParam(defaultValue = "1") int range) {
        CustomerStatisticsReportVo customerStatisticsReportVo = csFormInstService.customerStatisticsReportCount(range);
        return ResponseMessage.ok(customerStatisticsReportVo);
    }

    @ApiOperation(value = "报事统计下级-客户", notes = "报事统计下级-客户")
    @RequestMapping(value = "/customer/statistics/reportNext", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部 1:按年 2：按月")})
    @ResponseBody
    public ResponseMessage customerStatisticsReportNext(@RequestParam(defaultValue = "1") int range, String firstSortCode) {
        List<CustomerStatisticsReportGroupVo> statisticsGroupList = csFormInstService.customerStatisticsReportCountNext(range, firstSortCode);
        return ResponseMessage.ok(statisticsGroupList);
    }

    @ApiOperation(value = "报事升级统计-客户", notes = "报事升级统计-客户")
    @RequestMapping(value = "/customer/statistics/reportUp", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部 1:按年 2:按月"), @ApiImplicitParam(name = "region", value = "region 1:城市 2:区域"),
            @ApiImplicitParam(name = "type", value = "type 1:投诉 2：报修")})
    @ResponseBody
    public ResponseMessage customerStatisticsReportUp(Integer range, Integer region, Integer type) {
        CustomerStatisticsReportUpVo customerStatisticsReportUpVo = csFormInstService.customerStatisticsReportUpCount(range, region, type);
        return ResponseMessage.ok(customerStatisticsReportUpVo);
    }

    @ApiOperation(value = "报事升级统计下级-客户", notes = "报事升级统计下级-客户")
    @RequestMapping(value = "/customer/statistics/reportUpNext", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部 1:按年 2:按月"), @ApiImplicitParam(name = "region", value = "region 1:城市 2:区域"),
            @ApiImplicitParam(name = "type", value = "type 1:投诉 2:报修"), @ApiImplicitParam(name = "regionName", value = "regionName")})
    @ResponseBody
    public ResponseMessage reportUpNext(Integer range, Integer region, Integer type, String regionName) {
        CustomerStatisticsReportUpNextVo customerStatisticsReportUpNextVo = csFormInstService.customerStatisticsReportUpNextCount(range, region, type, regionName);
        return ResponseMessage.ok(customerStatisticsReportUpNextVo);
    }

    @ApiOperation(value = "报事满意度统计-客户", notes = "报事满意度统计-客户")
    @RequestMapping(value = "/customer/statistics/satisficing", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部1:按年2：按月")})
    @ResponseBody
    public ResponseMessage customerStatisticsSatisficing(Integer range) {
        CustomerStatisticsSatisficingVo customerStatisticsSatisficingVo = csFormInstService.customerStatisticsSatisficingCount(range);
        return ResponseMessage.ok(customerStatisticsSatisficingVo);
    }

    @ApiOperation(value = "报事趋势统计(日均)-员工", notes = "报事趋势统计(日均)-员工")
    @RequestMapping(value = "/staff/statistics/dayOfMonthReport", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage dayOfMonthReport() {
        List<DayOfMonthReportVo> dayOfMonthReportVo = csFormInstService.dayOfMonthReportCount();
        return ResponseMessage.ok(dayOfMonthReportVo);
    }

    @ApiOperation(value = "报事统计-员工", notes = "报事统计-员工")
    @RequestMapping(value = "/staff/statistics/itReport", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部1:按年2：按月3:当日"), @ApiImplicitParam(name = "type", value = "type 1:员工 2:VIP")})
    @ResponseBody
    public ResponseMessage itReport(Integer range, Integer type) {
        CustomerStatisticsReportVo customerStatisticsReportVo = csFormInstService.itStatisticsReportCount(range, type);
        return ResponseMessage.ok(customerStatisticsReportVo);
    }

    @ApiOperation(value = "报事统计下级-员工", notes = "报事统计下级-员工")
    @RequestMapping(value = "/staff/statistics/itReportNext", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部1:按年2：按月3:当日"), @ApiImplicitParam(name = "firstSortCode", value = "项目code"),
            @ApiImplicitParam(name = "type", value = "type 1:员工 2:VIP")})
    @ResponseBody
    public ResponseMessage itReportNext(Integer range, String firstSortCode, Integer type) {
        CustomerStatisticsReportVo customerStatisticsReportVo = csFormInstService.itStatisticsReportGroupNext(range, firstSortCode, type);
        return ResponseMessage.ok(customerStatisticsReportVo);
    }
    @ApiOperation(value = "报事统计事故-员工", notes = "报事统计事故-员工")
    @RequestMapping(value = "/staff/statistics/itAccidentReport", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部1:按年2：按月3:当日"), @ApiImplicitParam(name = "type", value = "type 1:事故 2:投诉")})
    @ResponseBody
    public ResponseMessage itAccidentReport(Integer range, Integer type) {
        CustomerStatisticsReportAccidentVo customerStatisticsReportAccidentVo = csFormInstService.itStatisticsAccidentReportCount(range, type);
        return ResponseMessage.ok(customerStatisticsReportAccidentVo);
    }
    @ApiOperation(value = "报事统计事故下级-员工", notes = "报事统计事故下级-员工")
    @RequestMapping(value = "/staff/statistics/itAccidentReportNext", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部1:按年2：按月3:当日"), @ApiImplicitParam(name = "type", value = "type 1:事故 2:投诉"),@ApiImplicitParam(name = "firstSortCode", value = "项目code")})
    @ResponseBody
    public ResponseMessage itAccidentReportNext(Integer range, Integer type,String firstSortCode) {
        List<ItAccidentReportNextVo> itAccidentReportNextVoList = csFormInstService.itAccidentReportNextCount(range, type,firstSortCode);
        return ResponseMessage.ok(itAccidentReportNextVoList);
    }
    @ApiOperation(value = "报事统计事故下级查询-员工", notes = "报事统计事故下级查询-员工")
    @RequestMapping(value = "/staff/statistics/itAccidentReportNextQuery", method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "range", value = "range 0:全部1:按年2：按月3:当日"), @ApiImplicitParam(name = "type", value = "type 1:事故 2:投诉"),@ApiImplicitParam(name = "firstSortCode", value = "项目code"),@ApiImplicitParam(name = "time", value = "时间格式为2019-01-01或2019-01")})
    @ResponseBody
    public ResponseMessage itAccidentReportNextQuery(Integer range, Integer type,String firstSortCode,String time) {
        List<ItAccidentReportNextVo> itAccidentReportNextVoList = csFormInstService.itAccidentReportNextQuery(range, type,firstSortCode,time);
        return ResponseMessage.ok(itAccidentReportNextVoList);
    }
}
