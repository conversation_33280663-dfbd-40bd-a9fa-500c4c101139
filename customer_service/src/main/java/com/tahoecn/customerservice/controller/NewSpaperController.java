package com.tahoecn.customerservice.controller;

import com.tahoecn.customerservice.common.utils.MD5Util;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 报事 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-07
 */
@Api(tags = "物业接口", value = "物业接口")
@Controller
@RequestMapping("/wy/newspaper")
public class NewSpaperController {
	
	@Value("${qd_key}")
	private String qdKey;
	
    private static final Log log = LogFactory.get();

    @Autowired
    private CsFormInstService csFormInstService;

    /**
     * <AUTHOR>
     * @Description //TODO
     * 物业报事推送
     * @Date 15:40 2018/12/9
     * @Param
     * @return
     **/
    @ApiOperation(value = "地产状态更新", notes = "地产状态更新")
    @RequestMapping(value = "/propertyNewsPush", method = RequestMethod.GET)
    @ApiImplicitParams({@ApiImplicitParam(name = "formNo", value = "地产工单编号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processStateName", value = "业务步骤", required = true, dataType = "String"),
            @ApiImplicitParam(name = "Mac", value = "Md5加密", required = true, dataType = "String")})
    @ResponseBody
    public ResponseMessage publicAreas(String formNo, String processStateName, String Mac) {

        try {
            if (Mac == null || Mac.equals("")){
                return ResponseMessage.error("Mac为空,请传递参数");
            }
            if (processStateName == null || processStateName.equals("")){
                return ResponseMessage.error("processStateName为空,请传递参数");
            }
            if (formNo == null){
                return ResponseMessage.error("缺少地产工单编号,请输入正确的地产工单编号");
            }
            //生成自己mac
            Date date = new Date();
            String date1 = getDate(date);
            //加密规则 : class的路径+方法路径+地产工单编号+当前时间
            String macMy = "/wy/newspaper/propertyNewsPush"+formNo+date1;
            //加密mac
            String md5String1 = MD5Util.getMD5String(macMy);
            log.info("-------M-T---物业报事推送Md5------->>>>>>>>>>>>>>> Md5明文 : [/wy/newspaper/propertyNewsPush"+formNo+processStateName+date1+"] 密文 : "+md5String1+"  时间 : "+new Date()+ "   <<<<<<<<<<<<<<<<<<---------------------");
            //对比加密字符串
            if (!Mac.equals(md5String1)){
                return ResponseMessage.error("mac加密串对比失败");
            }
            ResponseMessage responseMessage = csFormInstService.updateProcessStateNameByformNo(formNo,processStateName);
            return responseMessage;
        }catch (Exception e){
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }

    }



    /**
     * <AUTHOR>
     * 物业推地产报事提交
     * @Date 15:40 2018/12/9
     * @Param
     * @return
     **/
    @ApiOperation(value = "物业报事提交", notes = "物业报事提交")
    @RequestMapping(value = "/publicAreas", method = RequestMethod.POST)
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "wyFormNo", value = "物业工单编号", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "firstSortCode", value = "一级分类编码：投诉：coTS 报修：coBX", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "mobile", value = "移动电话", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "customerDemand", value = "客户诉求", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "projectCode", value = "项目编码", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "ownerId", value = "业主ID", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "houseInfoId", value = "房屋ID", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "createUserName", value = "创建人名称", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "Mac", value = "Md5加密", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "creationDate", value = "创建日期", required = true, dataType = "String")
//    })
    @ResponseBody
    public ResponseMessage publicAreas(@RequestBody Map<String , Object> pamam) {
        try {

            //获取body里的数据
            String wyFormNo = (String) pamam.get("wyFormNo");
            String firstSortCode = (String) pamam.get("firstSortCode");
            String mobile = (String) pamam.get("mobile");
            String customerDemand = (String) pamam.get("customerDemand");
            String projectCode = (String) pamam.get("projectCode");
            String ownerId = (String) pamam.get("ownerId");
            String houseInfoId = (String) pamam.get("houseInfoId");
            String createUserName = (String) pamam.get("createUserName");
            String Mac = (String) pamam.get("Mac");
            String creationDate = (String) pamam.get("creationDate");

            //非空判断
            if (Mac == null || Mac.equals("")){
                return ResponseMessage.error("Mac为空,请传递参数");
            }
            if (wyFormNo == null || wyFormNo.equals("")){
                return ResponseMessage.error("物业工单编号为空,请传递参数");
            }
            if (firstSortCode == null || firstSortCode.equals("")){
                return ResponseMessage.error("一级分类编码为空,请传递参数");
            }
            if (mobile == null || mobile.equals("")){
                return ResponseMessage.error("移动电话为空,请传递参数");
            }
            if (projectCode == null || projectCode.equals("")){
                return ResponseMessage.error("项目编码为空,请传递参数");
            }
            if (customerDemand == null || customerDemand.equals("")){
                return ResponseMessage.error("客户诉求号为空,请传递参数");
            }
            if (creationDate == null || creationDate.equals("")){
                return ResponseMessage.error("创建日期为空,请传递参数");
            }
            if (ownerId == null || ownerId.equals("")){
                return ResponseMessage.error("业主ID为空,请传递参数");
            }
            if (houseInfoId == null || houseInfoId.equals("")){
                return ResponseMessage.error("房屋ID为空,请传递参数");
            }
            if (createUserName == null || createUserName.equals("")){
                return ResponseMessage.error("创建人名称为空,请传递参数");
            }
            //对多个电话,分割符进行更正
            if (mobile.contains("/")){
                mobile.replace("/", ",");
            }
            //生成自己mac
            Date date = new Date();
            String date1 = getDate(date);
            //class的路径+方法路径+物业工单编号当前时间
            String macMy = "/wy/newspaper/publicAreas"+wyFormNo+date1;
            //加密mac
            String md5String1 = MD5Util.getMD5String(macMy);
            log.info("-------M-T---物业报事提交Md5------->>>>>>>>>>>>>>> Md5明文 : [/wy/newspaper/publicAreas"+wyFormNo+date1+"] 密文 : "+md5String1+"  时间 : "+new Date()+ "   <<<<<<<<<<<<<<<<<<---------------------");
            //对比加密字符串
            if (!Mac.equals(md5String1)){
                return ResponseMessage.error("mac加密串对比失败");
            }
            //进行物业工单添加
            ResponseMessage responseMessage = csFormInstService.propertyReportSubmission(wyFormNo,firstSortCode,mobile,customerDemand,projectCode,creationDate,ownerId,houseInfoId,createUserName);
            return responseMessage;
        }catch (Exception e){
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    /**
     * 时间转换
     * */
    public String getDate(Date date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String strtodate = formatter.format(date);
        return strtodate;
    }

    @ApiOperation(value = "接口测试地址", notes = "接口测试地址")
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage ghj() {
        try {
            csFormInstService.sectorNewspaperSubmission(null);
            return null;
        }catch (Exception e){
            e.getMessage();
            return ResponseMessage.error("请联系管理员");
        }
    }
    
    /**
     * <AUTHOR>
     * 物业推地产报事提交
     * @Date 15:40 2018/12/9
     * @Param
     * @return
     **/
    @ApiOperation(value = "千丁报事提交", notes = "千丁报事提交")
    @RequestMapping(value = "/qdForm", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage qdForm(@RequestBody Map<String , Object> pamam) {
        try {

            //获取body里的数据
        	String thirdSysId = (String) pamam.get("thirdSysId");//第三方系统id（类似appkey的作用）
            String qdProblemTaskId = (String) pamam.get("qdProblemTaskId");//千丁问题工单id
            String qdProblemTypeId = (String) pamam.get("qdProblemTypeId");//千丁原因大类id
            String thirdProblemTypeId = (String) pamam.get("thirdProblemTypeId");//泰禾原因大类id
            String problemTypeName = (String) pamam.get("problemTypeName");//原因大类名称
            Boolean isPublicArea = (Boolean) pamam.get("isPublicArea");//是否公区，固定值true
            String memo = (String) pamam.get("memo");//问题描述
            String pics = (String) pamam.get("pics");//报事图片，最多3张,逗号分隔
            String createUserId = (String) pamam.get("createUserId");//报事人id
            String createUserName = (String) pamam.get("createUserName");//报事人姓名
            String createUserMobile = (String) pamam.get("createUserMobile");//报事人电话
            Long createTime = (Long) pamam.get("createTime");//工单创建时间
            String handler = (String) pamam.get("handler");//处理人，固定空值“”
            String regionId = (String) pamam.get("regionId");//千丁项目id
            String regionName = (String) pamam.get("regionName");//千丁项目名称
            Long timestamp = (Long) pamam.get("timestamp");//时间戳
            String sign = (String) pamam.get("sign");//MD5签名串

            //非空判断
            if (thirdSysId == null || "".equals(thirdSysId)){
                return ResponseMessage.error("thirdSysId为空,请传递参数");
            }
            if (qdProblemTaskId == null || "".equals(qdProblemTaskId)){
            	return ResponseMessage.error("千丁问题工单id为空,请传递参数");
            }
            if (qdProblemTypeId == null || "".equals(qdProblemTypeId)){
            	return ResponseMessage.error("千丁原因大类id为空,请传递参数");
            }
            if (thirdProblemTypeId == null || "".equals(thirdProblemTypeId)){
            	return ResponseMessage.error("泰禾原因大类id为空,请传递参数");
            }
            if (problemTypeName == null || "".equals(problemTypeName)){
            	return ResponseMessage.error("原因大类名称为空,请传递参数");
            }
            if (isPublicArea == null || "".equals(isPublicArea)){
            	return ResponseMessage.error("是否公区为空,请传递参数");
            }
            if (memo == null || "".equals(memo)){
            	return ResponseMessage.error("问题描述为空,请传递参数");
            }
            if (createUserId == null || "".equals(createUserId)){
            	return ResponseMessage.error("报事人id为空,请传递参数");
            }
            if (createUserName == null || "".equals(createUserName)){
            	return ResponseMessage.error("报事人姓名为空,请传递参数");
            }
            if (createUserMobile == null || "".equals(createUserMobile)){
            	return ResponseMessage.error("报事人电话为空,请传递参数");
            }
            if (createTime == null || "".equals(createTime)){
            	return ResponseMessage.error("工单创建时间为空,请传递参数");
            }
            if (regionId == null || "".equals(regionId)){
            	return ResponseMessage.error("千丁项目id为空,请传递参数");
            }
            if (regionName == null || "".equals(regionName)){
            	return ResponseMessage.error("千丁项目名称为空,请传递参数");
            }
            if (timestamp == null || "".equals(timestamp)){
            	return ResponseMessage.error("时间戳为空,请传递参数");
            }
            if (sign == null || "".equals(sign)){
            	return ResponseMessage.error("MD5签名串为空,请传递参数");
            }
            
            //生成自己mac
          //将所有业务参数（包括时间戳，签名结果sign除外，空值不参与签名），按字典升序排序，属性间用&连接
            String macMy = "createTime=" + createTime +"&createUserId=" + createUserId 
            		+ "&createUserMobile=" + createUserMobile + "&createUserName=" + createUserName;
           if(handler != null && !"".equals(handler)){
        	   macMy = macMy + "&handler=" + handler;
           }		
           macMy += "&isPublicArea=" + isPublicArea + "&memo=" + memo;
            		
            if(pics != null && !"".equals(pics)){
            	macMy = macMy + "&pics=" + pics;
            }
            macMy += "&problemTypeName="+problemTypeName + "&qdProblemTaskId=" + qdProblemTaskId
            		+"&qdProblemTypeId=" + qdProblemTypeId
            		+"&regionId=" + regionId + "&regionName=" + regionName
            		+ "&thirdProblemTypeId=" + thirdProblemTypeId +"&thirdSysId=" + thirdSysId 
            		+ "&timestamp=" + timestamp+qdKey;
            //加密mac
            String md5String1 = MD5Util.getMD5String(macMy).toUpperCase();
            log.info("-------Q-D---千丁报事提交Md5------->>>>>>>>>>>>>>> Md5明文 : ["+macMy+"] 密文 : "+md5String1+"  时间 : "+new Date()+ "   <<<<<<<<<<<<<<<<<<---------------------");
            //对比加密字符串
            if (!sign.equals(md5String1)){
                return ResponseMessage.error("mac加密串对比失败");
            }
            //进行千丁工单添加
            ResponseMessage responseMessage = csFormInstService.qdSubmission(pamam);
            return responseMessage;
        }catch (Exception e){
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
	public static void main(String[] args) {
//		String str = "createUserId=123&createUserName=134&createUserMobile=231"
//				+ "&createTime=1557218549243&handler=&isPublicArea=true"
//				+ "&memo=不知道1234&problemTypeName=客户服务&pics=http://a/b/1.png,http://a/b/2.png"
//				+ "&qdProblemTaskId=qwe&qdProblemTypeId=1&regionId=201904261717076322a"
//				+ "&regionName=红峪&thirdProblemTypeId=0010&thirdSysId=1"
//				+ "&timestamp=1557218549243"+"172891D331093BE1";
//		String str = "createTime=1557194165000&createUserId=201904221611372954b&createUserMobile=15313889066&createUserName=王建华&isPublicArea=true&memo=神神叨叨的&pics=https://img1.qdingnet.com/qiniu:qding:api:dd6fe3cb-c6e8-40c5-a3a1-54ae5ac476a7.jpg&problemTypeName=客服类&qdProblemTaskId=1126030193246408704&qdProblemTypeId=1118772850147753984&regionId=2019041812021304462&regionName=0418xfd测试社区2&thirdProblemTypeId=0010&thirdSysId=QDWUYE&timestamp=1557301497355luanqibazaod_zhend_buzhid";
		String str = "/wy/newspaper/propertyNewsPushWY20190508002220190509";
		
		System.out.println(str);
		System.out.println(MD5Util.getMD5String(str));
	}
}
