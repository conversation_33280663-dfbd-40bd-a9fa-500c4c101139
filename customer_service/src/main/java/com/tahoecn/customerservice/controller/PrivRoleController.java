package com.tahoecn.customerservice.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.UserPackage;
import com.tahoecn.customerservice.service.CsUcUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@RequestMapping("/api/perm")
@Api(tags = "权限", value = "权限")
public class PrivRoleController extends BaseController {
	@Autowired
	private CsUcUserService csUcUserService;

	@RequestMapping(value = "/userPackage", method = { RequestMethod.GET })
	@ApiOperation(value = "获取用户信息包", notes = "获取用户信息包")
	@ResponseBody
	public ResponseMessage userPackage(HttpServletRequest request) {
		UserPackage userPackage = csUcUserService.getUserPackage();
		return ResponseMessage.ok(userPackage);
	}
	@RequestMapping(value = "/userLogin", method = { RequestMethod.GET })
	@ApiOperation(value = "登录", notes = "登录")
	@ResponseBody
	public ResponseMessage userLogin(HttpServletRequest request,String account,String password) {
		try {
			csUcUserService.userLogin(account, password,request);
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseMessage.error(e.getMessage());
		}
		return ResponseMessage.ok("登录成功");
	}
}
