package com.tahoecn.customerservice.controller;


import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.ProcessService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Api(tags = "流程处理 处理接口", value = "流程处理 处理接口")
@Controller
@RequestMapping("/api/process")
public class ProcessController {

    @Resource(name = "qConsultationService")
    ProcessService qConsultationService;

    @Resource(name = "qPraiseAndSuggestionService")
    ProcessService qPraiseAndSuggestionService;

    @Resource(name = "sConsultationService")
    ProcessService sConsultationService;

    @Resource(name = "sComplaintService")
    ProcessService sComplaintService;

    @Resource(name = "sRepairService")
    ProcessService sRepairService;
    
    @Resource(name = "wyProcessService")
    ProcessService wyProcessService;
    
    @Resource(name = "itProcessService")
    ProcessService itProcessService;

    @Resource(name = "processService")
    ProcessService processService;
    @Autowired
    private CsFormInstService csFormInstService;
    @Autowired
    private CsUcUserService csUcUserService;
    @Autowired
    private CsProcessWorkitemService itemService;


    /**
     * 根据类型判断走向
     * 流程类别	        一级分类
     * 快速处理流程	    咨询
     * 快速处理流程     表扬与建议
     * 标准流程	        咨询
     * 标准流程         投诉
     * 标准流程         报修
     *
     * @return
     */
    @ApiOperation(value = "工单提交", notes = "工单提交")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage submit(@RequestParam(name = "formInstId", required = true) String formInstId,
                                  String comment,
                                  String assignUserId,
                                  String assignUserName,
                                  String mobile,
                                  @RequestParam(name = "operateType", required = true) String operateType) throws Exception {
        //CsUcUser curUser = csUcUserService.selectByUsername();
        //csFormInstService.saveOrUpate(familyListDto, curUser.getFdUsername());
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formInstId);
        CsFormInst csFormInst = csFormInstService.selectOne(formWrapper);
        String processCode = csFormInst.getProcessCode();
        String firstSortCode = csFormInst.getFirstSortCode();
        String deptCode = csFormInst.getDeptCode();
        String processStateCode = csFormInst.getProcessStateCode();
        String handleRecord = csFormInst.getHandleRecord();
        ProcessService processService = this.getService(csFormInst.getDeptCode(),processCode,firstSortCode);
        processService.process(csFormInst,formInstId,firstSortCode,deptCode,processStateCode,handleRecord,comment,assignUserId,assignUserName,mobile,operateType);
        if(operateType!=null && !"".equals(operateType) && operateType.equals("submit")){
            return ResponseMessage.okm("提交成功");
        }else{
            return ResponseMessage.okm("退回成功");
        }
    }


    @ApiOperation(value = "批量分派", notes = "批量分派")
    @RequestMapping(value = "/batchSubmit", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage batchSubmit(@RequestParam(name = "formInstIds", required = true) String formInstIds,
                                  @RequestParam(name = "assignUserId", required = true) String assignUserId,
                                  @RequestParam(name = "assignUserName", required = true) String assignUserName,
                                  @RequestParam(name = "mobile", required = true) String mobile) {
        String[] formInstIdArr = formInstIds.split(",");
        for(int i=0;i<formInstIdArr.length;i++){
            String formInstId = formInstIdArr[i];
            Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
            formWrapper.where("id={0}",formInstId);
            CsFormInst form = csFormInstService.selectOne(formWrapper);
            String processCode = form.getProcessCode();
            String firstsortCode = form.getFirstSortCode();
            if(!"toBeAssigned".equals(form.getProcessStateCode())){
                continue;
            }
            ProcessService processService = this.getService(form.getDeptCode(),processCode,firstsortCode);

            String deptCode = form.getDeptCode();
            String processStateCode = form.getProcessStateCode()==""?"draft":form.getProcessStateCode();
            String comment = "";
            String operateType = "submit";
            String handleRecord = "";
            processService.process(form,formInstId,firstsortCode,deptCode,processStateCode,handleRecord,comment,assignUserId,assignUserName,mobile,operateType);
        }
        return ResponseMessage.okm("提交成功");
    }

    @RequestMapping(value = "/upgrade", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage upgrade(@RequestParam(name = "formInstId", required = true) Long formInstId) throws Exception {
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formInstId);
        CsFormInst csFormInst = csFormInstService.selectOne(formWrapper);
        String processCode = csFormInst.getProcessCode();
        String firstSortCode = csFormInst.getFirstSortCode();
        ProcessService processService = this.getService(csFormInst.getDeptCode(),processCode,firstSortCode);
        try {
            processService.updrade(csFormInst);
            return ResponseMessage.okm("成功");
        }catch (Exception e){
            return ResponseMessage.error("没有升级人员");
        }
    }



    @RequestMapping(value = "/specialEnd", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage specialEnd(@RequestParam(name = "formInstId", required = true) String formInstId) {
        processService.specialEnd(formInstId);
        return ResponseMessage.okm("成功");
    }

    @RequestMapping(value = "/getTaskUser", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getTaskUser(@RequestParam(name = "formInstId", required = true) String formInstId,
                                       @RequestParam(name = "processCode", required = true) String processCode,
                                       @RequestParam(name = "firstSortCode", required = true) String firstsortCode,
                                       @RequestParam(name = "operateType", required = true) String operateType) {
    	CsFormInst csFormInst = csFormInstService.selectById(formInstId);
    	if(csFormInst == null) {
    		return ResponseMessage.error("表单不存在");
    	}
        ProcessService processService = this.getService(csFormInst.getDeptCode(),processCode,firstsortCode);
        List userList = processService.getUserByRole(operateType,new Long(formInstId.split(",")[0]));
        return ResponseMessage.ok(userList);
    }


    public ProcessService getService(String deptCode, String processCode,String firstsortCode){
    	if("deptIT".equals(deptCode)) {
    		return this.itProcessService;
    	}
        if (processCode.equals("processKSCL")) {//快速流程
            if (firstsortCode.equals("coZX")) {//咨询
                return this.qConsultationService;
            } else if (firstsortCode.equals("coJYBY")) {//建议表扬
                return this.qPraiseAndSuggestionService;
            }
        } else if (processCode.equals("processBZ")) {
        	if("deptWY".equals(deptCode)) {
            	return this.wyProcessService;
            }
            if (firstsortCode.equals("coZX")) {//咨询
                return this.sConsultationService;
            } else if (firstsortCode.equals("coTS")) {//投诉
                return this.sComplaintService;
            } else if (firstsortCode.equals("coBX")) {//报修
                return this.sRepairService;
            }
        }
        
        return this.processService;
    }
}

