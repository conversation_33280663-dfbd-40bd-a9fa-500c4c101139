package com.tahoecn.customerservice.controller;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.vo.ScreenReportCountVo;
import com.tahoecn.customerservice.service.CsFormInstService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by zhanghw on 2019/1/10.
 * 大屏显示
 */
@Controller
@RequestMapping("/screenDisplay")
@Api(tags = "大屏显示", value = "大屏显示")
public class ScreenDisplayController extends BaseController {

    @Autowired
    private CsFormInstService csFormInstService;

    @ApiOperation(value = "报事统计", notes = "报事统计")
    @RequestMapping(value = "/reportCount", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseMessage reportCount() {
        ScreenReportCountVo screenReportCountVo = csFormInstService.screenReportCount();
        return ResponseMessage.ok(screenReportCountVo);
    }
}
