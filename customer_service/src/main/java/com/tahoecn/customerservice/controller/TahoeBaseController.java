package com.tahoecn.customerservice.controller;

import com.tahoecn.core.json.JSONResult;
import com.tahoecn.crypto.SecureUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Calendar;
import java.util.List;

@Component
public class TahoeBaseController {

    @Value("${file.physicalPath}")
    private String physicalPath;

    public JSONResult uploadFiles(List<MultipartFile> files) {
        File dir;
        JSONResult json = new JSONResult();

        for (MultipartFile file : files) {
            String extensionName = "";
            String originalFilename =  file.getOriginalFilename();
            if(originalFilename!=null&&!"".equals(originalFilename)){
                int index = originalFilename.lastIndexOf(".");
                if(index > 0){
                    extensionName = originalFilename.substring(index, originalFilename.length());
                }
            }

            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH)+1;

            String ypath = physicalPath + "/" + year;
            String mpath = physicalPath + "/" + year+ "/" + month;

            //检查目录是否存在，存在就直接使用，不存在就创建目录
            dir = new File(ypath);
            if(!dir.exists()){
                dir = new File(mpath);
                dir.mkdirs();
            }else{
                dir = new File(mpath);
                if(!dir.exists()){
                    dir.mkdirs();
                }
            }

            //获取一个UUID来作为存入服务器中的文件的名字
            String filename = SecureUtil.simpleUUID();
            filename = filename+extensionName;
            try {
                //将文件转存到指定位置
                file.transferTo(new File(dir,filename));
            } catch (Exception e) {
                e.printStackTrace();
            }

            //将文件的服务器地址存到数据库
            String path = "/" + year+ "/" + month + "/" + filename;

            json.setCode(0);
            json.setMsg("upload files success!");
            json.setData(path);
        }

        return json;
    }
}
