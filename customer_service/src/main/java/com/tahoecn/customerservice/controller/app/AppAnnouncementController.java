package com.tahoecn.customerservice.controller.app;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsAnnouncement;
import com.tahoecn.customerservice.service.CsAnnouncementService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Api(tags = "移动端接口描述-公告", value = "移动端接口描述-公告")
@RestController
@RequestMapping(value = "/web/app/announcement")
public class AppAnnouncementController {
    
	@Autowired
	private CsAnnouncementService csAnnouncementService;
	
    @ApiOperation(value = "公告内容", notes = "公告内容")
	@RequestMapping(value = "/announcement", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage announcement() {
        try {
        	Wrapper<CsAnnouncement> wrapper = new EntityWrapper<CsAnnouncement>();
        	wrapper.where("state = {0}", "1");
        	List<CsAnnouncement> list = csAnnouncementService.selectList(wrapper);
        	if(list != null && list.size() != 0){
        		return ResponseMessage.ok(list.get(0));
        	}else{
        		return ResponseMessage.ok(null);
        	}
        } catch (Exception e) {
        	e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
}
