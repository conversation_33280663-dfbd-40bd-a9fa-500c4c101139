package com.tahoecn.customerservice.controller.app;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsDictItem;
import com.tahoecn.customerservice.model.CsSupplierInfo;
import com.tahoecn.customerservice.service.CsDictItemService;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsSupplierInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Api(tags = "移动端接口描述-基础数据", value = "移动端接口描述-基础数据")
@RestController
@RequestMapping(value = "/web/app/webapi/common")
public class AppCommonController {
	
    @Autowired
    private CsDictItemService csDictItemService;
    @Autowired
    private CsHouseInfoService csHouseInfoService;
    @Autowired
    private CsSupplierInfoService csSupplierInfoService;
    
    @ApiOperation(value = "数据字典", notes = "根据字典组编码查询所有字典项")
	@RequestMapping(value = "/dictItem", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage dictItem() {
        try {
        	Wrapper<CsDictItem> wrapper = new EntityWrapper<CsDictItem>();
        	List<CsDictItem> list = csDictItemService.selectList(wrapper);
        	List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
        	for(CsDictItem dict : list){
        		Map<String,Object> map = new HashMap<String,Object>();
        		map.put("dictCode", dict.getDictCode());
        		map.put("dictName", dict.getDictName());
        		map.put("itemCode", dict.getItemCode());
        		map.put("itemName", dict.getItemValue());
        		result.add(map);
        	}
            return ResponseMessage.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    
    @ApiOperation(value = "房屋信息检索", notes = "根据项目查询房屋列表，返回房屋编号")
    @ApiImplicitParams({ @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "String")})
    @RequestMapping(value = "/houseList", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseMessage houseList(String projectId) {
    	try {
    		if(StringUtils.isBlank(projectId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
    		//查询项目下的所有房屋列表
    		List<Map<String,Object>> list = csHouseInfoService.getHouseListByProjectId(projectId);
    		return ResponseMessage.ok(list);
    	} catch (Exception e) {
    		e.printStackTrace();
    		return ResponseMessage.error("系统错误，请联系管理员");
    	}
    }
    
    @ApiOperation(value = "房屋详情", notes = "根据房屋编码查询房屋详情，关联房屋对应的业主信息（默认取第一条）")
    @ApiImplicitParams({ @ApiImplicitParam(name = "houseNum", value = "房屋编码", dataType = "String")})
    @RequestMapping(value = "/houseDetail", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseMessage houseDetail(String houseNum) {
    	try {
    		if(StringUtils.isBlank(houseNum)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
    		//查询项目下的所有房屋列表
    		List<Map<String,Object>> list = csHouseInfoService.getHouseDetailByNum(houseNum);
    		
    		return ResponseMessage.ok(list == null || list.size() == 0 ? null : list.get(0));
    	} catch (Exception e) {
    		e.printStackTrace();
    		return ResponseMessage.error("系统错误，请联系管理员");
    	}
    }
    
    @ApiOperation(value = "主责单位，维修单位检索", notes = "主责单位检索，维修单位检索；根据工单区域")
    @ApiImplicitParams({ @ApiImplicitParam(name = "supplierAreas", value = "区域编码", dataType = "String"),
    	@ApiImplicitParam(name = "query", value = "查询条件", dataType = "String")})
    @RequestMapping(value = "/supplierInfo", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseMessage supplierInfo(String supplierAreas,String query) {
    	try {
    		if(StringUtils.isBlank(supplierAreas)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
    		Map<String,Object> map = new HashMap<String,Object>();
    		map.put("supplierAreas", supplierAreas);
    		map.put("query", query);
    		//根据区域查询主责单位，维修单位
    		List<CsSupplierInfo> list = csSupplierInfoService.getAppNameByArea(map);
            return ResponseMessage.ok(list);
    	} catch (Exception e) {
    		e.printStackTrace();
    		return ResponseMessage.error("系统错误，请联系管理员");
    	}
    }
    
    @ApiOperation(value = "获取版本号", notes = "获取版本号")
    @RequestMapping(value = "/versionNumber", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseMessage versionNumber() {
    	try {
    		Map<String,Object> map = new HashMap<String,Object>();
    		Wrapper<CsDictItem> wrapper = new EntityWrapper<CsDictItem>();
    		wrapper.where("dict_code = {0}", "version");
    		List<CsDictItem> list = csDictItemService.selectList(wrapper);
    		for(CsDictItem l : list){
    			map.put(l.getItemCode(), l.getItemValue());
    		}
            return ResponseMessage.ok(map);
    	} catch (Exception e) {
    		e.printStackTrace();
    		return ResponseMessage.error("系统错误，请联系管理员");
    	}
    }
}
