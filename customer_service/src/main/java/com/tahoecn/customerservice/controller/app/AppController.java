package com.tahoecn.customerservice.controller.app;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.tahoecn.core.util.JsonUtil;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.dto.ResponseDto;
import com.tahoecn.customerservice.model.dto.UserInfoDto;
import com.tahoecn.http.HttpClient;
import com.tahoecn.http.HttpUtil;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Api(tags = "移动端接口描述", value = "移动端接口描述")
@RestController
@RequestMapping(value = "/web/app/webapi")
public class AppController {

	@Value("${uc_api_url}")
	private String apiUrl;
	@Value("${uc_sysId}")
	private String sysId;
	@Value("${uc_priv_key}")
	private String privKey;

	private static final Log log = LogFactory.get();

	@ResponseBody
	@ApiOperation(value = "登陆接口", notes = "登陆接口")
	@RequestMapping(value = "/login", method = { RequestMethod.POST })
	public ResponseMessage demo(String keys) {

//		try {
//			String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
//			String token = SecureUtil.md5(timestamp + privKey);
//			keys = new String(Base64Utils.decodeFromString(keys), "UTF-8");
//			Map<String, String> paramMap = URLRequest(keys);
//			paramMap.put("pd", Base64Utils.encodeToString(paramMap.get("pd").getBytes("UTF-8")));
//
//			paramMap.put("sysId", sysId);
//			paramMap.put("token", token);
//			paramMap.put("timestamp", timestamp);
//			StringBuilder sb = new StringBuilder(apiUrl);
//			String url = sb.append("/v1/user/verify?").append(HttpUtil.mapToUrlParameter(paramMap)).toString();
//			log.info("login_url=" + url);
//			String result = HttpClient.httpGet(url);
//			log.info("login result=" + result);
//			if (StringUtils.isBlank(result)) {
//				return ResponseMessage.error("登陆失败");
//			}
//
////			ResponseDto<List<UserInfoDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
////			if (responseDto.getCode() == 0) {
////				paramMap.put("password", paramMap.get("pd"));
////				paramMap.remove("pd");
////				if(paramMap.get("userName") == null ) {
////					paramMap.put("userName", paramMap.get("username"));
////					paramMap.remove("username");
////				}
////				return ResponseMessage.ok(paramMap);
////			}
//			ResponseDto<List<UserInfoDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
//			if (responseDto.getCode() == 0) {
//				paramMap.put("password", paramMap.get("pd"));
//				paramMap.remove("pd");
//				if(paramMap.get("userName") == null ) {
//					paramMap.put("userName", paramMap.get("username"));
//					paramMap.remove("username");
//				}
//				sb = new StringBuilder(apiUrl);
//				url = sb.append("/v4/login/cookie").toString();
//				log.info("login_token_url=" + url);
//				result = HttpClient.httpPost(url, paramMap);
//				log.info("login_token result=" + result);
//				if (StringUtils.isBlank(result)) {
//					return ResponseMessage.error("登陆失败");
//				}
//				ResponseDto tokens = JsonUtil.convertJsonToBean(result, ResponseDto.class);
//				return ResponseMessage.ok(tokens.getResult());
//			}
//			return ResponseMessage.error("用户名密码错误");
//		} catch (UnsupportedEncodingException e) {
//			e.printStackTrace();
//		}
		return ResponseMessage.ok("登陆成功");
//		return ResponseMessage.error("登陆失败");
	}
	
	public static Map<String, String> URLRequest(String strUrlParam) {
		Map<String, String> mapRequest = new HashMap<String, String>();
		String[] arrSplit = null;
		if (strUrlParam == null) {
			return mapRequest;
		}
		// 每个键值为一组
		arrSplit = strUrlParam.split("[&]");
		for (String strSplit : arrSplit) {
			String[] arrSplitEqual = null;
			arrSplitEqual = strSplit.split("[=]");
			// 解析出键值
			if (arrSplitEqual.length > 1) {
				// 正确解析
				mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
			} else {
				if (arrSplitEqual[0] != "") {
					// 只有参数没有值，不加入
					mapRequest.put(arrSplitEqual[0], "");
				}
			}
		}
		return mapRequest;
	}

}
