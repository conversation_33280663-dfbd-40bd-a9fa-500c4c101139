package com.tahoecn.customerservice.controller.app;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.google.common.collect.Maps;
import com.tahoecn.customerservice.common.utils.NoteAttribute;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.controller.report.RepFormController;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.CsCustomerExtension;
import com.tahoecn.customerservice.model.CsFile;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.CsUpgradeCfgBak;
import com.tahoecn.customerservice.model.dto.AppFormListDto;
import com.tahoecn.customerservice.model.dto.CsFormInstDto;
import com.tahoecn.customerservice.model.dto.FamilyListDto;
import com.tahoecn.customerservice.model.vo.CompareVo;
import com.tahoecn.customerservice.service.CsCustInfoService;
import com.tahoecn.customerservice.service.CsCustomerExtensionService;
import com.tahoecn.customerservice.service.CsFileService;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsProjectInfoService;
import com.tahoecn.customerservice.service.CsSyncWyService;
import com.tahoecn.customerservice.service.CsUpgradeCfgService;
import com.tahoecn.customerservice.service.RolePrivInfoService;
import com.tahoecn.customerservice.service.impl.CsFormInstServiceImpl;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Api(tags = "移动端接口描述-报事接口", value = "移动端接口描述-报事相关")
@RestController
@RequestMapping(value = "/web/app/webapi/form")
public class AppFormController {
	private static final Log log = LogFactory.get();
	@Autowired
	private RepFormController repFormController;
    @Autowired
    private CsFormInstService csFormInstService;
    @Autowired
    private CsFormInstServiceImpl csFormInstServiceImpl;
    @Autowired
    private CsProcessWorkitemService csProcessWorkitemService;
    @Autowired
    private CsFileService csFileService;
    @Autowired
    private CsProjectInfoService csProjectInfoService;
    @Autowired
    private CsCustInfoService csCustInfoService;
    @Autowired
    private CsHouseInfoService csHouseInfoService;
    @Autowired
    private CsUpgradeCfgService csUpgradeCfgService;
    @Autowired
	RolePrivInfoService rolePrivInfoService;
    @Autowired
    private CsCustomerExtensionService csCustomerExtensionService;
    @Autowired
    private CsSyncWyService csSyncWyService;
    
    @ApiOperation(value = "报事查询列表", notes = "报事查询列表:toBeAssigned:分派;handle:处理;upgrade:升级;deptDC:报事查询;grab抢单")
    @ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
		@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
		@ApiImplicitParam(name = "type", value = "工单类型", dataType = "String"),
		@ApiImplicitParam(name = "query", value = "模糊查询条件", dataType = "String"),
		@ApiImplicitParam(name = "sort", value = "最后更新时间排序，desc，asc", dataType = "String",defaultValue="desc"),
		@ApiImplicitParam(name = "create", value = "创建时间排序，desc，asc", dataType = "String",defaultValue="desc"),
		@ApiImplicitParam(name = "createStart", value = "报事查询列表创建时间start", dataType = "String"),
		@ApiImplicitParam(name = "createEnd", value = "报事查询列表创建时间end", dataType = "String"),
		@ApiImplicitParam(name = "custId", value = "业主id", dataType = "String")})
	@RequestMapping(value = "/getFormList", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage getFormList(@RequestParam(defaultValue="1")Integer pageNum,
    		@RequestParam(defaultValue="20")Integer pageSize,
    		String type, String query, String sort, String create,String createStart,
    		String createEnd,String custId) {
    	Map<String, Object> map = new HashMap<>();
		try {
			//1.判断参数完整性
			if(StringUtils.isBlank(type)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			//是否是400坐席
			Integer isSiBaiSeats = 0;
			CsCustomerExtension csCustomerExtension = csCustomerExtensionService.getLogin();
			if(csCustomerExtension != null){
				if("1".equals(csCustomerExtension.getFlag()) || 1 == csCustomerExtension.getFlag()) {
					isSiBaiSeats = 1;
				}
			}
	        //获取是否是管理员  0:不是  1：是
	        Integer isAdmin = rolePrivInfoService.isAdmin();
			
			map.put("curAssigneeId", ThreadLocalUtils.getUserName());//当前登录人
			map.put("type",type);//工单类型
			map.put("query",query);//模糊查询条件
			map.put("sort",sort);//最后更新时间排序
			map.put("create", create);//创建时间排序,只有报事查询存在该字段
			map.put("count", (pageNum - 1) * pageSize);
			map.put("pageSize", pageSize);
			map.put("createStart", createStart);//报事查询列表创建时间start
			map.put("createEnd", createEnd);//报事查询列表创建时间end
			map.put("isAdmin", isAdmin);//是否是管理员
            map.put("isSiBaiSeats", isSiBaiSeats);//是否是400坐席
            map.put("custId", custId);//是否是400坐席
			List<CsFormInst> list = new ArrayList<CsFormInst>();
			//2.根据列表类型，查询数据
			//分派，处理：流程节点：当前登录人+process_state_code=toBeAssigned分派;process_state_code=handle处理;
			//升级：upgrade_flag=1升级+流程表process_state_code='upgrade'+流程表assign_id=登录人
			//报事查询：dept_code = 'deptDC'查询地产+用户权限限制
			if("toBeAssigned".equals(type) || "handle".equals(type)){//报事分派，报事处理
				list = csFormInstService.getAssAndHandleList(map);
			}else if("upgrade".equals(type)){//报事升级
				list = csFormInstService.getUpgradeFormList(map);
			}else if("deptDC".equals(type)){//报事查询
				//报事查询权限限制
				if (repFormController.setUserDataPriv(map)) {
					return ResponseMessage.ok(list);
				}
				list = csFormInstService.getDeptDCFormList(map);
			}else if("grab".equals(type)){//报事抢单
				list = csFormInstService.getGrabFormList(map);
			}else{
				return ResponseMessage.error("type参数错误，请检查参数信息");
			}
			//格式化一级分类等数据
			List<AppFormListDto> result = new ArrayList<AppFormListDto>();
			if(list != null  && list.size() > 0){
				result = formateData(list);
			}
			return ResponseMessage.ok(result);
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
    }
    
    /**
     * 格式化数据
     * @param list
     * @return
     */
    private List<AppFormListDto> formateData(List<CsFormInst> list) {
    	List<AppFormListDto> result = new ArrayList<AppFormListDto>();
		for(CsFormInst l : list){
			AppFormListDto dto = new AppFormListDto();
			dto.setFormId(l.getId().toString());
			dto.setFormNo(l.getFormNo());
			String classification = "";
			if(l.getFirstSortName() != null && !"".equals(l.getFirstSortName())){
				classification = classification + l.getFirstSortName();
			}
			if(l.getSecSortName() != null && !"".equals(l.getSecSortName())){
				classification = classification + "-" + l.getSecSortName();
			}
			if(l.getThirdSortName() != null && !"".equals(l.getThirdSortName())){
				classification = classification + "-" + l.getThirdSortName();
			}
			if(l.getFourthSortName() != null && !"".equals(l.getFourthSortName())){
				classification = classification + "-" + l.getFourthSortName();
			}

			dto.setClassification(classification);
			
	        DateFormat bf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//多态

	        if(l.getSubmitDate()!= null)
	        	dto.setSubmitDate(bf.format(l.getSubmitDate()));
			dto.setIsGrab(l.getIsGrab());
			dto.setUpgradeFlag((l.getUpgradeFlag() == null || "".equals(l.getUpgradeFlag())) ? null:l.getUpgradeFlag().toString());
			dto.setRejectFlag((l.getRejectFlag() == null || "".equals(l.getRejectFlag())) ? null:l.getRejectFlag().toString());
			dto.setReworkFlag((l.getReworkFlag() == null || "".equals(l.getReworkFlag())) ? null:l.getReworkFlag().toString());
			dto.setMobile(l.getMobile());
			dto.setAssignName(l.getAssignName());
			dto.setCurAssigneeName(l.getCurAssigneeName());
			dto.setCreationDate(bf.format(l.getCreationDate()));
			String project = "";
			String houseInfo = "";
			if(l.getRegion() != null && !"".equals(l.getRegion())){
				project = project + l.getRegion();
				houseInfo = houseInfo + l.getRegion();
			}
			if(l.getCity() != null && !"".equals(l.getCity())){
				project = project + "-" + l.getCity();
				houseInfo = houseInfo + "-" + l.getCity();
			}
			if(l.getProject() != null && !"".equals(l.getProject())){
				project = project + "-" + l.getProject();
				houseInfo = houseInfo + "-" + l.getProject();
			}
			if(l.getBuildingNo() != null && !"".equals(l.getBuildingNo())){
				houseInfo = houseInfo + "-" + l.getBuildingNo();
			}
			if(l.getBuildingUnit() != null && !"".equals(l.getBuildingUnit())){
				houseInfo = houseInfo + "-" + l.getBuildingUnit();
			}
			if(l.getRoomNo() != null && !"".equals(l.getRoomNo())){
				houseInfo = houseInfo + "-" + l.getRoomNo();
			}
			dto.setProject(project);
			dto.setHouseInfo(houseInfo);
			dto.setFormUserName(l.getFormUserName());
			dto.setCustomerDemand(l.getCustomerDemand());
			result.add(dto);
		}
		return result;
	}

	@ApiOperation(value = "工单详情", notes = "根据工单id查询工单详情：基础信息，处理记录，附件")
    @ApiImplicitParams({ @ApiImplicitParam(name = "formId", value = "工单id", dataType = "String")})
	@RequestMapping(value = "/getFormInfo", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage getFormInfo(String formId) {
    	Map<String, Object> map = new HashMap<>();
		try {
			//1.判断参数完整性
			if(StringUtils.isBlank(formId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			if(StringUtils.isNotBlank(formId)) {
				CsFormInst csFormInst = csFormInstService.selectById(formId);
				List<CsProcessWorkitem> processWorkitem = csProcessWorkitemService.getWorkItemList(formId);
				List<CsFile> file = csFileService.selectFileByFormNo(formId);
				CsFormInstDto dto = new CsFormInstDto();
				BeanUtils.copyProperties(csFormInst, dto);
				dto.setFormId(dto.getId()+"");
				map.put("formInst", dto);//基本信息

				map.put("processWorkitem", processWorkitem);//流程信息
				map.put("file", file);//附件
				map.put("countdown", countdown(csFormInst));//剩余处理时间
			}
			return ResponseMessage.ok(map);
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
    }
    
	/**
	 * 处理剩余时间
	 * 投诉类查区域天数，报修查城市天数
	 * @param csFormInst
	 * @return
	 * @throws ParseException 
	 */
    private String countdown(CsFormInst csFormInst) throws ParseException {
    	Date submitDate = csFormInst.getSubmitDate();
    	SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    	CsUpgradeCfgBak upgrade = csUpgradeCfgService.selDayByFormId(csFormInst.getId().toString());
    	if(upgrade != null){
    		Long cityDay = upgrade.getCityDays();
    		Long regionDay = upgrade.getRegionDays();
    		String type = upgrade.getFirstSortCode();
    		Calendar ca = Calendar.getInstance();
    		ca.setTime(submitDate);
    		int day = 0;
    		if("coTS".equals(type)){
    			day = (regionDay == null || "".equals(regionDay)) ? 0 : regionDay.intValue();
    		}else if("coBX".equals(type)){
    			day = (cityDay == null || "".equals(cityDay)) ? 0 : cityDay.intValue();
    		}
    		if(day > 0){
    			ca.add(Calendar.DATE, day);// num为增加的天数，可以改变的
    			submitDate = ca.getTime();
    			return format.format(submitDate);
    		}else{//升级天数为空
    			return null;
    		}
    	}else{//没有配置升级
    		return null;
    	}
	}

	@ApiOperation(value = "附件上传", notes = "根据工单id上传附件，文件流")
	@RequestMapping(value = "/uploadFile", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage uploadFile(@RequestParam("files") List<MultipartFile> files, String formId, String type) {
		try {
			//1.判断参数完整性
			if(StringUtils.isBlank(formId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			//3.上传文件到服务器
			List<Long> list = new ArrayList<>();
			for (MultipartFile file : files) {
				if (file.isEmpty()) {
					continue;
				}
				Long id = csFileService.saveUploadFile(file, type);
				list.add(id);
			}
			//4.关联到报账单
			List<CsFile> result = null;
			if ((list != null && list.size() > 0) && (formId != null)) {
				Long row = csFileService.relationFormAndFiles(Long.parseLong(formId), list);
				if (row > 0) {
					//5.返回信息
					result = csFileService.selectCsFileByIdList(list);
					return ResponseMessage.ok(result);
				}
				return ResponseMessage.error("系统错误，附件丢失");
			}
			return ResponseMessage.ok(result);
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
    }
    
    @ApiOperation(value = "工单保存修改", notes = "房屋编号(房屋信息)+业主id(业主信息)+报事信息(工单基本信息)=工单，没有流程只保存工单信息")
	@RequestMapping(value = "/saveFormInfo", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage saveFormInfo(CsFormInstDto dto) {
		try {
			CsFormInst formInfo = new CsFormInst();
			BeanUtils.copyProperties(dto, formInfo);
			log.info(formInfo.toString());
			CompareVo saveBefore = null;
			if(StringUtils.isBlank(dto.getFormId())) {
				dto.setOperateType("draft");
				if(StringUtils.isBlank(formInfo.getDeptCode())
						|| StringUtils.isBlank(formInfo.getFirstSortCode())
						|| StringUtils.isBlank(formInfo.getProjectCode())
							) {
					return ResponseMessage.error("参数录入不完整，请检查参数信息");
				}
				// 过滤物业数据
				if("deptWY".equals(formInfo.getDeptCode())) {
					Map<String, Object> map = Maps.newHashMap();
					if(!"1".equals(formInfo.getPublicArea())) {
						if(formInfo.getOwnerId() == null || "".equals(formInfo.getOwnerId())){
							return ResponseMessage.error("业主未选择，请在筛选界面选择业主后进行报事录入！");
						}
						if(formInfo.getHouseInfoId() == null || "".equals(formInfo.getHouseInfoId())){
							return ResponseMessage.error("房屋未选择，请在筛选界面选择房屋后进行报事录入！");
						}
						if(!csSyncWyService.checkCust(formInfo.getProjectCode(), formInfo.getOwnerId())) {
							return ResponseMessage.error("业主不属于物业，请检查录入信息");
						}
						if(!csSyncWyService.checkHouse(formInfo.getProjectCode(), formInfo.getHouseInfoId())) {
							return ResponseMessage.error("房屋不属于物业，请检查录入信息");
						}
					}
					map = Maps.newHashMap();
					map.put("project_code", formInfo.getProjectCode());
					map.put("project_source", "1");
					if(csProjectInfoService.selectByMap(map).size() == 0) {
						return ResponseMessage.error("项目不属于物业，请检查录入信息");
					}

					//1工单编号
		            if (StringUtils.isBlank(formInfo.getFormNo())) {
						formInfo.setProcessCode("processBZ");
						formInfo.setProcessName("标准流程");
		            	formInfo.setFormNo(csFormInstServiceImpl.getNo(formInfo.getDeptCode()));
		            }
				}			
				//1.存储业主信息
				formInfo = ownerInfo(formInfo,formInfo.getOwnerId());
				//2.项目信息
				formInfo = projectInfo(formInfo,formInfo.getProjectCode());
				//3.房屋信息
				formInfo = houseInfo(formInfo,formInfo.getHouseNo());

				//4.2默认值
				formInfo.setReportChannelCode("channelAPP");
				formInfo.setReportChannelName("APP报事");
				formInfo.setAcceptChannelCode("项目");
				formInfo.setAcceptChannelName("项目");
			} else {
				formInfo.setId(Long.valueOf(dto.getFormId()));
				saveBefore = csFormInstService.getCompareField(Long.valueOf(dto.getFormId()));
			}
			
			FamilyListDto familyListDto = new FamilyListDto();
			familyListDto.setCsFormInst(formInfo);
			//生成工单信息
			Long id = csFormInstService.saveOrUpate(familyListDto);
			
			formInfo = csFormInstService.selectById(id);
			
			CompareVo saveAfter = csFormInstService.getCompareField(id);
			String text = this.compareObj(saveBefore,saveAfter,dto.getOperateType());
			if("draft".equals(dto.getOperateType())){
				familyListDto.setOperateType("draft");
				familyListDto.setComment("暂存");
			}else {
				familyListDto.setComment(familyListDto.getCsFormInst().getHandleRecord() + text);
			}
			CsFormInst form = familyListDto.getCsFormInst();
			if(!"".equals(text)){
				form.setChangeRecord(text);
				csFormInstService.updateById(form);
			}


			Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
			wrapper.where("form_inst_id={0}",id).and("process_state_code={0}","draft").and("task_status={0}",new Long(20));
			CsProcessWorkitem item = csProcessWorkitemService.selectOne(wrapper);
			if(item == null && !"deptWY".equals(familyListDto.getCsFormInst().getDeptCode()) && !"processKSCL".equals(familyListDto.getCsFormInst().getProcessCode()) && !"submit".equals(dto.getOperateType())) {
				csProcessWorkitemService.addWorkitem(id, familyListDto.getComment(), dto.getOperateType(),form);
			}
			return ResponseMessage.ok(id.toString());
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
    }
    
    private String compareObj(CompareVo obj1, CompareVo obj2,String operateType) {

		if(obj1==null || obj2 ==null)
			return "";

		StringBuffer text = new StringBuffer();
		try {
			Class clazz = obj1.getClass();
			Field[] fields = obj1.getClass().getDeclaredFields();
			for (Field field : fields) {
				PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
				Method getMethod = pd.getReadMethod();
				Object o1 = getMethod.invoke(obj1);
				Object o2 = getMethod.invoke(obj2);
				if ((o1 == null || o2 == null || o1 instanceof String == false || o2 instanceof String == false)
						&& ("draft").equals(operateType))
					continue;
				String s1 = o1 == null ? "" : o1.toString();
				String s2 = o2 == null ? "" : o2.toString();
				if (!s1.equals(s2)) {
					if (s1.equals("-1"))
						s1 = "否";
					if (s1.equals("1"))
						s1 = "是";
					if (s2.equals("-1"))
						s2 = "否";
					if (s2.equals("1"))
						s2 = "是";

					text.append("<br>变更：" + field.getAnnotation(NoteAttribute.class).name() + "  " + s1 + " > " + s2);
				}
			}
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return text.toString();
	}
    
    /**
     * 房屋信息
     * @param formInfo
     * @param houseNo
     * @return
     */
    private CsFormInst houseInfo(CsFormInst formInfo, String houseNo) {
    	//1.根据房屋编码查询房屋信息
    	CsHouseInfo houstInfo = csHouseInfoService.selectAllHoustInfoBy(houseNo);
    	if(houstInfo != null ){
    		formInfo.setHouseNo(houstInfo.getHouseNum());// 房屋编号
    		formInfo.setHouseName(houstInfo.getHouseName());// 房屋名称
    		formInfo.setBuildingNo(houstInfo.getBuilding());// 楼栋
    		formInfo.setBuildingUnit(houstInfo.getUnit());// 单元号
    		formInfo.setRoomNo(houstInfo.getRoomNum());// 房间号
    		formInfo.setHouseInfoId(houstInfo.getHouseNum());
    		// 使用性质
    		// 1 车位-无产权车位 2 住宅-叠拼 3 住宅-合院 4 写字楼-LOFT 5 住宅-高层
    		String useProperty = houstInfo.getUseProperty();
    		String up = "";
    		if (useProperty != null && useProperty.equals("")) {
    		    if (useProperty.equals("车位-无产权车位")) {
    		        up = "1";
    		    } else if (useProperty.equals("住宅-叠拼")) {
    		        up = "2";
    		    } else if (useProperty.equals("住宅-合院")) {
    		        up = "3";
    		    } else if (useProperty.equals("写字楼-LOFT")) {
    		        up = "4";
    		    } else if (useProperty.equals("住宅-高层")) {
    		        up = "5";
    		    }
    		} else {
    			formInfo.setUsePropertyCode("");
    		}
    		formInfo.setUsePropertyCode(up);
    		// 使用性质名称
    		formInfo.setUsePropertyName(houstInfo.getUseProperty());
    		// 合同交房时间
    		formInfo.setContractDeliveryTime(csFormInstServiceImpl.getDate(houstInfo.getDeliveryDate()));
    		// 签约时间
    		formInfo.setSigningTime(csFormInstServiceImpl.getDate(houstInfo.getSignDate()));
    		// 集中交房时间从
    		formInfo.setFocusDeliveryTimeFrom(houstInfo.getFocusStartDate());
    		// 集中交房时间到
    		formInfo.setFocusDeliveryTimeTo(houstInfo.getFocusEndDate());
    		// 交付状态1:已交付 -1未交付
    		Integer deliveryStatus = houstInfo.getDeliveryStatus();
    		if (deliveryStatus != null) {
    			formInfo.setDeliveryState(deliveryStatus.equals(1) ? "已交付" : "未交付");
    		} else {
    			formInfo.setDeliveryState(null);
    		}
    		// 是否精装1:是 -1：否
    		String fitment = houstInfo.getFitment();
    		if (fitment == null) {
    		    fitment = "";
    		}
    		if (fitment.equals("精装修")) {
    			formInfo.setHardcoverState("1");
    		} else {
    			formInfo.setHardcoverState("-1");
    		}
    		// 实际交房时间
    		formInfo.setActualDeliverTime(houstInfo.getActualDeliveryDate());
    		// 入住时间
    		formInfo.setCheckInTime(houstInfo.getStayTime());
    		// 预计脱保时间
    		formInfo.setEstimatedReleaseTime(houstInfo.getOffAidDate());
    	}
		return formInfo;
	}
    /**
     * 业主信息
     * @param csformInst
     * @param ownerId
     * @return
     */
	private CsFormInst ownerInfo(CsFormInst csformInst, String ownerId){
		// 证件类型名称
        csformInst.setIdName("身份证");
		List<CsCustInfo> csCustInfos = csCustInfoService.selectByCustId(ownerId);
		if(csCustInfos != null && csCustInfos.size() != 0){
			CsCustInfo csCustInfo = csCustInfos.get(0);
        // 其他版块会员-1：否，1：是
        String otherBoardMember = csCustInfo.getOtherBoardMember();
        if (otherBoardMember != null && otherBoardMember.equals("")) {
            int obm = otherBoardMember.equals("是") ? 1 : -1;
            csformInst.setOrtherMember(obm);
        } else {
        	csformInst.setOrtherMember(null);
        }
        // 特殊客户-1：否，1：是
        String specialCustomer = csCustInfo.getSpecialCustomer();
        if (specialCustomer != null && specialCustomer.equals("")) {
            int sc = specialCustomer.equals("是") ? 1 : -1;
            csformInst.setSpecialUser(sc);
        } else {
        	csformInst.setSpecialUser(null);
        }
        // 国籍
        String national = csCustInfo.getNational();
        csformInst.setNationality(national);
        // 证件类型编码 1 身份证 2 护照
        csformInst.setIdCode("");
        // 证件类型名称
        csformInst.setIdName("身份证");
        // 出生日期
        String birthday = csCustInfo.getBirthday();
        csformInst.setBirthDate(csFormInstServiceImpl.getDate(birthday));
        // 工作单位
        csformInst.setWorkUnit(csCustInfo.getWorkUnit());
        // 职业
        csformInst.setOccupation(csCustInfo.getProfession());
        // 爱好
        csformInst.setHobby(csCustInfo.getHobbies());
        // 传真电话
        csformInst.setFaxPhone(csCustInfo.getFax());
        // 联系地址
        csformInst.setContactAddress(csCustInfo.getContactAddress());
        // 固定电话
        csformInst.setFixedTelephone(csCustInfo.getFixedTelephone());
        // 电子邮件
        csformInst.seteMail(csCustInfo.getEmail());
        // 邮政编码
        csformInst.setPostalCode(csCustInfo.getPostcode());
        // 管家姓名
        csformInst.setHousekeeperName(csCustInfo.getStewardName());
        // 管家电话
        csformInst.setHousekeeperTel(csCustInfo.getStewardTelephone());
		}
        return csformInst;
    }
	/**
	 * 项目信息
	 * @param csformInst
	 * @param projectId
	 * @return
	 */
	private CsFormInst projectInfo(CsFormInst csformInst,String projectId){
		//1.根据项目查询项目信息
		CsProjectInfo csProjectInfo = csProjectInfoService.selectAllProjectByProjectCode(projectId);
		if(csProjectInfo != null){
			 // 区域编码
			csformInst.setRegionCode(csProjectInfo.getRegionCode());
		    // 区域
			csformInst.setRegion(csProjectInfo.getRegion());
		    // 城市编码
			csformInst.setCityCode(csProjectInfo.getCityCompanyCode());
		    // 城市
			csformInst.setCity(csProjectInfo.getCityCompany());
			// 项目
			csformInst.setProject(csProjectInfo.getProject());
		}
		return csformInst;
	}
	
	@ApiOperation(value = "附件删除", notes = "根据附件id删除附件")
    @ApiImplicitParams({ @ApiImplicitParam(name = "id", value = "附件id", dataType = "String")})
	@RequestMapping(value = "/deleteFile", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage deleteFile(String id) {
		try {
			//1.判断参数完整性
			if(StringUtils.isBlank(id)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			csFileService.deleteCsFile(null, Long.parseLong(id));
			return ResponseMessage.ok("删除成功");
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
    }
}
