package com.tahoecn.customerservice.controller.app;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsMessage;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsMessageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Api(tags = "移动端接口描述-消息记录", value = "移动端接口描述-消息记录")
@RestController
@RequestMapping(value = "/web/app/message")
public class AppMessageController {
    
	@Autowired
	private CsMessageService csMessageService;
	@Autowired
	private CsFormInstService csFormInstService;
	
    @ApiOperation(value = "消息列表", notes = "消息列表")
    @ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
		@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
		@ApiImplicitParam(name = "state", value = "已读未读状态0已读1未读", dataType = "String")})
	@RequestMapping(value = "/messageList", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage messageList(String state,@RequestParam(defaultValue="1")Integer pageNum, 
    		@RequestParam(defaultValue="20")Integer pageSize) {
        try {
        	Map<String, Object> map = new HashMap<>();
			map.put("loginName", ThreadLocalUtils.getUserName());
			map.put("state", state);
			map.put("count", (pageNum - 1) * pageSize);
			map.put("pageSize", pageSize);
        	return ResponseMessage.ok(csMessageService.getMessageListMg(map));
        } catch (Exception e) {
        	e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    
    @ApiOperation(value = "设置APP待办消息为已读", notes = "设置APP待办消息为已读")
    @ApiImplicitParams({@ApiImplicitParam(name = "messId", value = "消息id", dataType = "String")})
	@RequestMapping(value = "/setMessRead", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage setMessRead(String messId) {
        try {
        	//1.判断参数完整性
			if(StringUtils.isBlank(messId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
        	csMessageService.setMessRead(messId);
        	return ResponseMessage.ok("设置成功");
        } catch (Exception e) {
        	e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    
    @ApiOperation(value = "消息,代办数量", notes = "未读消息条数,分派/处理/升级列表数量")
	@RequestMapping(value = "/unReadMessageCount", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage unReadMessageCount() {
        try {
        	Map<String,Object> map = new HashMap<String,Object>();
        	//1.未读消息条数
        	Wrapper<CsMessage> messWrapper = new EntityWrapper<CsMessage>();
        	messWrapper.where("state = {0}", "1").and("login_name = {0}", ThreadLocalUtils.getUserName());
        	int messageCount = csMessageService.selectCount(messWrapper);
        	//2.分派列表数量
        	Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        	formWrapper.where("process_state_code = {0}", "toBeAssigned")
        	.and("dept_code != {0}","deptIT")
        	.and("cur_assignee_id = {0}", ThreadLocalUtils.getUserName());
        	int toBeAssignedCount = csFormInstService.selectCount(formWrapper);
        	//3.处理列表数量
        	Wrapper<CsFormInst> handleWrapper = new EntityWrapper<CsFormInst>();
        	handleWrapper.where("process_state_code = {0}", "handle")
        	.and("dept_code != {0}","deptIT")
        	.and("cur_assignee_id = {0}", ThreadLocalUtils.getUserName());
        	int handleCount = csFormInstService.selectCount(handleWrapper);
        	//4.升级列表数量
        	int upgradeCount = csFormInstService.selectUpgradeCount(ThreadLocalUtils.getUserName());
        	//5.抢单列表数量
        	int grabCount = csFormInstService.selectGrabCount(ThreadLocalUtils.getUserName());
        	map.put("messageCount", messageCount);
        	map.put("toBeAssignedCount", toBeAssignedCount);
        	map.put("handleCount", handleCount);
        	map.put("upgradeCount", upgradeCount);
        	map.put("grabCount", grabCount);
        	return ResponseMessage.ok(map);
        } catch (Exception e) {
        	e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
}
