package com.tahoecn.customerservice.controller.app;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.ProcessService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Api(tags = "移动端接口描述-流程处理 处理接口", value = "移动端接口描述-流程处理 处理接口")
@Controller
@RequestMapping("/web/app/process")
public class AppProcessController {

	@Resource(name = "qConsultationService")
	ProcessService qConsultationService;

	@Resource(name = "qPraiseAndSuggestionService")
	ProcessService qPraiseAndSuggestionService;

	@Resource(name = "sConsultationService")
	ProcessService sConsultationService;

	@Resource(name = "sComplaintService")
	ProcessService sComplaintService;

	@Resource(name = "sRepairService")
	ProcessService sRepairService;

	@Resource(name = "wyProcessService")
	ProcessService wyProcessService;

	@Resource(name = "processService")
	private ProcessService processService;
	@Autowired
	private CsFormInstService csFormInstService;
	@Autowired
	RedisTemplate redisTemplate;

	/**
	 * 根据类型判断走向 流程类别 一级分类 快速处理流程 咨询 快速处理流程 表扬与建议 标准流程 咨询 标准流程 投诉 标准流程 报修
	 *
	 * @return
	 */
	@ApiOperation(value = "工单提交", notes = "工单提交")
	@RequestMapping(value = "/submit", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage submit(String formInstId, String comment, String assignUserId, String assignUserName,
			String mobile, String operateType) throws Exception {
		Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
		formWrapper.where("id={0}", formInstId);
		CsFormInst csFormInst = csFormInstService.selectOne(formWrapper);
		if (csFormInst == null) {
			return ResponseMessage.error("表单不存在");
		}
		String processCode = csFormInst.getProcessCode();
		String firstSortCode = csFormInst.getFirstSortCode();
		String deptCode = csFormInst.getDeptCode();
		String processStateCode = csFormInst.getProcessStateCode();
		String handleRecord = csFormInst.getHandleRecord();
		ProcessService processService = this.getService(csFormInst.getDeptCode(), processCode, firstSortCode);
		processService.process(csFormInst, formInstId, firstSortCode, deptCode, processStateCode, handleRecord, comment,
				assignUserId, assignUserName, mobile, operateType);
		if (operateType != null && !"".equals(operateType) && operateType.equals("submit")) {
			return ResponseMessage.okm("提交成功");
		} else {
			return ResponseMessage.okm("退回成功");
		}
	}

	@SuppressWarnings("unchecked")
	@ApiOperation(value = "工单抢单", notes = "工单抢单")
	@RequestMapping(value = "/rushForm", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage rushForm(String formInstId) throws Exception {
		Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
		formWrapper.where("id={0}", formInstId);
		CsFormInst csFormInst = csFormInstService.selectOne(formWrapper);
		if (csFormInst == null) {
			return ResponseMessage.error("表单不存在");
		}
		String processCode = csFormInst.getProcessCode();
		String firstSortCode = csFormInst.getFirstSortCode();
		String deptCode = csFormInst.getDeptCode();
		String processStateCode = csFormInst.getProcessStateCode();
		String handleRecord = csFormInst.getHandleRecord();
		Object object = redisTemplate.opsForValue().get("rushForm_" + formInstId);
		if(object == null && "toBeAssigned".equals(processStateCode)) {
			redisTemplate.opsForValue().set("rushForm_" + formInstId, formInstId);
			try {
				ProcessService processService = this.getService(csFormInst.getDeptCode(), processCode, firstSortCode);
				processService.process(csFormInst, formInstId, firstSortCode, deptCode, processStateCode, handleRecord,
						"由" + ThreadLocalUtils.getRealName() + "抢单", ThreadLocalUtils.getUserName(),
						ThreadLocalUtils.getRealName(), ThreadLocalUtils.get().getFdTel(), "submit");

				CsFormInst inst = new CsFormInst();
				inst.setId(csFormInst.getId());
				inst.setIsGrab("1");
				csFormInstService.updateById(inst);
			} catch (Exception e) {
				return ResponseMessage.error("系统异常，请联系管理员处理");
			} finally {
				redisTemplate.delete("rushForm_" + formInstId);
			}
			return ResponseMessage.okm("提交成功");
		}
		return ResponseMessage.error("单子已经被抢走");
	}

	@ApiOperation(value = "工单升级", notes = "工单升级")
	@RequestMapping(value = "/upgrade", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage upgrade(String formInstId) throws Exception {
		Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
		formWrapper.where("id={0}", formInstId);
		CsFormInst csFormInst = csFormInstService.selectOne(formWrapper);
		if (csFormInst == null) {
			return ResponseMessage.error("表单不存在");
		}
		String processCode = csFormInst.getProcessCode();
		String firstSortCode = csFormInst.getFirstSortCode();
		ProcessService processService = this.getService(csFormInst.getDeptCode(), processCode, firstSortCode);
		try {
			processService.updrade(csFormInst);
			return ResponseMessage.okm("成功");
		} catch (Exception e) {
			return ResponseMessage.error("没有升级人员");
		}
	}

	@ApiOperation(value = "工单特殊关闭", notes = "工单特殊关闭")
	@RequestMapping(value = "/specialEnd", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage specialEnd(String formInstId) {
		processService.specialEnd(formInstId);
		return ResponseMessage.okm("成功");
	}

	/**
	 * 获取处理/分派人列表
	 * 
	 * @param formInstId
	 * @param operateType
	 * @return
	 */
	@ApiOperation(value = "获取处理/分派人列表", notes = "获取处理/分派人列表")
	@RequestMapping(value = "/getTaskUser", method = RequestMethod.POST)
	@ResponseBody
	public ResponseMessage getTaskUser(String formInstId, String operateType) {
		CsFormInst csFormInst = csFormInstService.selectById(formInstId);
		if (csFormInst == null) {
			return ResponseMessage.error("表单不存在");
		}
		ProcessService processService = this.getService(csFormInst.getDeptCode(), csFormInst.getProcessCode(),
				csFormInst.getFirstSortCode());
		List<?> userList = processService.getUserByRole(operateType, new Long(formInstId.split(",")[0]));
		return ResponseMessage.ok(userList);
	}

	public ProcessService getService(String deptCode, String processCode, String firstsortCode) {
		if (processCode.equals("processKSCL")) {// 快速流程
			if (firstsortCode.equals("coZX")) {// 咨询
				return this.qConsultationService;
			} else if (firstsortCode.equals("coJYBY")) {// 建议表扬
				return this.qPraiseAndSuggestionService;
			}
		} else if (processCode.equals("processBZ")) {
			if ("deptWY".equals(deptCode)) {
				return this.wyProcessService;
			}
			if (firstsortCode.equals("coZX")) {// 咨询
				return this.sConsultationService;
			} else if (firstsortCode.equals("coTS")) {// 投诉
				return this.sComplaintService;
			} else if (firstsortCode.equals("coBX")) {// 报修
				return this.sRepairService;
			}
		}
		return this.processService;
	}
}
