package com.tahoecn.customerservice.controller.app;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.dto.AppCustInfoDto;
import com.tahoecn.customerservice.model.dto.CsLabelTreeDto;
import com.tahoecn.customerservice.model.dto.MenuDO;
import com.tahoecn.customerservice.service.CsCustInfoService;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsLabelService;
import com.tahoecn.customerservice.service.CsProjectInfoService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.impl.CsMenuCfgServiceImpl;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Api(tags = "移动端接口描述-用户信息", value = "移动端接口描述-用户信息")
@RestController
@RequestMapping(value = "/web/app/webapi/user")
public class AppUserController {
	@Value("${root_path}")
    private String rootPath;
    @Autowired
    private CsMenuCfgServiceImpl csMenuCfgService;
    @Autowired
    private CsCustInfoService csCustInfoService;
    @Autowired
    private CsHouseInfoService csHouseInfoService;
    @Autowired
    private CsLabelService csLabelService;
    @Autowired
    private CsProjectInfoService csProjectInfoService;
    @Autowired
    private RedisTemplate redisTemplate;
	@Autowired
	CsUcUserService csUcUserService;
    
    @ApiOperation(value = "用户个人信息", notes = "获取用户的个人信息")
	@RequestMapping(value = "/ucUserInfo", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage ucUserInfo() {
        try {
        	String loginName = "admin";
    		if (StringUtils.isNotBlank(loginName)) {
    			CsUcUser csUcUser = (CsUcUser) redisTemplate.opsForValue().get(loginName);
    			if (csUcUser == null) {
    				csUcUser = csUcUserService.selectByUsername(loginName);
    				redisTemplate.opsForValue().set(loginName, csUcUser, 1, TimeUnit.DAYS);
    			}
    			ThreadLocalUtils.setUser(csUcUser);
    		}
        	Object object = redisTemplate.opsForValue().get("userInfo_"+ThreadLocalUtils.getUserName());
        	if(object == null) {
        		String menuCodes = "";
            	//获取用户权限菜单
            	List<MenuDO> menuDOs = csMenuCfgService.listMenuByUser();
            	for(MenuDO menuDO : menuDOs){
            		menuCodes = menuCodes + menuDO.getMenuCode() + ",";
            	}
            	HashMap<String,Object> resultMap = new HashMap<String,Object>();
            	resultMap.put("userInfo", ThreadLocalUtils.get());//用户信息
            	resultMap.put("menuCodes",menuCodes);//用户菜单id
//            	resultMap.put("menuCodes","cs_1,cs_2,cs_3,cs_4,cs_2_2,cs_2_3,cs_2_4,cs_2_5,cs_4_1,cs_4_2,cs_4_3,cs_4_4,cs_3_1,cs_5,cs_5_1,cs_5_2,cs_3_2,cs_3_3,cs_3_4,cs_3_5,cs_3_6,cs_3_7,cs_4_5,cs_4_6,cs_2_6,cs_4_7,cs_2_7,cs_2_8,cs_3_8");//用户菜单id
            	redisTemplate.opsForValue().set("userInfo_"+ThreadLocalUtils.getUserName(), resultMap,1,TimeUnit.DAYS);
                return ResponseMessage.ok(resultMap);
        	}
        	return ResponseMessage.ok(object);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    
    @ApiOperation(value = "客户列表", notes = "获取客户的信息")
    @ApiImplicitParams({ @ApiImplicitParam(name = "query", value = "模糊查询条件", dataType = "String"),
    	@ApiImplicitParam(name = "cusIdentity", value = "业主身份", dataType = "String"),
    	@ApiImplicitParam(name = "sex", value = "性别", dataType = "String"),
    	@ApiImplicitParam(name = "birthdayStart", value = "出生日期-开始", dataType = "String"),
    	@ApiImplicitParam(name = "birthdayEnd", value = "出生日期-结束", dataType = "String"),
    	@ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
		@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20")
    })
	@RequestMapping(value = "/custList", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage custList(String query,String cusIdentity,String sex,String birthdayStart,
    		String birthdayEnd,@RequestParam(defaultValue="1")Integer pageNum, 
    		@RequestParam(defaultValue="20")Integer pageSize) {
        try {
        	Map<String,Object> map = new HashMap<String,Object>();
        	map.put("query", query);
        	map.put("cusIdentity", cusIdentity);
        	map.put("sex", sex);
        	map.put("birthdayStart", birthdayStart);
        	map.put("birthdayEnd", birthdayEnd);
        	map.put("count", (pageNum - 1) * pageSize);
			map.put("pageSize", pageSize);
			List<AppCustInfoDto> resultList = new ArrayList<AppCustInfoDto>();
			List<CsCustInfo> custList = csCustInfoService.getCustInfo(map);
			for(CsCustInfo cust : custList){
				AppCustInfoDto dto = new AppCustInfoDto();
				Wrapper<CsHouseInfo> wrapper = new EntityWrapper<CsHouseInfo>();
				wrapper.where("house_num={0}",cust.getHouseNum());
				List<CsHouseInfo> house = csHouseInfoService.selectList(wrapper);
				dto.setCustInfo(cust);
				dto.setHouseList(house);
				resultList.add(dto);
			}
            return ResponseMessage.ok(resultList);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("系统错误，请联系管理员");
        }
    }
    
    @ApiOperation(value = "客户标签列表", notes = "查询标签列表，树形结构返回")
	@RequestMapping(value = "/custLabel", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage custLabel() {
		try{
			Map<String, Object> map = new HashMap<>();
			//查询条件
			map.put("flag", "cust");//查询客户、活动标志
			//查询列表
			List<CsLabelTreeDto> list = csLabelService.getLabelTree(map);
			return ResponseMessage.ok(list);
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
	}
    
    @ApiOperation(value = "客户标签保存", notes = "查询标签列表，树形结构返回")
    @ApiImplicitParams({ @ApiImplicitParam(name = "custId", value = "业主id", dataType = "String"),
    	@ApiImplicitParam(name = "labelId", value = "标签id", dataType = "String"),
    	@ApiImplicitParam(name = "labelName", value = "标签名称", dataType = "String"),
    })
	@RequestMapping(value = "/saveCustLabel", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage saveCustLabel(String custId, String labelId,String labelName) {
		try{
			if(StringUtils.isBlank(custId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			Map<String, Object> map = new HashMap<>();
			//查询条件
			map.put("custId", custId);//业主id
			map.put("labelId", labelId);//标签id
			map.put("labelName", labelName);//标签名称
			
			csCustInfoService.updateCustLabel(map);
			return ResponseMessage.ok("标签保存成功");
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
	}
    
    @ApiOperation(value = "区域城市项目列表", notes = "查询区域城市项目列表，所有，不限制")
	@RequestMapping(value = "/projectList", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMessage projectList() {
		try{
			List<Map<String,Object>> list = csProjectInfoService.getProjectList();
			return ResponseMessage.ok(list);
		}catch(Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
	}
    
    @ApiOperation(value = "客户头像上传", notes = "上传客户头像")
	@RequestMapping(value = "/uploadHeadPortrait", method = { RequestMethod.POST })
	@ResponseBody
    public ResponseMessage uploadHeadPortrait(@RequestParam("files") List<MultipartFile> files, String custId) {
		try {
			//1.判断参数完整性
			if(StringUtils.isBlank(custId)){
				return ResponseMessage.error("参数录入不完整，请检查参数信息");
			}
			//2.上传文件到服务器
			List<String> list = new ArrayList<String>();
			for (MultipartFile file : files) {
				if (file.isEmpty()) {
					continue;
				}
				String path = saveUploadFile(file);
				list.add(path);
			}
			//4.关联到客户
			Map<String,Object> map = new HashMap<String,Object>();
			if ((list != null && list.size() > 0) && (custId != null)) {
				map.put("custId", custId);
				map.put("headPortrait", list.get(0));
				csCustInfoService.updateHeadPortrait(map);
			}
			return ResponseMessage.ok(map);
//			return ResponseMessage.ok("上传成功");
		}catch (Exception e){
			e.printStackTrace();
			return ResponseMessage.error("系统错误，请联系管理员");
		}
    }

    /**
     * 上传附件
     * @param file
     * @return
     */
	private String saveUploadFile(MultipartFile file) {
		try {
			File dir;
	        String extensionName = "";
	        String originalFilename = file.getOriginalFilename();
	        if (originalFilename != null && !"".equals(originalFilename)) {
	            int index = originalFilename.lastIndexOf(".");
	            if (index > 0) {
	                extensionName = originalFilename.substring(index, originalFilename.length());
	            }
	        }
	        Calendar calendar = Calendar.getInstance();
	        int year = calendar.get(Calendar.YEAR);
	        int month = calendar.get(Calendar.MONTH) + 1;
	        String ypath = rootPath + "/" + year;
	        String mpath = rootPath + "/" + year + "/" + month;
	        //检查目录是否存在，存在就直接使用，不存在就创建目录
	        dir = new File(ypath);
	        if (!dir.exists()) {
	            dir = new File(mpath);
	            dir.mkdirs();
	        } else {
	            dir = new File(mpath);
	            if (!dir.exists()) {
	                dir.mkdirs();
	            }
	        }
	        //获取一个UUID来作为存入服务器中的文件的名字
	        String filename = SecureUtil.simpleUUID();
	        filename = filename + extensionName;
	        String filePath = dir.getPath() + "/" + filename;
	        //将文件转存到指定位置
	        FileOutputStream out = new FileOutputStream(filePath);
	        out.write(file.getBytes());
	        out.flush();
	        out.close();
	        //将文件的服务器地址存到数据库
	        String path = "/" + year + "/" + month + "/" + filename;
			return path;
		} catch (Exception e) {
	        e.printStackTrace();
	    }
	    return null;
	}
}
