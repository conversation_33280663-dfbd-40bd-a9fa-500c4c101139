/**
 * 
 */
package com.tahoecn.customerservice.controller.report;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.common.utils.ExcelUtil;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.excelDTO.ExpForm;
import com.tahoecn.customerservice.model.excelDTO.ItExpForm;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.http.HttpClient;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/api/repForm")
@Api(tags = "统计查询", value = "统计查询")
public class RepFormController {

	protected Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${qx_uc_api_url}")
	private String apiUrl;
	@Value("${qx_uc_sysId}")
	private String sysId;
	@Value("${qx_uc_priv_key}")
	private String privKey;

	@Autowired
	private CsFormInstService csFormInstService;

	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
			@ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCompanyCode", value = "城市编码", dataType = "String"),
			@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
			@ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
			@ApiImplicitParam(name = "ownerName", value = "报事人姓名", dataType = "String"),
			@ApiImplicitParam(name = "mobile", value = "电话", dataType = "String"),
			@ApiImplicitParam(name = "processStateCode", value = "业务步骤", dataType = "String"),
			@ApiImplicitParam(name = "acceptChannelCode", value = "受理渠道", dataType = "String"),
			@ApiImplicitParam(name = "deptCode", value = "部门", dataType = "String"),
			@ApiImplicitParam(name = "customerDemand", value = "客户述求", dataType = "String"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "reworkFlag", value = "返工", dataType = "String"),
			@ApiImplicitParam(name = "upgradeFlag", value = "升级", dataType = "String"),
			@ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "astartDate", value = "分派开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "aendDate", value = "分派结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "rstartDate", value = "处理开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "rendDate", value = "处理结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "isAdmin", value = "是否是管理员", dataType = "String", defaultValue = "0"),
			@ApiImplicitParam(name = "isSiBaiSeats", value = "是否是400坐席", dataType = "String", defaultValue = "0"),
			@ApiImplicitParam(name = "isFromHome", value = "是否是首页穿透", dataType = "String", defaultValue = "0"),
			@ApiImplicitParam(name = "createUserName", value = "创建人", dataType = "String"),
            @ApiImplicitParam(name = "orderBy", value = "排序规则", dataType = "String")
	})
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage exportList(HttpServletResponse response, String regionCode, String cityCompanyCode,
			String projectCode, String formNo, String ownerName, String mobile, String processStateCode, String customerDemand,
			String acceptChannelCode, String deptCode, String firstSortCode, String reworkFlag, String upgradeFlag,
			String startDate, String endDate, String astartDate, String aendDate, String rstartDate, String rendDate,
			@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "20") Integer pageSize, 
			String isAdmin, String isSiBaiSeats, String createUserName, String isFromHome, @RequestParam(required = false) String signal,
            @RequestParam(required = false) String curAssigneeName,@RequestParam(required = false) String orderBy) {
		// 查询map
		Map<String, Object> map = new HashMap<>();
		Page<ExpForm> page = new Page<ExpForm>(pageNum, pageSize);

//		//报事查询权限限制，上生产需放开
//		if (setUserDataPriv(map)) {
//			return ResponseMessage.ok(page);
//		}

            // 添加查询条件
            map.put("regionCode", regionCode);
            map.put("cityCode", cityCompanyCode);
            map.put("projectCode", projectCode);
            map.put("formNo", formNo);
            map.put("createUserName", createUserName);//chenyy 20190110
            map.put("ownerName", ownerName);
            map.put("mobile", StringUtils.isBlank(mobile) ? null : mobile.trim());
            map.put("processStateCode", processStateCode);
            map.put("firstSortCode", firstSortCode);
            //signal空按原查询,不空 为改后查询
        if (StringUtils.isBlank(signal)) {
            map.put("acceptChannelCode", acceptChannelCode);
            map.put("deptCode", deptCode);
            map.put("reworkFlag", reworkFlag);
            map.put("upgradeFlag", upgradeFlag);
            map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
            map.put("startDate", startDate);
            map.put("aendDate", StringUtils.isBlank(aendDate) ? null : aendDate);
            map.put("astartDate", astartDate);
            map.put("rendDate", StringUtils.isBlank(rendDate) ? null : rendDate);
            map.put("rstartDate", rstartDate);
            map.put("customerDemand", customerDemand);
        }
            map.put("isAdmin", isAdmin);//chenyy 20190108
            map.put("isSiBaiSeats", isSiBaiSeats);//chenyy 20190108
            map.put("isFromHome", isFromHome);//chenyy 20190110

        if (StringUtils.isNotBlank(signal)&&StringUtils.isNotBlank(curAssigneeName)){
            map.put("curAssigneeName",curAssigneeName);
        }
            map.put("orderBy",orderBy);
		List<ExpForm> list = csFormInstService.expForm(page, map);
        page.setRecords(list);
        return ResponseMessage.ok(page);
	}

    @ApiOperation(value = "修改处理人", notes = "修改处理人")
    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage modify(@RequestBody List<Map<String,String>> list){
        String msg=csFormInstService.modify(list);
        if ("修改成功!".equals(msg)){
            return ResponseMessage.ok(msg);
        }
        return ResponseMessage.error(msg);
    }

	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", defaultValue = "1"),
						 @ApiImplicitParam(name = "pageSize", value = "数量", dataType = "Integer", defaultValue = "20"),
						 @ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
						 @ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
						 @ApiImplicitParam(name = "secSortCode", value = "二级分类", dataType = "String"),
						 @ApiImplicitParam(name = "processStateCode", value = "业务步骤", dataType = "String"),
						 @ApiImplicitParam(name = "reportChannelCode", value = "报事渠道", dataType = "String"),
						 @ApiImplicitParam(name = "creationStartDate", value = "报事时间  开始", dataType = "Date"),
						 @ApiImplicitParam(name = "creationEndDate", value = "报事时间  结束", dataType = "Date"),
						 @ApiImplicitParam(name = "ownerName", value = "报事人姓名", dataType = "String"),
						 @ApiImplicitParam(name = "createUserName", value = "创建人", dataType = "String"),
						 @ApiImplicitParam(name = "customerDemand", value = "客户诉求", dataType = "String"),
						 @ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
						 @ApiImplicitParam(name = "handleRecord", value = "处理意见", dataType = "String")})
	@RequestMapping(value = "/getITList", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage getITList(HttpServletResponse response, String projectCode, String firstSortCode,String createUserName,
		String secSortCode, String reportChannelCode, String creationStartDate, String creationEndDate, String ownerName,
		String processStateCode, String customerDemand, String handleRecord,String formNo,
		@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "20") Integer pageSize) {
		// 查询map
		Map<String, Object> map = new HashMap<>();
		Page<ItExpForm> page = new Page<ItExpForm>(pageNum, pageSize);
		// 添加查询条件
		map.put("projectCode", projectCode);
		map.put("firstSortCode", firstSortCode);
		map.put("secSortCode", secSortCode);
		map.put("processStateCode", processStateCode);
		map.put("reportChannelCode", reportChannelCode);
		map.put("creationStartDate", StringUtils.isBlank(creationStartDate) ? null : creationStartDate);
		map.put("creationEndDate", StringUtils.isBlank(creationEndDate) ? null : creationEndDate);
		map.put("ownerName", ownerName);
		map.put("createUserName", createUserName);
		map.put("customerDemand", customerDemand);
		map.put("handleRecord", handleRecord);
		map.put("formNo", formNo);
		List<ItExpForm> selectPage = csFormInstService.getITList(page,map);
		page.setRecords(selectPage);
		return ResponseMessage.ok(page);
	}
	
	/**
	 * Excel导出演示
	 * 
	 * @param response
	 */

	@ApiImplicitParams({ @ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
		@ApiImplicitParam(name = "cityCompanyCode", value = "城市编码", dataType = "String"),
		@ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
		@ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
		@ApiImplicitParam(name = "ownerName", value = "报事人姓名", dataType = "String"),
		@ApiImplicitParam(name = "mobile", value = "电话", dataType = "String"),
		@ApiImplicitParam(name = "processStateCode", value = "业务步骤", dataType = "String"),
		@ApiImplicitParam(name = "acceptChannelCode", value = "受理渠道", dataType = "String"),
		@ApiImplicitParam(name = "deptCode", value = "部门", dataType = "String"),
		@ApiImplicitParam(name = "customerDemand", value = "客户述求", dataType = "String"),
		@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
		@ApiImplicitParam(name = "reworkFlag", value = "返工", dataType = "String"),
		@ApiImplicitParam(name = "upgradeFlag", value = "升级", dataType = "String"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
		@ApiImplicitParam(name = "astartDate", value = "分派开始时间", dataType = "Date"),
		@ApiImplicitParam(name = "aendDate", value = "分派结束时间", dataType = "Date"),
		@ApiImplicitParam(name = "rstartDate", value = "处理开始时间", dataType = "Date"),
		@ApiImplicitParam(name = "rendDate", value = "处理结束时间", dataType = "Date"),
		@ApiImplicitParam(name = "isAdmin", value = "是否是管理员", dataType = "String", defaultValue = "0"),
		@ApiImplicitParam(name = "isSiBaiSeats", value = "是否是400坐席", dataType = "String", defaultValue = "0"),
		@ApiImplicitParam(name = "isFromHome", value = "是否是首页穿透", dataType = "String", defaultValue = "0"),
		@ApiImplicitParam(name = "createUserName", value = "创建人", dataType = "String") })
	@RequestMapping(value = "/export", method = RequestMethod.GET)
	public void exportExcel(HttpServletResponse response, String regionCode, String cityCompanyCode,
			String projectCode, String formNo, String ownerName, String mobile, String processStateCode, String customerDemand,
			String acceptChannelCode, String deptCode, String firstSortCode, String reworkFlag, String upgradeFlag,
			String startDate, String endDate, String astartDate, String aendDate, String rstartDate, String rendDate,
			String isAdmin, String isSiBaiSeats, String createUserName, String isFromHome) {

		try {
			Map<String, Object> map = new HashMap<>();

			response.setHeader("content-Type", "application/vnd.ms-excel");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode("客服管理系统工单明细.xls", "utf-8"));

			if (setUserDataPriv(map)) {
				ExcelUtil.listToExcel(new ArrayList<ExpForm>(), "客服管理系统工单明细", response.getOutputStream());
				return;
			}

			map.put("regionCode", regionCode);
			map.put("cityCode", cityCompanyCode);
			map.put("projectCode", projectCode);
			map.put("formNo", formNo);
			map.put("ownerName", ownerName);
			map.put("mobile", StringUtils.isBlank(mobile) ? null : mobile.trim());
			map.put("processStateCode", processStateCode);
			map.put("acceptChannelCode", acceptChannelCode);
			map.put("deptCode", deptCode);
			map.put("firstSortCode", firstSortCode);
			map.put("reworkFlag", reworkFlag);
			map.put("upgradeFlag", upgradeFlag);
			map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
			map.put("startDate", startDate);
			map.put("aendDate", StringUtils.isBlank(aendDate) ? null : aendDate);
			map.put("astartDate", astartDate);
			map.put("rendDate", StringUtils.isBlank(rendDate) ? null : rendDate);
			map.put("rstartDate", rstartDate);
			map.put("customerDemand", customerDemand);
			map.put("isAdmin", isAdmin);//chenyy 20190108
			map.put("isSiBaiSeats", isSiBaiSeats);//chenyy 20190108
			map.put("createUserName", createUserName);//chenyy 20190110
			map.put("isFromHome", isFromHome);//chenyy 20190110
			List<ExpForm> list = csFormInstService.expForm(null, map);

			ExcelUtil.listToExcel(list, "客服管理系统工单明细", response.getOutputStream());

		} catch (Exception e) {
			System.out.println("===================================");
		}
	}
	
	/**
	 * IT Excel导出演示
	 * 
	 * @param response
	 */
	
	@ApiImplicitParams({ @ApiImplicitParam(name = "projectCode", value = "项目编码", dataType = "String"),
		 @ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
		 @ApiImplicitParam(name = "secSortCode", value = "二级分类", dataType = "String"),
		 @ApiImplicitParam(name = "processStateCode", value = "业务步骤", dataType = "String"),
		 @ApiImplicitParam(name = "reportChannelCode", value = "报事渠道", dataType = "String"),
		 @ApiImplicitParam(name = "creationStartDate", value = "报事时间  开始", dataType = "Date"),
		 @ApiImplicitParam(name = "creationEndDate", value = "报事时间  结束", dataType = "Date"),
		 @ApiImplicitParam(name = "ownerName", value = "报事人姓名", dataType = "String"),
		 @ApiImplicitParam(name = "customerDemand", value = "客户诉求", dataType = "String"),
		 @ApiImplicitParam(name = "formNo", value = "工单编号", dataType = "String"),
		 @ApiImplicitParam(name = "handleRecord", value = "处理意见", dataType = "String")})
	@RequestMapping(value = "/exportIT", method = RequestMethod.GET)
	public void exportITExcel(HttpServletResponse response, String projectCode, String firstSortCode,String processStateCode,
			String secSortCode, String reportChannelCode, String creationStartDate, String creationEndDate, String ownerName,
			String customerDemand, String handleRecord, String formNo) {
		
		try {
			Map<String, Object> map = new HashMap<>();
			
			response.setHeader("content-Type", "application/vnd.ms-excel");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode("客服管理系统IT工单明细.xls", "utf-8"));
			
//			if (setUserDataPriv(map)) {
//				ExcelUtil.listToExcel(new ArrayList<ExpForm>(), "客服管理系统IT工单明细", response.getOutputStream());
//				return;
//			}
			
			map.put("projectCode", projectCode);
			map.put("firstSortCode", firstSortCode);
			map.put("secSortCode", secSortCode);
			map.put("processStateCode", processStateCode);
			map.put("reportChannelCode", reportChannelCode);
			map.put("creationStartDate", StringUtils.isBlank(creationStartDate) ? null : creationStartDate);
			map.put("creationEndDate", StringUtils.isBlank(creationEndDate) ? null : creationEndDate);
			map.put("ownerName", ownerName);
			map.put("customerDemand", customerDemand);
			map.put("handleRecord", handleRecord);
			map.put("formNo", formNo);
			List<ItExpForm> list = csFormInstService.getITList(null,map);
			
			ExcelUtil.listToExcel(list, "客服管理系统IT工单明细", response.getOutputStream());
			
		} catch (Exception e) {
			System.out.println("===================================");
		}
	}

	/**
	 * 设置数据权限
	 * 
	 * @param map
	 * @return
	 */
	public Boolean setUserDataPriv(Map<String, Object> map) {
		Long timestamp = System.currentTimeMillis() / 1000;
		String token = SecureUtil.md5(timestamp + privKey);

		String url = String.format(apiUrl + "/v1/userDataPriv/list?sysId=%s&timestamp=%d&token=%s&userName=%s", sysId,
				timestamp, token, ThreadLocalUtils.getUserName());
		String result = HttpClient.httpGet(url);
//eq:   String result = "{\"code\":0,\"msg\":\"成功\",\"result\":[{\"fdPrivRangeSid\":-1,\"fdDataRangeName\":\"本机构\",\"fdCode\":\"KF-GD-XM\",\"fdSysName\":\"客服系统\",\"fdCreateTime\":\"2018-11-27 19:11:06\",\"fdStandardRoleSids\":[\"7e2b486c07a64aeaa9dc8ef957ab8e73\"],\"fdSysSid\":\"KEFU\",\"fdDataRangePcode\":\"FK-ORG\",\"fdUpdateTime\":\"2018-11-27 19:11:06\",\"fdDataPrivList\":[{\"fdCode\":\"JG0004\",\"fdOrgSid\":\"上海区域\",\"fdSid\":\"15a7f350f527700ad0350db44bb95998\",\"fdName\":\"上海区域\"}],\"fdSid\":\"2456800b20864f7294e562b45dced916\",\"fdPrivRangeCode\":\"FK-ORG\",\"fdDataRangeCode\":\"2\",\"fdName\":\"客服工单项目\",\"fdPrivRangeName\":\"组织机构\",\"fdOrder\":0,\"fdDataRangeSid\":\"df961d10aa1c4050b36a68b9f0a51748\"}]}";
		JSONObject object = JSONUtil.parseObj(result);
		logger.info("url:{} \n result:{}", url, result);
		if (0 == object.getInt("code")) {
			JSONArray array = object.getJSONArray("result");
			if (array == null || array.size() == 0) {
				return true; // 无权限处理
			}
			for (Object o : array) {
				JSONObject jo = new JSONObject(o);
				if (jo.get("fdDataRangeCode", Integer.class) == 1) {
					map.put("userName", "");
					map.put("fdOrgSid", "");
					break;
				}
				if (jo.get("fdDataRangeCode", Integer.class) == 2) {
					JSONArray priv = jo.getJSONArray("fdDataPrivList");
					if (priv == null || priv.size() == 0) {
						continue;
					}
					StringBuffer fdOrgSid = new StringBuffer();
					for (Object op : priv) {
						JSONObject joP = new JSONObject(op);
						fdOrgSid.append(joP.get("fdOrgSid", String.class));
						fdOrgSid.append(",");
					}
					map.put("fdOrgSid", fdOrgSid.toString());
				}
				if (jo.get("fdDataRangeCode", Integer.class) == 4) {
					map.put("userName", ThreadLocalUtils.getUserName());
				}
			}
		} else {
			return true; // 权限获取失败处理
		}
		return false;
	}

}