/**
 * 
 */
package com.tahoecn.customerservice.controller.report;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.common.utils.ExcelUtil;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.report.ChannelDto;
import com.tahoecn.customerservice.model.report.ClassifyDto;
import com.tahoecn.customerservice.model.report.CurReportDto;
import com.tahoecn.customerservice.model.report.RepairWholeDto;
import com.tahoecn.customerservice.model.report.UpgradeDto;
import com.tahoecn.customerservice.service.ReportFormService;
import com.tahoecn.http.HttpClient;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/api/reportForm")
@Api(tags = "统计报表", value = "统计报表")
public class ReportFormController {

	protected Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${qx_uc_api_url}")
	private String apiUrl;
	@Value("${qx_uc_sysId}")
	private String sysId;
	@Value("${qx_uc_priv_key}")
	private String privKey;

	@Autowired
	private ReportFormService reportFormService;

	@ApiOperation(value = "报修整体数据统计", notes = "报修整体数据统计")
	@ApiImplicitParams({ @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "isExport", value = "是否导出", dataType = "Integer") })
	@RequestMapping(value = "/repairWhole", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage repairWhole(HttpServletResponse response, String startDate, String endDate,
			Integer isExport) {
		// 查询map
		Map<String, Object> map = new HashMap<>();

		// if (setUserDataPriv(map)) {
		// return ResponseMessage.ok(new ArrayList<>());
		// }

		map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
		map.put("startDate", startDate);
		List<RepairWholeDto> list = reportFormService.repairWhole(map);
		if (isExport != null && isExport == 1) {
			try {
				response.setHeader("content-Type", "application/vnd.ms-excel");
				response.setHeader("Content-Disposition",
						"attachment;filename=" + URLEncoder.encode("报修整体数据统计.xls", "utf-8"));
				ExcelUtil.listToExcel(list, "报修整体数据统计", response.getOutputStream());
			} catch (Exception e) {
				e.printStackTrace();
			}
			return ResponseMessage.ok("导出数据");
		}
		return ResponseMessage.ok(list);
	}

	@ApiOperation(value = "报事分类数据统计", notes = "报事分类数据统计")
	@ApiImplicitParams({ @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCode", value = "城市", dataType = "String"),
			@ApiImplicitParam(name = "isExport", value = "是否导出", dataType = "Integer") })
	@RequestMapping(value = "/classify", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage classify(HttpServletResponse response, String startDate, String endDate,
			String firstSortCode, String regionCode, String cityCode, Integer isExport) {
		// 查询map
		Map<String, Object> map = new HashMap<>();

		// if (setUserDataPriv(map)) {
		// return ResponseMessage.ok(new ArrayList<>());
		// }

		map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
		map.put("startDate", startDate);
		map.put("firstSortCode", firstSortCode);
		map.put("regionCode", regionCode);
		map.put("cityCode", cityCode);

		List<ClassifyDto> list = reportFormService.classify(map);
		if (isExport != null && isExport == 1) {
			try {
				response.setHeader("content-Type", "application/vnd.ms-excel");
				response.setHeader("Content-Disposition",
						"attachment;filename=" + URLEncoder.encode("报事分类数据统计.xls", "utf-8"));
				ExcelUtil.listToExcel(list, "报事分类数据统计", response.getOutputStream());
			} catch (Exception e) {
				e.printStackTrace();
			}
			return ResponseMessage.ok("导出数据");
		}
		return ResponseMessage.ok(list);
	}

	@ApiOperation(value = "报事升级统计", notes = "报事升级统计")
	@ApiImplicitParams({ @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "cityCode", value = "城市", dataType = "String"),
			@ApiImplicitParam(name = "isExport", value = "是否导出", dataType = "Integer") })
	@RequestMapping(value = "/upgrade", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage upgrade(HttpServletResponse response, String startDate, String endDate, String firstSortCode,
			String regionCode, String cityCode, Integer isExport) {
		// 查询map
		Map<String, Object> map = new HashMap<>();

		// if (setUserDataPriv(map)) {
		// return ResponseMessage.ok(new ArrayList<>());
		// }

		map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
		map.put("startDate", startDate);
		map.put("firstSortCode", firstSortCode);
		map.put("regionCode", regionCode);
		map.put("cityCode", cityCode);
		map.put("isUpgrade", "isUpgrade");

		List<UpgradeDto> list = reportFormService.upgrade(map);
		if (isExport != null && isExport == 1) {
			try {
				response.setHeader("content-Type", "application/vnd.ms-excel");
				response.setHeader("Content-Disposition",
						"attachment;filename=" + URLEncoder.encode("报事升级统计.xls", "utf-8"));
				ExcelUtil.listToExcel(list, "报事升级统计", response.getOutputStream());
			} catch (Exception e) {
				e.printStackTrace();
			}
			return ResponseMessage.ok("导出数据");
		}
		return ResponseMessage.ok(list);
	}

	@ApiOperation(value = "报事渠道统计", notes = "报事渠道统计")
	@ApiImplicitParams({ @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "isExport", value = "是否导出", dataType = "Integer") })
	@RequestMapping(value = "/channel", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage channel(HttpServletResponse response, String startDate, String endDate, String firstSortCode,
			String regionCode, Integer isExport) {

		// 查询map
		Map<String, Object> map = new HashMap<>();

		// if (setUserDataPriv(map)) {
		// return ResponseMessage.ok(new ArrayList<>());
		// }

		map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
		map.put("startDate", startDate);
		map.put("firstSortCode", firstSortCode);
		map.put("regionCode", regionCode);
		List<ChannelDto> list = reportFormService.channel(map);
		if (isExport != null && isExport == 1) {
			try {
				response.setHeader("content-Type", "application/vnd.ms-excel");
				response.setHeader("Content-Disposition",
						"attachment;filename=" + URLEncoder.encode("报事渠道统计.xls", "utf-8"));
				ExcelUtil.listToExcel(list, "报事渠道统计", response.getOutputStream());
			} catch (Exception e) {
				e.printStackTrace();
			}
			return ResponseMessage.ok("导出数据");
		}

		return ResponseMessage.ok(list);
	}

	@ApiOperation(value = "关闭升级返修报表", notes = "关闭升级返修报表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Date"),
			@ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Date"),
			@ApiImplicitParam(name = "firstSortCode", value = "一级分类", dataType = "String"),
			@ApiImplicitParam(name = "regionCode", value = "区域", dataType = "String"),
			@ApiImplicitParam(name = "isExport", value = "是否导出", dataType = "Integer") })
	@RequestMapping(value = "/curReport", method = RequestMethod.GET)
	@ResponseBody
	public ResponseMessage curReport(HttpServletResponse response, String startDate, String endDate,
			String firstSortCode, String regionCode, Integer isExport) {
		// 查询map
		Map<String, Object> map = new HashMap<>();

		// if (setUserDataPriv(map)) {
		// return ResponseMessage.ok(new ArrayList<>());
		// }

		map.put("endDate", StringUtils.isBlank(endDate) ? null : endDate);
		map.put("startDate", startDate);
		map.put("firstSortCode", firstSortCode);
		map.put("regionCode", regionCode);

		List<CurReportDto> list = reportFormService.curReport(map);
		if (isExport != null && isExport == 1) {
			try {
				response.setHeader("content-Type", "application/vnd.ms-excel");
				response.setHeader("Content-Disposition",
						"attachment;filename=" + URLEncoder.encode("关闭升级返修报表.xls", "utf-8"));
				ExcelUtil.listToExcel(list, "关闭升级返修报表", response.getOutputStream());
			} catch (Exception e) {
				e.printStackTrace();
			}
			return ResponseMessage.ok("导出数据");
		}
		return ResponseMessage.ok(list);
	}

	/**
	 * 设置数据权限
	 * 
	 * @param map
	 * @return
	 */
	private Boolean setUserDataPriv(Map<String, Object> map) {
		Long timestamp = System.currentTimeMillis() / 1000;
		String token = SecureUtil.md5(timestamp + privKey);

		String url = String.format(apiUrl + "/v1/userDataPriv/list?sysId=%s&timestamp=%d&token=%s&userName=%s", sysId,
				timestamp, token, ThreadLocalUtils.getUserName());
		String result = HttpClient.httpGet(url);
		JSONObject object = JSONUtil.parseObj(result);
		logger.info("url:{} \n result:{}", url, result);
		if (0 == object.getInt("code")) {
			JSONArray array = object.getJSONArray("result");
			if (array == null || array.size() == 0) {
				return true; // 无权限处理
			}
			for (Object o : array) {
				JSONObject jo = new JSONObject(o);
				if (jo.get("fdDataRangeCode", Integer.class) == 1) {
					break;
				}
				if (jo.get("fdDataRangeCode", Integer.class) == 2) {
					JSONArray priv = jo.getJSONArray("fdDataPrivList");
					if (priv == null || priv.size() == 0) {
						continue;
					}
					StringBuffer fdOrgSid = new StringBuffer();
					for (Object op : priv) {
						JSONObject joP = new JSONObject(op);
						fdOrgSid.append(joP.get("fdOrgSid", String.class));
						fdOrgSid.append(",");
					}
					map.put("fdOrgSid", fdOrgSid.toString());
				}
				if (jo.get("fdDataRangeCode", Integer.class) == 4) {
					map.put("userName", ThreadLocalUtils.getUserName());
				}
			}
		} else {
			return true; // 权限获取失败处理
		}
		return false;
	}

}