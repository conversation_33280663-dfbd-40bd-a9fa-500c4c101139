package com.tahoecn.customerservice.controller.sample;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.customerservice.common.utils.ExcelUtil;
import com.tahoecn.customerservice.controller.TahoeBaseController;
import com.tahoecn.customerservice.model.User;
import com.tahoecn.customerservice.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
public class SampleController extends TahoeBaseController {

    @Autowired
    private UserService userServiceImpl;

    @RequestMapping(value = "/info",method = RequestMethod.GET)
    public String info(HttpServletRequest request, HttpServletResponse response, Model model){
        List<Map<String, Object>> list = this.userServiceImpl.getUsersByXml();
        System.out.println(list.size());

        System.out.println(request);
        System.out.println(response);
        model.addAttribute("info","customerservice");

        return "info";
    }


    /**
     * Controller层的validation演示，使用BindingResult（验证规则在Model上定义）
     * @param user
     * @param result
     * @param model
     * @return
     */
    @RequestMapping(value = "/postuser", method = RequestMethod.POST)
    public String postUser(@Valid User user,BindingResult result, Model model) {

        if (result.hasErrors()){
            List<ObjectError> errorList = result.getAllErrors();
            for(ObjectError error : errorList){
                System.out.println(error.getDefaultMessage());
            }

            System.out.println("================================");
            return "info";
        }else{
            model.addAttribute("user",user);
        }
        return "postuser";
    }

    /**
     * 非Controller层的validation演示（验证规则在Model上定义）
     *  @param model
     * @return
     */
    @RequestMapping(value = "/filteruser",method = RequestMethod.GET)
    @ResponseBody
    public int filterUser(Model model){
        List<User> list = new ArrayList();
        User user;

        user = new User();
        user.setId(100);
        user.setName("白居易");
        user.setType(1);
        user.setStatus(1);
        user.setCreatedTime(new Date());
        user.setAge(10);
        list.add(user);

        user = new User();
        user.setId(200);
        user.setName("李世石");
        user.setType(1);
        user.setStatus(1);
        user.setCreatedTime(new Date());
        user.setAge(20);
        list.add(user);

        user = new User();
        user.setId(300);
        user.setName("张信哲");
        user.setType(1);
        user.setStatus(1);
        user.setCreatedTime(new Date());
        user.setAge(30);
        list.add(user);

        model.addAttribute("users",list);
        return this.userServiceImpl.filterUser(list);
    }

    /**
     * Excel导出演示
     * @param response
     */
    @RequestMapping(value = "/export",method = RequestMethod.GET)
    public void exportExcel(HttpServletResponse response){
        List<User> list = this.userServiceImpl.selectList(new EntityWrapper<User>(){});

        try {
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename="+
                    URLEncoder.encode("exportData.xls", "utf-8"));

            ExcelUtil.listToExcel(list,"2018年泰禾员工数据",response.getOutputStream());

        } catch (Exception e) {
            System.out.println("===================================");
        }
    }

    /**
     * 文件上传演示
     * @param files
     * @return
     */
    @RequestMapping(value = "/postfile",method = RequestMethod.POST)
    @ResponseBody
    public JSONResult postFile(@RequestParam("localfile") List<MultipartFile> files){
        return super.uploadFiles(files);
    }

    /**
     * 在线预览Excel
     * @param excel
     * @return
     */
    @RequestMapping(value = "/loadexcel",method = RequestMethod.POST)
    @ResponseBody
    public JSONResult loadExcel(@RequestParam("excelfile") MultipartFile excel){

        JSONResult jsonResult = null;
        try {
            jsonResult = ExcelUtil.excelToList(excel.getInputStream(),User.class,1,1);
        } catch (Exception e) {
            jsonResult.setCode(-1);
            jsonResult.setMsg("IO错误");
            jsonResult.setData(e);
            e.printStackTrace();
        }
        return jsonResult;
    }

    @RequestMapping(value = "/excel",method = RequestMethod.GET)
    public String excel(){
        return "excel";
    }
}