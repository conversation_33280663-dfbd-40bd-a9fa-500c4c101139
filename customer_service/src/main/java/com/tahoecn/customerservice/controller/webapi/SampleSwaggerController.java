package com.tahoecn.customerservice.controller.webapi;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "接口描述", value = "接口描述")
@RestController
@RequestMapping(value = "/webapi")
public class SampleSwaggerController {

    @ApiOperation(value = "接口名称", notes = "接口描述")
    @ApiImplicitParams({ @ApiImplicitParam(name = "infoId", value = "变量注释", required = true, dataType = "String") })
    @RequestMapping(value = "/info", method = { RequestMethod.POST, RequestMethod.GET })
    public String demo(String infoId) {
        return "code:{0},msg:{访问提示信息（成功/失败）},data{"+infoId+"}";
    }
}
