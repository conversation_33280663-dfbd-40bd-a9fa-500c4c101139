package com.tahoecn.customerservice.interceptor;

import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.tahoecn.customerservice.common.utils.MD5Util;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.service.CsUcUserService;

/**
 * 移动端信息的拦截器
 */
@Component
public class AppInterceptor implements HandlerInterceptor {

	@Autowired
	CsUcUserService csUcUserService;
	@Autowired
	RedisTemplate redisTemplate;

	/**
	 * 在处理请求之前要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
//		if (request.getRequestURI().indexOf("/versionNumber") > 0 || request.getRequestURI().indexOf("/login") > 0) {
//			return true;
//		}
//
//		String userName = request.getHeader("userName");
//		String random = request.getHeader("random");
//		String key = request.getHeader("key");
//		ThreadLocalUtils.setPlatform(request.getHeader("thPlatform"));
//		if (redisTemplate.opsForValue().get("randomKey_" + key) != null) {
//			response.sendError(401, "加密验证失败");
//			return false;
//		}
//		redisTemplate.opsForValue().set("randomKey_" + key, key, 1, TimeUnit.DAYS);
//		if (key != null && key.equals(MD5Util.getMD5String(userName + random))) {
		String loginName = "admin";
			CsUcUser csUcUser = (CsUcUser) redisTemplate.opsForValue().get(loginName);
			if (csUcUser == null) {
				csUcUser = csUcUserService.selectByUsername(loginName);
				if (csUcUser == null) {
					response.sendError(401, "加密验证失败");
					return false;
				}
				redisTemplate.opsForValue().set(loginName, csUcUser, 1, TimeUnit.DAYS);
			}
			ThreadLocalUtils.setUser(csUcUser);
			return true;
//		}
//		response.sendError(401, "加密验证失败");
//		return false;
	}

	/**
	 * 完成请求处理后要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @param modelAndView
	 * @throws Exception
	 */
	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
	}

	/**
	 * 请求结束后要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @param ex
	 * @throws Exception
	 */
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		ThreadLocalUtils.remove();
		ThreadLocalUtils.removePlatform();
	}
}
