package com.tahoecn.customerservice.interceptor;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.uc.sso.web.interceptor.SSOSpringInterceptor;

/**
 * 全局的拦截器
 */
@Component
public class GlobalInterceptor extends SSOSpringInterceptor {

	@Autowired
	CsUcUserService csUcUserService;
	@Autowired
	RedisTemplate redisTemplate;
	
	private static final String[] IGNORE_URI = {"/api/perm/userLogin"
    		}; 

	/**
	 * 在处理请求之前要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
//		// SSO 魔改
//		if ((handler instanceof HandlerMethod)) {
//			HandlerMethod handlerMethod = (HandlerMethod) handler;
//			Method method = handlerMethod.getMethod();
//			Login login = (Login) method.getAnnotation(Login.class);
//			if ((login != null) && (login.action() == Action.Skip)) {
//				return true;
//			}
//		}
//		SSOToken ssoToken = SSOHelper.getSSOToken(request);
//		if (ssoToken == null) {
//			sendError(response);
//			return false;
//		}
//
//		String LtpaTokenCookie = CookieHelper.getCookie(request, SSOConfig.getInstance().getOaCookieName());
//		if ((StringUtils.isEmpty(LtpaTokenCookie)) || (LtpaTokenCookie.length() < 10)) {
//			String tokenValue = LtpaToken.generateTokenByUserName(ssoToken.getIssuer(),
//					String.valueOf(SSOConfig.getInstance().getExpireT()), SSOConfig.getInstance().getOatokenKey());
//			String domain = SSOConfig.getInstance().getCookieDomain();
//
//			response.addHeader("Set-Cookie", SSOConfig.getInstance().getOaCookieName() + "=" + tokenValue + ";Domain="
//					+ domain + "; Path=" + SSOConfig.getInstance().getCookiePath());
//		}
//		request.setAttribute("ucssoTokenAttr", ssoToken);
//
//		// 单点登陆用户过滤
//		Optional<SSOToken> sso = Optional.ofNullable(SSOHelper.attrToken(request));
		//特殊接口跳过拦截器
		String requestUri = request.getRequestURI();  
		String contextPath = request.getContextPath();  
        String url = requestUri.substring(contextPath.length());  
    	for (String s : IGNORE_URI){
            if(url.startsWith(s)){
    			return true;
    		}
    	}
		String loginName = ThreadLocalUtils.getUserName();
		if (StringUtils.isBlank(loginName)) {
			loginName = (String) request.getSession().getAttribute("user");
		}
		if (StringUtils.isNotBlank(loginName)) {
			CsUcUser csUcUser = (CsUcUser) redisTemplate.opsForValue().get(loginName);
			if (csUcUser == null) {
				csUcUser = csUcUserService.selectByUsername(loginName);
				if(csUcUser == null) {
					sendError(response);
					return false;
				}
				redisTemplate.opsForValue().set(loginName, csUcUser, 1, TimeUnit.DAYS);
				//将用户权限对象存储到session
				request.getSession().setAttribute("user", loginName);
			}
			ThreadLocalUtils.setUser(csUcUser);
			return true;
		}
		sendError(response);
		return false;
	}

	private void sendError(HttpServletResponse response) {
		try {
			response.sendError(401, "获取授权失败");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 完成请求处理后要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @param modelAndView
	 * @throws Exception
	 */
	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
	}

	/**
	 * 请求结束后要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @param ex
	 * @throws Exception
	 */
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		ThreadLocalUtils.remove();
	}
}
