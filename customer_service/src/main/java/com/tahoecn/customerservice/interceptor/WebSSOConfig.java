//package com.tahoecn.customerservice.interceptor;
//
//import java.util.Arrays;
//
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
//
//import com.landray.sso.client.EKPSSOClient;
//
///**
// * Created by zhanghw on 2018/10/11.
// */
//@SuppressWarnings("deprecation")
//@Configuration
//public class WebSSOConfig extends WebMvcConfigurerAdapter {
//
//	/**
//	 * sso过滤器
//	 */
////	@SuppressWarnings({ "rawtypes", "unchecked" })
////	@Bean
////	public FilterRegistrationBean ssoFilterRegistration() {
////		FilterRegistrationBean registration = new FilterRegistrationBean();
////		registration.setFilter(new EKPSSOClient());
////		registration.setUrlPatterns(Arrays.asList("/api/*", "/swagger*"));
////		registration.setName("EKPSSOClient");
////		registration.addInitParameter("filterConfigFile", "/sso-config.properties");
////		registration.setOrder(-1);// 过滤器顺序
////		return registration;
////	}
//
//	@Override
//	public void addCorsMappings(CorsRegistry registry) {
//		registry.addMapping("/weChatApi/**").allowedOrigins("*");
//	}
//}
