package com.tahoecn.customerservice.mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.session.RowBounds;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.dto.CsCustInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;

import io.lettuce.core.dynamic.annotation.Param;

/**
 * <p>
 * 客户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsCustInfoMapper extends BaseMapper<CsCustInfo> {
	
	/**
	 * 获取房人工单 信息
	 * 
	 * @param rowBounds 分页参数
	 * @param dto
	 * @return
	 */
	List<CsCustInfo> selectHcf(RowBounds rowBounds, HcfInfoDto dto);

	List<CsCustInfo> selectByCustId(@Param("custId")String custId);

	List<CsCustInfoDto> selectCustInfoList(Map<String, Object> map);

	Integer getListCount(Map<String, Object> map);

	int insertMyData();

	void updateHasMoreHouse();
	
	void updateNoHasMoreHouse();
	
	void insertTeltempData();
	
	List<HashMap<String, Object>> selectDifferentTel();

	void updateTelBycustId(HashMap<String, Object> map);
	
	void deleteTeltempDate();
	
	List<CsHouseInfoCustInfoFrom> selectCsHouseInfoCustInfoFrom(Map<String, Object> map);

	void updateCanNotDelete();

	/**
	 * 查询业主信息
	 * @param map
	 * @return
	 */
	List<CsCustInfo> findCustInfo(Map<String,Object> map);

	/**
	 * 根据业主id和房屋num查询业主信息
	 * @param map
	 * @return
	 */
	List<CsCustInfo> selectCustByIdNum(Map<String, Object> map);

	/**
	 * APP-查询客户信息
	 * @param map
	 * @return
	 */
	List<CsCustInfo> getCustInfo(Map<String, Object> map);

	/**
	 * APP-更新业主标签
	 */
	void updateCustLabel(Map<String, Object> map);

	/**
	 * 手机APP-更改客户头像
	 */
	void updateHeadPortrait(Map<String, Object> map);
	
	/**
	 * 同步初始化数据
	 */
	void initData();
	
	/**
	 * 清理无关联数据
	 */
	@Delete("DELETE c FROM cs_cust_info c LEFT OUTER JOIN cs_sync_cust2house m ON c.certificate_num = m.certificate_num AND c.house_num = m.house_code WHERE m.certificate_num is null")
	void delData();
	
	/**
	 * 通过物业ID查找业主
	 * 
	 * @param custId
	 * @return
	 */
	List<CsCustInfo> selectWyCust(@Param("custId") String custId);
	
	/**
	 * 保留数据处理
	 */
	void retainCust(@Param("certificateNum") String certificateNum);
	
	void syncDeleteTeltempDate();
	
	void syncInsertTeltemp();
	
	void syncUpdateCanNotDelete();
}
