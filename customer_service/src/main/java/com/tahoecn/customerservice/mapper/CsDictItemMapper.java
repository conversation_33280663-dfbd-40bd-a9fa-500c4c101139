package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsDictItem;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsUcUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface CsDictItemMapper extends BaseMapper<CsDictItem> {

    List<CsDictItem> selectAllCsDictItemByDictCode(String dictCode);

    CsDictItem selectCsDictItemByItemCode(String itemCode);

    List<CsDictItem> selectAllCsDictItemByList(List<String> list);

    List<CsDictItem> selectAllCsDictItem();

    /**
     * 查询一级分类下的信息
     * */
    CsDictItem selectFirstSortCode(@Param("firstSortCode") String firstSortCode);

    //查询当前级别下的分类数据
    List<Map<String, Object>>selectListDictItem(@Param("panam")Map<String,Object> panam);

    //查询当前字典下的所有信息
    Map<String, Object> selectOneDictItem(@Param("itemCode")String itemCode);

    //获取当前字典信息
    CsDictItem selectCsDictItemOne(@Param("itemCode") String itemCode);

    //获取四级分类信息
    List<CsDictItem> selectLikeDictName(@Param("itemCode") String itemCode);

    List<Map<String, Object>> selectCodeGeneration(@Param("itemCode") String itemCode);

    int selectCounth(@Param("itemCode") String itemCode);

    List<CsDictItem> selectErDictItem(@Param("panam") Map<String, Object> panam);

    CsDictItem selectShangJiByDictCode(@Param("dictCode") String dictCode);

	void insertItDict(CsDictItem dictItem);

	void updateItDictItemValue(Map<String, Object> updateMap);

	String selectOldItItemValue(CsDictItem dictItem);

	String selectMaxCode();

	/**
	 * 查询二级分类
	 * @param firstSortCode 一级分类
	 * @return
	 */
	List<CsDictItem> selectSecSortCode(String firstSortCode);

}
