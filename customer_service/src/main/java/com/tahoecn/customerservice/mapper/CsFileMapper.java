package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsFile;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.vo.FormAndFileVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 附件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsFileMapper extends BaseMapper<CsFile> {

    Long selectbyUrl(String serverPath);

    List<CsFile> selectByFormNo(String formGenerateId);

    CsFile selectCsFileById(Long id);

    void deleteCsFile(String formNo);

    void deleteCsFileById(Long id);

    List<CsFile> selectCsFileByIdList(List<Long> list);

    Long relationFormAndFiles(FormAndFileVo vo);

    Long insertFile(CsFile cf);

}
