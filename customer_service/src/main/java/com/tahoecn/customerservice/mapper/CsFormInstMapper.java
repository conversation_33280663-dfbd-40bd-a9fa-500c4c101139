package com.tahoecn.customerservice.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.dto.AppFormListDto;
import com.tahoecn.customerservice.model.dto.ComplaintPageDto;
import com.tahoecn.customerservice.model.dto.CsFormInstDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.dto.OwnerFormDto;
import com.tahoecn.customerservice.model.excelDTO.ExpForm;
import com.tahoecn.customerservice.model.excelDTO.ItExpForm;
import com.tahoecn.customerservice.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsFormInstMapper extends BaseMapper<CsFormInst> {

    List<CsFormInst> findTemporaryList(Map<String, Object> map, Pagination page);

    List<CsFormInstDto> findReturnVisitList(Map<String, Object> map, Page<CsFormInstDto> page);

    List<CsFormInst> selectFormAssignList(Page page, Map<String, Object> map);

    List<CsFormInst> selectFormHandlingList(Page page, Map<String, Object> map);

    /**
     * 级联查询 获取工单信息
     *
     * @param rowBounds
     * @param dto
     * @return
     */
    List<CsFormInst> selectHcf(RowBounds rowBounds, HcfInfoDto dto);

    CompareVo getCompareField(Long formInstId);

    List<CsFormInst> selectUpgradeList();

    List<CsFormInst> findComplaintByCondition(Page page, ComplaintPageDto complaintPageDto);

    List<ExpForm> expForm(Map<String, Object> map);

    List<ExpForm> expForm(Page<ExpForm> page, Map<String, Object> map);

    Integer customerStatisticsReportCount(Map<String, Object> params);

    Integer customerStatisticsReportCount2(Map<String, Object> params);

    List<CustomerStatisticsReportGroupVo> customerStatisticsReportGroup(Map<String, Object> params);

    List<CustomerStatisticsReportGroupVo> customerStatisticsReportGroupNext(Map<String, Object> params);

    List<CustomerStatisticsReportGroupVo> customerStatisticsReportGroup2(Map<String, Object> params);

    List<CustomerStatisticsReportUpGroupVo> customerStatisticsReportUpRegionGroup(Map<String, Object> params);

    List<CustomerStatisticsReportUpGroupVo> customerStatisticsReportUpCityGroup(Map<String, Object> params);

    List<CustomerStatisticsReportUpNextGroupVo> customerStatisticsReportUpNextGroup(Map<String, Object> params);

    List<CustomerStatisticsSatisficingGroupVo> customerStatisticsSatisficingGroup(@Param("range") Integer rang);

    CustomerStatisticsSatisficingVo customerStatisticsSatisficingCount(@Param("range") Integer range);

    //根据报事id查询报事编号
    Map<String, Object> selectFormNo(@Param("id") Integer id);

    //根据报事编号查询所有报事信息
    CsFormInst selectAllCsFormInsByFormNo(@Param("formNo") String formNo);

    //根据工单id修改工单报事编号updateCurAssigneeName
    Boolean updateFormNo(CsFormInst csFormInst);

    Boolean updateCurAssigneeName(@Param("csFormInst") CsFormInst csFormInst);

    //根据工单id修改工单业务步骤
    Boolean updateProcessStateName(CsFormInst csFormInst);

    //根据物业报事编号进行统计
    int selectWyFormNoCount(@Param("wyFormNo") String wyFormNo);

    //根据分类code进行查询类型id
    List<Map<String, Object>> selectTypeId(@Param("secSortCode") String secSortCode, @Param("projectId") String projectId);

    //根据房屋编码获取房屋id
    String selectHoudsId(@Param("RoomIDs") String RoomIDs);

    //查询工单信息
    List<CsFormInst> selectFormInstByFourthSortCode(@Param("itemCode") String itemCode);

    //统计不同月每日平均报事量，本月
    DayOfMonthReportVo dayOfMonthReportCountCur();

    //统计不同月每日平均报事量，diff与当月的相差的月份
    DayOfMonthReportVo dayOfMonthReportCount(@Param("diff") int diff);
    //统计员工或vip报事
    int itStatisticsReportCount(Map<String, Object> params);

    //报事统计第二级分组统计 -员工
    List<CustomerStatisticsReportGroupVo> itStatisticsReportGroup(Map<String, Object> params);
    //报事统计第一级分组统计 -员工
    List<CustomerStatisticsReportGroupVo> itStatisticsReportProjectGroup(Map<String, Object> params);

	List<ItExpForm> getITList(Page<ItExpForm> page, Map<String, Object> map);

	List<ItExpForm> getITList(Map<String, Object> map);

    Integer customerStatisticsAccidentReportCount(Map<String, Object> params);

    List<CustomerStatisticsReportAccidentGroupVo> customerStatisticsAccidenReportGroup(Map<String, Object> params);

    List<ItAccidentReportNextVo> itAccidentReportNextList(Map<String, Object> params);

    List<ItAccidentReportNextVo> itAccidentReportNextQueryList(Map<String, Object> params);

    /**
     * 屏显统计每日和每月
     *
     * @param range 2当月 3当日
     * @return
     */
    List<ScreenReportCountItemVo> screenReportCountItem(@Param("range") int range);
    
    List<ScreenReportCountRegionVo> screenReportCountRegion();
    
    /**
     * 查询业主报事列表
     * @param map
     * @return
     */
    List<OwnerFormDto> getOwnerForm(Map<String, Object> map);
    List<OwnerFormDto> getOwnerForm(Page<OwnerFormDto> page, Map<String, Object> map);

    /**
     * 报事进度查询列表
     * @param map
     * @return
     */
	List<CsFormInst> getWeChatFormList(Map<String, String> map);

	/**
	 * 查询千丁编码
	 * @param qdFormNo
	 * @return
	 */
	int selectQdFormNoCount(String qdFormNo);
	
	/**
     * APP-查询报事处理，分派列表
     */
	List<CsFormInst> getAssAndHandleList(Map<String, Object> map);
	
	/**
     * APP-查询报事升级列表
     */
	List<CsFormInst> getUpgradeFormList(Map<String, Object> map);
	
	/**
     * APP-查询报事查询列表
     */
	List<CsFormInst> getDeptDCFormList(Map<String, Object> map);
	/**
     * APP-查询报事抢单列表
     */
	List<CsFormInst> getGrabFormList(Map<String, Object> map);

	/**
     * APP-查询报事升级列表数量
     * @param userName
     * @return
     */
	int selectUpgradeCount(String curAssigneeId);
	/**
	 * APP-查询报事抢单列表数量
	 * @param userName
	 * @return
	 */
	int selectGrabCount(String curAssigneeId);

}
