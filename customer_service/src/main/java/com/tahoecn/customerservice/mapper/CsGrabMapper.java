package com.tahoecn.customerservice.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsGrab;
import com.tahoecn.customerservice.model.CsProjectInfo;

/**
 * 抢单配置表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-06-21
 */
public interface CsGrabMapper extends BaseMapper<CsGrab> {

	/**
	 * 获取当前code 所有项目
	 * 
	 * @param code
	 * @return
	 */
	List<CsProjectInfo> selectProjectByCode(@Param("code") String code);

	/**
	 * 通过 code 删除数据
	 * 
	 * @param code
	 */
	void deleteByCode(@Param("code") String code);

	/**
	 * 批量添加 code
	 * 
	 * @param code
	 * @param projects
	 */
	void insertGrab(@Param("code") String code, @Param("projects") String[] projects);

}
