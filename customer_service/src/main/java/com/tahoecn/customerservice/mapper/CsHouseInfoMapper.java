package com.tahoecn.customerservice.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.session.RowBounds;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.dto.CsHouseCodeDto;
import com.tahoecn.customerservice.model.dto.CsHouseInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;

import io.lettuce.core.dynamic.annotation.Param;

/**
 * <p>
 * 房屋信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsHouseInfoMapper extends BaseMapper<CsHouseInfo> {

	//查楼栋
	List<CsHouseInfo> selectBuildings(CsHouseInfo ci);

	//查单元
	List<CsHouseInfo> selectUnits(CsHouseInfo ci);

	//楼栋为空的楼栋
	List<CsHouseInfo> selectUnitByNullBudding(CsHouseInfo ci);

	//查房间
	List<CsHouseInfo> selectRoomNums(CsHouseInfo ci);
	//楼栋为空的单元
	List<CsHouseInfo> selectRoomNumsByNullUnit(CsHouseInfo ci);

	/**
	 * 获取房人工单 信息
	 * 
	 * @param rowBounds 分页参数
	 * @param dto
	 * @return
	 */
	List<CsHouseInfo> selectHcf(RowBounds rowBounds, HcfInfoDto dto);
	
	/**
	 * 通过房号查房
	 * 
	 * @param houseNum
	 * @return
	 */
	CsHouseInfoDto selectPHouse(String houseNum);

	/**
	 * 通过房屋编码查询房屋的所有信息
	 * @param  houseInfoId
	 * @return Map
	 */
	CsHouseInfo selectAllHoustInfo(@Param("houseInfoId")String houseInfoId);
	
	/**
	 * 分页查询列表
	 * 
	 * @param map
	 * @param page
	 * @return
	 */
	List<CsHouseInfo> selectListPage(Map<String, Object> map, Page<CsHouseInfo> page);

	List<CsHouseInfo> selectListPage(Map<String, Object> map);

	int insertMyData();
	
	List<CsHouseInfoCustInfoFrom> selectCsHouseInfoCustInfoFrom(Map<String, Object> map);

	/**
	 * 获取业主房屋信息
	 * @param custId 业主id
	 * @return
	 */
	List<CsHouseInfo> selectCustHouseByCustId(String[] custId);
	
	/**
	 * 查询业主房产对应项目列表
	 * @param custId 业主id
	 * @return
	 */
	List<CsHouseCodeDto> selectCustHouseProjectByCustId(String[] custId);
	
	/**
	 * 查询业主房产对应区域列表
	 * @param custId 业主id
	 * @return
	 */
	List<CsHouseCodeDto> selectCustHouseRegionByCustId(String[] custId);

	CsHouseInfo selectAllHoustInfoBy(String houseNum);

	/**
	 * 根据房屋项目id查询区域城市
	 * @param projectId
	 * @return
	 */
	List<CsProjectInfo> selectRegionByProId(String projectId);

	/**
	 * APP-根据项目id查询房屋列表
	 */
	List<Map<String, Object>> getHouseListByProjectId(String projectId);

	/**
	 * APP-根据房屋编码查询详情，关联业主信息
	 */
	List<Map<String, Object>> getHouseDetailByNum(String houseNum);
	
	/**
	 * 初始化数据
	 */
	void initData();
	
	/**
	 * 转换物业数据
	 * 
	 * @param houseId
	 * @return
	 */
	List<CsHouseInfo> selectWyHouse(String houseId);
}
