package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsLabel;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;

/**
 * <p>
 * 标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-09
 */
public interface CsLabelMapper extends BaseMapper<CsLabel> {

	/**
	 * 级联删除标签
	 * @param labelId
	 */
	public void delLabelsById(String labelId);

	/**
	 * 查询标签列表
	 * @param map
	 * @return
	 */
	public List<CsLabel> getLabelTree(Map<String, Object> map);

	/**
	 * 查询标签列表
	 * @param page
	 * @param map
	 * @return
	 */
	public List<CsLabel> getLabelTree(Page<CsLabel> page, Map<String, Object> map);

}
