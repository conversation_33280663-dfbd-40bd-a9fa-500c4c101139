package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import java.util.List;

/**
 * <p>
 * 待办表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsProcessWorkitemMapper extends BaseMapper<CsProcessWorkitem> {
    public List getProgressWorkItemList(String formInstId);
    CsProcessWorkitem getLastProcessWorkitem(Long formInstId);

    CsProcessWorkitem selectByFormId(String id);
}
