package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.dto.StatisticalDto;
import com.tahoecn.customerservice.model.vo.CsProjectInfoNameAndCode;
import com.tahoecn.customerservice.model.vo.StatisticalVo;
import com.baomidou.mybatisplus.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 初始化项目信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsProjectInfoMapper extends BaseMapper<CsProjectInfo> {

    List<CsProjectInfo> selectProjectInfoRelation(CsProjectInfo pi);

    List<CsProjectInfoNameAndCode> getSlaveNameList(@Param("organName") String organName, @Param("cityName") String cityName);

    List<StatisticalVo> satisfaction(@Param("limitTimeStart") String limitTimeStart,@Param("limitTimeEnd") String limitTimeEnd);

    Integer getCountNum(@Param("organName") String organName, @Param("cityName") String cityName,
                        @Param("projectName") String projectName, @Param("condition") String condition, @Param("limitTimeStart") String limitTimeStart,@Param("limitTimeEnd") String limitTimeEnd, @Param("method") Integer method);

    /**
     * 查询项目相关信息
     * 根据项目编码
     */
    CsProjectInfo selectAllProjectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 查询项目code
     */
    String selectCsProjectIDImfoProjectCodeByProjectId(@Param("projectId") String projectId);

    /**
	 * 查询项目id
	 * */
	String selectCsProjectIDImfoProjectIdByProjectCode(@Param("projectCode")String projectCode);

	//查询某一区域的项目
	List<CsProjectInfo> selectProjectCodeNew(@Param("regionCode")String regionCode);

	//查询城市下的项目
	List<CsProjectInfo> selectProjectCodeNum(@Param("regionCode")String regionCode, @Param("city")String city);

    List<String> groupCity(String region);

    List<String> groupRegion();

	Map<String, Object> getProDetailByCode(@Param("code")String code, @Param("codeType")String codeType);

	/**
	 * 根据千丁项目id查询物业项目id
	 * @param regionId
	 * @return
	 */
	String selectWyIdByQdId(String regionId);

	/**
	 * APP-查询所有区域城市项目
	 * @return
	 */
	List<Map<String, Object>> getProjectList();
}
