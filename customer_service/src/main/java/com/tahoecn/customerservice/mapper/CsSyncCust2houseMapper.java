package com.tahoecn.customerservice.mapper;

import java.time.LocalDateTime;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsSyncCust2house;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public interface CsSyncCust2houseMapper extends BaseMapper<CsSyncCust2house> {

	/**
	 * 获取同步时间
	 * 
	 * @param source
	 * @return
	 */
	LocalDateTime syncDate(@Param("source") Integer source);
	
	void delMyActive();

	/**
	 * 同步明源房屋业主关系数据 (全量)
	 * 
	 * @param syncDate
	 */
	void insertMySync();

	/**
	 * 同步物业房屋业主关系数据
	 * 
	 * @param syncDate
	 */
	void insertWySync(@Param("syncDate") LocalDateTime syncDate);
	
	void delWyActive();
	
	void deleteByHouse();

}
