package com.tahoecn.customerservice.mapper;

import java.util.List;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.model.CsSyncCustAbnormal;
import com.tahoecn.customerservice.model.dto.CsSyncCustAbnormalDto;

/**
 * <p>
 * 客户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-08
 */
public interface CsSyncCustAbnormalMapper extends BaseMapper<CsSyncCustAbnormal> {

	/**
	 * 查询异常数据列表 带房间号
	 * 
	 * @param page
	 * @param abnormal
	 * @return
	 */
	List<CsSyncCustAbnormalDto> selectDtoList(Page<CsSyncCustAbnormalDto> page, CsSyncCustAbnormal abnormal);
	
	/**
	 * 清空无关系数据
	 */
	void delCustAbnormal();

}
