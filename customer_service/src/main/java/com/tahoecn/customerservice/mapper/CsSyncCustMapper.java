package com.tahoecn.customerservice.mapper;

import java.time.LocalDateTime;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsSyncCust;

/**
 * <p>
 * 客户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public interface CsSyncCustMapper extends BaseMapper<CsSyncCust> {

	/**
	 * 获取同步时间
	 * 
	 * @param source
	 * @return
	 */
	LocalDateTime syncDate(@Param("source") Integer source);

	/**
	 * 同步明源业主数据
	 * 
	 * @param syncDate
	 */
	void insertMySync(@Param("syncDate") LocalDateTime syncDate);

	/**
	 * 同步物业业主数据
	 * 
	 * @param syncDate
	 */
	void insertWySync(@Param("syncDate") LocalDateTime syncDate);
	
	/**
	 * 清理多余客户信息
	 */
	void delCust();
	
	/**
	 * 保留数据
	 * @param id
	 */
	void retainCustById(String id);
	
	// 以下五个方法为处理中间表数据及异常数据使用，顺序保持一致
	void insertCenterToCust();
	void delCenterByCust();
	void insertCustToAbn();
	void insertCenterToAbn();
	void delCenter();
	// end

}
