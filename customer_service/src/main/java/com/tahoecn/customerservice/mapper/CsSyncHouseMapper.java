package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsSyncHouse;
import com.tahoecn.customerservice.model.excelDTO.DeliverDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;

/**
 * <p>
 * 房屋信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public interface CsSyncHouseMapper extends BaseMapper<CsSyncHouse> {
	
	/**
	 * 获取同步时间
	 * 
	 * @param source
	 * @return
	 */
	LocalDateTime syncDate(@Param("source") Integer source);

	/**
	 * 同步明源房源数据
	 * 
	 * @param syncDate
	 */
	void insertMySync(@Param("syncDate") LocalDateTime syncDate);

	/**
	 * 同步物业房源数据
	 * 
	 * @param syncDate
	 */
	void insertWySync(@Param("syncDate") LocalDateTime syncDate);
	
	/**
	 * 查询 交付列表
	 * 
	 * @param page
	 * @param csSyncHouse
	 * @return
	 */
	List<CsSyncHouse> selectDelivery(Page<CsSyncHouse> page,CsSyncHouse csSyncHouse);
	
	/**
	 * 导出交付数据
	 * 
	 * @param csSyncHouse
	 * @return
	 */
	List<DeliverDto> expDeliver(CsSyncHouse csSyncHouse);
	
	/**
	 * 导出交付数据
	 * 
	 * @param list
	 * @return
	 */
	List<DeliverDto> expDeliverNew(List<Map<String, String>> list);
	
	/**
	 * 更新签约时间
	 */
	void updateSignDate();

}
