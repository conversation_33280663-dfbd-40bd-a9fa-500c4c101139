package com.tahoecn.customerservice.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsSyncMyCust;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
public interface CsSyncMyCustMapper extends BaseMapper<CsSyncMyCust> {

	/**
	 * 获取同步最后更新时间
	 * 
	 * @return
	 */
	LocalDateTime syncDate();

	/**
	 * 批量同步数据
	 * 
	 * @param list
	 */
	void insertSync(List<CsSyncMyCust> list);

}
