package com.tahoecn.customerservice.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsSyncWyCust2house;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
public interface CsSyncWyCust2houseMapper extends BaseMapper<CsSyncWyCust2house> {

	/**
	 * 获取同步最后更新时间
	 * 
	 * @return
	 */
	LocalDateTime syncDate();

	/**
	 * 批量同步数据
	 * 
	 * @param list
	 */
	Long insertSync(List<CsSyncWyCust2house> list);

}
