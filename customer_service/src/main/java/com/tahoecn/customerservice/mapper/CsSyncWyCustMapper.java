package com.tahoecn.customerservice.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsSyncWyCust;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
public interface CsSyncWyCustMapper extends BaseMapper<CsSyncWyCust> {

	/**
	 * 获取同步最后更新时间
	 * 
	 * @return
	 */
	LocalDateTime syncDate();

	/**
	 * 批量同步数据
	 * 
	 * @param list
	 */
	Long insertSync(List<CsSyncWyCust> list);

	/**
	 * 转换ID
	 * 
	 * @param projectId
	 * @param custId
	 * @return
	 */
	String convertCustId(@Param("projectId") String projectId, @Param("custId") String custId);

}
