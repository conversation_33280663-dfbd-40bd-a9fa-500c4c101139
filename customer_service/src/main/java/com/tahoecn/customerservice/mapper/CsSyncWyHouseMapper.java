package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsSyncWyHouse;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
public interface CsSyncWyHouseMapper extends BaseMapper<CsSyncWyHouse> {

	/**
	 * 获取同步最后更新时间
	 * 
	 * @return
	 */
	LocalDateTime syncDate();

	/**
	 * 批量同步数据
	 * 
	 * @param list
	 */
	Long insertSync(List<CsSyncWyHouse> list);

	/**
	 * 转换ID
	 * 
	 * @param projectId
	 * @param houseId
	 * @return
	 */
	String convertRoomId(@Param("projectId") String projectId, @Param("houseId") String houseId);

}
