package com.tahoecn.customerservice.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.dto.CsUserRoleDto;
import com.tahoecn.customerservice.model.vo.UserVo;

/**
 * <p>
 * UC用户信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsUcUserMapper extends BaseMapper<CsUcUser> {
	
	/**
	 * 用户 工单联查
	 * @return
	 */
	List<CsUcUser> userForm(Map<String, Object> map, Page<CsUcUser> page);
	
    List<CsUserRoleDto> selectRoleByUserId(String userId);

    List<UserVo> findUserByOrgId(@Param("orgId") String orgId);

	/**
	 * 查询用户信息，实现添加管理员选人功能
	 * @param username  用户名称
	 * @param usercode  用户帐号
	 * @return
	 */
	List<UserVo> findUsersByUsername(@Param("username") String username,@Param("usercode") String usercode);
}
