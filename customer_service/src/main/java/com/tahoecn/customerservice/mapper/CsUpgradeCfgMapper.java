package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsUpgradeCfg;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.CsUpgradeCfgBak;
import com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 升级配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsUpgradeCfgMapper extends BaseMapper<CsUpgradeCfg> {

    List<CsUpgradeCfgBak> selectUpgradeCfgByFourthSortCode(@Param("itemCode") String itemCode);

    void updateByIdBak(CsUpgradeCfgBak csUpgradeCfg);

    void insertByBak(CsUpgradeCfgBak csUpgradeCfg);

    List<UpgradeCfgSortVo> selectFirstSort();

    List<UpgradeCfgSortVo> selectSecSort(String sortCode);

    List<UpgradeCfgSortVo> selectThirdSort(@Param("sortCode")String sortCode, @Param("secSortCode")String secSortCode);

    List<UpgradeCfgSortVo> selectFourthSort(@Param("sortCode")String sortCode, @Param("secSortCode")String secSortCode, @Param("thirdSortCode")String thirdSortCode);

    UpgradeCfgSortVo selectThirdSortFinal(@Param("sortCode")String sortCode, @Param("secSortCode")String secSortCode, @Param("thirdSortCode")String thirdSortCode);

    List<CsUpgradeCfgBak> selectFirstSortByCode(@Param("type")String type,@Param("code")String code);

    /**
     * 手机APP-查询工单剩余处理天数
     * @param formId
     * @return
     */
    CsUpgradeCfgBak selDayByFormId(String formId);
}
