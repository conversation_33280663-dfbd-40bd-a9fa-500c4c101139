package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.CsUserRole;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.tahoecn.customerservice.model.dto.ChargerExtDo;
import com.tahoecn.customerservice.model.dto.UserRoleDictDto;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-16
 */
public interface CsUserRoleMapper extends BaseMapper<CsUserRole> {

    //查询集团相关信息
    List<CsUserRole> selcetJiTuan();

    //查询集团相关项目编码
    List<CsUserRole> selectJiTuanProjectCode();

    //查询个人信息
    CsUcUser selectCUU(@Param("userId") String userId);

    //查询区域内项目相关内容
    List<CsUserRole> selectProjectCode(@Param("regionCode")String regionCode);

    //根据区域编码查询项目编码
    List<CsUserRole> selectAllCsUserRole(@Param("projectCode")String projectCode);

    //根据区域编码和城市编码查询项目编码
    List<CsUserRole> selcetcCity(@Param("regionCode")String regionCode, @Param("city")String city);

    //查询当前集团客服负责人数
    List<CsUserRole> selectKFNum();

    //查询当前集团房修负责人数
    List<CsUserRole> selectFXNum();
    /**
     * 集团
     * @return
     */
    List<ChargerExtDo> selectType1();

    /**
     * 区域
     * @param typeName
     * @return
     */
    List<ChargerExtDo> selectType2(String typeName);

    /**
     * 城市
     * @param typeName
     * @return
     */
    List<ChargerExtDo> selectType3(String typeName);

    /**
     * 项目
     * @param typeName
     * @return
     */
    List<ChargerExtDo> selectType4(String typeName);


    void deleteUserRole();

    void deleteUserRoleQY(String projectCode);

    void deleteUserRoleCS(Long id);

	List<UserRoleDictDto> selectItUser();

	void insertUserList(List<CsUserRole> insertList);
}
