package com.tahoecn.customerservice.mapper;

import java.util.List;
import java.util.Map;

import com.tahoecn.customerservice.model.report.ChannelDto;
import com.tahoecn.customerservice.model.report.RepairWholeDto;
import com.tahoecn.customerservice.model.report.SortTreeDto;

/**
 * 报表统计接口
 * 
 * @ClassName ReportFormMapper
 * <AUTHOR>
 * @date 2019年1月9日
 */
public interface ReportFormMapper {

	/**
	 * 报修整体数据统计
	 * 
	 * @param map
	 * @return
	 */
	List<RepairWholeDto> repairWhole(Map<String, Object> map);

	/**
	 * 报事渠道统计
	 * 
	 * @param map
	 * @return
	 */
	List<ChannelDto> channel(Map<String, Object> map);

	/**
	 * 获取统计树
	 * 
	 * @param map
	 * @return
	 */
	List<SortTreeDto> sortTree(Map<String, Object> map);

}
