package com.tahoecn.customerservice.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.tahoecn.customerservice.model.UserInfo;
//import com.tahoecn.standard.model.vo.UserVo;

/**
 * <p>
 * 用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-10-10
 */
public interface UserInfoMapper extends BaseMapper<UserInfo> {

//	public List<UserVo> findUserByOrgId(@Param("orgId") String orgId);
//	
//	/**
//	 * 查询用户信息，实现添加管理员选人功能
//	 * @param username  用户名称
//	 * @param usercode  用户帐号
//	 * @return
//	 */
//	List<UserVo> findUsersByUsername(@Param("username") String username,@Param("usercode") String usercode);
}
