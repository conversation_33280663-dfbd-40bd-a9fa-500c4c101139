package com.tahoecn.customerservice.mapper;

import com.tahoecn.customerservice.model.User;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-16
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 将SQL放在XML中
     * @return
     */
    List<Map<String,Object>> selectListByXml();

    /**
     * 将SQL放在java类中
     * @return
     */
    @Select("select * from User")
    List<Map<String,Object>> selectListByAnnotation();
}
