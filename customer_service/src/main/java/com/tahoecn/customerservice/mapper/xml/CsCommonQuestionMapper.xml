<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsCommonQuestionMapper">
    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsCommonQuestion">
        <result column="id" property="id" jdbcType="DECIMAL" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="question_desc" property="questionDesc" jdbcType="VARCHAR" />
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
        <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
        <result column="order_by" property="orderBy" jdbcType="DECIMAL" />
        <result column="question_title" property="questionTitle" jdbcType="VARCHAR" />
        <result column="is_delete" property="isDelete" jdbcType="SMALLINT" />
    </resultMap>

    <select id="selectCommQuestionAndPersonal" parameterType="java.lang.String" resultMap="BaseResultMap">
        select * from cs_common_question
        where is_delete = 0 AND (user_name is null or user_name = #{userName})
        order by user_name desc,order_by,last_update_date desc
    </select>

    <insert id="insertCommonQuestion" parameterType="com.tahoecn.customerservice.model.CsCommonQuestion">
        insert into cs_common_question
        (user_name,question_desc,create_date,last_update_date,order_by,question_title,is_delete)
        values (#{userName},#{questionDesc},#{createDate},#{lastUpdateDate},#{orderBy},#{questionTitle},0)
    </insert>

</mapper>
