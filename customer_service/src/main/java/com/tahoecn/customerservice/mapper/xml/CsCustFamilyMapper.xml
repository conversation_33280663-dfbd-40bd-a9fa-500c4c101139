<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsCustFamilyMapper">

	<!-- 查询家庭成员 -->
	<select id="findCustFamilyInfo" resultType="com.tahoecn.customerservice.model.CsCustFamily">
		SELECT *
		FROM cs_cust_family cu
		WHERE 1=1 
		<if test="certificateNum != null and certificateNum != ''">
			AND cu.id_number like concat('%',#{certificateNum},'%')
		</if>
		<if test="telephone != null and telephone != ''">
			AND cu.mobile like concat('%',#{telephone},'%')
		</if>
	</select>
</mapper>
