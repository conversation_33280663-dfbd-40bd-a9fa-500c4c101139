<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsCustInfoMapper">
 	
 	<select id="selectHcf" resultType="com.tahoecn.customerservice.model.CsCustInfo">
 		SELECT distinct
			c.cust_id AS cust_id,
			c.cust_name AS cust_name,
			c.telephone AS telephone
		FROM
			cs_cust_info c 
			<if test="(area != null and area != '') or (city != null and city != '') or (project != null and project != '') or (building != null and building != '') or (sHouseName != null and sHouseName != '') or (sHouseNum != null and sHouseNum != '') or (unit != null and unit != '') or (roomNum != null and roomNum != '')">
				LEFT JOIN cs_house_info h ON c.house_num = h.house_num
				<if test="(area != null and area != '') or (city != null and city != '') or (project != null and project != '')">
					LEFT JOIN cs_project_info p ON h.project_id = p.project_code
				</if>
			</if>
 			
			<if test="createDateStart != null or createDateEnd != null or (processStateCode != null and processStateCode != '')">
				LEFT JOIN cs_form_inst f ON c.cust_id = f.owner_id
			</if>
 		<where>
 			<choose>
				<when test="houseNum != null and houseNum != ''">
					and c.house_num = #{houseNum}
				</when>
				<otherwise>
		 			<if test="area != null and area != ''">
						and p.region_code = #{area}
					</if>
					<if test="city != null and city != ''">
						and p.city_company_code = #{city}
					</if>
					<if test="project != null and project != ''">
						and p.project_code = #{project}
					</if>
					<if test="building != null and building != ''">
						and h.building = #{building}
					</if>
					<if test="telephone != null and telephone != ''">
						and (c.telephone like concat('%',#{telephone},'%') or ( SELECT count(1) FROM cs_cust_family y WHERE y.cust_id = c.cust_id and y.mobile like concat('%',#{telephone},'%') ) > 0)
					</if>
					<if test="sHouseNum != null and sHouseNum != ''">
						and h.house_num like concat('%',#{sHouseNum},'%')
					</if>
					<if test="sHouseName != null and sHouseName != ''">
						and h.house_name like concat('%',#{sHouseName},'%')
					</if>
					<if test="roomNum != null and roomNum != ''">
						and h.room_num like concat('%',#{roomNum},'%')
					</if>
					<if test="unit != null and unit != ''">
						and h.unit = #{unit}
					</if>
					
					<if test="createDateStart != null">
						and f.creation_date &gt;= #{createDateStart}
					</if>
					<if test="createDateEnd != null">
						and f.creation_date is not null and f.creation_date &lt; DATE_ADD(#{createDateEnd},INTERVAL 1 DAY) 
					</if>
					<if test="processStateCode != null and processStateCode != ''">
						and f.process_state_code = #{processStateCode}
					</if>
				</otherwise>
			</choose>
 		</where>
 	</select>
 
	<select id="selectByCustId" resultType="com.tahoecn.customerservice.model.CsCustInfo">
		SELECT *
		FROM cs_cust_info cci
		WHERE cci.cust_id = #{custId}
	</select>
	
	<select id="selectCustInfoList" parameterType="map" resultType="com.tahoecn.customerservice.model.dto.CsCustInfoDto">
		SELECT DISTINCT
			ci.custHouseCount,
			c.cust_id,
			c.cust_name,
			c.cus_identity,
			c.belong,
			c.sex,
			c.is_vip,
			c.sug_leader,
			c.telephone,
			c.province,
			c.city,
			c.area,
			c.contact_address,
			c.fixed_telephone,
			c.email,
			c.fax,
			c.postcode,
			c.national,
			c.certificate_name,
			c.certificate_num,
			c.birthday,
			c.work_unit,
			c.profession,
			c.years_receive,
			c.is_f_zgold_card,
			c.is_has_more_house,
			c.is_medical_care_user,
			c.is_finance_user,
			c.is_real_estate_user,
			c.is_education_user,
			c.is_cinema_user,
			c.has_car,
			c.hobbies
		FROM
			(SELECT cust_id,COUNT(*) custHouseCount FROM cs_cust_info GROUP BY cust_id) ci
			LEFT JOIN cs_cust_info c ON ci.cust_id = c.cust_id
			LEFT JOIN cs_house_info h ON c.house_num = h.house_num
			LEFT JOIN cs_project_info p ON h.project_id = p.project_code
		WHERE 
			1 = 1 			
			<if test="sex != null and sex != ''">
				AND c.sex = #{sex} 
			</if>
			<if test="email != null and email != ''">
				AND c.email like concat('%',#{email},'%') 
			</if>
			<if test="stewardName != null and stewardName != ''">
				AND h.steward_name like concat('%',#{stewardName},'%') 
			</if>
			<if test="national != null and national != ''">
				AND c.national like concat('%',#{national},'%')
			</if>			
			<if test="houseNum != null and houseNum != ''">
				AND c.house_num = #{houseNum} 
			</if>
			<if test="sHouseNum != null and sHouseNum != ''">
				AND c.house_num like concat('%',#{sHouseNum},'%') 
			</if>
			<if test="sHouseName != null and sHouseName != ''">
				AND h.house_name like concat('%',#{sHouseName},'%') 
			</if>
			<if test="custName != null and custName != ''">
				AND c.cust_name like concat('%',#{custName},'%') 
			</if>
			<if test="telephone != null and telephone != ''">
				AND c.telephone like concat('%',#{telephone},'%') 
			</if>
			<if test="certificateNum != null and certificateNum != ''">
				AND c.certificate_num like concat('%',#{certificateNum},'%') 
			</if>
			<if test="belong == '个人'">
				AND (c.belong = '0' OR c.belong ='个人')
			</if>
			<if test="belong == '单位'">
				AND (c.belong = '1' OR c.belong ='单位')
			</if>
			<if test="isHasMoreHouse != null and isHasMoreHouse != ''">
				AND c.is_has_more_house = #{isHasMoreHouse}
			</if>
			<if test="bStartDate != null">
				AND c.birthday &gt;= #{bStartDate} 
			</if>
			<if test="bEndDate != null">
				AND c.birthday is not null AND c.birthday &lt; DATE_ADD(#{bEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="houseNum != null and houseNum != ''">
				AND c.house_num like concat('%',#{houseNum},'%')
			</if>
			<if test="cusIdentity != null and cusIdentity != ''">
				AND c.cus_identity = #{cusIdentity}
			</if>
			<if test="regionCode != null and regionCode != ''">
				AND p.region_code = #{regionCode}
			</if>
			<if test="cityCompanyCode != null and cityCompanyCode != ''">
				AND p.city_company_code = #{cityCompanyCode}
			</if>
			<if test="cityCode != null and cityCode != ''">
				AND p.city_code = #{cityCode}
			</if>
			<if test="projectCode != null and projectCode != ''">
				AND p.project_code = #{projectCode}
			</if>
			
			<if test="pageSize != null">
		      <if test="count != null">
		        	limit ${count}, ${pageSize}
		      </if>
		      <if test="count == null">
		        	limit ${pageSize}
		      </if>
    		</if>
	</select>
	
	
	<select id="getListCount" parameterType="map" resultType="int">
		SELECT 
			<!-- count(DISTINCT CONCAT(c.cust_id,IFNULL(c.steward_name,""))) --> 
			count(DISTINCT c.cust_id) 
		FROM 
			cs_cust_info c
		<if test="(stewardName != null and stewardName != '') or (regionCode != null and regionCode != '') or (cityCompanyCode != null and cityCompanyCode != '') or (projectCode != null and projectCode != '') or (sHouseName != null and sHouseName != '')">
			LEFT JOIN cs_house_info h ON c.house_num = h.house_num
			LEFT JOIN cs_project_info p ON h.project_id = p.project_code
		</if>
		<where>
<!-- 			<if test="houseNum != null and houseNum != ''">
				AND c.house_num = #{houseNum} 
			</if>
			<if test="sHouseNum != null and sHouseNum != ''">
				AND c.house_num like concat('%',#{sHouseNum},'%') 
			</if>
			<if test="sHouseName != null and sHouseName != ''">
				AND h.house_name like concat('%',#{sHouseName},'%') 
			</if>
			<if test="custName != null and custName != ''">
				AND c.cust_name like concat('%',#{custName},'%') 
			</if>
			<if test="telephone != null and telephone != ''">
				AND c.telephone like concat('%',#{telephone},'%') 
			</if>
			<if test="certificateNum != null and certificateNum != ''">
				AND c.certificate_num like concat('%',#{certificateNum},'%') 
			</if>
			<if test="belong != null and belong != ''">
				AND c.belong = #{belong} 
			</if>
			<if test="isHasMoreHouse != null and isHasMoreHouse != ''">
				AND c.is_has_more_house = #{isHasMoreHouse}
			</if>
			<if test="cusIdentity != null and cusIdentity != ''">
				AND c.cus_identity = #{cusIdentity}
			</if>
			<if test="bStartDate != null">
				AND c.birthday &gt;= #{bStartDate} 
			</if>
			<if test="bEndDate != null">
				AND c.birthday is not null AND c.birthday &lt; DATE_ADD(#{bEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="houseNum != null and houseNum != ''">
				AND c.house_num like concat('%',#{houseNum},'%')
			</if>
			<if test="regionCode != null and regionCode != ''">
				AND p.region_code = #{regionCode}
			</if>
			<if test="cityCompanyCode != null and cityCompanyCode != ''">
				AND p.city_company_code = #{cityCompanyCode}
			</if>
			<if test="cityCode != null and cityCode != ''">
				AND p.city_code = #{cityCode}
			</if>
			<if test="projectCode != null and projectCode != ''">
				AND p.project_code = #{projectCode}
			</if> -->
			<if test="sex != null and sex != ''">
				AND c.sex = #{sex} 
			</if>
			<if test="email != null and email != ''">
				AND c.email = #{email} 
			</if>
			<if test="stewardName != null and stewardName != ''">
				AND h.steward_name = #{stewardName} 
			</if>
			<if test="national != null and national != ''">
				AND c.national = #{national} 
			</if>			
			<if test="sHouseNum != null and sHouseNum != ''">
				AND c.house_num like concat('%',#{sHouseNum},'%') 
			</if>
			<if test="sHouseName != null and sHouseName != ''">
				AND h.house_name like concat('%',#{sHouseName},'%') 
			</if>
			<if test="custName != null and custName != ''">
				AND c.cust_name like concat('%',#{custName},'%') 
			</if>
			<if test="telephone != null and telephone != ''">
				AND c.telephone like concat('%',#{telephone},'%') 
			</if>
			<if test="certificateNum != null and certificateNum != ''">
				AND c.certificate_num like concat('%',#{certificateNum},'%') 
			</if>
			<if test="belong == '个人'">
				AND (c.belong = '0' OR c.belong ='个人')
			</if>
			<if test="belong == '单位'">
				AND (c.belong = '1' OR c.belong ='单位')
			</if>
			<if test="isHasMoreHouse != null and isHasMoreHouse != ''">
				AND c.is_has_more_house = #{isHasMoreHouse}
			</if>
			<if test="bStartDate != null">
				AND c.birthday &gt;= #{bStartDate} 
			</if>
			<if test="bEndDate != null">
				AND c.birthday is not null AND c.birthday &lt; DATE_ADD(#{bEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="houseNum != null and houseNum != ''">
				AND c.house_num like concat('%',#{houseNum},'%')
			</if>
			<if test="cusIdentity != null and cusIdentity != ''">
				AND c.cus_identity = #{cusIdentity}
			</if>
			<if test="regionCode != null and regionCode != ''">
				AND p.region_code = #{regionCode}
			</if>
			<if test="cityCompanyCode != null and cityCompanyCode != ''">
				AND p.city_company_code = #{cityCompanyCode}
			</if>
			<if test="cityCode != null and cityCode != ''">
				AND p.city_code = #{cityCode}
			</if>
			<if test="projectCode != null and projectCode != ''">
				AND p.project_code = #{projectCode}
			</if>
		</where>
	</select>
	
	<insert id="insertMyData">
		INSERT INTO cs_cust_info (<!-- id,  -->cust_id, cust_name, house_num, certificate_num, certificate_name, fixed_telephone, 
			 telephone, fax, contact_address, postcode, email, belong, sex, national, birthday, work_unit, profession, 
			 create_date, update_date,cus_identity, source)
			(SELECT
				<!-- -myc.id AS id, -->
				myc.cust_id,
				myc.cust_name,
				myc.house_num,
				myc.certificate_num,
				myc.certificate_name,
				myc.fixed_telephone,
				myc.telephone,
				myc.fax,
				myc.contact_address,
				myc.postcode,
				myc.email,
				myc.belong,
				myc.sex,
				myc.national,
				myc.birthday,
				myc.work_unit,
				myc.profession,
				myc.create_date,
				myc.update_date,
				'准业主' AS cus_identity,
				2 source
			FROM
				cs_sys_my_cust myc
			WHERE
				myc.house_num IN (
					SELECT myh.house_num FROM cs_sys_my_house myh
						WHERE 
							myh.project_id IN ( SELECT cpi.project_code FROM cs_project_info cpi WHERE cpi.project_source = 2 )
				)
			)
	</insert>
	
	<update id="updateHasMoreHouse">
		UPDATE cs_cust_info SET is_has_more_house = 1 WHERE certificate_num IN (
			SELECT t.certificate_num FROM (
					SELECT DISTINCT certificate_num FROM cs_cust_info GROUP BY certificate_num HAVING COUNT(*) > 1 
			) t
		)
	</update>
	
	<update id="updateNoHasMoreHouse">
		UPDATE cs_cust_info SET is_has_more_house = 0
	</update>
	<update id="insertTeltempData">
		INSERT INTO cs_cust_info_teltemp SELECT * FROM cs_cust_info where cs_cust_info.source = 2;
	</update>
	<select id="selectDifferentTel" resultType="java.util.HashMap">
		select distinct a.cust_id as custId,a.telephone as telephoneMy,b.telephone as telephonebak from cs_cust_info a,cs_cust_info_teltemp b where a.cust_id = b.cust_id and a.telephone != b.telephone;
	</select>
	<update id="updateTelBycustId" parameterType="map">
		update cs_cust_info set cs_cust_info.telephone = #{telAll} where cs_cust_info.cust_id = #{custId};
	</update>
	<update id="deleteTeltempDate">
		delete from cs_cust_info_teltemp;
	</update>
	<update id="updateCanNotDelete">
		UPDATE cs_cust_info,
		cs_cust_info_teltemp 
		SET cs_cust_info.years_receive = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_f_zgold_card = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_has_more_house = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_Medical_Care_User = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_finance_user = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_real_estate_user = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_education_user = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_cinema_user = cs_cust_info_teltemp.years_receive,
		cs_cust_info.has_car = cs_cust_info_teltemp.years_receive,
		cs_cust_info.contact_tel = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_vip = cs_cust_info_teltemp.years_receive,
		cs_cust_info.sug_leader = cs_cust_info_teltemp.years_receive,
		cs_cust_info.we_chat = cs_cust_info_teltemp.we_chat,
		cs_cust_info.open_id = cs_cust_info_teltemp.open_id,
		cs_cust_info.label_id = cs_cust_info_teltemp.label_id,
		cs_cust_info.label_name = cs_cust_info_teltemp.label_name,
		cs_cust_info.integral = cs_cust_info_teltemp.integral
		WHERE
			cs_cust_info.cust_id = cs_cust_info_teltemp.cust_id;
	</update>
	
	<select  id="selectCsHouseInfoCustInfoFrom" parameterType="map" resultType="com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom">
	SELECT DISTINCT
			h.house_num,
			h.house_name,
			p.region AS area,
			p.city,
			h.project,
			h.building,
			h.unit,
			h.room_num,
			h.fitment,
			h.use_property,
			h.delivery_date,
			h.sign_date,
			h.focus_start_date,
			h.focus_end_date,
			h.actual_delivery_date,
			h.off_aid_date,
			h.obtain_time,
			h.stay_time,
			h.delivery_status,
			c.cust_name,
			c.telephone,
			c.fixed_telephone,
			c.fax,
			c.certificate_name,
			c.certificate_num,
			c.cus_identity,
			c.belong,
			c.sex,
			c.is_vip,
			c.sug_leader,
			c.province,
			c.city as cust_city,
			c.area as cust_area,
			c.contact_address,
			c.postcode,
			c.email,
			h.steward_name,
			h.steward_telephone,			
			c.national,
			c.birthday,
			c.work_unit,
			c.profession,
			c.years_receive,
			c.is_f_zgold_card,
			c.is_has_more_house,
			c.is_medical_care_user,
			c.is_finance_user,
			c.is_real_estate_user,
			c.is_education_user,
			c.is_cinema_user,
			c.has_car,
			c.hobbies,
			c.other_board_member,
			c.special_customer
			
		FROM
			cs_cust_info c
			LEFT JOIN cs_house_info h ON c.house_num = h.house_num
			LEFT JOIN cs_project_info p ON h.project_id = p.project_code
		WHERE 1=1
			<if test="sex != null and sex != ''">
				AND c.sex = #{sex} 
			</if>
			<if test="email != null and email != ''">
				AND c.email = #{email} 
			</if>
			<if test="stewardName != null and stewardName != ''">
				AND h.steward_name = #{stewardName} 
			</if>
			<if test="national != null and national != ''">
				AND c.national = #{national} 
			</if>			
			<if test="sHouseNum != null and sHouseNum != ''">
				AND c.house_num like concat('%',#{sHouseNum},'%') 
			</if>
			<if test="sHouseName != null and sHouseName != ''">
				AND h.house_name like concat('%',#{sHouseName},'%') 
			</if>
			<if test="custName != null and custName != ''">
				AND c.cust_name like concat('%',#{custName},'%') 
			</if>
			<if test="telephone != null and telephone != ''">
				AND c.telephone like concat('%',#{telephone},'%') 
			</if>
			<if test="certificateNum != null and certificateNum != ''">
				AND c.certificate_num like concat('%',#{certificateNum},'%') 
			</if>
			<if test="belong == '个人'">
				AND (c.belong = '0' OR c.belong ='个人')
			</if>
			<if test="belong == '单位'">
				AND (c.belong = '1' OR c.belong ='单位')
			</if>
			<if test="isHasMoreHouse != null and isHasMoreHouse != ''">
				AND c.is_has_more_house = #{isHasMoreHouse}
			</if>
			<if test="bStartDate != null">
				AND c.birthday &gt;= #{bStartDate} 
			</if>
			<if test="bEndDate != null">
				AND c.birthday is not null AND c.birthday &lt; DATE_ADD(#{bEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="houseNum != null and houseNum != ''">
				AND c.house_num like concat('%',#{houseNum},'%')
			</if>
			<if test="cusIdentity != null and cusIdentity != ''">
				AND c.cus_identity = #{cusIdentity}
			</if>
			<if test="regionCode != null and regionCode != ''">
				AND p.region_code = #{regionCode}
			</if>
			<if test="cityCompanyCode != null and cityCompanyCode != ''">
				AND p.city_company_code = #{cityCompanyCode}
			</if>
			<if test="cityCode != null and cityCode != ''">
				AND p.city_code = #{cityCode}
			</if>
			<if test="projectCode != null and projectCode != ''">
				AND p.project_code = #{projectCode}
			</if>
			
<!-- 			<if test="pageSize != null">
		      <if test="count != null">
		        	limit ${count}, ${pageSize}
		      </if>
		      <if test="count == null">
		        	limit ${pageSize}
		      </if>
    		</if> -->
	</select>
	
	<!-- 查询业主基本信息 -->
	<select id="findCustInfo" resultType="com.tahoecn.customerservice.model.CsCustInfo">
		SELECT *
		FROM cs_cust_info cu
		WHERE 1=1 
		<if test="certificateNum != null and certificateNum != ''">
			AND cu.certificate_num = #{certificateNum} 
		</if>
		<if test="telephone != null and telephone != ''">
			AND cu.telephone like concat('%',#{telephone},'%')
		</if>
	</select>
	
	<!-- 根据业主id和房屋num查询业主信息 -->
	<select id="selectCustByIdNum" resultType="com.tahoecn.customerservice.model.CsCustInfo">
		SELECT *
		FROM cs_cust_info cu
		WHERE 1=1 
		<if test="houseNum != null and houseNum != ''">
			AND cu.house_num = #{houseNum}
		</if>
		<if test="custId != null and custId != ''">
			AND cu.cust_id in 
			<foreach item="id" index="index" collection="custId" open="(" separator="," close=")">  
				#{id} 
			</foreach>
		</if>
	</select>
	<!-- 查询客户信息 -->
	<select id="getCustInfo" parameterType="map" resultType="com.tahoecn.customerservice.model.CsCustInfo">
		SELECT *
		FROM cs_cust_info cu
		WHERE 1=1 
		<if test="cusIdentity != null and cusIdentity != ''">
			AND cu.cus_identity = #{cusIdentity}
		</if>
		<if test="sex != null and sex != ''">
			AND cu.sex = #{sex}
		</if>
		<if test="birthdayStart != null and birthdayStart != ''">
                AND cu.birthday <![CDATA[ >= ]]> #{birthdayStart}
		</if>
		<if test="birthdayEnd != null and birthdayEnd != ''">
			AND cu.birthday <![CDATA[ <= ]]> #{birthdayEnd}
		</if>
		<if test="query != null and query != ''">
			AND (cu.cust_name like CONCAT('%',#{query},'%') or cu.house_num like CONCAT('%',#{query},'%') 
			or cu.telephone like CONCAT('%',#{query},'%') or cu.fixed_telephone like CONCAT('%',#{query},'%'))
		</if>
		order by CONVERT(cust_name USING GBK)
		<if test="pageSize != null">
		      <if test="count != null">
		        	limit ${count}, ${pageSize}
		      </if>
		      <if test="count == null">
		        	limit ${pageSize}
			</if>
		</if>
	</select>
	<!-- APP-更新业主标签 -->
	<update id="updateCustLabel">
		UPDATE cs_cust_info SET label_id = #{labelId},label_name = #{labelName} WHERE cust_id = #{custId}
	</update>
	<!-- 手机APP-更改客户头像 -->
	<update id="updateHeadPortrait">
		UPDATE cs_cust_info SET head_portrait = #{headPortrait} WHERE cust_id = #{custId}
	</update>
	
	
	<update id="syncDeleteTeltempDate">
		delete from cs_cust_info_teltemp;
	</update>
	<update id="syncInsertTeltemp">
		INSERT INTO cs_cust_info_teltemp SELECT * FROM cs_cust_info;
	</update>
	<update id="syncUpdateCanNotDelete">
		UPDATE cs_cust_info,
		cs_cust_info_teltemp 
		SET cs_cust_info.years_receive = cs_cust_info_teltemp.years_receive,
		cs_cust_info.is_f_zgold_card = cs_cust_info_teltemp.is_f_zgold_card,
		cs_cust_info.is_has_more_house = cs_cust_info_teltemp.is_has_more_house,
		cs_cust_info.is_Medical_Care_User = cs_cust_info_teltemp.is_Medical_Care_User,
		cs_cust_info.is_finance_user = cs_cust_info_teltemp.is_finance_user,
		cs_cust_info.is_real_estate_user = cs_cust_info_teltemp.is_real_estate_user,
		cs_cust_info.is_education_user = cs_cust_info_teltemp.is_education_user,
		cs_cust_info.is_cinema_user = cs_cust_info_teltemp.is_cinema_user,
		cs_cust_info.has_car = cs_cust_info_teltemp.has_car,
		cs_cust_info.contact_tel = cs_cust_info_teltemp.contact_tel,
		cs_cust_info.is_vip = cs_cust_info_teltemp.is_vip,
		cs_cust_info.sug_leader = cs_cust_info_teltemp.sug_leader,
		cs_cust_info.we_chat = cs_cust_info_teltemp.we_chat,
		cs_cust_info.open_id = cs_cust_info_teltemp.open_id,
		cs_cust_info.label_id = cs_cust_info_teltemp.label_id,
		cs_cust_info.label_name = cs_cust_info_teltemp.label_name,
		cs_cust_info.integral = cs_cust_info_teltemp.integral
		WHERE
			cs_cust_info.cust_id = cs_cust_info_teltemp.certificate_num;
	</update>
	
	<insert id="initData">
		INSERT INTO cs_cust_info (
			cust_id,
			cust_name,
			house_num,
			certificate_num,
			certificate_name,
			fixed_telephone,
			telephone,
			fax,
			contact_address,
			postcode,
			email,
			belong,
			sex,
			national,
			birthday,
			work_unit,
			profession,
			hobbies,
			create_date,
			update_date,
			source,
			cus_identity 
		) SELECT
			c.certificate_num,
			c.cust_name,
			ch.house_code,
			c.certificate_num,
			c.certificate_name,
			c.fixed_telephone,
			c.telephone,
			c.fax,
			c.contact_address,
			c.postcode,
			c.email,
			c.belong,
			c.sex,
			c.national,
			c.birthday,
			c.work_unit,
			c.profession,
			c.hobbies,
			c.create_date,
			c.update_date,
			c.source,
			IF ( c.source = 1, "业主", "准业主" ) 
		FROM
			cs_sync_cust c
			LEFT JOIN cs_sync_cust2house ch ON c.certificate_num = ch.certificate_num 
		<!-- ON DUPLICATE KEY UPDATE cust_name = c.cust_name,
			certificate_name = c.certificate_name,
			fixed_telephone = c.fixed_telephone,
			telephone = c.telephone,
			fax = c.fax,
			contact_address = c.contact_address,
			postcode = c.postcode,
			email = c.email,
			belong = c.belong,
			sex = c.sex,
			national = c.national,
			birthday = c.birthday,
			work_unit = c.work_unit,
			profession = c.profession,
			hobbies = c.hobbies,
			source = c.source,
			cus_identity = IF ( c.source = 1, "业主", "准业主" ) -->
	</insert>
	
	<update id="retainCust">
		UPDATE cs_cust_info ci , cs_sync_cust sc
		SET ci.cust_name = sc.cust_name,
			ci.certificate_name = sc.certificate_name,
			ci.fixed_telephone = sc.fixed_telephone,
			ci.telephone = sc.telephone,
			ci.fax = sc.fax,
			ci.contact_address = sc.contact_address,
			ci.postcode = sc.postcode,
			ci.email = sc.email,
			ci.belong = sc.belong,
			ci.sex = sc.sex,
			ci.national = sc.national,
			ci.birthday = sc.birthday,
			ci.work_unit = sc.work_unit,
			ci.profession = sc.profession,
			ci.hobbies = sc.hobbies,
			ci.source = sc.source,
			ci.cus_identity = IF ( sc.source = 1, "业主", "准业主" )
		where ci.certificate_num = sc.certificate_num AND ci.certificate_num = #{certificateNum}
	</update>
	
	<select id="selectWyCust" resultType="com.tahoecn.customerservice.model.CsCustInfo">
		SELECT c.* FROM cs_cust_info c LEFT JOIN cs_sync_wy_cust w ON c.certificate_num = w.PaperCode WHERE w.CustID = #{custId}
	</select>
</mapper>
