<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsDictItemMapper">

    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsDictItem">
        <result column="id" property="id" jdbcType="DECIMAL" />
        <result column="dict_id" property="dictId" jdbcType="DECIMAL" />
        <result column="dict_code" property="dictCode" jdbcType="VARCHAR" />
        <result column="dict_name" property="dictName" jdbcType="VARCHAR" />
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
        <result column="item_value" property="itemValue" jdbcType="VARCHAR" />
        <result column="display_order" property="displayOrder" jdbcType="DECIMAL" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
        <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- 查询字典组-->
    <select id="selectAllCsDictItemByDictCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        di.dict_code,di.dict_name,di.item_code,
        di.item_value,di.display_order,di.status
        from cs_dict_item di
        where di.dict_code = #{dictCode}
        and di.status = '1'
        order by di.display_order
    </select>

    <!-- 字典项查询-->
    <select id="selectCsDictItemByItemCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        di.dict_code,di.dict_name,di.item_code,
        di.item_value,di.display_order,di.status
        from cs_dict_item di
        where di.item_code = #{itemCode}
        and di.status = '1'
        order by di.display_order
    </select>

    <!-- 多字典项列表查询-->
    <select id="selectAllCsDictItemByList" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        di.dict_code,di.dict_name,di.item_code,
        di.item_value,di.display_order,di.status
        from cs_dict_item di
        where di.status = '1' and di.item_code in
        <foreach collection="list" item="item" index="index"  open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by di.display_order
    </select>

    <!-- 初始化列表查询-->
    <select id="selectAllCsDictItem" resultMap="BaseResultMap">
        select
        di.dict_id,
        di.dict_code,di.dict_name,di.item_code,
        di.item_value,di.display_order,di.status
        from cs_dict_item di
        where di.status = '1'
        and di.dict_id not in ('5','6','7')
        order by di.dict_code,di.display_order
    </select>

    <!-- 查询一级分类信息 -->
    <select id="selectFirstSortCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsDictItem">
        select * from
        cs_dict_item cdi
        where cdi.item_code = #{firstSortCode}
    </select>

    <select id="selectListDictItem" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
          id,
          dict_id as "dictId",
          dict_code as "dictCode",
          dict_name as "dictName",
          item_code as "itemCode",
          item_value as "itemValue",
          display_order as "displayOrder",
          status,
          last_update_date as "lastUpdateDate"
        from cs_dict_item cdi
        where cdi.dict_id = #{panam.dictId}
        <if test="panam.itemCode != null and panam.itemCode != ''">
            and cdi.dict_code = #{panam.itemCode}
        </if>
        <if test="panam.dictCode != null and panam.dictCode != ''">
            and cdi.dict_code = #{panam.dictCode}
        </if>
        order by display_order asc
    </select>

    <select id="selectOneDictItem" parameterType="java.lang.String" resultType="java.util.Map">
        select
          id,
          dict_id as "dictId",
          dict_code as "dictCode",
          dict_name as "dictName",
          item_code as "itemCode",
          item_value as "itemValue",
          display_order as "displayOrder",
          status,
          last_update_date as "lastUpdateDate"
        from cs_dict_item cfi
        where cfi.item_code = #{itemCode}
    </select>

    <select id="selectCsDictItemOne" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsDictItem">
        select
          id,
          dict_id as "dictId",
          dict_code as "dictCode",
          dict_name as "dictName",
          item_code as "itemCode",
          item_value as "itemValue",
          display_order as "displayOrder",
          last_update_date as "lastUpdateDate"
        from cs_dict_item cfi
        where cfi.item_code = #{itemCode}
    </select>

    <select id="selectLikeDictName" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsDictItem">
        select
          *
        from cs_dict_item cfi
        where cfi.dict_code = #{itemCode}
    </select>

    <select id="selectCodeGeneration" parameterType="java.lang.String" resultType="java.util.HashMap">
        select item_code as "itemCode", display_order as "displayOrder" from cs_dict_item where dict_code = #{itemCode} and (item_code like '%000%' or item_code like '%00%' or item_code like '%0%') ORDER BY item_code asc
    </select>

    <select id="selectCounth" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from cs_dict_item where dict_code = #{itemCode}
    </select>

    <select id="selectErDictItem" parameterType="java.util.HashMap" resultType="com.tahoecn.customerservice.model.CsDictItem">
        select * from cs_dict_item cfi where cfi.dict_code = #{panam.itemCode}
    </select>

    <select id="selectShangJiByDictCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsDictItem">
        select * from cs_dict_item cfi where cfi.item_code = #{dictCode}
    </select>
    
    <insert id="insertItDict" parameterType="com.tahoecn.customerservice.model.CsDictItem">
    	INSERT INTO cs_dict_item (dict_id, dict_code, dict_name, item_code, item_value, display_order, status, remarks, creation_date, last_update_date) 
    		VALUES (#{dictId}, #{dictCode}, #{dictName}, #{itemCode}, #{itemValue}, #{displayOrder}, #{status}, #{remarks}, NOW(), NOW());
    </insert>
    
    <select id="selectOldItItemValue"  parameterType="com.tahoecn.customerservice.model.CsDictItem" resultType="String">
    	SELECT item_value FROM cs_dict_item WHERE remarks = 'IT' AND item_code=#{itemCode} limit 1
    </select>
    
    <update id="updateItDictItemValue" parameterType="java.util.HashMap">
		update cs_dict_item set 
			item_value = 
				REPLACE(item_value,#{oldItemValue},#{itemValue}),
			status = #{status}  
			where item_value like CONCAT(#{oldItemValue},'%') and remarks = 'IT'
    </update>
    
    <select id="selectMaxCode" resultType="String">
    	SELECT
			max(t.code)
		FROM
			(
				SELECT
					substring_index(item_code, '_', - 1) AS code
				FROM
					cs_dict_item
				WHERE
					remarks = 'IT'
				AND dict_id = 644
			) t
		WHERE
			t.code > 0
    </select>
    
    <!-- 查询二级分类信息 -->
    <select id="selectSecSortCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsDictItem">
        select * from cs_dict_item 
        where cs_dict_item.item_value='品质报事'
        and cs_dict_item.dict_code = #{firstSortCode}
    </select>
</mapper>
