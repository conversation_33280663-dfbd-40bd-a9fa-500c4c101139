<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsFileMapper">
    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsFile">
        <result column="id" property="id" jdbcType="DECIMAL" />
        <result column="form_generate_id" property="formGenerateId" jdbcType="VARCHAR" />
        <result column="file_name" property="fileName" jdbcType="VARCHAR" />
        <result column="file_type" property="fileType" jdbcType="VARCHAR" />
        <result column="server_path" property="serverPath" jdbcType="VARCHAR" />
        <result column="client_path" property="clientPath" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 查主键-->
    <select id="selectbyUrl" parameterType="java.lang.String" resultType="java.lang.Long">
        select id from cs_file where server_path = #{serverPath}
    </select>

    <!-- 插入-->
    <insert id="insertFile" parameterType="com.tahoecn.customerservice.model.CsFile">
        insert into cs_file (form_generate_id,file_name,file_type,server_path,client_path,status)
        values (#{formGenerateId},#{fileName},#{fileType},#{serverPath},#{clientPath},#{status})
    </insert>

    <!-- 更新-->

    <!-- 多id查询-->
    <select id="selectCsFileByIdList" parameterType="java.util.List" resultMap="BaseResultMap">
        select * from cs_file where status = '-1' and id in
        <foreach collection="list" item="item" index="index"  open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 根据单据查询-->
    <select id="selectByFormNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select * from cs_file where form_generate_id = #{formGenerateId} and status = '-1'
    </select>

    <!-- 根据id查询-->
    <select id="selectCsFileById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select * from cs_file where id = #{id} and status = '-1'
    </select>

    <!-- 批量删除-->
    <update id="deleteCsFile" parameterType="java.lang.String">
        update cs_file set status = '1' where form_generate_id = #{formGenerateId}
    </update>

    <!-- 单个附件删除-->
    <update id="deleteCsFileById" parameterType="java.lang.Long">
        update cs_file set status = '1' where id = #{id}
    </update>

    <!-- 附件关联工单-->
    <update id="relationFormAndFiles" parameterType="com.tahoecn.customerservice.model.vo.FormAndFileVo">
        update cs_file set form_generate_id = #{id}  where status = '-1' and id in
        <foreach collection="ids" item="item" index="index"  open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
