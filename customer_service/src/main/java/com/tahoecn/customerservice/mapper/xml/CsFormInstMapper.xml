<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsFormInstMapper">

    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsFormInst">

        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="form_no" property="formNo" jdbcType="VARCHAR"/>
        <result column="dept_code" property="deptCode" jdbcType="VARCHAR"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="first_sort_code" property="firstSortCode"
                jdbcType="VARCHAR"/>
        <result column="first_sort_name" property="firstSortName"
                jdbcType="VARCHAR"/>
        <result column="sec_sort_code" property="secSortCode" jdbcType="VARCHAR"/>
        <result column="sec_sort_name" property="secSortName" jdbcType="VARCHAR"/>
        <result column="project" property="project" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="owner_id" property="ownerId" jdbcType="BIGINT"/>
        <result column="owner_name" property="ownerName" jdbcType="VARCHAR"/>
        <result column="third_sort_code" property="thirdSortCode"
                jdbcType="VARCHAR"/>
        <result column="third_sort_name" property="thirdSortName"
                jdbcType="VARCHAR"/>
        <result column="third_sort_name" property="thirdSortName"
                jdbcType="VARCHAR"/>
        <result column="creation_date" property="creationDate"
                jdbcType="TIMESTAMP"/>
        <result column="assign_id" property="assignId" jdbcType="VARCHAR"/>
        <result column="assign_name" property="assignName" jdbcType="VARCHAR"/>
        <result column="assign_date" property="assignDate" jdbcType="TIMESTAMP"/>
        <result column="cur_assignee_id" property="curAssigneeId"
                jdbcType="VARCHAR"/>
        <result column="cur_assignee_name" property="curAssigneeName"
                jdbcType="VARCHAR"/>
        <result column="process_state_code" property="processStateCode"
                jdbcType="VARCHAR"/>
        <result column="process_state_name" property="processStateName"
                jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName"
                jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findTemporaryList" parameterType="java.util.HashMap"
            resultMap="BaseResultMap">
        select * from cs_form_inst cfi
        WHERE process_state_code = 'draft'
        <if test="curAssigneeId != null and curAssigneeId != '' ">
            AND EXISTS(select DISTINCT(form_inst_id) from cs_process_workitem b where b.assign_id = #{curAssigneeId} and
            cfi.id = b.form_inst_id )
        </if>
        <if test="regionCode!=null and regionCode!=''">
            AND (cfi.region = #{regionCode} OR cfi.region_code = #{regionCode})
        </if>
        <if test="cityCode!=null and cityCode!=''">
            AND (cfi.city = #{cityCode} OR cfi.city_code = #{cityCode})
        </if>

        <if test="projectCode!=null and projectCode!=''">
            AND cfi.project_code = #{projectCode}
        </if>

        <if test="buildingNoCode!=null and buildingNoCode!=''">
            AND cfi.building_no = #{buildingNoCode}
        </if>

        <if test="mobile!=null and mobile!=''">
            AND cfi.mobile LIKE CONCAT('%',#{mobile},'%')
        </if>

        <if test="startDate!=null and startDate!=''">
            AND cfi.creation_date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate!=null and endDate!='' ">
            AND cfi.creation_date <![CDATA[ <= ]]> #{endDate}
        </if>
        order by cfi.creation_date
    </select>

    <select id="findReturnVisitList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.dto.CsFormInstDto">
        SELECT * FROM
			(
				SELECT
					c.*,
					cp.rVStartTime
				FROM
					cs_form_inst c 
					<!-- 报事回访开始时间查询  chenyy 20190114-->
					LEFT JOIN 
					(SELECT DISTINCT
						form_inst_id,
						MIN(task_start_time) rVStartTime
					FROM
						cs_process_workitem
					WHERE
						process_state_code = 'returnVisit'
					GROUP BY
						form_inst_id
					) cp ON cp.form_inst_id = c.id 
					<!-- 报事回访开始时间查询   chenyy 20190114-->
				WHERE
					EXISTS (
						SELECT DISTINCT
							cpw.form_inst_id
						FROM
							cs_process_workitem cpw
						WHERE
							cpw.process_state_code IN (
								'toBeAssigned',
								'handle',
								'upgrade',
								'returnVisit',
								'nomalEnd',
								'specialEnd'
							)
						AND cpw.form_inst_id = c.id
					)
			) cfi
		WHERE
			1 = 1
		AND cfi.first_sort_code IN ('coBX', 'coTS')
		AND cfi.process_code != 'processKSCL'
        <if test="formNo!=null and formNo!=''">
            AND cfi.form_no LIKE CONCAT('%',#{formNo},'%')
        </if>
        <if test="deptCode!=null and deptCode!=''">
            AND cfi.dept_code = #{deptCode}
        </if>
        <if test="firstSortCode!=null and firstSortCode!=''">
            AND cfi.first_sort_code = #{firstSortCode}
        </if>
        <if test="curAssigneeName !=null and curAssigneeName !=''">
            AND cfi.cur_assignee_name = #{curAssigneeName}
        </if>
        <if test="assignName != null and assignName!=''">
            and assign_name like CONCAT('%',#{assignName},'%')
        </if>
        <if test="processStateCode!=null and processStateCode!=''">
            AND cfi.process_state_code = #{processStateCode}
        </if>
        <if test="startDate!=null and startDate!=''">
            AND cfi.creation_date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
            AND cfi.creation_date <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="ownerName != null and ownerName != ''">
            AND cfi.owner_name like CONCAT('%',#{ownerName},'%')
        </if>
        <if test="mobile != null and mobile != ''">
            AND cfi.mobile like CONCAT('%',#{mobile},'%')
        </if>
        <if test="regionCode!=null and regionCode!=''">
            AND (cfi.region = #{regionCode} OR cfi.region_code = #{regionCode})
        </if>
        <if test="cityCode!=null and cityCode!=''">
            AND (cfi.city = #{cityCode} OR cfi.city_code = #{cityCode})
        </if>

        <if test="projectCode!=null and projectCode!=''">
            AND cfi.project_code = #{projectCode}
        </if>

        <if test="rVStartTimeBegin != null and rVStartTimeBegin != ''">
            AND cfi.rVStartTime <![CDATA[ >= ]]> #{rVStartTimeBegin}
        </if>
        <if test="rVStartTimeEnd != null and rVStartTimeEnd != '' and rVStartTimeEnd != rVStartTimeBegin ">
            AND cfi.rVStartTime <![CDATA[ <= ]]> #{rVStartTimeEnd}
        </if>
        <!-- order by cfi.rework_flag desc,cfi.upgrade_flag desc,cfi.creation_date -->
        ORDER BY cfi.rework_flag desc, cfi.upgrade_flag desc,<!-- 报事回访开始时间查询   chenyy 20190114-->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                 cfi.creation_date ${orderBy}
            </when>
            <otherwise>
                cfi.rVStartTime DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectFormAssignList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
        select * from cs_form_inst a
        where a.cur_assignee_id = #{curAssigneeId}
        and 1=1 and process_state_code = 'toBeAssigned'
        <!-- and project_code = #{projectCode} -->
        <!-- <if test="firstListForPermission != null and firstListForPermission.size > 0">
            AND first_sort_code IN
            <foreach collection="firstListForPermission" open="(" close=")"
                     separator="," index="index" item="item">
                #{firstListForPermission[${index}]}
            </foreach>
        </if>
        <if test="ProjectListForPermission != null and ProjectListForPermission.size > 0">
            AND project_code IN
            <foreach collection="ProjectListForPermission" open="(" close=")"
                     separator="," index="index" item="item">
                #{ProjectListForPermission[${index}]}
            </foreach>
        </if>
        <if test="curAssigneeName != null and curAssigneeName != '' ">
            and cur_assignee_name = #{curAssigneeName}
        </if> -->
        <if test="formNo != null and formNo!=''">
            and form_no like CONCAT('%',#{formNo},'%')
        </if>
        <!-- <if test="processCode != null and processCode!=''">
            and process_code = #{processCode}
        </if> -->
        <if test="firstSortCode != null and firstSortCode!=''">
            and first_sort_code = #{firstSortCode}
        </if>
        <!-- <if test="processStateCode != null and processStateCode!=''">
            and process_state_code = #{processStateCode}
        </if> -->
        order by a.rework_flag desc,
        a.upgrade_flag desc,
        <choose>
            <when test="orderBy != null and orderBy !=''">
                a.creation_date ${orderBy}
            </when>
            <otherwise>
                a.creation_date
            </otherwise>
        </choose>
    </select>

    <select id="selectFormHandlingList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
        select * from cs_form_inst a
        where a.cur_assignee_id = #{curAssigneeId}
        <!-- and (assign_id = ${assignId}) -->
        <if test="curAssigneeName != null and curAssigneeName != '' ">
            and cur_assignee_name = #{curAssigneeName}
        </if>
        <if test="formNo != null and formNo!=''">
            and form_no like CONCAT('%',#{formNo},'%')
        </if>
        <if test="deptCode != null and deptCode!=''">
            and dept_code = #{deptCode}
        </if>
        <if test="firstSortCode != null and firstSortCode!=''">
            and first_sort_code = #{firstSortCode}
        </if>
        <if test="processStateCode != null and processStateCode!=''">
            and process_state_code = #{processStateCode}
        </if>
        <if test="startDate!=null and startDate!=''">
            AND a.assign_date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
            AND a.assign_date <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="ownerName != null and ownerName != ''">
            AND a.owner_name like CONCAT('%',#{ownerName},'%')
        </if>
        <if test="mobile != null and mobile != ''">
            AND a.mobile like CONCAT('%',#{mobile},'%')
        </if>
        <if test="roomNo != null and roomNo != '' ">
            and a.room_no like CONCAT('%',#{roomNo},'%')
        </if>
        order by a.rework_flag desc,a.upgrade_flag desc,
        <choose>
            <when test="orderBy != null and orderBy !=''">
                a.creation_date ${orderBy}
            </when>
            <otherwise>
                a.creation_date
            </otherwise>
        </choose>

    </select>

    <select id="selectHcf" resultType="com.tahoecn.customerservice.model.CsFormInst">
        select f.* from cs_form_inst f
        <where>
            <if test="deptCode == null or deptCode == ''">
                and f.dept_code != 'deptIT'
            </if>
            <if test="deptCode != null and deptCode != ''">
                and f.dept_code = #{deptCode}
            </if>
            <choose>
                <when test="(houseNum == null or houseNum == '') and (custId == null or custId == '')">
                    <if test="area != null and area != ''">
                        and f.region_code = #{area}
                    </if>
                    <if test="city != null and city != ''">
                        and f.city_code = #{city}
                    </if>
                    <if test="project != null and project != ''">
                        and f.project_code = #{project}
                    </if>
                    <if test="building != null and building != ''">
                        and f.building_no = #{building}
                    </if>
                    <if test="unit != null and unit != ''">
                        and f.building_unit = #{unit}
                    </if>
                    <if test="telephone != null and telephone != ''">
                        and f.mobile like concat('%',#{telephone},'%')
                    </if>
                    <if test="sHouseNum != null and sHouseNum != ''">
                        and f.house_info_id like concat('%',#{sHouseNum},'%')
                    </if>
                    <if test="sHouseName != null and sHouseName != ''">
                        and f.house_name like concat('%',#{sHouseName},'%')
                    </if>
					<if test="roomNum != null and roomNum != ''">
						and f.room_no like concat('%',#{roomNum},'%')
					</if>
                    <if test="createDateStart != null">
                        and f.creation_date &gt;= #{createDateStart}
                    </if>
                    <if test="createDateEnd != null">
                        and f.creation_date is not null and f.creation_date &lt;= DATE_ADD(#{createDateEnd},INTERVAL 1
                        DAY)
                    </if>
                    <if test="processStateCode != null and processStateCode != ''">
                        and f.process_state_code = #{processStateCode}
                    </if>
                </when>
                <otherwise> <!-- 房找人 -->
                    <if test="houseNum != null and houseNum != ''">
                        and f.house_info_id = #{houseNum}
                    </if>
                    <if test="custId != null and custId != ''">
                        and f.owner_id = #{custId}
                    </if>
                </otherwise>
            </choose>
            
            <!-- <if test="curAssigneeId != null and curAssigneeId != '' ">
            	and (
            		EXISTS(select DISTINCT(form_inst_id) from cs_process_workitem b where b.assign_id =
	                #{curAssigneeId} and f.id = b.form_inst_id )
	                or f.process_state_code = 'draft' or f.create_user_id = #{curAssigneeId} 
            	)
            </if> -->
        </where>
        order by case when f.process_state_code = 'draft' then 1 end desc,
        <choose>
            <when test="orderBy != null and orderBy != ''">
                f.creation_date ${orderBy}
            </when>
            <otherwise>
                f.creation_date
            </otherwise>
        </choose>
    </select>


    <select id="getCompareField" resultType="com.tahoecn.customerservice.model.vo.CompareVo">
		select sec_sort_name,third_sort_name,fourth_sort_name,problem_level_name,
		problem_position_name,revision_classification_name,
		decoration_stage_name,maintenance_period_name,inter_res_department_name,
		 main_res_unit,repair_unit,third_party_flag,third_party_name,third_party_reason from cs_form_inst where id=#{formInstId}
	</select>

    <select id="selectUpgradeList" resultType="com.tahoecn.customerservice.model.CsFormInst">
        select a.id,
        a.form_no,
        a.dept_code,
        a.dept_name,
        a.regist_code,
        a.regist_name,
        a.process_code,
        a.process_name,
        a.first_sort_code,
        a.first_sort_name,
        a.sec_sort_code,
        a.sec_sort_name,
        a.third_sort_code,
        a.third_sort_name,
        a.fourth_sort_code,
        a.fourth_sort_name,
        a.problem_level_code,
        a.problem_level_name,
        a.problem_position_code,
        a.problem_position_name,
        a.revision_classification_code,
        a.revision_classification_name,
        a.decoration_stage_code,
        a.decoration_stage_name,
        a.maintenance_period_code,
        a.maintenance_period_name,
        a.inter_res_department_code,
        a.inter_res_department_name,
        a.owner_name,
        a.owner_type,
        a.orther_member,
        a.special_user,
        a.mobile,
        a.nationality,
        a.id_code,
        a.id_name,
        a.id_no,
        a.birth_date,
        a.work_unit,
        a.occupation,
        a.hobby,
        a.fax_phone,
        a.contact_address,
        a.fixed_telephone,
        a.e_mail,
        a.postal_code,
        a.housekeeper_name,
        a.housekeeper_tel,
        a.remark,
        a.common_problem_code,
        a.common_problem_name,
        a.customer_demand,
        a.house_no,
        a.house_name,
        a.region_code,
        a.region,
        a.city_code,
        a.city,
        a.project_code,
        a.project,
        a.building_no,
        a.building_unit,
        a.room_no,
        a.use_property_code,
        a.use_property_name,
        a.contract_delivery_time,
        a.signing_time,
        a.estimated_release_time,
        a.focus_delivery_time_from,
        a.focus_delivery_time_to,
        a.delivery_state,
        a.hardcover_state,
        a.actual_deliver_time,
        a.check_in_time,
        a.assign_id,
        a.assign_name,
        a.assign_date,
        a.creation_date,
        a.cur_assignee_id,
        a.cur_assignee_name,
        a.process_state_code,
        a.process_state_name,
        a.last_update_date,
        a.updrade_reason,
        a.owner_id,
        a.satisfaction_code,
        a.satisfaction_name,
        a.upgrade_flag,
        a.rework_flag,
        a.reject_flag,
        a.gender_code,
        a.gender_name,
        a.lock_user_id,
        a.lock_time,
        a.house_info_id,
        a.create_user_id,
        a.create_user_name,
        a.handle_record,
        a.form_generate_id,
        a.report_channel_code,
        a.report_channel_name,
        a.accept_channel_code,
        a.accept_channel_name,
        a.submit_date,
        a.main_res_unit,
        a.repair_unit,
        a.third_party_flag,
        a.third_party_name,
        a.third_party_reason,
        a.change_record,
        IFNULL(a.upgrade_level,0) as upgrade_level,
        case when TIMESTAMPDIFF(DAY, a.submit_date, CURRENT_TIMESTAMP()) = b.city_days then 1 <!--城市-->
        when TIMESTAMPDIFF(DAY, a.submit_date, CURRENT_TIMESTAMP()) = b.region_days then 2 <!--区域-->
        when TIMESTAMPDIFF(DAY, a.submit_date, CURRENT_TIMESTAMP()) = b.group_dasy then 3 <!--集团-->
        else 0
        end as schedual_upgrade_level
        from cs_form_inst a left join cs_upgrade_cfg b on a.first_sort_code = b.first_sort_code and
        a.sec_sort_code = b.sec_sort_code and
        a.third_sort_code = b.third_sort_code
        and IFNULL(a.fourth_sort_code,'') = IFNULL(b.fourth_sort_code,'')
        where a.process_state_code = 'handle' and
        a.first_sort_code in ('coTS','coBX') and
        a.submit_date is not null
        and (
        TIMESTAMPDIFF(DAY, a.submit_date, CURRENT_TIMESTAMP()) = b.city_days
        or TIMESTAMPDIFF(DAY, a.submit_date, CURRENT_TIMESTAMP()) = b.region_days
        or TIMESTAMPDIFF(DAY, a.submit_date, CURRENT_TIMESTAMP()) = b.group_dasy
        ) and a.creation_date > '2018-12-25'
    </select>
    <!--<select id="findComplaintByCondition" resultType="com.tahoecn.customerservice.model.CsFormInst">
		select id,form_no,sec_sort_name,third_sort_name,cur_assignee_name,owner_name,region,city,project,creation_date,complaint_headlines,TIMESTAMPDIFF(DAY,creation_date,now()) owner_type from cs_form_inst
		where process_code ='processBZ' and first_sort_code='coTS' and ( process_state_code='handle' or process_state_code='returnVisit' or process_state_code='toBeAssigned')
		  and problem_level_code='problemHigh' and <![CDATA[DATE_ADD(creation_date,INTERVAL 70 DAY) <= NOW()]]> order by owner_type
    </select>-->

    <select id="findComplaintByCondition" resultType="com.tahoecn.customerservice.model.CsFormInst">
        select id,form_no,sec_sort_name,third_sort_name,cur_assignee_name,owner_name,region,city,project,creation_date,complaint_headlines,TIMESTAMPDIFF(DAY,creation_date,now()) owner_type from cs_form_inst
        where process_code ='processBZ' and first_sort_code='coTS' and ( process_state_code='handle' or process_state_code='returnVisit' or process_state_code='toBeAssigned')
        and (<![CDATA[DATE_ADD(creation_date,INTERVAL 70 DAY) <= NOW()]]>  or problem_level_code='problemHigh') order by owner_type
    </select>

    <select id="customerStatisticsReportCount" resultType="java.lang.Integer">
        select count(1) from cs_form_inst where process_code ='processBZ' and process_state_code!='draft'
        <if test="firstSortCode ==null or firstSortCode==''">
            and (first_sort_code='coTS' or
            first_sort_code='coBX')
        </if>
        <if test="processStateCode!=null and processStateCode!=''">
            and process_state_code like concat('%End')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>

        <if test="upgradeFlag!=null and upgradeFlag!=''">
            and upgrade_flag=#{upgradeFlag}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
    </select>

    <select id="customerStatisticsReportGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportGroupVo">
        select (select item_value from cs_dict_item where item_code=a.first_sort_code) firstSortName,(select item_value
        from cs_dict_item where item_code=a.sec_sort_code) secSortName,a.c groupCount,b.c finishCount from
        (select first_sort_code,sec_sort_code,count(1) c from cs_form_inst where process_code ='processBZ' and
        process_state_code!='draft' and (first_sort_code='coTS' or first_sort_code='coBX')
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code,sec_sort_code) a
        left join (select first_sort_code,sec_sort_code,count(1) c from cs_form_inst where process_code ='processBZ' and
        process_state_code!='draft' and (first_sort_code='coTS' or first_sort_code='coBX') and
        process_state_code='nomalEnd'
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code,sec_sort_code) b
        on a.first_sort_code=b.first_sort_code and a.sec_sort_code=b.sec_sort_code
    </select>
    <select id="customerStatisticsReportGroupNext"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportGroupVo">
        select (select item_value from cs_dict_item where item_code=a.first_sort_code) firstSortName ,a.first_sort_code
        firstSortCode,(select item_value from cs_dict_item where item_code=a.sec_sort_code) secSortName,a.sec_sort_code
        secSortCode,a.c groupCount,b.c finishCount from
        (select first_sort_code,sec_sort_code,count(1) c from cs_form_inst where process_state_code!='draft' and
        first_sort_code=#{firstSortCode}
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code,sec_sort_code) a
        left join (select first_sort_code,sec_sort_code,count(1) c from cs_form_inst where process_state_code!='draft'
        and first_sort_code=#{firstSortCode}
        and process_state_code like concat('%End')
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code,sec_sort_code) b
        on a.first_sort_code=b.first_sort_code and a.sec_sort_code=b.sec_sort_code
    </select>
    <select id="customerStatisticsReportCount2" resultType="java.lang.Integer">
        select count(1) from cs_form_inst where process_state_code!='draft'
        <if test="firstSortCode ==null or firstSortCode==''">
            and first_sort_code in('coTS','coBX','coJYBY','coZX')
        </if>
        <if test="processStateCode!=null and processStateCode!=''">
            and process_state_code like concat('%End')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>

        <if test="upgradeFlag!=null and upgradeFlag!=''">
            and upgrade_flag=#{upgradeFlag}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
    </select>

    <select id="customerStatisticsReportGroup2"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportGroupVo">
        select (select item_value from cs_dict_item where item_code=a.first_sort_code) firstSortName,a.first_sort_code
        firstSortCode,a.c groupCount,b.c finishCount from
        (select first_sort_code,count(1) c from cs_form_inst where process_state_code!='draft' and first_sort_code
        in('coTS','coBX','coJYBY','coZX')
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code) a
        left join (select first_sort_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and first_sort_code in('coTS','coBX','coJYBY','coZX')
        and process_state_code like concat('%End')
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code) b
        on a.first_sort_code=b.first_sort_code
    </select>

    <select id="customerStatisticsReportUpRegionGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportUpGroupVo">
        select a.region regionName,a.c regionAllCount,b.c regionCount from (select region,count(1) c from cs_form_inst
        where process_code ='processBZ' and process_state_code!='draft'
        <if test="firstSortCode ==null or firstSortCode==''">
            and (first_sort_code='coTS' or
            first_sort_code='coBX')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by region) a
        left join (select region,count(1) c from cs_form_inst
        where process_code ='processBZ' and process_state_code!='draft' and upgrade_flag=1
        <if test="firstSortCode ==null or firstSortCode==''">
            and (first_sort_code='coTS' or
            first_sort_code='coBX')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by region) b on a.region=b.region
    </select>

    <select id="customerStatisticsReportUpCityGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportUpGroupVo">
        select a.city regionName,a.c regionAllCount,b.c regionCount from (select city,count(1) c from cs_form_inst
        where process_code ='processBZ' and process_state_code!='draft'
        <if test="firstSortCode ==null or firstSortCode==''">
            and (first_sort_code='coTS' or
            first_sort_code='coBX')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by city) a
        left join (select city,count(1) c from cs_form_inst
        where process_code ='processBZ' and process_state_code!='draft' and upgrade_flag=1
        <if test="firstSortCode ==null or firstSortCode==''">
            and (first_sort_code='coTS' or
            first_sort_code='coBX')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by city) b on a.city=b.city
    </select>

    <select id="customerStatisticsReportUpNextGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportUpNextGroupVo">
        select (select item_value from cs_dict_item where item_code=first_sort_code) firstSortName,(select item_value
        from cs_dict_item where item_code=sec_sort_code) secSortName,count(1) groupCount from cs_form_inst
        where process_code ='processBZ' and process_state_code!='draft' and upgrade_flag=1
        <if test="firstSortCode ==null or firstSortCode==''">
            and (first_sort_code='coTS' or
            first_sort_code='coBX')
        </if>
        <if test="firstSortCode !=null and firstSortCode!=''">
            and first_sort_code=#{firstSortCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="region!=null and region!='' and region==1 and regionName!=null">
            and city=#{regionName}
        </if>
        <if test="region!=null and region!='' and region==2 and regionName!=null">
            and region=#{regionName}
        </if>
        group by first_sort_code,sec_sort_code
    </select>

    <select id="customerStatisticsSatisficingGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsSatisficingGroupVo">
        select region regionName,sum(satisfaction_code) satisfactionSum,count(1) satisfactionGroup from cs_form_inst
        where process_state_code='nomalEnd' and (first_sort_code='coTS' or
        first_sort_code='coBX')
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( DATE_SUB(CURDATE(), INTERVAL 1 MONTH) ,'%Y%m')
        </if>
        group by region
    </select>

    <select id="customerStatisticsSatisficingCount"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsSatisficingVo">
        select sum(satisfaction_code) satisfactionSum,count(1) satisfactionAll from cs_form_inst
        where process_state_code='nomalEnd' and (first_sort_code='coTS' or
        first_sort_code='coBX') and satisfaction_code is not null and satisfaction_code != '-1'
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
    </select>

    <select id="expForm" resultType="com.tahoecn.customerservice.model.excelDTO.ExpForm">
        SELECT * FROM (
        SELECT
	        f.id as id,
	        f.form_no as formNo,
	        f.house_no as houseNo,
	        f.house_name as houseName,
	        f.region as region,
	        f.city as city,
	        f.project as project,
	        f.building_no as buildingNo,
	        f.building_unit as buildingUnit,
	        f.room_no as roomNo,
	        f.owner_name as ownerName,
	        f.mobile as mobile,
	        f.housekeeper_name as housekeeperName,
	        f.housekeeper_tel as housekeeperTel,
	        f.report_channel_name as reportChannelName,
	        f.accept_channel_code as acceptChannelCode,
	        f.process_state_name as processStateName,
	        f.process_name as processName,
	        f.dept_name as deptName,
	        f.first_sort_name as firstSortName,
	        f.sec_sort_name as secSortName,
	        f.third_sort_name as thirdSortName,
	        f.fourth_sort_name as fourthSortName,
	        f.problem_level_name as problemLevelName,
	        f.problem_position_name as problemPositionName,
	        f.revision_classification_name as revisionClassificationName,
	        f.decoration_stage_name as decorationStageName,
	        f.inter_res_department_name as interResDepartmentName,
	        f.maintenance_period_name as maintenancePeriodName,
	        f.customer_demand as customerDemand,
	        f.handle_record as handleRecord,
	        f.repair_unit as repairUnit,
	        f.third_party_flag as thirdPartyFlag,
	        f.third_party_reason as thirdPartyReason,
	        f.third_party_name as thirdPartyName,
	        f.creation_date as creationDate,
	        f.assign_date as assignDate,
	        f.create_user_name as createUserName,
	        f.cur_assignee_name as curAssigneeName,
            f.cur_assignee_id as curAssigneeId,
	        f.rework_flag as reworkFlag,
	        f.upgrade_flag as upgradeFlag,
	        (SELECT pw.task_end_time FROM cs_process_workitem pw WHERE pw.form_inst_id = f.id AND pw.process_state_code = 'handle' ORDER BY pw.task_end_time DESC LIMIT 1) AS runTime,
	        MAX(IF(p.process_state_code = 'handle',p.last_update_date,null)) AS h,
	        MAX(IF(p.process_state_code = 'upgrade',p.last_update_date,null)) AS upTime,
	        (SELECT assign_name FROM cs_process_workitem WHERE id = MAX(IF(p.process_state_code = 'upgrade',p.id,0))) AS upUser,
	        MAX(IF(f.rework_flag = 1 and p.process_state_code = 'toBeAssigned',p.creation_date,null)) AS doTime,
	        (SELECT assign_name FROM cs_process_workitem WHERE id = MAX(IF(f.rework_flag = 1 and p.process_state_code = 'toBeAssigned',p.id,0))) AS doUser 
        FROM
        	cs_form_inst f
        	LEFT JOIN cs_process_workitem p ON p.form_inst_id = f.id
        <where>
            f.dept_code !='deptIT'
            <if test="regionCode!=null and regionCode!=''">
                AND (f.region = #{regionCode} OR f.region_code = #{regionCode})
            </if>
            <if test="cityCode!=null and cityCode!=''">
                AND (f.city = #{cityCode} OR f.city_code = #{cityCode})
            </if>

            <if test="projectCode!=null and projectCode!=''">
                AND f.project_code = #{projectCode}
            </if>

            <if test="formNo !=null and formNo !=''">
                AND f.form_no LIKE CONCAT('%',#{formNo},'%')
            </if>

            <if test="ownerName !=null and ownerName !=''">
                AND f.owner_name LIKE CONCAT('%',#{ownerName},'%')
            </if>

            <if test="mobile!=null and mobile!=''">
                AND f.mobile LIKE CONCAT('%',#{mobile},'%')
            </if>

            <if test="processStateCode !=null and processStateCode !=''">
                <!-- AND f.process_state_code = #{processStateCode} -->
                AND LOCATE(f.process_state_code,#{processStateCode}) > 0
            </if>

            <if test="acceptChannelCode !=null and acceptChannelCode !=''">
                AND f.accept_channel_code = #{acceptChannelCode}
            </if>

            <if test="deptCode !=null and deptCode !=''">
                AND f.dept_code = #{deptCode}
            </if>

            <if test="customerDemand !=null and customerDemand !=''">
                AND f.customer_demand like concat('%',#{customerDemand},'%')
            </if>

            <if test="firstSortCode !=null and firstSortCode !=''">
                AND f.first_sort_code = #{firstSortCode}
            </if>

            <if test="reworkFlag !=null and reworkFlag !=''">
                AND (f.rework_flag = #{reworkFlag}
                <if test="reworkFlag == '-1'">
                    OR f.rework_flag is null
                </if>)
            </if>

            <if test="upgradeFlag !=null and upgradeFlag !=''">
                AND (f.upgrade_flag = #{upgradeFlag}
                <if test="upgradeFlag == '-1'">
                    OR f.upgrade_flag is null
                </if>)
            </if>


            <if test="startDate!=null and startDate!=''">
                AND f.creation_date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate!=null and endDate!='' ">
                AND f.creation_date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="astartDate!=null and astartDate!=''">
                AND f.assign_date <![CDATA[ >= ]]> #{astartDate}
            </if>
            <if test="aendDate!=null and aendDate!=''">
                AND f.assign_date <![CDATA[ <= ]]> #{aendDate}
            </if>
            <if test="rstartDate!=null and rstartDate!=''">
                AND (SELECT pw.task_end_time FROM cs_process_workitem pw WHERE pw.form_inst_id = f.id AND pw.process_state_code = 'handle' ORDER BY pw.task_end_time DESC LIMIT 1)<![CDATA[ >= ]]> #{rstartDate}
            </if>
            <if test="rendDate!=null and rendDate!='' ">
                AND (SELECT pw.task_end_time FROM cs_process_workitem pw WHERE pw.form_inst_id = f.id AND pw.process_state_code = 'handle' ORDER BY pw.task_end_time DESC LIMIT 1) <![CDATA[ <= ]]> #{rendDate}
            </if>
            <if test="createUserName != null and createUserName != ''">
                AND f.create_user_name like concat('%',#{createUserName},'%')
            </if>
            <if test="curAssigneeName != null and curAssigneeName != ''">
                AND f.cur_assignee_name like concat('%',#{curAssigneeName},'%')
            </if>


            <!-- 数据权限处理 -->
            <if test="(fdOrgSid != null and fdOrgSid != '') or (userName != null and userName != '') ">
                AND (
                <if test="fdOrgSid != null and fdOrgSid != ''">
                    (LOCATE(f.region_code,#{fdOrgSid}) > 0 OR LOCATE(f.city_code,#{fdOrgSid}) > 0 OR
                    LOCATE(f.project_code,#{fdOrgSid}) > 0)
                </if>
                <if test="fdOrgSid != null and fdOrgSid != '' and userName != null and userName != ''">
                    OR
                </if>
                <if test="userName != null and userName != ''">
                    (f.create_user_id = #{userName} OR p.assign_id = #{userName})
                </if>)
            </if>
            <if test="isFromHome != 1">
	            <if test="isAdmin != 1 and isSiBaiSeats != 1">
	            	AND (
						(
							f.process_state_code = 'draft'
							AND f.create_user_id = #{userName}
						)
						OR f.process_state_code != 'draft'
					)
	            </if>
            </if>
            <if test="isFromHome == 1">
	            <!-- 首页穿透不看暂存 -->
           		AND f.process_state_code != 'draft'
            </if>
        </where>
        GROUP BY f.id
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY f.creation_date ${orderBy}
            </when>
            <otherwise>
                ORDER BY f.id
            </otherwise>
        </choose>
        ) a
    </select>

    <select id="selectFormInstByFourthSortCode" parameterType="java.lang.String"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
        select
        *
        from cs_form_inst cfi where cfi.fourth_sort_code = #{itemCode}
    </select>
    <select id="selectFormNo" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        select
        cfi.form_no as "formNo"
        from cs_form_inst cfi where cfi.id = #{id}
    </select>
    <select id="selectAllCsFormInsByFormNo" parameterType="java.lang.String"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
        select *
        from cs_form_inst cfi where cfi.form_no = #{formNo}
    </select>

    <update id="updateFormNo" parameterType="com.tahoecn.customerservice.model.CsFormInst">
        update cs_form_inst cfi set cfi.form_no = #{formNo} where cfi.id = #{id}
    </update>

    <update id="updateCurAssigneeName" parameterType="com.tahoecn.customerservice.model.CsFormInst">
        update cs_form_inst cfi set cfi.cur_assignee_name = #{csFormInst.curAssigneeName} , cfi.cur_assignee_id = #{csFormInst.curAssigneeId}  where cfi.id = #{csFormInst.id}
    </update>

    <update id="updateProcessStateName" parameterType="com.tahoecn.customerservice.model.CsFormInst">
        update cs_form_inst cfi set cfi.process_state_name = #{processStateName} where cfi.id = #{id}
    </update>

    <select id="selectWyFormNoCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select COUNT(*) from cs_form_inst cfi where cfi.wy_form_no = #{wyFormNo}
    </select>

    <select id="selectTypeId" parameterType="java.lang.String" resultType="java.util.HashMap">
        select type_id as "typeId" from cs_wy_corp_type cwct where cwct.type_code = #{secSortCode} and cwct.type_id like #{projectId}
    </select>

    <select id="selectHoudsId" parameterType="java.lang.String" resultType="java.lang.String">
        select house_id from
        cs_house_info chi where chi.house_num = #{RoomIDs}
    </select>

    <select id="getITList" resultType="com.tahoecn.customerservice.model.excelDTO.ItExpForm">
        SELECT
	        f.id as id,
	        f.form_no as formNo,
	        f.owner_id AS ownerId,
	        f.house_no as houseNo,
	        f.project_code AS projectCode,
	        f.region as region,
	        f.city as city,
	        f.project as project,
	        f.building_no as buildingNo,
	        f.building_unit as buildingUnit,
	        f.room_no as roomNo,
	        f.owner_name as ownerName,
	        f.mobile as mobile,
	        f.housekeeper_name as housekeeperName,
	        f.housekeeper_tel as housekeeperTel,
	        f.report_channel_name as reportChannelName,
	        f.accept_channel_code as acceptChannelCode,
	        f.process_state_name as processStateName,
	        f.process_name as processName,
	        f.dept_name as deptName,
	        f.first_sort_name as firstSortName,
	        f.sec_sort_name as secSortName,
	        f.first_sort_code AS firstSortCode,
	        f.sec_sort_code AS secSortCode,
	        f.third_sort_name as thirdSortName,
	        f.fourth_sort_name as fourthSortName,
	        f.problem_level_name as problemLevelName,
	        f.cur_assignee_id AS curAssigneeId,
	        f.problem_position_name as problemPositionName,
	        f.revision_classification_name as revisionClassificationName,
	        f.decoration_stage_name as decorationStageName,
	        f.inter_res_department_name as interResDepartmentName,
	        f.process_state_code AS processStateCode,
	        f.maintenance_period_name as maintenancePeriodName,
	        f.customer_demand as customerDemand,
	        f.handle_record as handleRecord,
	        f.repair_unit as repairUnit,
	        f.third_party_flag as thirdPartyFlag,
	        f.third_party_reason as thirdPartyReason,
	        f.third_party_name as thirdPartyName,
	        f.creation_date as creationDate,
	        f.assign_date as assignDate,
	        f.create_user_name as createUserName,
	        f.cur_assignee_name as curAssigneeName,
	        f.rework_flag as reworkFlag,
	        f.upgrade_flag as upgradeFlag,
	        (
	        SELECT
	        pw.task_end_time
	        FROM
	        cs_process_workitem pw
	        WHERE
	        pw.form_inst_id = f.id
	        AND pw.process_state_code = 'handle'
	        ORDER BY
	        pw.task_end_time DESC
	        LIMIT 1
	        ) AS runTime,
	        f.report_channel_code AS reportChannelCode
        FROM
        cs_form_inst f
        <where>
            1 = 1
            <!-- AND f.process_state_code != 'draft' -->
            AND f.dept_code = 'deptIT'
            <if test="projectCode != null and projectCode != ''">
                AND f.project_code = #{projectCode}
            </if>
            <if test="ownerName !=null and ownerName !=''">
                AND (f.owner_name LIKE CONCAT('%',#{ownerName},'%') OR f.owner_id LIKE CONCAT('%',#{ownerName},'%'))
            </if>

            <if test="firstSortCode !=null and firstSortCode !=''">
                AND f.first_sort_code = #{firstSortCode}
            </if>

            <if test="secSortCode !=null and secSortCode !=''">
                AND f.sec_sort_code = #{secSortCode}
            </if>

			<if test="processStateCode !=null and processStateCode !=''">
                <!-- AND f.process_state_code = #{processStateCode} -->
                AND LOCATE(f.process_state_code,#{processStateCode}) > 0
            </if>
            
            <if test="reportChannelCode !=null and reportChannelCode !=''">
                AND f.report_channel_code = #{reportChannelCode}
            </if>

            <if test="creationStartDate != null and creationStartDate != ''">
                AND f.creation_date <![CDATA[ >= ]]> #{creationStartDate}
            </if>
            <if test="creationEndDate != null and creationEndDate != '' and creationEndDate != creationStartDate ">
                AND f.creation_date <![CDATA[ <= ]]> #{creationEndDate}
            </if>
            <if test="createUserName != null and createUserName != ''">
                AND f.create_user_name like concat('%',#{createUserName},'%') 
            </if>
            
            <if test="customerDemand != null and customerDemand != ''">
                AND f.customer_demand LIKE concat('%',#{customerDemand},'%') 
            </if>
            <if test="handleRecord != null and handleRecord != ''">
				AND f.handle_record LIKE concat('%',#{handleRecord},'%') 
            </if>
            <if test="formNo != null and formNo != ''">
				AND f.form_no = #{formNo} 
            </if>
        </where>
        ORDER BY f.creation_date DESC
    </select>

    <select id="itStatisticsReportCount" resultType="java.lang.Integer">
        select count(1) from cs_form_inst where process_state_code!='draft' and dept_code='deptIT'
        <if test="processStateCode!=null and processStateCode!=''">
            and process_state_code like concat('%End')
        </if>
        <if test="projectCode !=null and projectCode!=''">
            and project_code=#{projectCode}
        </if>
        <if test="secSortName !=null and secSortName!=''">
            and sec_sort_name=#{secSortName}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
    </select>
    <select id="itStatisticsReportProjectGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportGroupVo">
        select (select item_value from cs_dict_item where item_code=a.project_code) firstSortName,a.project_code
        firstSortCode,a.c groupCount,b.c finishCount from
        (select project_code,count(1) c from cs_form_inst where process_state_code!='draft' and dept_code='deptIT'
        <if test="secSortName !=null and secSortName!=''">
            and sec_sort_name=#{secSortName}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by project_code) a
        left join (select project_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and dept_code='deptIT' and process_state_code like concat('%End')
        <if test="secSortName !=null and secSortName!=''">
            and sec_sort_name=#{secSortName}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by project_code) b
        on a.project_code=b.project_code order by groupCount desc, firstSortName desc
    </select>
    <select id="itStatisticsReportGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportGroupVo">
        select (select item_value from cs_dict_item where item_code=a.first_sort_code ORDER BY id LIMIT 1) firstSortName,a.first_sort_code
        firstSortCode,a.c groupCount,b.c finishCount from
        (select first_sort_code,count(1) c from cs_form_inst where process_state_code!='draft' and dept_code='deptIT'
        <if test="secSortName !=null and secSortName!=''">
            and sec_sort_name=#{secSortName}
        </if>
        <if test="projectCode !=null and projectCode!=''">
            and project_code=#{projectCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by first_sort_code) a
        left join (select first_sort_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and dept_code='deptIT' and process_state_code like concat('%End')
        <if test="secSortName !=null and secSortName!=''">
            and sec_sort_name=#{secSortName}
        </if>
        <if test="projectCode !=null and projectCode!=''">
            and project_code=#{projectCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by first_sort_code) b
        on a.first_sort_code=b.first_sort_code order by groupCount desc
    </select>
    <select id="dayOfMonthReportCountCur" resultType="com.tahoecn.customerservice.model.vo.DayOfMonthReportVo">
        select count(1) mcount,DAYOFMONTH(curdate()) mday,month(curdate()) month from cs_form_inst t where dept_code='deptIT' and process_state_code!='draft' and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m');
    </select>
    <select id="dayOfMonthReportCount" resultType="com.tahoecn.customerservice.model.vo.DayOfMonthReportVo">
        select count(1) mcount,DAYOFMONTH(last_day(DATE_SUB(curdate(), INTERVAL #{diff} MONTH))) mday,month(DATE_SUB(curdate(), INTERVAL #{diff} MONTH)) month from cs_form_inst t where dept_code='deptIT' and process_state_code!='draft' and  DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( DATE_SUB(curdate(), INTERVAL #{diff} MONTH) ,'%Y%m')
    </select>


    <select id="customerStatisticsAccidentReportCount" resultType="java.lang.Integer">
        select count(1) from cs_form_inst where process_state_code!='draft' and dept_code='deptIT'
        <if test="type!=null and type==1">
            and first_sort_name like '%事故%'
        </if>
        <if test="type!=null and type==2">
            and sec_sort_code='itSec_03'
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
    </select>

    <select id="customerStatisticsAccidenReportGroup"
            resultType="com.tahoecn.customerservice.model.vo.CustomerStatisticsReportAccidentGroupVo">
        select (select item_value from cs_dict_item where item_code=a.project_code) codeName,a.project_code code,a.c
        codeAllCount,b.c codeCount
        from (select project_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and dept_code='deptIT'
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by project_code) a
        left join (select project_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and dept_code='deptIT'

        <if test="type!=null and type==1">
            and first_sort_name like '%事故%'
        </if>
        <if test="type!=null and type==2">
            and sec_sort_code='itSec_03'
        </if>

        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by project_code) b on a.project_code=b.project_code order by codeAllCount desc,codeName desc
    </select>

    <select id="itAccidentReportNextList" resultType="com.tahoecn.customerservice.model.vo.ItAccidentReportNextVo">
        select (select item_value from cs_dict_item where item_code=first_sort_code) firstSortName,first_sort_code firstSortCode,owner_name ownerName,
        cur_assignee_name curAssigneeName,customer_demand customerDemand  from cs_form_inst
         where process_state_code!='draft' and dept_code='deptIT'
        <if test="type!=null and type==1">
            and first_sort_name like '%事故%'
        </if>
        <if test="type!=null and type==2">
            and sec_sort_code='itSec_03'
        </if>
        <if test="projectCode !=null and projectCode!=''">
            and project_code=#{projectCode}
        </if>
        <if test="range!=null and range==1">
            and DATE_FORMAT( creation_date,'%Y') = DATE_FORMAT( CURDATE() ,'%Y')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
            order by creation_date desc
    </select>

    <select id="itAccidentReportNextQueryList" resultType="com.tahoecn.customerservice.model.vo.ItAccidentReportNextVo">
        select (select item_value from cs_dict_item where item_code=first_sort_code) firstSortName,first_sort_code firstSortCode,owner_name ownerName,
        cur_assignee_name curAssigneeName,customer_demand customerDemand  from cs_form_inst
        where process_state_code!='draft' and dept_code='deptIT'
        <if test="type!=null and type==1">
            and first_sort_name like '%事故%'
        </if>
        <if test="type!=null and type==2">
            and sec_sort_code='itSec_03'
        </if>
        <if test="projectCode !=null and projectCode!=''">
            and project_code=#{projectCode}
        </if>
        <if test="range!=null and (range==1 or range==0 or range==2) ">
            and DATE_FORMAT( creation_date,'%Y-%m') = #{time}
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y-%m-%d') =#{time}
        </if>
        order by creation_date desc
    </select>

    <select id="screenReportCountItem"
            resultType="com.tahoecn.customerservice.model.vo.ScreenReportCountItemVo">
        select (select item_value from cs_dict_item where item_code=a.first_sort_code) typeName,a.first_sort_code
        typeCode,a.c allCount,c.c processCount,b.c finishCount from
        (select first_sort_code,count(1) c from cs_form_inst where process_state_code!='draft' and first_sort_code
        in('coZX','coTS','coBX','coJYBY')
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        <if test="range!=null and range==3">
          and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        group by first_sort_code) a
        left join (select first_sort_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and first_sort_code in('coZX','coTS','coBX','coJYBY')
        and process_state_code like concat('%End')
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code) b
        on a.first_sort_code=b.first_sort_code
        left join (select first_sort_code,count(1) c from cs_form_inst
        where process_state_code!='draft' and first_sort_code in('coZX','coTS','coBX','coJYBY')
        and (process_state_code='toBeAssigned' or 'handle'=process_state_code or process_state_code='returnVisit')
        <if test="range!=null and range==3">
            and DATE_FORMAT( creation_date,'%Y%m%d') = DATE_FORMAT( CURDATE() ,'%Y%m%d')
        </if>
        <if test="range!=null and range==2">
            and DATE_FORMAT( creation_date,'%Y%m') = DATE_FORMAT( CURDATE() ,'%Y%m')
        </if>
        group by first_sort_code) c
        on b.first_sort_code=c.first_sort_code
    </select>

   <select id="screenReportCountRegion"
            resultType="com.tahoecn.customerservice.model.vo.ScreenReportCountRegionVo">
        SELECT a.region regionName, b.item_code typeCode, b.item_value typeName, ( SELECT count(1) FROM cs_form_inst c WHERE c.region = regionName AND c.first_sort_code = typeCode AND c.process_state_code != 'draft' AND DATE_FORMAT(c.creation_date, '%Y%m%d') = DATE_FORMAT(CURDATE(), '%Y%m%d')) allCount FROM ( SELECT region FROM cs_project_info GROUP BY region ORDER BY region ) a INNER JOIN ( SELECT item_value, item_code FROM cs_dict_item WHERE item_code IN ( 'coZX', 'coTS', 'coBX', 'coJYBY' )) b
    </select>
    
    <!-- 查询业主报事列表 -->
    <select id="getOwnerForm" resultType="com.tahoecn.customerservice.model.CsFormInst">
        select *
		from cs_form_inst f
		where f.is_owner is not null and f.is_owner = '1'
		and f.process_state_code = 'draft' 
        <if test="regionCode != null and regionCode != ''">
            AND f.region_code = #{regionCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND f.city_code = #{cityCode}
        </if>
        <if test="projectCode != null and projectCode != ''">
            AND f.project_code = #{projectCode}
        </if>
        <if test="mobile != null and mobile != ''">
            AND f.mobile like concat('%',#{mobile},'%')
        </if>
        <if test="creationStartDate != null and creationStartDate != ''">
            AND f.creation_date <![CDATA[ >= ]]> #{creationStartDate}
        </if>
        <if test="creationEndDate != null and creationEndDate != '' and creationEndDate != creationStartDate ">
            AND f.creation_date <![CDATA[ <= ]]> #{creationEndDate}
        </if>
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY f.creation_date ${orderBy}
            </when>
            <otherwise>
                ORDER BY f.creation_date
            </otherwise>
        </choose>
    </select>
    
    <!-- 报事进度查询列表 -->
    <select id="getWeChatFormList" resultType="com.tahoecn.customerservice.model.CsFormInst">
        select *
		from cs_form_inst f 
		where 1=1
        <if test="custId != null and owcustIdnerId != ''">
        	AND LOCATE(f.owner_id, #{custId}) &gt; 0
        </if>
        and f.is_Owner = '1'
		order by (case when f.process_state_code = 'draft' then 1 
					when f.process_state_code = 'toBeAssigned' then 2 
					when f.process_state_code = 'handle' then 3
					when f.process_state_code = 'upgrade' then 4
					when f.process_state_code = 'returnVisit' then 5
					else 6 end),
		f.last_update_date desc;
    </select>
    <!-- 查询千丁编码 -->
    <select id="selectQdFormNoCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select COUNT(*) from cs_form_inst cfi where cfi.qd_form_no = #{qdFormNo}
    </select>
    
    <!-- APP-查询报事处理，分派列表 -->
    <select id="getAssAndHandleList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
	        select * from cs_form_inst a
	        where a.cur_assignee_id = #{curAssigneeId}
	        and process_state_code = #{type}
	        and a.dept_code !='deptIT'
	        <if test="query != null and query !=''">
	            <!-- and (a.house_no like CONCAT('%',#{query},'%') or a.mobile like CONCAT('%',#{query},'%')) -->
	            and (a.house_name like CONCAT('%',#{query},'%') or a.mobile like CONCAT('%',#{query},'%'))
	        </if>
	        order by a.last_update_date ${sort}
	        <if test="pageSize != null">
				<if test="count != null">
					limit ${count}, ${pageSize}
				</if>
				<if test="count == null">
					limit ${pageSize}
				</if>
			</if>
    </select>
    <!-- APP-查询报事升级列表 -->
    <select id="getUpgradeFormList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
	        select distinct cs_form_inst.* 
	        from cs_form_inst 
	        LEFT JOIN cs_process_workitem on cs_form_inst.id = cs_process_workitem.form_inst_id
			where cs_form_inst.upgrade_flag='1' 
			and cs_process_workitem.process_state_code='upgrade' 
			and cs_process_workitem.assign_id=#{curAssigneeId}
			and cs_form_inst.dept_code !='deptIT'
	        <if test="query != null and query !=''">
	            <!-- and (cs_form_inst.house_no like CONCAT('%',#{query},'%') or cs_form_inst.mobile like CONCAT('%',#{query},'%')) -->
	            and (cs_form_inst.house_name like CONCAT('%',#{query},'%') or cs_form_inst.mobile like CONCAT('%',#{query},'%'))
	        </if>
	        order by cs_form_inst.last_update_date ${sort}
	        <if test="pageSize != null">
				<if test="count != null">
					limit ${count}, ${pageSize}
				</if>
				<if test="count == null">
					limit ${pageSize}
				</if>
			</if>
    </select>
    <!-- APP-查询报事查询列表 -->
    <select id="getDeptDCFormList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
	        SELECT distinct f.*
	        FROM
	        	cs_form_inst f
	        	LEFT JOIN cs_process_workitem p ON p.form_inst_id = f.id
	        where 1=1
	            and f.dept_code !='deptIT'
	            <!-- 数据权限处理 -->
	            <if test="(fdOrgSid != null and fdOrgSid != '') or (userName != null and userName != '') ">
	                AND (
	                <if test="fdOrgSid != null and fdOrgSid != ''">
	                    (LOCATE(f.region_code,#{fdOrgSid}) > 0 OR LOCATE(f.city_code,#{fdOrgSid}) > 0 OR
	                    LOCATE(f.project_code,#{fdOrgSid}) > 0)
	                </if>
	                <if test="fdOrgSid != null and fdOrgSid != '' and userName != null and userName != ''">
	                    OR
	                </if>
	                <if test="userName != null and userName != ''">
	                    (f.create_user_id = #{userName} OR p.assign_id = #{userName})
	                </if>)
	            </if>
	            <if test="isAdmin != 1 and isSiBaiSeats != 1">
	            	AND (
						(
							f.process_state_code = 'draft'
							AND f.create_user_id = #{userName}
						)
						OR f.process_state_code != 'draft'
					)
	            </if>
	        <if test="query != null and query !='' and (custId == '' or custId == null)" >
	            <!-- and (f.house_no like CONCAT('%',#{query},'%') or f.mobile like CONCAT('%',#{query},'%')
	            	or f.owner_name like CONCAT('%',#{query},'%')) -->
	            and (f.house_name like CONCAT('%',#{query},'%') or f.mobile like CONCAT('%',#{query},'%')
	            	or f.owner_name like CONCAT('%',#{query},'%'))
	        </if>
	        <if test="custId != '' and custId != ''" >
	            and f.owner_id = #{custId}
	        </if>
	        
	        <if test="createStart != null and createStart != ''">
	            AND f.creation_date <![CDATA[ >= ]]> #{createStart}
	        </if>
	        <if test="createEnd != null and createEnd != ''">
	            AND f.creation_date <![CDATA[ <= ]]> #{createEnd}
	        </if>
	        order by f.last_update_date ${sort}
	        <!-- <if test="create != null and create !=''">
	            ,f.creation_date ${create}
	        </if> -->
	        <if test="pageSize != null">
				<if test="count != null">
					limit ${count}, ${pageSize}
				</if>
				<if test="count == null">
					limit ${pageSize}
				</if>
			</if>
    </select>
    <!-- APP-查询报事抢单列表 -->
    <select id="getGrabFormList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsFormInst">
	        select * from cs_form_inst a
	        where process_state_code = 'toBeAssigned'
	        and a.dept_code !='deptIT'
	        and EXISTS (select * from cs_user_role u where 
				u.role_code in ('sComplaint-handle','sRepair-handle')
				and a.project_code = u.project_code
				and u.user_id = #{curAssigneeId})
	        and EXISTS(select * from cs_grab g
				where a.project_code = g.project_code
				and a.first_sort_code = g.first_sort_code
				and a.sec_sort_code = g.sec_sort_code
				and IFNULL(a.third_sort_code,'') = IFNULL(g.third_sort_code,'')
				and IFNULL(a.fourth_sort_code,'') = IFNULL(g.fourth_sort_code,''))
	        <if test="query != null and query !=''">
	            <!-- and (a.house_no like CONCAT('%',#{query},'%') or a.mobile like CONCAT('%',#{query},'%')) -->
	            and (a.house_name like CONCAT('%',#{query},'%') or a.mobile like CONCAT('%',#{query},'%'))
	        </if>
	        order by a.last_update_date ${sort}
	        <if test="pageSize != null">
				<if test="count != null">
					limit ${count}, ${pageSize}
				</if>
				<if test="count == null">
					limit ${pageSize}
				</if>
			</if>
    </select>
    <!-- APP-查询报事升级列表数量 -->
    <select id="selectUpgradeCount" parameterType="java.lang.String" resultType="java.lang.Integer">
	    select count(*) from (
			select distinct cs_form_inst.* 
			from cs_form_inst 
			LEFT JOIN cs_process_workitem on cs_form_inst.id = cs_process_workitem.form_inst_id
			where cs_form_inst.upgrade_flag = '1' 
			and cs_process_workitem.process_state_code = 'upgrade' 
			and cs_process_workitem.assign_id = #{curAssigneeId}
			and cs_form_inst.dept_code != 'deptIT'
		) a
    </select>
    <!-- APP-查询报事抢单列表数量 -->
    <select id="selectGrabCount" parameterType="java.lang.String" resultType="java.lang.Integer">
	    select count(*) from (
			select * from cs_form_inst a
	        where process_state_code = 'toBeAssigned'
	        and a.dept_code !='deptIT'
	        and EXISTS (select * from cs_user_role u where 
				u.role_code in ('sComplaint-handle','sRepair-handle')
				and a.project_code = u.project_code
				and u.user_id = #{curAssigneeId})
	        and EXISTS(select * from cs_grab g
				where a.project_code = g.project_code
				and a.first_sort_code = g.first_sort_code
				and a.sec_sort_code = g.sec_sort_code
				and IFNULL(a.third_sort_code,'') = IFNULL(g.third_sort_code,'')
				and IFNULL(a.fourth_sort_code,'') = IFNULL(g.fourth_sort_code,''))
		) a
    </select>
</mapper>
