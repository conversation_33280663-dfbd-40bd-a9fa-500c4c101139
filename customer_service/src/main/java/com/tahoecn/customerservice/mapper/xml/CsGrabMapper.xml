<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsGrabMapper">

	<select id="selectProjectByCode" resultType="com.tahoecn.customerservice.model.CsProjectInfo">
		SELECT DISTINCT p.* FROM cs_project_info p LEFT JOIN cs_grab g ON p.project_code = g.project_code
		<where>
			g.first_sort_code = #{code} or g.sec_sort_code = #{code} or
			g.third_sort_code = #{code} or g.fourth_sort_code = #{code}
		</where>
	</select>

	<delete id="deleteByCode">
		DELETE FROM cs_grab
		<where>
			first_sort_code = #{code} or sec_sort_code = #{code} or
			third_sort_code = #{code} or fourth_sort_code = #{code}
		</where>
	</delete>

	<insert id="insertGrab">
		INSERT INTO cs_grab(project_code, first_sort_code,sec_sort_code,third_sort_code,fourth_sort_code)
		SELECT p.project_code,d1.item_code,d2.item_code,d3.item_code,d4.item_code
		FROM cs_dict_item d1
			LEFT JOIN cs_dict_item d2 ON d1.item_code = d2.dict_code
			LEFT JOIN cs_dict_item d3 ON d2.item_code = d3.dict_code
			LEFT JOIN cs_dict_item d4 ON d3.item_code = d4.dict_code,
			cs_project_info p
		<where>
			d1.dict_code = 'firstSortCode' AND
			(d1.item_code = #{code} or d2.item_code = #{code} or d3.item_code = #{code} or d4.item_code = #{code}) AND
			p.project_code in
	        <foreach collection="projects" index="index" item="item"
	            open="(" separator="," close=")">
	            #{item}
	        </foreach>
		</where>
	</insert>

</mapper>
