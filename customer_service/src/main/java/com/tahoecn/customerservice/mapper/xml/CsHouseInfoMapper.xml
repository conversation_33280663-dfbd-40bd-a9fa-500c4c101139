<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsHouseInfoMapper">
	<resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsHouseInfo">
		<result column="id" property="id" jdbcType="DECIMAL" />
		<result column="project_id" property="projectId" jdbcType="VARCHAR" />
		<result column="house_id" property="houseId" jdbcType="VARCHAR" />
		<result column="house_num" property="houseNum" jdbcType="VARCHAR" />
		<result column="house_name" property="houseName" jdbcType="VARCHAR" />
		<result column="area" property="area" jdbcType="VARCHAR" />
		<result column="city" property="city" jdbcType="VARCHAR" />
		<result column="project" property="project" jdbcType="VARCHAR" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="building" property="building" jdbcType="VARCHAR" />
		<result column="room_num" property="roomNum" jdbcType="VARCHAR" />
		<result column="use_property" property="useProperty" jdbcType="VARCHAR" />
		<result column="delivery_date" property="deliveryDate"
			jdbcType="VARCHAR" />
		<result column="sign_date" property="signDate" jdbcType="VARCHAR" />
		<result column="focus_start_date" property="focusStartDate"
			jdbcType="TIMESTAMP" />
		<result column="focus_end_date" property="focusEndDate"
			jdbcType="TIMESTAMP" />
		<result column="delivery_status" property="deliveryStatus"
			jdbcType="DECIMAL" />
		<result column="fitment" property="fitment" jdbcType="VARCHAR" />
		<result column="actual_delivery_date" property="actualDeliveryDate"
			jdbcType="TIMESTAMP" />
		<result column="stay_time" property="stayTime" jdbcType="TIMESTAMP" />
		<result column="off_aid_date" property="offAidDate" jdbcType="TIMESTAMP" />
		<result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
		<result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
	</resultMap>

	<!-- 查楼栋-->
	<select id="selectBuildings" parameterType="com.tahoecn.customerservice.model.CsHouseInfo"
			resultMap="BaseResultMap">
		  select distinct building from cs_house_info
		  where project_id = #{projectId}
	</select>
	<!-- 查单元-->
	<select id="selectUnits" parameterType="com.tahoecn.customerservice.model.CsHouseInfo"
			resultMap="BaseResultMap">
		select distinct unit from cs_house_info
		where building = #{building}
		and project_id = #{projectId}
	</select>
	<select id="selectUnitByNullBudding" parameterType="com.tahoecn.customerservice.model.CsHouseInfo"
			resultMap="BaseResultMap">
		select distinct unit from cs_house_info
		where building is null
		and project_id = #{projectId}
	</select>

	<!-- 查房间-->
	<select id="selectRoomNums" parameterType="com.tahoecn.customerservice.model.CsHouseInfo"
			resultMap="BaseResultMap">
		select distinct room_num from cs_house_info
		where unit = #{unit}
		and project_id = #{projectId}
		and building = #{building}
		order by room_num
	</select>
	<select id="selectRoomNumsByNullUnit" parameterType="com.tahoecn.customerservice.model.CsHouseInfo"
			resultMap="BaseResultMap">
		select distinct room_num from cs_house_info
		where unit is null
		and project_id = #{projectId}
		and building = #{building}
		order by room_num
	</select>
	
	<select id="selectListPage" parameterType="map" resultType="com.tahoecn.customerservice.model.CsHouseInfo">
		SELECT DISTINCT
			h.project_id,
			h.house_id,
			h.house_num,
			h.house_name,
			p.region AS area,
			p.city,
			h.project,
			h.unit,
			h.building,
			h.room_num,
			h.use_property,
			h.delivery_date,
			h.sign_date,
			h.focus_start_date,
			h.focus_end_date,
			h.delivery_status,
			h.fitment,
			h.actual_delivery_date,
			h.stay_time,
			h.off_aid_date,
			h.steward_name,
			h.steward_telephone,
			h.create_date,
			h.update_date,
			h.source
		FROM
			cs_house_info h
			LEFT JOIN (SELECT cust_id,cust_name,house_num FROM cs_cust_info) c ON c.house_num = h.house_num
			LEFT JOIN cs_project_info p ON h.project_id = p.project_code
 		<where>
			<if test="custName != null and custName != ''">
				and c.cust_Name like concat('%',#{custName},'%')
			</if>
						
			<if test="building != null and building != ''">
				and h.building like concat('%',#{building},'%')
			</if>			
			<if test="fitment != null and fitment != ''">
				and h.fitment = #{fitment}
			</if>			
			<if test="useProperty != null and useProperty != ''">
				and h.use_property = #{useProperty}
			</if>
 			<if test="sStartDate !=null ">
				and h.sign_date &gt;= #{sStartDate}
			</if>
			<if test="sEndDate !=null ">
				and h.sign_date is not null and h.sign_date &lt; DATE_ADD(#{sEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="focusStartDate !=null ">
				and h.focus_start_date is not null and h.focus_start_date &gt;= #{focusStartDate}
			</if>
			<if test="focusEndDate !=null ">
				and h.focus_end_date is not null and h.focus_end_date &lt; DATE_ADD(#{focusEndDate} ,INTERVAL 1 DAY) 
			</if>
			<if test="aStartDate != null">
				and h.actual_delivery_date &gt;= #{aStartDate}
			</if>
			<if test="aEndDate != null">
				and h.actual_delivery_date is not null and h.actual_delivery_date &lt; DATE_ADD(#{aEndDate},INTERVAL 1 DAY) 
			</if>				
			<if test="obtainStartDate != null">
				and h.obtain_time &gt;= #{obtainStartDate}
			</if>
			<if test="obtainEndDate != null">
				and h.obtain_time is not null and h.obtain_time &lt; DATE_ADD(#{obtainEndDate},INTERVAL 1 DAY) 
			</if>
																	
 			<if test="regionCode != null and regionCode != ''">
				and p.region_code = #{regionCode}
			</if>
			<if test="cityCompanyCode != null and cityCompanyCode != ''">
				and p.city_company_code = #{cityCompanyCode}
			</if>
			<if test="projectId != null and projectId != ''">
				and p.project_code = #{projectId}
			</if>
			<if test="deliveryStatus != null and deliveryStatus != ''">
				and h.delivery_status = #{deliveryStatus}
			</if>
			<if test="houseNum != null and houseNum != ''">
				and h.house_num like concat('%',#{houseNum},'%')
			</if>
			<if test="houseName != null and houseName != ''">
				and h.house_name like concat('%',#{houseName},'%')
			</if>
			<if test="dStartDate != null">
				and h.delivery_date &gt;= #{dStartDate}
			</if>
			<if test="dEndDate != null">
				and h.delivery_date is not null and h.delivery_date &lt; DATE_ADD(#{dEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="oStartDate != null">
				and h.off_aid_date &gt;= #{oStartDate}
			</if>
			<if test="oEndDate != null">
				and h.off_aid_date is not null and h.off_aid_date &lt; DATE_ADD(#{oEndDate},INTERVAL 1 DAY) 
			</if>
 		</where>
 	</select>
	
	<select id="selectHcf" resultType="com.tahoecn.customerservice.model.CsHouseInfo">
		SELECT
			distinct
			p.region as area,
			p.city_company,
			p.city,
			p.project,
			p.project_code,
			h.house_id,
			h.house_num,
			h.house_name,
			h.building,
			h.unit,
			h.room_num
		FROM
			cs_house_info h
			LEFT JOIN cs_project_info p ON h.project_id = p.project_code
			<if test="(custId != null and custId != '' ) or (telephone != null and telephone != '')">
				LEFT JOIN cs_cust_info c ON c.house_num = h.house_num
			</if>
			<if test="createDateStart != null or createDateEnd != null or (processStateCode != null and processStateCode != '')">
				LEFT JOIN  cs_form_inst f ON h.house_num = f.house_info_id
			</if>
 		<where>
 			<choose>
				<when test="custId != null and custId != ''">
					and c.cust_id = #{custId}
				</when>
				<otherwise>
		 			<if test="area != null and area != ''">
						and p.region_code = #{area}
					</if>
					<if test="city != null and city != ''">
						and p.city_company_code = #{city}
					</if>
					<if test="project != null and project != ''">
						and p.project_code = #{project}
					</if>
					<if test="building != null and building != ''">
						and h.building = #{building}
					</if>
					<if test="unit != null and unit != ''">
						and h.unit = #{unit}
					</if>
					<!-- <if test="telephone != null and telephone != ''">
						and c.telephone like concat('%',#{telephone},'%')
					</if> -->
					<if test="telephone != null and telephone != ''">
						and (c.telephone like concat('%',#{telephone},'%') or ( SELECT count(1) FROM cs_cust_family y WHERE y.cust_id = c.cust_id and y.mobile like concat('%',#{telephone},'%') ) > 0)
					</if>
					<if test="sHouseNum != null and sHouseNum != ''">
						and h.house_num like concat('%',#{sHouseNum},'%')
					</if>
					<if test="sHouseName != null and sHouseName != ''">
						and h.house_name like concat('%',#{sHouseName},'%')
					</if>
					<if test="roomNum != null and roomNum != ''">
						and h.room_num like concat('%',#{roomNum},'%')
					</if>
					
					<if test="createDateStart != null">
						and f.creation_date &gt;= #{createDateStart}
					</if>
					<if test="createDateEnd != null">
						and f.creation_date is not null and f.creation_date &lt; DATE_ADD(#{createDateEnd},INTERVAL 1 DAY) 
					</if>
					<if test="processStateCode != null and processStateCode != ''">
						and f.process_state_code = #{processStateCode}
					</if>
				</otherwise>
			</choose>
 		</where>
 		<!-- <if test="(createDateStart != null or createDateEnd != null or (processStateCode != null and processStateCode != '') ) and curAssigneeId != null and curAssigneeId != '' ">
           	and (
           		EXISTS(select DISTINCT(form_inst_id) from cs_process_workitem b where b.assign_id = #{curAssigneeId} and f.id = b.form_inst_id )
                or f.process_state_code = 'draft' or f.create_user_id = #{curAssigneeId} 
           	)
        </if> -->
 	</select>
 	
 	<select id="selectPHouse" resultType="com.tahoecn.customerservice.model.dto.CsHouseInfoDto">
 		SELECT
			h.id,
			h.project_id,
			h.house_id,
			h.house_num,
			h.house_name,
			p.region as area,
			p.region_code as areaCode,
			p.city_company as city,
			p.city_company_code as cityCode,
			p.project,
			h.unit,
			h.building,
			h.room_num,
			h.use_property,
			h.delivery_date,
			h.sign_date,
			h.focus_start_date,
			h.focus_end_date,
			h.delivery_status,
			h.fitment,
			h.actual_delivery_date,
			h.stay_time,
			h.off_aid_date,
			h.steward_name,
			h.steward_telephone,
			h.create_date,
			h.update_date,
			h.source
		FROM
			cs_house_info AS h
		LEFT JOIN cs_project_info AS p ON h.project_id = p.project_code
		WHERE h.house_num = #{0}
			 		
 	</select>

	<!-- 查询房屋信息 根据房屋并编码-->
	<select id="selectAllHoustInfo" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsHouseInfo">
		select * from cs_house_info chi
		where chi.house_id = #{houseInfoId}
	</select>
	<!-- 查询房屋信息 根据房屋并编码-->
	<select id="selectAllHoustInfoBy" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsHouseInfo">
		select * from cs_house_info chi
		where chi.house_Num = #{houseNum}
	</select>
	
	<insert id="insertMyData">
		INSERT INTO cs_house_info (<!-- id, -->project_id,house_id,house_num,house_name,area,city,project,unit,building,room_num,
			use_property,delivery_date,sign_date,off_aid_date,fitment,create_date,update_date,source,obtain_time)
		( SELECT
				<!-- -myh.id AS id, -->
				myh.project_id,
				myh.house_id,
				myh.house_num,
				myh.house_name,
				myh.area,
				myh.city,
				myh.project,
				myh.unit,
				myh.building,
				myh.room_num,
				myh.use_property,
				myh.delivery_date,
				myh.sign_date,
				IF((delivery_date is NULL OR delivery_date = ''),NULL,DATE_ADD(delivery_date,INTERVAL+2 YEAR)) AS off_aid_date,
				myh.fitment,
				myh.create_date,
				myh.update_date,
				2 source,
				myh.obtain_time
			FROM
				cs_sys_my_house myh
			WHERE
				myh.project_id IN (SELECT cpi.project_code FROM cs_project_info cpi WHERE cpi.project_source = 2)
			)
	</insert>
	
	
	<select  id="selectCsHouseInfoCustInfoFrom" parameterType="map" resultType="com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom">
	SELECT DISTINCT
			h.house_num,
			h.house_name,
			p.region AS area,
			p.city,
			h.project,
			h.building,
			h.unit,
			h.room_num,
			h.fitment,
			h.use_property,
			h.delivery_date,
			h.sign_date,
			h.focus_start_date,
			h.focus_end_date,
			h.actual_delivery_date,
			h.off_aid_date,
			h.obtain_time,
			h.stay_time,
			h.delivery_status,
			c.cust_name,
			c.telephone,
			c.fixed_telephone,
			c.fax,
			c.certificate_name,
			c.certificate_num,
			c.cus_identity,
			c.belong,
			c.sex,
			c.is_vip,
			c.sug_leader,
			c.province,
			c.city as cust_city,
			c.area as cust_area,
			c.contact_address,
			c.postcode,
			c.email,
			h.steward_name,
			h.steward_telephone,			
			c.national,
			c.birthday,
			c.work_unit,
			c.profession,
			c.years_receive,
			c.is_f_zgold_card,
			c.is_has_more_house,
			c.is_medical_care_user,
			c.is_finance_user,
			c.is_real_estate_user,
			c.is_education_user,
			c.is_cinema_user,
			c.has_car,
			c.hobbies,
			c.other_board_member,
			c.special_customer
			
		FROM
			(SELECT * FROM cs_cust_info) c
			LEFT JOIN cs_house_info h ON c.house_num = h.house_num
			LEFT JOIN cs_project_info p ON h.project_id = p.project_code
		WHERE 1=1
			<if test="custName != null and custName != ''">
				and c.cust_Name like concat('%',#{custName},'%')
			</if>
						
			<if test="building != null and building != ''">
				and h.building like concat('%',#{building},'%')
			</if>			
			<if test="fitment != null and fitment != ''">
				and h.fitment = #{fitment}
			</if>			
			<if test="useProperty != null and useProperty != ''">
				and h.use_property = #{useProperty}
			</if>
 			<if test="sStartDate !=null ">
				and h.sign_date &gt;= #{sStartDate}
			</if>
			<if test="sEndDate !=null ">
				and h.sign_date is not null and h.sign_date &lt; DATE_ADD(#{sEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="focusStartDate !=null ">
				and h.focus_start_date is not null and h.focus_start_date &gt;= #{focusStartDate}
			</if>
			<if test="focusEndDate !=null ">
				and h.focus_end_date is not null and h.focus_end_date &lt; DATE_ADD(#{focusEndDate} ,INTERVAL 1 DAY) 
			</if>
			<if test="aStartDate != null">
				and h.actual_delivery_date &gt;= #{aStartDate}
			</if>
			<if test="aEndDate != null">
				and h.actual_delivery_date is not null and h.actual_delivery_date &lt; DATE_ADD(#{aEndDate},INTERVAL 1 DAY) 
			</if>				
			<if test="obtainStartDate != null">
				and h.obtain_time &gt;= #{obtainStartDate}
			</if>
			<if test="obtainEndDate != null">
				and h.obtain_time is not null and h.obtain_time &lt; DATE_ADD(#{obtainEndDate},INTERVAL 1 DAY) 
			</if>
																	
 			<if test="regionCode != null and regionCode != ''">
				and p.region_code = #{regionCode}
			</if>
			<if test="cityCompanyCode != null and cityCompanyCode != ''">
				and p.city_company_code = #{cityCompanyCode}
			</if>
			<if test="projectId != null and projectId != ''">
				and p.project_code = #{projectId}
			</if>
			<if test="deliveryStatus != null and deliveryStatus != ''">
				and h.delivery_status = #{deliveryStatus}
			</if>
			<if test="houseNum != null and houseNum != ''">
				and h.house_num like concat('%',#{houseNum},'%')
			</if>
			<if test="houseName != null and houseName != ''">
				and h.house_name like concat('%',#{houseName},'%')
			</if>
			<if test="dStartDate != null">
				and h.delivery_date &gt;= #{dStartDate}
			</if>
			<if test="dEndDate != null">
				and h.delivery_date is not null and h.delivery_date &lt; DATE_ADD(#{dEndDate},INTERVAL 1 DAY) 
			</if>
			<if test="oStartDate != null">
				and h.off_aid_date &gt;= #{oStartDate}
			</if>
			<if test="oEndDate != null">
				and h.off_aid_date is not null and h.off_aid_date &lt; DATE_ADD(#{oEndDate},INTERVAL 1 DAY) 
			</if>
	</select>
		
	<!-- 根据业主id查询房屋列表 -->
	<select id="selectCustHouseByCustId" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsHouseInfo">
		select * from cs_house_info h 
		left join cs_cust_info c ON c.house_num = h.house_num 
		where (h.use_property is null or h.use_property not like '%车位%') 
		and c.cust_id in 
		<foreach item="custId" index="index" collection="array" open="(" separator="," close=")">  
			#{custId} 
		</foreach> 
	</select>
	
	<!-- 根据业主id查询业主房产对应项目列表 -->
	<select id="selectCustHouseProjectByCustId" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.dto.CsHouseCodeDto">
		select distinct p.project_code as code ,p.project as name 
		from cs_project_info p left join cs_house_info h on p.project_code = h.project_id 
		left join cs_cust_info c on h.house_num = c.house_num 
		where c.cust_id in 
		<foreach item="custId" index="index" collection="array" open="(" separator="," close=")">  
			#{custId} 
		</foreach> 
	</select>
	
	<!-- 根据业主id查询业主房产对应区域列表 -->
	<select id="selectCustHouseRegionByCustId" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.dto.CsHouseCodeDto">
		select distinct p.region_code as code,p.region as name
		from cs_project_info p left join cs_house_info h on p.project_code = h.project_id 
		left join cs_cust_info c on h.house_num = c.house_num 
		where c.cust_id in 
		<foreach item="custId" index="index" collection="array" open="(" separator="," close=")">  
			#{custId} 
		</foreach> 
	</select>
	
	<!-- 根据房屋项目id查询区域城市 -->
	<select id="selectRegionByProId" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsProjectInfo">
		select *
		from cs_project_info p  
		where p.project_code = #{projectId}
	</select>
	
	<!-- APP-根据项目查询所有房屋信息 -->
    <select id="getHouseListByProjectId" parameterType="java.lang.String" resultType="map">
        select distinct house_id as houseId,house_num as houseNum,house_name as houseName
        from cs_house_info
        where project_id = #{projectId}
    </select>
	<!-- APP-根据房屋编码查询详情，关联业主信息 -->
    <select id="getHouseDetailByNum" parameterType="java.lang.String" resultType="map">
        select cust.cust_id as custId,cust.house_num as houseNum,cust.cust_name as custName,cust.belong as belong,
        cust.sex as sex,cust.certificate_name as certificateName,cust.certificate_num as certificateNum,cust.telephone as telephone
		from cs_cust_info cust 
        where cust.house_num = #{houseNum} limit 1
    </select>
    
    <insert id="initData">
		INSERT INTO cs_house_info SELECT
			h.id,
			h.project_id,
			h.house_id,
			h.house_num,
			h.house_name,
			h.area,
			h.city,
			h.project,
			h.unit,
			h.building,
			h.room_num,
			h.use_property,
			h.obtain_time,
			h.delivery_date,
			h.sign_date,
			h.focus_start_date,
			h.focus_end_date,
			h.delivery_status,
			h.fitment,
			h.actual_delivery_date,
			h.stay_time,
			h.off_aid_date,
			h.steward_name,
			h.steward_telephone,
			h.create_date,
			h.update_date,
			h.source 
		FROM
			cs_sync_house h
		ON DUPLICATE KEY UPDATE 
			project_id =  h.project_id,
			house_id =  h.house_id,
			house_name =  h.house_name,
			area =  h.area,
			city =  h.city,
			project =  h.project,
			unit =  h.unit,
			building =  h.building,
			room_num =  h.room_num,
			use_property =  h.use_property,
			obtain_time =  h.obtain_time,
			delivery_date =  h.delivery_date,
			sign_date =  h.sign_date,
			focus_start_date =  h.focus_start_date,
			focus_end_date =  h.focus_end_date,
			delivery_status =  IFNULL(h.delivery_status, IF(h.source = 1,1,0)),
			fitment =  h.fitment,
			actual_delivery_date =  h.actual_delivery_date,
			stay_time =  h.stay_time,
			off_aid_date =  h.off_aid_date,
			steward_name =  h.steward_name,
			steward_telephone =  h.steward_telephone,
			create_date =  h.create_date,
			update_date =  h.update_date,
			source = h.source
    </insert>
    
    
	<select id="selectWyHouse" resultType="com.tahoecn.customerservice.model.CsHouseInfo">
		SELECT h.* FROM cs_house_info h LEFT JOIN cs_sync_wy_house w ON h.house_num = w.RoomSign WHERE w.RoomID = #{hosueId}
	</select>
</mapper>
