<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsLabelMapper">
	<!-- 根据id级联删除标签-->
    <delete id="delLabelsById" parameterType="java.lang.String" >
        delete from cs_label  where cs_label.id = #{labelId} or cs_label.father_id = #{labelId}
    </delete>
    
    <!-- 查询标签列表返回树形结构 -->
    <select id="getLabelTree" resultType="com.tahoecn.customerservice.model.CsLabel">
        select * from cs_label 
        WHERE 1 = 1 
        <if test="flag != null and flag != '' ">
        	and cs_label.type = #{flag} AND state = '1'
        </if>
<!--         <if test="itemName != null and itemName != '' ">
            and cs_label.item_Name like CONCAT('%',#{itemName},'%')
        </if>
        <if test="collection !=null and collection !=''">
            AND cs_label.collection = #{collection}
        </if>
        <if test="score != null and score != ''">
            AND cs_label.score = #{score}
        </if>
 -->        order by cs_label.id
    </select>
</mapper>
