<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsMenuCfgMapper">
    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsMenuCfg">
        <result column="id" property="id" jdbcType="DECIMAL" />
        <result column="menu_code" property="menuCode" jdbcType="VARCHAR" />
        <result column="menu_url" property="menuUrl" jdbcType="VARCHAR" />
        <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- 编码查询url-->
    <select id="selectUrlByMenuCode" parameterType="java.lang.String" resultType="java.lang.String">
        select menu_url from cs_menu_cfg where menu_code = #{menuCode}
    </select>

</mapper>
