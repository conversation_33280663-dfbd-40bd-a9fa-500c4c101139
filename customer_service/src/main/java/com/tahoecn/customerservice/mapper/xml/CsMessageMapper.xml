<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsMessageMapper">

	<resultMap type="java.util.HashMap" id="messageList">
		<result column="cd" property="day"/>
		<collection property="message" javaType="java.util.List" ofType="com.tahoecn.customerservice.model.CsMessage">
			<result column="id" property="id"/>
			<result column="form_id" property="formId"/>
			<result column="message" property="message"/>
			<result column="state" property="state"/>
			<result column="create_date" property="createDate"/>
			<result column="update" property="update"/>
			<result column="login_name" property="loginName"/>
		</collection>
	</resultMap>
	
	<select id="getMessageListMg" resultMap="messageList" parameterType="java.util.HashMap">
		select *,DATE_FORMAT(create_date,'%Y-%m-%d') as cd from cs_message
		where 1=1 and cs_message.login_name = #{loginName}
		<if test="state != null and state != ''">
			and cs_message.state = #{state}
		</if>
        order by cs_message.create_date desc,cs_message.state desc
        <if test="pageSize != null">
			<if test="count != null">
				limit ${count}, ${pageSize}
			</if>
			<if test="count == null">
				limit ${pageSize}
			</if>
		</if>
	</select>
	
	<select id="getMessageList" parameterType="java.util.HashMap"
            resultType="com.tahoecn.customerservice.model.CsMessage">
		select * from cs_message where 1=1 and cs_message.login_name = #{loginName}
	        order by cs_message.create_date desc,cs_message.state desc
	        <if test="pageSize != null">
				<if test="count != null">
					limit ${count}, ${pageSize}
				</if>
				<if test="count == null">
					limit ${pageSize}
				</if>
			</if>
    </select>
</mapper>
