<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsProcessWorkitemMapper">
    <select id="getProgressWorkItemList" resultType="com.tahoecn.customerservice.model.CsProcessWorkitem">
        select * from cs_process_workitem a where a.form_inst_id = #{formInstId} and id >=
        (select max(id) from cs_process_workitem b where b.form_inst_id = a.form_inst_id and process_state_code = 'draft' group by b.form_inst_id)
    </select>
    <select id="getLastProcessWorkitem" resultType="com.tahoecn.customerservice.model.CsProcessWorkitem">
        select * from cs_process_workitem a where a.form_inst_id = #{id} order by last_update_date desc limit 0,1
    </select>
    <select id="selectByFormId" resultType="com.tahoecn.customerservice.model.CsProcessWorkitem">
        SELECT * FROM cs_process_workitem WHERE form_inst_id=#{id} AND task_status=20
    </select>

</mapper>
