<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsProjectInfoMapper">

    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsProjectInfo">
        <result column="id" property="id" jdbcType="DECIMAL" />
        <result column="region_code" property="regionCode" jdbcType="VARCHAR" />
        <result column="region" property="region" jdbcType="VARCHAR" />
        <result column="city_company_code" property="cityCompanyCode" jdbcType="VARCHAR" />
        <result column="city_company" property="cityCompany" jdbcType="VARCHAR" />
        <result column="city_code" property="cityCode" jdbcType="VARCHAR" />
        <result column="city" property="city" jdbcType="VARCHAR" />
        <result column="project_code" property="projectCode" jdbcType="VARCHAR" />
        <result column="project" property="project" jdbcType="VARCHAR" />
        <result column="project_source" property="projectSource" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 区域、城市(城市+城市公司)、项目查询-->
    <select id="selectProjectInfoRelation" parameterType="com.tahoecn.customerservice.model.CsProjectInfo"
            resultMap="BaseResultMap">
        select * from cs_project_info where 1=1
        <if test="regionCode != null and regionCode!=''">
            and region_code = #{regionCode}
        </if>
        <if test="region != null and region !=''">
            and region_code = #{regionCode}
        </if>
        <if test="cityCompany != null and cityCompany !=''">
            and city_company = #{cityCompany}
        </if>
        <if test="cityCompanyCode != null and cityCompanyCode!=''">
            and city_company_code = #{cityCompanyCode}
        </if>
        <if test="city != null and city !=''">
            and city = #{city}
        </if>
        <if test="cityCode != null and cityCode!=''">
            and city_code = #{cityCode}
        </if>
        <if test="projectCode != null and projectCode!=''">
            and project_code = #{projectCode}
        </if>
        order by id
    </select>
    
    
      <select id="getSlaveNameList" resultType="com.tahoecn.customerservice.model.vo.CsProjectInfoNameAndCode">
    

      SELECT  DISTINCT
      <choose>
				<when test="organName ==null or organName ==''">
					cpi.region name ,cpi.region_code code
				</when>
				<when test="cityName ==null or cityName ==''">
                    cpi.city_company name ,cpi.city_company_code code
				</when>
				<otherwise>
				    cpi.project	name ,cpi.project_code code		
				</otherwise>					    
	  </choose>
      
		
		FROM
		cs_project_info cpi
		left join cs_form_inst cfi
		on cpi.city_company_code=cfi.city_code
		<where>
			1=1
		<if test="organName !=null and organName !=''">
		 and  cpi.region_code =#{organName}
		<if test="cityName !=null and cityName !=''">		
		and cpi.city_company_code=#{cityName}
		</if>

		</if>			
		</where>		
</select>


<select id="satisfaction" resultType="com.tahoecn.customerservice.model.vo.StatisticalVo">
       SELECT  
		satisfaction_code statisticalDo,COUNT(id) num		
		FROM		
	    cs_form_inst  		
		<where>
	    process_state_code='nomalEnd' and satisfaction_code is not null and satisfaction_code != '' and satisfaction_code != '-1'
	    AND (first_sort_code ='coTS' OR first_sort_code ='coBX')
		<if test="limitTimeStart !=null and limitTimeStart !=''">
		 and creation_date &gt;= #{limitTimeStart}
		</if>
		<if test="limitTimeEnd !=null and limitTimeEnd !=''">
		 and creation_date &lt;=#{limitTimeEnd}
		</if>
		and project_code is not null and city_code is not null and region_code is not null
	    GROUP BY satisfaction_code		
		</where> 	
				
</select>
<select id="getCountNum"  resultType="java.lang.Integer">
        SELECT  
		COUNT(id)		
		FROM
	    cs_form_inst 
		<where>
		<if test="method !=null ">
		<choose>
		<when test="method == 1"><!-- 报事总量 -->
		(process_state_code>'draft' or 'draft'>process_state_code)		
		</when>
		<when test="method == 2"><!-- 完成总量 -->
		(process_state_code='nomalEnd' or process_state_code='specialEnd')
		</when>
		<when test="method == 3"><!-- 处理中 -->
	    (process_state_code='toBeAssigned' or 'handle'=process_state_code or process_state_code='returnVisit')
		</when>
		<when test="method == 4"><!-- 升级 -->
		 upgrade_flag=1
		</when>
		<when test="method == 5"><!-- 返工 -->
		 rework_flag=1		
		</when>
		</choose>
		<if test="organName !=null and organName !=''">
		and region_code=#{organName}	
		<if test="cityName !=null and cityName !=''">
		and city_code=#{cityName}
		<if test="projectName !=null and projectName !=''">
		and project_code=#{projectName}
		</if>
		</if>
		</if>
		<if test="condition !=null and condition !=''">
		and first_sort_code=#{condition}<!-- 报事类别 投诉，咨询，报修，建议表扬 -->
		</if>
		<if test="limitTimeStart !=null and limitTimeStart !=''">
		and creation_date &gt;= #{limitTimeStart}
		</if>
		<if test="limitTimeEnd !=null and limitTimeEnd !=''">
		and creation_date &lt;=#{limitTimeEnd}
		</if>
		and project_code is not null and city_code is not null and region_code is not null  and dept_code = 'deptDC'
		</if>

		</where> 	
</select>

	<select id="selectAllProjectByProjectCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsProjectInfo">
		select
 			*
 		from
 			cs_project_info cpi
 		where
 			cpi.project_code = #{projectCode}
	</select>

	<select id="selectCsProjectIDImfoProjectCodeByProjectId" parameterType="java.lang.String" resultType="java.lang.String">
		select cpi.project_code from cs_project_IDinfo cpi where cpi.project_id = #{projectId} and cpi.project_source = 1
	</select>

	<select id="selectCsProjectIDImfoProjectIdByProjectCode" parameterType="java.lang.String" resultType="java.lang.String">
		select cpi.project_id from cs_project_IDinfo cpi where cpi.project_code = #{projectCode} and cpi.project_source = 1
	</select>

	<select id="selectProjectCodeNew" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsProjectInfo">
		select
			distinct cpi.project_code as "projectCode"
	    from
	        cs_project_info cpi
       	where
			cpi.region = #{regionCode}
	</select>

	<select id="selectProjectCodeNum" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsProjectInfo">
		select
			distinct cpi.project_code
	      from
	      		cs_project_info cpi
       	  where
				cpi.region = #{regionCode}
				and cpi.city_company = #{city}
	</select>
	<select id="groupRegion"  resultType="string">
		select region from cs_project_info group by region
	</select>
	<select id="groupCity" resultType="string">
		select city_company from cs_project_info where region=#{region} group by city_company
	</select>
	
	<select id="getProDetailByCode" parameterType="java.lang.String" resultType="map">
		SELECT
			region_code,
			region,
			city_company_code,
			city_company
			<if test="codeType == 'project'">
				,project_code,
				project
			</if>
		FROM
			cs_project_info
		WHERE
			1 = 1
			<choose>
				<when test="codeType == 'project'">
					AND project_code = #{code} 
				</when>
				<otherwise>
					AND city_company_code = #{code} 
				</otherwise>
			</choose>
		LIMIT 1
	</select>
	
	<!-- 根据千丁项目id查询物业项目id -->
	<select id="selectWyIdByQdId" parameterType="java.lang.String" resultType="java.lang.String">
		select qd.project_wy_id from cs_project_qdtowy_idinfo qd 
		where qd.project_qd_id = #{regionId}
	</select>
	<!-- APP-查询所有区域城市项目 -->
	<select id="getProjectList" resultType="java.util.HashMap">
		select distinct region_code as regionCode,region as regionName,city_company_code as cityCode,
		city_company as cityName,project_code as projectCode,project as projectName 
		from cs_project_info
	</select>
	
	
	
	
	
	
	
	
	
	
	
	
	
</mapper>
