<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSupplierInfoMapper">
    <resultMap id="BaseResultMap" type="com.tahoecn.customerservice.model.CsSupplierInfo">
        <result column="id" property="id" jdbcType="DECIMAL" />
        <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
        <result column="supplier_contact_name" property="supplierContactName" jdbcType="VARCHAR" />
        <result column="supplier_contact_mobile" property="supplierContactMobile" jdbcType="VARCHAR" />
        <result column="supplier_classification" property="supplierClassification" jdbcType="VARCHAR" />
        <result column="supplier_areas" property="supplierAreas" jdbcType="VARCHAR" />
        <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
        <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- -->
    <select id="selectNameByArea" parameterType="com.tahoecn.customerservice.model.CsSupplierInfo"
            resultMap="BaseResultMap">
        select a.supplier_name from cs_supplier_info a
        where a.supplier_areas like CONCAT('%',#{supplierAreas},'%')
    </select>
    
    <!-- APP-手机端查询主责单位，维修单位 -->
    <select id="getAppNameByArea" parameterType="java.util.HashMap" resultType="map">
        select a.supplier_name as supplierName from cs_supplier_info a
        where a.supplier_areas like CONCAT('%',#{supplierAreas},'%')
        <if test="query != null and query != ''">
            and a.supplier_name like CONCAT('%',#{query},'%')
        </if>
    </select>
</mapper>
