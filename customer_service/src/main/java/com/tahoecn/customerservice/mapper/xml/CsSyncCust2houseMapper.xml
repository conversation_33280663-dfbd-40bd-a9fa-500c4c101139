<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncCust2houseMapper">

	<select id="syncDate">
		SELECT MAX(update_date) FROM cs_sync_cust2house WHERE source = #{source}
	</select>
	
	<delete id="delMyActive">
		DELETE
		FROM
			cs_sync_cust2house
		WHERE
			source = 2
	</delete>
	
	<insert id="insertMySync">
		INSERT IGNORE INTO cs_sync_cust2house ( house_code, certificate_num, update_date, source )
		SELECT
			room_code,
			substring_index( substring_index( certificate_num, ',', b.help_topic_id + 1 ), ',',- 1 ) certificate_num,
			NOW(),
			2
		FROM
			cs_sync_my_cust2house h
			JOIN mysql.help_topic b ON b.help_topic_id &lt; ( length( h.certificate_num ) - length( REPLACE ( h.certificate_num, ',', '' ) ) + 1 ) 
		WHERE
			room_code IS NOT NULL AND room_code != ''
			AND certificate_num IS NOT NULL AND certificate_num != '' AND substring_index( substring_index( h.certificate_num, ',', b.help_topic_id + 1 ), ',',- 1 ) != ''
			<!-- <if test="syncDate != null">
				AND cq_date &gt;= #{syncDate}
			</if> -->
	</insert>
	
	<insert id="insertWySync">
		INSERT IGNORE INTO cs_sync_cust2house ( house_code, certificate_num, update_date, source )
		SELECT
			RoomSign,
			substring_index( substring_index( h.PaperCode, ',', b.help_topic_id + 1 ), ',',- 1 ) PaperCode,
			NOW(),
			1
		FROM
			cs_sync_wy_cust2house h
			JOIN mysql.help_topic b ON b.help_topic_id &lt; ( length( h.PaperCode ) - length( REPLACE ( h.PaperCode, ',', '' ) ) + 1 ) 
		WHERE
			RoomSign IS NOT NULL AND RoomSign != ''
			AND PaperCode IS NOT NULL AND PaperCode != '' AND substring_index( substring_index( h.PaperCode, ',', b.help_topic_id + 1 ), ',',- 1 ) != ''
			AND IsActive = 1
			<if test="syncDate != null">
				AND ChargeTime &gt; #{syncDate}
			</if>
	</insert>
	
	<delete id="delWyActive">
		DELETE ch From cs_sync_cust2house ch 
		LEFT JOIN cs_sync_wy_cust2house wch ON ch.certificate_num = wch.PaperCode AND ch.house_code = wch.RoomSign 
		WHERE wch.IsActive = 0
	</delete>
	
	<delete id="deleteByHouse">
		DELETE ch FROM cs_sync_cust2house ch LEFT JOIN cs_sync_house h ON ch.house_code = h.house_num WHERE h.id IS NULL;
	</delete>
	
</mapper>
