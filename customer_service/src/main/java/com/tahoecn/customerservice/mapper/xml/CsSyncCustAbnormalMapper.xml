<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncCustAbnormalMapper">

	<select id="selectDtoList" resultType="com.tahoecn.customerservice.model.dto.CsSyncCustAbnormalDto">
		SELECT
			a.*,
			( SELECT GROUP_CONCAT( house_code ) FROM cs_sync_cust2house WHERE certificate_num = a.certificate_num ) house_num 
		FROM
			cs_sync_cust_abnormal a 
		<where>
			<if test="custName != null and custName != ''">
				cust_name like CONCAT('%',#{custName},'%')
			</if>
			<if test="certificateNum != null and certificateNum != ''">
				certificate_num like CONCAT('%',#{certificateNum},'%')
			</if>
			<if test="telephone != null and telephone != ''">
				telephone like CONCAT('%',#{telephone},'%')
			</if>
		</where>
		ORDER BY a.certificate_num,a.abnormal desc,a.update_date 
	</select>
	
	<delete id="delCustAbnormal">
		DELETE c FROM cs_sync_cust_abnormal c LEFT OUTER JOIN cs_sync_cust2house m ON c.certificate_num = m.certificate_num WHERE m.certificate_num is null
	</delete>
	
</mapper>
