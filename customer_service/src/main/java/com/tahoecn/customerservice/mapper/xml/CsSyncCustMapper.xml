<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncCustMapper">

	<select id="syncDate">
		SELECT MAX(update_date) FROM cs_sync_cust WHERE source = #{source}
	</select>
	
	<delete id="delCust">
		DELETE c FROM cs_sync_cust c LEFT OUTER JOIN cs_sync_cust2house m ON c.certificate_num = m.certificate_num WHERE m.certificate_num is null
	</delete>

	<insert id="insertMySync">
		INSERT INTO cs_sync_cust_center (
			cust_id,
			cust_name,
			certificate_num,
			certificate_name,
			fixed_telephone,
			telephone,
			fax,
			contact_address,
			postcode,
			email,
			belong,
			sex,
			national,
			birthday,
			work_unit,
			profession,
			create_date,
			update_date,
			source
		) SELECT DISTINCT
			c.cust_id,
			if(locate(',',c.certificate_num) > 0, substring_index( substring_index( c.cust_name, ',', b.help_topic_id + 1 ), ',',- 1 ), c.cust_name) cust_name,
			substring_index( substring_index( c.certificate_num, ',', b.help_topic_id + 1 ), ',',- 1 ) certificate_num,
			c.certificate_name,
			c.fixed_telephone,
			if(locate(',',c.certificate_num) > 0, substring_index( substring_index( c.telephone, ',', b.help_topic_id + 1 ), ',',- 1 ), c.telephone) telephone,
			c.fax_telephone,
			c.contact_address,
			c.PostCode,
			c.Email,
			c.belong,
			c.sex,
			c.national,
			c.birthday,
			c.work_unit,
			c.profession,
			NOW( ),
			NOW( ),
			2
		FROM
			cs_sync_my_cust c
			JOIN mysql.help_topic b ON b.help_topic_id &lt; ( length( c.certificate_num ) - length( REPLACE ( c.certificate_num, ',', '' ) ) + 1 ) 
		WHERE
			certificate_num IS NOT NULL AND certificate_num != ''
			AND LENGTH(substring_index( substring_index( c.certificate_num, ',', b.help_topic_id + 1 ), ',',- 1 )) > 4
			AND telephone IS NOT NULL AND telephone != ''
			<if test="syncDate != null">
				AND update_date &gt; #{syncDate}
			</if>
	</insert>
	
	<insert id="insertWySync">
		INSERT INTO cs_sync_cust_center (
			cust_id,
			cust_name,
			certificate_num,
			certificate_name,
			telephone,
			fax,
			contact_address,
			postcode,
			email,
			belong,
			sex,
			national,
			birthday,
			work_unit,
			hobbies,
			is_delete,
			create_date,
			update_date,
			source
		) SELECT DISTINCT
			CustID,
			if(locate(',',c.PaperCode) > 0, substring_index( substring_index( c.CustName, ',', b.help_topic_id + 1 ), ',',- 1 ), c.CustName) CustName,
			substring_index( substring_index( c.PaperCode, ',', b.help_topic_id + 1 ), ',',- 1 ) PaperCode,
			PaperName,
			if(locate(',',c.PaperCode) > 0, substring_index( substring_index( c.MobilePhone, ',', b.help_topic_id + 1 ), ',',- 1 ), c.MobilePhone) MobilePhone,
			FaxTel,
			Address,
			PostCode,
			EMail,
			IsUnit,
			Sex,
			Nationality,
			Birthday,
			Job,
			Hobbies,
			IsDelete,
			NOW( ),
			NOW( ),
			1
		FROM
			cs_sync_wy_cust c
			JOIN mysql.help_topic b ON b.help_topic_id &lt; ( length( c.PaperCode ) - length( REPLACE ( c.PaperCode, ',', '' ) ) + 1 ) 
		WHERE
			PaperCode IS NOT NULL AND PaperCode != ''
			AND LENGTH(substring_index( substring_index( c.PaperCode, ',', b.help_topic_id + 1 ), ',',- 1 )) > 4
			AND MobilePhone IS NOT NULL AND MobilePhone != ''
			<if test="syncDate != null">
				AND custupdatetime &gt; #{syncDate}
			</if>
	</insert>
	
	<insert id="insertCenterToCust">
		INSERT INTO cs_sync_cust 
		SELECT *,0 FROM cs_sync_cust_center 
		ON DUPLICATE KEY UPDATE abnormal = 1,update_date = NOW()
	</insert>
	<delete id="delCenterByCust">
		DELETE cc FROM cs_sync_cust_center cc ,cs_sync_cust c WHERE cc.id = c.id
	</delete>
	<insert id="insertCustToAbn">
		REPLACE INTO cs_sync_cust_abnormal SELECT * FROM cs_sync_cust c WHERE c.abnormal = 1 
	</insert>
	<insert id="insertCenterToAbn">
		INSERT INTO cs_sync_cust_abnormal SELECT *,0 FROM cs_sync_cust_center
	</insert>
	<delete id="delCenter">
		DELETE FROM cs_sync_cust_center
	</delete>
	
	<update id="retainCustById">
		UPDATE cs_sync_cust c , cs_sync_cust_abnormal a
		SET 
			c.cust_id = a.cust_id,
			c.cust_name = a.cust_name,
			c.certificate_name = a.certificate_name,
			c.fixed_telephone = a.fixed_telephone,
			c.telephone = a.telephone,
			c.fax = a.fax,
			c.contact_address = a.contact_address,
			c.postcode = a.postcode,
			c.email = a.email,
			c.belong = a.belong,
			c.sex = a.sex,
			c.national = a.national,
			c.birthday = a.birthday,
			c.work_unit = a.work_unit,
			c.profession = a.profession,
			c.hobbies = a.hobbies,
			c.is_delete = a.is_delete,
			c.update_date = NOW(),
			c.source = a.source,
			c.abnormal = 0
		WHERE
		  c.certificate_num = a.certificate_num AND a.id = #{id}
		<!-- INSERT INTO cs_sync_cust SELECT a.* FROM cs_sync_cust_abnormal a WHERE a.id = #{id}
		ON DUPLICATE KEY UPDATE
			cust_id = a.cust_id,
			cust_name = a.cust_name,
			certificate_name = a.certificate_name,
			fixed_telephone = a.fixed_telephone,
			telephone = a.telephone,
			fax = a.fax,
			contact_address = a.contact_address,
			postcode = a.postcode,
			email = a.email,
			belong = a.belong,
			sex = a.sex,
			national = a.national,
			birthday = a.birthday,
			work_unit = a.work_unit,
			profession = a.profession,
			hobbies = a.hobbies,
			is_delete = a.is_delete,
			update_date = NOW(),
			source = a.source,
			abnormal = 0 -->
	</update>

</mapper>
