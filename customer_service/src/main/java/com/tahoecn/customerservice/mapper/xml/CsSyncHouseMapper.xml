<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncHouseMapper">

	<select id="syncDate">
		SELECT MAX(update_date) FROM cs_sync_house WHERE source = #{source}
	</select>

	<insert id="insertMySync">
		INSERT INTO cs_sync_house (
			project_id,
			house_id,
			house_num,
			house_name,
			area,
			region_code,
			city,
			city_company_code,
			project,
			unit,
			building,
			room_num,
			use_property,
			delivery_status,
			fitment,
			obtain_time,
			my_status,
			create_date,
			update_date,
			source
		) SELECT
			p.project_code,
			h.house_id,
			h.room_code,
			h.house_code,
			p.region,
			p.region_code,
			p.city_company,
			p.city_company_code,
			p.project,
			h.Unit,
			h.building,
			h.room_num,
			h.use_property,
			0,
			h.fitment,
			h.obtain_time,
			h.`Status`,
			NOW( ),
			NOW( ),
			2
		FROM
			cs_sync_my_house h
			LEFT JOIN cs_project_idinfo pi ON pi.project_code = substring_index(h.project_id, '.', 2)
			LEFT JOIN cs_project_info p ON pi.project_id = p.project_code 
		WHERE
			p.project_code IS NOT NULL 
			AND h.house_num IS NOT NULL AND h.house_num != ''
			<if test="syncDate != null">
				AND update_date &gt; #{syncDate}
			</if>
		ON DUPLICATE KEY UPDATE
			<!-- 交付状态处理，delivery_status = 1 已交付 -->
			project_id = IF(delivery_status = 1 ,cs_sync_house.project_id,p.project_code),
			house_id = IF(delivery_status = 1 ,cs_sync_house.house_id,h.house_id),
			house_num = IF(delivery_status = 1 ,cs_sync_house.house_num,h.room_code),
			house_name = IF(delivery_status = 1 ,cs_sync_house.house_name,h.house_code),
			area = IF(delivery_status = 1 ,cs_sync_house.area,p.region),
			region_code = IF(delivery_status = 1 ,cs_sync_house.region_code,p.region_code),
			city = IF(delivery_status = 1 ,cs_sync_house.city,p.city_company),
			city_company_code = IF(delivery_status = 1 ,cs_sync_house.city_company_code,p.city_company_code),
			project = IF(delivery_status = 1 ,cs_sync_house.project,p.project),
			unit = IF(delivery_status = 1 ,cs_sync_house.unit,h.Unit),
			building = IF(delivery_status = 1 ,cs_sync_house.building,h.building),
			room_num = IF(delivery_status = 1 ,cs_sync_house.room_num,h.room_num),
			use_property = IF(delivery_status = 1 ,cs_sync_house.use_property,h.use_property),
			fitment = IF(delivery_status = 1 ,cs_sync_house.fitment,h.fitment),
			obtain_time = IF(delivery_status = 1 ,cs_sync_house.obtain_time,h.obtain_time),
			my_status = IF(delivery_status = 1 ,cs_sync_house.my_status,h.`Status`),
			update_date = IF(delivery_status = 1 ,cs_sync_house.update_date,NOW( )),
			source = IF(delivery_status = 1 ,cs_sync_house.source,2)
	</insert>
	
	<insert id="insertWySync">
		INSERT INTO cs_sync_house (
			project_id,
			house_id,
			house_num,
			house_name,
			area,
			region_code,
			city,
			city_company_code,
			project,
			unit,
			building,
			room_num,
			use_property,
			delivery_date,
			focus_start_date,
			focus_end_date,
			delivery_status,
			fitment,
			actual_delivery_date,
			stay_time,
			is_delete,
			steward_name,
			steward_telephone,
			create_date,
			update_date,
			source,
			abnormal 
		) SELECT
			p.project_code,
			h.RoomID,
			h.RoomSign,
			h.RoomName,
			p.region,
			p.region_code,
			p.city_company,
			p.city_company_code,
			p.project,
			h.UnitSNum,
			h.BuildName,
			SUBSTRING_INDEX(h.RoomSign,'-',-1),
			h.PropertyUses,
			h.ContSubDate,
			h.getHouseStartDate,
			h.getHouseEndDate,
			1,
			h.BuildsRenovation,
			h.ActualSubDate,
			h.StayTime,
			h.IsDelete,
			h.UserName,
			h.MobileTel,
			NOW( ),
			NOW( ),
			1,
			0 
		FROM
			cs_sync_wy_house h
			LEFT JOIN cs_project_idinfo pi ON pi.project_code = h.CommID
			LEFT JOIN cs_project_info p ON pi.project_id = p.project_code 
		WHERE
			p.project_code IS NOT NULL
			AND h.RoomSign IS NOT NULL AND h.RoomSign != ''
			<if test="syncDate != null">
				AND UpdateTime &gt; #{syncDate}
			</if>
		ON DUPLICATE KEY UPDATE 
			<!-- 交付状态处理，delivery_status = 1 已交付 -->
			project_id = IF(delivery_status = 1 ,p.project_code,cs_sync_house.project_id),
			house_id = IF(delivery_status = 1 ,h.RoomID,cs_sync_house.house_id),
			house_num = IF(delivery_status = 1 ,h.RoomSign,cs_sync_house.house_num),
			house_name = IF(delivery_status = 1 ,h.RoomName,cs_sync_house.house_name),
			area = IF(delivery_status = 1 ,p.region,cs_sync_house.area),
			region_code = IF(delivery_status = 1 ,p.region_code,cs_sync_house.region_code),
			city = IF(delivery_status = 1 ,p.city_company,cs_sync_house.city),
			city_company_code = IF(delivery_status = 1 ,p.city_company_code,cs_sync_house.city_company_code),
			project = IF(delivery_status = 1 ,p.project,cs_sync_house.project),
			unit = IF(delivery_status = 1 ,h.UnitSNum,cs_sync_house.unit),
			building = IF(delivery_status = 1 ,h.BuildName,cs_sync_house.building),
			room_num = IF(delivery_status = 1 ,SUBSTRING_INDEX(h.RoomSign,'-',-1),cs_sync_house.room_num),
			use_property = IF(delivery_status = 1 ,h.PropertyUses,cs_sync_house.use_property),
			delivery_date = IF(delivery_status = 1 ,h.ContSubDate,cs_sync_house.delivery_date),
			focus_start_date = IF(delivery_status = 1 ,h.getHouseStartDate,cs_sync_house.focus_start_date),
			focus_end_date = IF(delivery_status = 1 ,h.getHouseEndDate,cs_sync_house.focus_end_date),
			fitment = IF(delivery_status = 1 ,h.BuildsRenovation,cs_sync_house.fitment),
			actual_delivery_date = IF(delivery_status = 1 ,h.ActualSubDate,cs_sync_house.actual_delivery_date),
			stay_time = IF(delivery_status = 1 ,h.StayTime,cs_sync_house.stay_time),
			is_delete = IF(delivery_status = 1 ,h.IsDelete,cs_sync_house.is_delete),
			steward_name = IF(delivery_status = 1 ,h.UserName,cs_sync_house.steward_name),
			steward_telephone = IF(delivery_status = 1 ,h.MobileTel,cs_sync_house.steward_telephone),
			update_date = IF(delivery_status = 1 ,NOW(),cs_sync_house.update_date),
			source = IF(delivery_status = 1 ,1,cs_sync_house.source)
	</insert>
	
	<select id="selectDelivery" resultType="com.tahoecn.customerservice.model.CsSyncHouse">
		SELECT DISTINCT area,region_code,city,city_company_code,project,project_id,building,delivery_status 
		FROM cs_sync_house WHERE source = 2
		<if test="regionCode != null and regionCode != ''">
			AND region_code = #{regionCode}
		</if>
		<if test="cityCompanyCode != null and cityCompanyCode != ''">
			AND city_company_code = #{cityCompanyCode}
		</if>
		<if test="projectId != null and projectId != ''">
			AND project_id = #{projectId}
		</if>
		<if test="deliveryStatus == 0 or (deliveryStatus != null and deliveryStatus != '') ">
			AND delivery_status = #{deliveryStatus}
		</if>
	</select>
	
	<select id="expDeliver" resultType="com.tahoecn.customerservice.model.excelDTO.DeliverDto">
		SELECT h.*,c.* FROM cs_sync_house h 
		LEFT JOIN cs_sync_cust2house ch ON h.house_num = ch.house_code 
		LEFT JOIN cs_sync_cust c ON ch.certificate_num = c.certificate_num 
		WHERE h.source = 2
		<if test="regionCode != null and regionCode != ''">
			AND h.region_code = #{regionCode}
		</if>
		<if test="cityCompanyCode != null and cityCompanyCode != ''">
			AND h.city_company_code = #{cityCompanyCode}
		</if>
		<if test="projectId != null and projectId != ''">
			AND h.project_id = #{projectId}
		</if>
		<if test="deliveryStatus == 0 or (deliveryStatus != null and deliveryStatus != '')">
			AND h.delivery_status = #{deliveryStatus}
		</if>
	</select>
	
	<select id="expDeliverNew" resultType="com.tahoecn.customerservice.model.excelDTO.DeliverDto">
		<foreach collection="list" item="item" index="index" separator=" UNION ">
			SELECT h.*,c.* FROM cs_sync_house h LEFT JOIN cs_sync_cust2house ch ON h.house_num = ch.house_code LEFT JOIN cs_sync_cust c ON ch.certificate_num = c.certificate_num 
			WHERE h.source = 2 AND h.project_id = #{item.projectId} AND h.building = #{item.building}
		</foreach>
	</select>
	
	<update id="updateSignDate">
		UPDATE cs_sync_house h, cs_sync_my_cust2house ch SET h.sign_date = ch.cq_date WHERE h.house_num = ch.room_code
	</update>
	
</mapper>
