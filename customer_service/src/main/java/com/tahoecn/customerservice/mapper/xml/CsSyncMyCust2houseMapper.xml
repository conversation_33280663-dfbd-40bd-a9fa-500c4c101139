<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncMyCust2houseMapper">

	<select id="syncDate" resultType="java.time.LocalDateTime">
		SELECT MAX(cq_date) FROM cs_sync_my_cust2house
	</select>

	<insert id="insertSync">
		INSERT INTO cs_sync_my_cust2house VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.houseId},
			#{item.roomCode},
			#{item.custId},
			#{item.certificateNum},
			#{item.cqDate})
		</foreach>
		ON DUPLICATE KEY UPDATE 
			house_id = VALUES(house_id),
			room_code = VALUES(room_code),
			cust_id = VALUES(cust_id),
			certificate_num = VALUES(certificate_num),
			cq_date = VALUES(cq_date)
	</insert>

	<delete id="deletesyncMyCust2house">
			delete from cs_sync_my_cust2house
	</delete>

</mapper>
