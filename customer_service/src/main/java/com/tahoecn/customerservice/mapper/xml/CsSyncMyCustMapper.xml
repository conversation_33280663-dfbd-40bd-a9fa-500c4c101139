<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncMyCustMapper">

	<select id="syncDate" resultType="java.time.LocalDateTime">
		SELECT MAX(update_date) FROM cs_sync_my_cust
	</select>
	
	<insert id="insertSync">
		INSERT INTO cs_sync_my_cust VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.custId},
			#{item.custName},
			#{item.certificateName},
			#{item.certificateNum},
			#{item.fixedTelephone},
			#{item.telephone},
			#{item.faxTelephone},
			#{item.contactAddress},
			#{item.PostCode},
			#{item.Email},
			#{item.belong},
			#{item.sex},
			#{item.national},
			#{item.birthday},
			#{item.workUnit},
			#{item.profession},
			#{item.updateDate},
			#{item.projid})
		</foreach>
		ON DUPLICATE KEY UPDATE
			cust_id = VALUES(cust_id),
			cust_name = VALUES(cust_name),
			certificate_name = VALUES(certificate_name),
			certificate_num = VALUES(certificate_num),
			fixed_telephone = VALUES(fixed_telephone),
			telephone = VALUES(telephone),
			fax_telephone = VALUES(fax_telephone),
			contact_address = VALUES(contact_address),
			PostCode = VALUES(PostCode),
			Email = VALUES(Email),
			belong = VALUES(belong),
			sex = VALUES(sex),
			national = VALUES(national),
			birthday = VALUES(birthday),
			work_unit = VALUES(work_unit),
			profession = VALUES(profession),
			update_date = VALUES(update_date),
			projid = VALUES(projid)
	</insert>
	
</mapper>
