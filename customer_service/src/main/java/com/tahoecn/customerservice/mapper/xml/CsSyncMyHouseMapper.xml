<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncMyHouseMapper">

	<select id="syncDate" resultType="java.time.LocalDateTime">
		SELECT MAX(update_date) FROM cs_sync_my_house
	</select>
	
	<insert id="insertSync">
		INSERT INTO cs_sync_my_house VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.projectId},
			<!-- #{item.houseCode}, 改为  #{item.roomCode},-->
			#{item.roomCode},
			#{item.houseId},
			#{item.houseNum},
			#{item.roomCode},
			#{item.area},
			#{item.city},
			#{item.project},
			#{item.building},
			#{item.Unit},
			#{item.roomNum},
			#{item.useProperty},
			#{item.fitment},
			#{item.updateDate},
			#{item.projid},
			#{item.obtainTime},
			#{item.Status})
		</foreach>
		ON DUPLICATE KEY UPDATE
			project_id = VALUES(project_id),
			house_code = VALUES(house_code),
			house_id = VALUES(house_id),
			house_num = VALUES(house_num),
			room_code = VALUES(house_code),
			area = VALUES(area),
			city = VALUES(city),
			project = VALUES(project),
			building = VALUES(building),
			Unit = VALUES(Unit),
			room_num = VALUES(room_num),
			use_property = VALUES(use_property),
			fitment = VALUES(fitment),
			update_date = VALUES(update_date),
			projid = VALUES(projid),
			obtain_time = VALUES(obtain_time),
			`Status` = VALUES(`Status`)
	</insert>
	
</mapper>
