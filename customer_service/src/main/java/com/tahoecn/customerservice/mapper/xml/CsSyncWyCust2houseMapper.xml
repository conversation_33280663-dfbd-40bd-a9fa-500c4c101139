<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncWyCust2houseMapper">

	<select id="syncDate" resultType="java.time.LocalDateTime">
		SELECT MAX(ChargeTime) FROM cs_sync_wy_cust2house
	</select>
	
	<insert id="insertSync">
		INSERT INTO cs_sync_wy_cust2house(
			LiveID,
			RoomID,
			RoomSign,
			CustID,
			CustName,
			PaperCode,
			IsActive,
			ChargeTime
		) VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.LiveID},
			#{item.RoomID},
			#{item.RoomSign},
			#{item.CustID},
			#{item.CustName},
			#{item.PaperCode},
			#{item.IsActive},
			#{item.ChargeTime})
		</foreach>
		ON DUPLICATE KEY UPDATE
			LiveID = VALUES(LiveID),
			RoomID = VALUES(RoomID),
			RoomSign = VALUES(RoomSign),
			CustID = VALUES(CustID),
			CustName = VALUES(CustName),
			PaperCode = VALUES(PaperCode),
			IsActive = VALUES(IsActive),
			ChargeTime = VALUES(ChargeTime)
	</insert>
	
</mapper>
