<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncWyCustMapper">

	<select id="syncDate" resultType="java.time.LocalDateTime">
		SELECT MAX(custupdatetime) FROM cs_sync_wy_cust
	</select>
	
	<insert id="insertSync">
		INSERT INTO cs_sync_wy_cust(
			CommID,
			CustID,
			CustName,
			PaperCode,
			PaperName,
			FaxTel,
			MobilePhone,
			Address,
			PostCode,
			EMail,
			IsUnit,
			Sex,
			Nationality,
			Birthday,
			Job,
			Hobbies,
			custupdatetime,
			IsDelete
		) VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.CommID},
			#{item.CustID},
			#{item.CustName},
			#{item.PaperCode},
			#{item.PaperName},
			#{item.FaxTel},
			#{item.MobilePhone},
			#{item.Address},
			#{item.PostCode},
			#{item.EMail},
			#{item.IsUnit},
			#{item.Sex},
			#{item.Nationality},
			#{item.Birthday},
			#{item.Job},
			#{item.Hobbies},
			#{item.custupdatetime},
			#{item.IsDelete})
		</foreach>
		ON DUPLICATE KEY UPDATE
			CommID = VALUES(CommID),
			CustID = VALUES(CustID),
			CustName = VALUES(CustName),
			PaperCode = VALUES(PaperCode),
			PaperName = VALUES(PaperName),
			FaxTel = VALUES(FaxTel),
			MobilePhone = VALUES(MobilePhone),
			Address = VALUES(Address),
			PostCode = VALUES(PostCode),
			EMail = VALUES(EMail),
			IsUnit = VALUES(IsUnit),
			Sex = VALUES(Sex),
			Nationality = VALUES(Nationality),
			Birthday = VALUES(Birthday),
			Job = VALUES(Job),
			Hobbies = VALUES(Hobbies),
			custupdatetime = VALUES(custupdatetime),
			IsDelete = VALUES(IsDelete)
	</insert>
	
	<select id="convertCustId" resultType="java.lang.String">
		SELECT CustID FROM cs_sync_wy_cust c 
		WHERE c.CommID IN(
			SELECT project_code FROM cs_project_idinfo p 
			WHERE p.project_source = 1 AND p.project_id = #{projectId}
		) AND c.PaperCode like concat('%',#{custId},'%')
	</select>
	
</mapper>
