<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsSyncWyHouseMapper">

	<select id="syncDate" resultType="java.time.LocalDateTime">
		SELECT MAX(UpdateTime) FROM cs_sync_wy_house
	</select>
	
	<insert id="insertSync">
		INSERT INTO cs_sync_wy_house(
			OrganName,
			City,
			CommID,
			CommName,
			RoomID,
			RoomSign,
			RoomName,
			UnitSNum,
			BuildName,
			BuildSNum,
			PropertyUses,
			ContSubDate,
			getHouseStartDate,
			getHouseEndDate,
			PayState,
			BuildsRenovation,
			ActualSubDate,
			StayTime,
			UpdateTime,
			IsDelete,
			UserName,
			MobileTel
		) VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.OrganName},
			#{item.City},
			#{item.CommID},
			#{item.CommName},
			#{item.RoomID},
			#{item.RoomSign},
			#{item.RoomName},
			#{item.UnitSNum},
			#{item.BuildName},
			#{item.BuildSNum},
			#{item.PropertyUses},
			#{item.ContSubDate},
			#{item.getHouseStartDate},
			#{item.getHouseEndDate},
			#{item.PayState},
			#{item.BuildsRenovation},
			#{item.ActualSubDate},
			#{item.StayTime},
			#{item.UpdateTime},
			#{item.IsDelete},
			#{item.UserName},
			#{item.MobileTel})
		</foreach>
		ON DUPLICATE KEY UPDATE
			OrganName = VALUES(OrganName),
			City = VALUES(City),
			CommID = VALUES(CommID),
			CommName = VALUES(CommName),
			RoomID = VALUES(RoomID),
			RoomSign = VALUES(RoomSign),
			RoomName = VALUES(RoomName),
			UnitSNum = VALUES(UnitSNum),
			BuildName = VALUES(BuildName),
			BuildSNum = VALUES(BuildSNum),
			PropertyUses = VALUES(PropertyUses),
			ContSubDate = VALUES(ContSubDate),
			getHouseStartDate = VALUES(getHouseStartDate),
			getHouseEndDate = VALUES(getHouseEndDate),
			PayState = VALUES(PayState),
			BuildsRenovation = VALUES(BuildsRenovation),
			ActualSubDate = VALUES(ActualSubDate),
			StayTime = VALUES(StayTime),
			UpdateTime = VALUES(UpdateTime),
			IsDelete = VALUES(IsDelete),
			UserName = VALUES(UserName),
			MobileTel = VALUES(MobileTel)
	</insert>
	
	<select id="convertRoomId" resultType="java.lang.String">
		SELECT RoomID FROM cs_sync_wy_house h 
		WHERE h.CommID IN(
			SELECT project_code FROM cs_project_idinfo p 
			WHERE p.project_source = 1 AND p.project_id = #{projectId}
		) AND h.RoomSign = #{houseId};
	</select>
	
</mapper>
