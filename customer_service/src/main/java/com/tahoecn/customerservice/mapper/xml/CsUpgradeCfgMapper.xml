<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsUpgradeCfgMapper">

    <select id="selectUpgradeCfgByFourthSortCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUpgradeCfgBak">
        select * from cs_upgrade_cfg cuc where cuc.fourth_sort_code = #{itemCode}
    </select>

    <update id="updateByIdBak" parameterType="com.tahoecn.customerservice.model.CsUpgradeCfgBak">
        UPDATE cs_upgrade_cfg cuc
        SET  cuc.first_sort_code = #{firstSortCode},
        cuc.first_sort_name = #{firstSortName},
        cuc.sec_sort_code = #{secSortCode},
        cuc.sec_sort_name = #{secSortName},
        cuc.third_sort_code = #{thirdSortCode},
        cuc.third_sort_name = #{thirdSortName},
        cuc.fourth_sort_code = #{fourthSortCode},
        cuc.fourth_sort_name = #{fourthSortName},
        cuc.city_days = #{cityDays},
        cuc.region_days = #{regionDays},
        cuc.group_dasy = #{groupDasy}
        WHERE cuc.id = #{id}
    </update>

    <insert id="insertByBak" parameterType="com.tahoecn.customerservice.model.CsUpgradeCfgBak">
        INSERT INTO cs_upgrade_cfg (
        first_sort_code,
        first_sort_name,
        sec_sort_code,
        sec_sort_name,
        third_sort_code,
        third_sort_name,
        fourth_sort_code,
        fourth_sort_name
        ) VALUES (
        #{firstSortCode},
        #{firstSortName},
        #{secSortCode},
        #{secSortName},
        #{thirdSortCode},
        #{thirdSortName},
        #{fourthSortCode},
        #{fourthSortName}
        )
    </insert>

    <select id="selectFirstSort" resultType="com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo">
        select first_sort_code sortCode,first_sort_name sortName,'一级' levelName,city_days cityDays,region_days regionDays,group_dasy groupDasy from cs_upgrade_cfg where first_sort_code is not null and (sec_sort_code is null or sec_sort_code='')
    </select>
    <select id="selectSecSort" resultType="com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo">
        select sec_sort_code sortCode,sec_sort_name sortName,'二级' levelName,city_days cityDays,region_days regionDays,group_dasy groupDasy from cs_upgrade_cfg  where first_sort_code=#{sortCode} and first_sort_code is not null and sec_sort_code is not null and (third_sort_code is null or third_sort_code='')
    </select>
    <select id="selectThirdSort" resultType="com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo">
      select third_sort_code sortCode,third_sort_name sortName,'三级' levelName,city_days cityDays,region_days regionDays,group_dasy groupDasy from cs_upgrade_cfg  where first_sort_code=#{sortCode} and sec_sort_code=#{secSortCode} and third_sort_code is not null and (fourth_sort_code is null or fourth_sort_code='')
    </select>
    <select id="selectThirdSortFinal" resultType="com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo">
        select third_sort_code sortCode,third_sort_name sortName,city_days cityDays,region_days regionDays,group_dasy groupDasy,'三级' levelName from cs_upgrade_cfg  where first_sort_code=#{sortCode} and sec_sort_code=#{secSortCode} and third_sort_code=#{thirdSortCode} and third_sort_code is not null and third_sort_code!=''
    </select>
    <select id="selectFourthSort" resultType="com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo">
        select fourth_sort_code sortCode,fourth_sort_name sortName,'四级' levelName,city_days cityDays,region_days regionDays,group_dasy groupDasy  from cs_upgrade_cfg where first_sort_code=#{sortCode} and sec_sort_code=#{secSortCode} and third_sort_code=#{thirdSortCode} and fourth_sort_code is not null and fourth_sort_code!=''
    </select>
    <select id="selectFirstSortByCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUpgradeCfgBak">
        select * from  cs_upgrade_cfg cuc where 1 = 1
         <if test="type == 1">
             and cuc.first_sort_code = #{code} and (cuc.sec_sort_code is null or cuc.sec_sort_code='')
         </if>
         <if test="type == 2">
             and cuc.sec_sort_code = #{code} and (cuc.third_sort_code is null or cuc.third_sort_code='')
         </if>
         <if test="type == 3">
             and cuc.third_sort_code = #{code} and (cuc.fourth_sort_code is null or cuc.fourth_sort_code='')
         </if>
         <if test="type == 4">
             and cuc.fourth_sort_code = #{code}
         </if>
    </select>
    
    <!-- 手机APP-查询工单剩余处理天数 -->
    <select id="selDayByFormId" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUpgradeCfgBak">
    	select b.* 
    	from cs_form_inst a left join cs_upgrade_cfg b 
    	on a.first_sort_code = b.first_sort_code 
    	and a.sec_sort_code = b.sec_sort_code 
    	and a.third_sort_code = b.third_sort_code
        and IFNULL(a.fourth_sort_code,'') = IFNULL(b.fourth_sort_code,'')
        where <!-- a.process_state_code = 'handle' and
        a.first_sort_code in ('coTS','coBX') and -->
        a.submit_date is not null
        <!-- and a.creation_date > '2018-12-25' -->
        and a.id = #{formId}
    </select>
</mapper>
