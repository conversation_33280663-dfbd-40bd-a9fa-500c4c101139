<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.CsUserRoleMapper">


	<!--查询集团有关信息-->
	<select id="selcetJiTuan" resultType="com.tahoecn.customerservice.model.CsUserRole">
		select
		id,
		`role_code` as "roleCode",
		`role_name` as "roleName",
		`user_id` as "userId",
		`user_name` as "userName",
		`project_code` as "projectCode",
		`mobile` as "mobile",
		`remarks` as "remarks"
		from
	        cs_user_role cur
        where
	        cur.role_code like '%upgrade-3%';
	</select>

	<select id="selectJiTuanProjectCode" resultType="com.tahoecn.customerservice.model.CsUserRole">
		select
	     distinct project_code
	    from
	        cs_project_info cpi
	</select>

	<select id="selectCUU" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUcUser">
		select cuu.fd_name as 'fdName',
			cuu.fd_username as 'fdUserName',
			cuu.fd_tel as 'fdTel'
		from  cs_uc_user cuu where fd_username = #{userId}
	</select>

	<delete id="deleteUserRoleQY" parameterType="java.lang.String">
		DELETE FROM cs_user_role WHERE remarks = '区域' and project_code = #{projectCode}
	</delete>

	<select id="selectProjectCode" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUserRole">
		select
			cur.id,
			cur.role_code as "roleCode",
			cur.role_name as "roleName",
			cur.user_id as "userId",
			cur.user_name as "userName",
			cur.project_code as "projectCode",
			cur.mobile as "mobile",
			cur.remarks as "remarks"
	    from
	        cs_project_info cpi
		LEFT JOIN cs_user_role cur on
			cpi.project_code = cur.project_code
        where
			cpi.region = #{regionCode} and
			cur.role_code like '%upgrade-2%';
	</select>

	<select id="selectAllCsUserRole" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUserRole">
		select
	     	id,
			`role_code` as "roleCode",
			`role_name` as "roleName",
			`user_id` as "userId",
			`user_name` as "userName",
			`project_code` as "projectCode",
			`mobile` as "mobile",
			`remarks` as "remarks"
	    from
	        cs_user_role cur
        where
			cur.project_code = #{projectCode}
			and cur.role_code != 'sComplaint-upgrade-1' and cur.role_code != 'sRepair-upgrade-1' and cur.role_code != 'sComplaint-upgrade-3' and cur.role_code != 'sRepair-upgrade-3' and cur.role_code != 'sComplaint-upgrade-2' and cur.role_code != 'sRepair-upgrade-2'
	</select>

	<select id="selcetcCity" parameterType="java.lang.String" resultType="com.tahoecn.customerservice.model.CsUserRole">
		  select
			cur.id,
			cur.role_code as "roleCode",
			cur.role_name as "roleName",
			cur.user_id as "userId",
			cur.user_name as "userName",
			cur.project_code as "projectCode",
			cur.mobile as "mobile",
			cur.remarks as "remarks"
	      from
	      		cs_project_info cpi
		  LEFT JOIN cs_user_role cur on
		  		cpi.project_code = cur.project_code
       	  where
				cpi.region = #{regionCode}  and
				cur.role_code like '%upgrade-1%'
				and cpi.city_company  = #{city}
	</select>

    <select id="selectType1"  resultType="com.tahoecn.customerservice.model.dto.ChargerExtDo">
        select DISTINCT a.role_code roleCode,a.user_id userId,a.user_name userName from cs_user_role a where a.role_code like '%upgrade-3'
    </select>
    <select id="selectType2"  resultType="com.tahoecn.customerservice.model.dto.ChargerExtDo">
        select DISTINCT a.role_code roleCode,a.user_id userId,a.user_name userName from (select u.*,p.city,p.city_code,p.city_company,p.city_company_code,p.project,p.region,p.region_code from cs_user_role u left join cs_project_info p on u.project_code = p.project_code  where u.role_code like '%upgrade-2'
        <if test="_parameter !=null">
            and p.region=#{typeName}
        </if>
        <if test="_parameter ==null ">
            and p.region is not null
        </if>
        ) a
    </select>
    <select id="selectType3"  resultType="com.tahoecn.customerservice.model.dto.ChargerExtDo">
        select  DISTINCT a.role_code roleCode,a.user_id userId,a.user_name userName from (select u.*,p.city,p.city_code,p.city_company,p.city_company_code,p.project,p.region,p.region_code from cs_user_role u left join cs_project_info p on u.project_code = p.project_code  where u.role_code like '%upgrade-1'
        <if test="_parameter !=null">
            and p.city_company=#{typeName}
        </if>
        <if test="_parameter ==null">
            and p.city_company is not null
        </if>
        ) a
    </select>
    <select id="selectType4"  resultType="com.tahoecn.customerservice.model.dto.ChargerExtDo">
        select DISTINCT a.role_code roleCode,a.user_id userId,a.user_name userName from cs_user_role  a where 1=1
        <if test="_parameter !=null">
            and a.project_code=#{typeName}
        </if>
    </select>

	<select id="selectKFNum" resultType="com.tahoecn.customerservice.model.CsUserRole">
		select cur.user_name as "userName" from cs_user_role cur where cur.role_code = 'sComplaint-upgrade-3' GROUP BY user_name
	</select>

	<select id="selectFXNum" resultType="com.tahoecn.customerservice.model.CsUserRole">
		select cur.user_name as "userName" from cs_user_role cur where cur.role_code = 'sRepair-upgrade-3' GROUP BY user_name
	</select>

	<delete id="deleteUserRole" parameterType="java.lang.String">
		DELETE FROM cs_user_role WHERE remarks = '集团'
	</delete>

	<delete id="deleteUserRoleCS" parameterType="java.lang.Long">
	  	DELETE FROM cs_user_role WHERE id = #{id}
	</delete>
	
	<select id="selectItUser" resultType="com.tahoecn.customerservice.model.dto.UserRoleDictDto">
		SELECT
			t.*, cdi.item_value projectName
		FROM
			(
				SELECT
					project_code,
					role_name,
					role_code,
					GROUP_CONCAT(user_name) userName,
					GROUP_CONCAT(user_id) userId
				FROM
					`cs_user_role`
				WHERE
					role_code LIKE 'itRepair%'
				GROUP BY
					project_code,
					role_code,
					role_name
			) t
		LEFT JOIN cs_dict_item cdi ON t.project_code = cdi.item_code
	</select>
	
	<insert id="insertUserList">
		INSERT INTO cs_user_role (
			role_code,
			role_name,
			user_id,
			user_name,
			project_code,
			mobile,
			remarks
		)
		VALUES
		<foreach collection="list" item="item" index="index"
			separator=",">
			(
				#{item.roleCode,jdbcType=VARCHAR},
				#{item.roleName,jdbcType=VARCHAR},
				#{item.userId,jdbcType=VARCHAR},
				#{item.userName,jdbcType=VARCHAR},
				#{item.projectCode,jdbcType=VARCHAR},
				#{item.mobile,jdbcType=VARCHAR},
				#{item.remarks,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>
</mapper>
