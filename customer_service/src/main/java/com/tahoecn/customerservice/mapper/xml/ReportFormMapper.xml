<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapper.ReportFormMapper">

	<!-- 报修整体数据统计 -->
	<select id="repairWhole" resultType="com.tahoecn.customerservice.model.report.RepairWholeDto">
		select
			region_code as regionCode,
			max(region) as region,
			city_code as cityCode,
			max(city) as city,
			sum( if ( f.accept_channel_code = '400', 1, 0 ) ) as fzzNum,
			count( * ) as total,
			sum( if ( f.process_state_code = 'nomalend' and (f.upgrade_flag is null or f.upgrade_flag != 1), 1, 0 ) ) as timelyClosureNum,
			sum( if ( f.process_state_code = 'nomalend', 1, 0 ) ) as closeNum,
			sum( if ( f.process_state_code = 'specialend', 1, 0 ) ) as specialClosureNum,
			sum( if ( f.process_state_code = 'nomalend' and f.satisfaction_code is not null and satisfaction_code != '-1' , 1, 0 ) ) as satisfactionTotalNum,
			sum( if ( f.process_state_code = 'nomalend' and f.satisfaction_code is not null and satisfaction_code != '-1' , f.satisfaction_code, 0 ) ) as satisfactionNum
		from
			cs_form_inst f 
		where
			f.region_code is not null and f.first_sort_code = 'cobx' and f.dept_code = 'deptDC' 
			<if test="startDate != null">
				and f.creation_date &gt;= #{startDate}
			</if>
			<if test="endDate != null">
				and f.creation_date is not null and f.creation_date &lt;= DATE_ADD(#{endDate},INTERVAL 1 DAY)
			</if>
			<!-- 数据权限处理 -->
            <if test="(fdOrgSid != null and fdOrgSid != '') or (userName != null and userName != '') ">
                AND (
                <if test="fdOrgSid != null and fdOrgSid != ''">
                    (LOCATE(f.region_code,#{fdOrgSid}) > 0 OR LOCATE(f.city_code,#{fdOrgSid}) > 0 OR LOCATE(f.project_code,#{fdOrgSid}) > 0)
                </if>
                <if test="fdOrgSid != null and fdOrgSid != '' and userName != null and userName != ''">
                    OR
                </if>
                <if test="userName != null and userName != ''">
                    (f.create_user_id = #{userName} OR p.assign_id = #{userName})
                </if>)
            </if>
		group by
			f.region_code,
			f.city_code
		order by f.region_code,f.city_code
	</select>
	
	<!-- 报修整体数据统计 -->
	<select id="channel" resultType="com.tahoecn.customerservice.model.report.ChannelDto">
		SELECT
			f.region_code as regionCode,
			MAX( f.region ) as region,
			f.city_code as cityCode,
			MAX( f.city ) as city,
			f.project_code as projectCode,
			MAX( f.project ) as project,
			f.first_sort_code as firstSortCode,
			MAX( f.first_sort_name ) as firstSortName,
			f.report_channel_code as reportChannelCode,
			MAX( f.report_channel_name ) as reportChannelName,
			COUNT( * ) AS reportChannelNum 
		FROM
			cs_form_inst f 
		WHERE
			f.region_code IS NOT NULL and f.dept_code = 'deptDC'
			<if test="startDate != null">
				and f.creation_date &gt;= #{startDate}
			</if>
			<if test="endDate != null">
				and f.creation_date is not null and f.creation_date &lt;= DATE_ADD(#{endDate},INTERVAL 1 DAY)
			</if>
			<if test="firstSortCode != null and firstSortCode != ''">
				and f.first_sort_code = #{firstSortCode}
			</if>
			<if test="regionCode != null and regionCode != ''">
				and f.region_code = #{regionCode}
			</if>
			<!-- 数据权限处理 -->
            <if test="(fdOrgSid != null and fdOrgSid != '') or (userName != null and userName != '') ">
                AND (
                <if test="fdOrgSid != null and fdOrgSid != ''">
                    (LOCATE(f.region_code,#{fdOrgSid}) > 0 OR LOCATE(f.city_code,#{fdOrgSid}) > 0 OR LOCATE(f.project_code,#{fdOrgSid}) > 0)
                </if>
                <if test="fdOrgSid != null and fdOrgSid != '' and userName != null and userName != ''">
                    OR
                </if>
                <if test="userName != null and userName != ''">
                    (f.create_user_id = #{userName} OR p.assign_id = #{userName})
                </if>)
            </if>
		GROUP BY
			f.region_code,
			f.city_code,
			f.project_code,
			f.first_sort_code,
			f.report_channel_code
		ORDER BY
			f.region_code,
			f.city_code,
			f.project_code,
			f.first_sort_code,
			f.report_channel_code
	</select>
	
	<select id="sortTree" resultType="com.tahoecn.customerservice.model.report.SortTreeDto">
		SELECT DISTINCT * FROM (
			SELECT
				MAX( f.region_code ) region_code,
				MAX( f.region ) region,
				MAX( f.city_code ) city_code,
				MAX( f.city ) city,
				f.project_code,
				MAX( f.project ) project,
				f.first_sort_code,
				MAX( f.first_sort_name ) first_sort_name,
				f.sec_sort_code,
				if(f.sec_sort_code is null ,null,MAX( f.sec_sort_name )) sec_sort_name,
				f.third_sort_code,
				if(f.third_sort_code is null ,null,MAX( f.third_sort_name )) third_sort_name,
				f.fourth_sort_code,
				if(f.fourth_sort_code is null ,null,MAX( f.fourth_sort_name )) fourth_sort_name,
				COUNT(*) total,
				SUM(IF(f.process_state_code LIKE '%End',1,0)) closeNum,
				SUM(IFNULL(f.reject_flag,0)) rejectNum,
				SUM(IFNULL(f.upgrade_flag,0)) upgradeNum
			FROM
				cs_form_inst f
			WHERE f.dept_code = 'deptDC' and f.first_sort_code != 'coJYBY'
			<if test="isUpgrade != null">
				and f.first_sort_code != 'coZX'
			</if>
			<if test="startDate != null">
				and f.creation_date &gt;= #{startDate}
			</if>
			<if test="endDate != null">
				and f.creation_date is not null and f.creation_date &lt;= DATE_ADD(#{endDate},INTERVAL 1 DAY)
			</if>
			<if test="firstSortCode != null and firstSortCode != ''">
				and f.first_sort_code = #{firstSortCode}
			</if>
			<if test="regionCode != null and regionCode != ''">
				and f.region_code = #{regionCode}
			</if>
			<if test="cityCode != null and cityCode != ''">
				and f.city_code = #{cityCode}
			</if>
			<!-- 数据权限处理 -->
            <if test="(fdOrgSid != null and fdOrgSid != '') or (userName != null and userName != '') ">
                AND (
                <if test="fdOrgSid != null and fdOrgSid != ''">
                    (LOCATE(f.region_code,#{fdOrgSid}) > 0 OR LOCATE(f.city_code,#{fdOrgSid}) > 0 OR LOCATE(f.project_code,#{fdOrgSid}) > 0)
                </if>
                <if test="fdOrgSid != null and fdOrgSid != '' and userName != null and userName != ''">
                    OR
                </if>
                <if test="userName != null and userName != ''">
                    (f.create_user_id = #{userName} OR p.assign_id = #{userName})
                </if>)
            </if>
			GROUP BY 
				f.project_code,
				f.first_sort_code,
				f.sec_sort_code,
				f.third_sort_code,
				f.fourth_sort_code WITH ROLLUP
			HAVING(
				f.project_code is not null and 
				f.first_sort_code is not null
			)
		) fi
		ORDER BY 
			fi.region_code,
			fi.city_code,
			fi.project_code,
			fi.first_sort_code,
			fi.sec_sort_code,
			fi.third_sort_code,
			fi.fourth_sort_code
	</select>

</mapper>
