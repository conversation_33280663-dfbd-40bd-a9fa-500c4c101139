<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mappermy.SyncMyCust2houseMapper">

	<select id="getList" resultType="com.tahoecn.customerservice.model.CsSyncMyCust2house">
		SELECT * FROM View_kf_room2customer
		<!-- <where>
			<if test="syncDate">
				cq_date &gt; #{syncDate}
			</if>
		</where>  -->
	</select>

</mapper>
