package com.tahoecn.customerservice.mapperwy;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.tahoecn.customerservice.model.CsSyncWyCust2house;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
public interface SyncWyCust2houseMapper {

	/**
	 * 获取待同步数据列表
	 * 
	 * @param syncDate
	 * @return
	 */
	List<CsSyncWyCust2house> getList(@Param("syncDate") LocalDateTime syncDate);

}
