<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapperwy.SyncWyCustMapper">

	<select id="getList" resultType="com.tahoecn.customerservice.model.CsSyncWyCust">
		SELECT * FROM view_HSPR_Customer_THKF
		<where>
			<if test="syncDate">
				custupdatetime &gt; #{syncDate}
			</if>
		</where>
	</select>

</mapper>
