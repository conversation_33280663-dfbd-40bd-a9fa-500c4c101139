<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.customerservice.mapperwy.SyncWyHouseMapper">

	<select id="getList" resultType="com.tahoecn.customerservice.model.CsSyncWyHouse">
		SELECT * FROM view_HSPR_Room_THKF
		<where>
			<if test="syncDate">
				UpdateTime &gt; #{syncDate}
			</if>
		</where>
	</select>

</mapper>
