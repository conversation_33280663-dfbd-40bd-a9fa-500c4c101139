package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;
import com.baomidou.mybatisplus.annotations.TableId;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-22
 */
public class CsAnnouncement implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 公告内容
     */
    private String announceMent;
    /**
     * 显示状态1显示0不显示
     */
    private String state;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAnnounceMent() {
        return announceMent;
    }

    public void setAnnounceMent(String announceMent) {
        this.announceMent = announceMent;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "CsAnnouncement{" +
        "id=" + id +
        ", announceMent=" + announceMent +
        ", state=" + state +
        "}";
    }
}
