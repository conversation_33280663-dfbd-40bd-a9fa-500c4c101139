package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 常见问题表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsCommonQuestion implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private Long id;
    /**
     * 用户名
     */
    private String userName;

    /**
     * 常见问题标题
     */
    private String questionTitle;

    /**
     * 常见问题描述
     */
    private String questionDesc;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 最后更新日期
     */
    private Date lastUpdateDate;
    /**
     * 排序
     */
    private Long orderBy;
    
    private Integer isDelete;

    public String getQuestionTitle() {
        return questionTitle;
    }

    public void setQuestionTitle(String questionTitle) {
        this.questionTitle = questionTitle;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getQuestionDesc() {
        return questionDesc;
    }

    public void setQuestionDesc(String questionDesc) {
        this.questionDesc = questionDesc;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Long getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Long orderBy) {
        this.orderBy = orderBy;
    }

    public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	@Override
	public String toString() {
		return "CsCommonQuestion [id=" + id + ", userName=" + userName + ", questionTitle=" + questionTitle
				+ ", questionDesc=" + questionDesc + ", createDate=" + createDate + ", lastUpdateDate=" + lastUpdateDate
				+ ", orderBy=" + orderBy + ", isDelete=" + isDelete + "]";
	}

}
