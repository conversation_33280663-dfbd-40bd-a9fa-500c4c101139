package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户家庭成员表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsCustFamily implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业主Id
     */
    private String custId;
    /**
     * 客户家庭成员唯一标识
     */
    private String memberId;

    /**
     * 与户主关系
     */
    private String householdRelation;
    /**
     * 证件名称
     */
    private String certificateName;
    /**
     * 成员名称
     */
    private String memberName;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 性别
     */
    private String sex;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 移动电话
     */
    private String mobile;
    /**
     * 兴趣爱好
     */
    private String hobbies;
    /**
     * 是否为其他版块会员
     */
    private Integer otherBoardMember;
    /**
     * 是否为特殊客户
     */
    private Integer specialCustomer;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 来源：1.物业2.明源
     */
    private Integer source;
    
    private Long formInstId;
    
    /**
     * 微信
     */
    private String weChat;
    /**
     * 微信
     */
    private String openId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getHouseholdRelation() {
        return householdRelation;
    }

    public void setHouseholdRelation(String householdRelation) {
        this.householdRelation = householdRelation;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getHobbies() {
        return hobbies;
    }

    public void setHobbies(String hobbies) {
        this.hobbies = hobbies;
    }

    public Integer getOtherBoardMember() {
        return otherBoardMember;
    }

    public void setOtherBoardMember(Integer otherBoardMember) {
        this.otherBoardMember = otherBoardMember;
    }

    public Integer getSpecialCustomer() {
        return specialCustomer;
    }

    public void setSpecialCustomer(Integer specialCustomer) {
        this.specialCustomer = specialCustomer;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

	public Long getFormInstId() {
		return formInstId;
	}

	public void setFormInstId(Long formInstId) {
		this.formInstId = formInstId;
	}

    public String getWeChat() {
		return weChat;
	}

	public void setWeChat(String weChat) {
		this.weChat = weChat;
	}
	
	public String getOpenId() {
		return openId;
	}
	
	public void setOpenId(String openId) {
		this.openId = openId;
	}
	
	@Override
    public String toString() {
        return "CsCustFamily{" +
                "id=" + id +
                ", memberId=" + memberId +
                ", householdRelation=" + householdRelation +
                ", certificateName=" + certificateName +
                ", memberName=" + memberName +
                ", idNumber=" + idNumber +
                ", sex=" + sex +
                ", birthday=" + birthday +
                ", nationality=" + nationality +
                ", mobile=" + mobile +
                ", hobbies=" + hobbies +
                ", otherBoardMember=" + otherBoardMember +
                ", specialCustomer=" + specialCustomer +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", source=" + source +
                ", weChat=" + weChat +
                ", openId=" + openId +
                "}";
    }
}
