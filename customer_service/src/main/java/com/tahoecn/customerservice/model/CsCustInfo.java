package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableField;
import java.io.Serializable;

/**
 * <p>
 * 客户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
public class CsCustInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客户ID
     */
    private String custId;
    
    /*====================导出所需字段，顺序不可改动========================*/
    /**
     * 客户名称
     */
    @Excel(name = "客户姓名")
    private String custName;
    /**
     * 客户身份
     */
    @Excel(name = "客户身份")
    private String cusIdentity;
    /**
     * 个人/单位
     */
    @Excel(name = "个人/单位")
    private String belong;
    /**
     * 性别
     */
    @Excel(name = "性别")
    private String sex;
    /**
     * VIP
     */
    @Excel(name = "VIP")
    private String isVip;
    /**
     * 意见领袖
     */
    @Excel(name = "意见领袖")
    private String sugLeader;
    /**
     * 移动电话
     */
    @Excel(name = "移动电话")
    private String telephone;
    /**
     * 省(直辖市、自治区)
     */
    @Excel(name = "省(直辖市、自治区)")
    private String province;
    /**
     * 市
     */
    @Excel(name = "市")
    private String city;
    /**
     * 区/县
     */
    @Excel(name = "区(县)")
    private String area;
    /**
     * 联系地址
     */
    @Excel(name = "联系地址")
    private String contactAddress;
    /**
     * 固定电话
     */
    @Excel(name = "固定电话")
    private String fixedTelephone;
    /**
     * 电子邮件
     */
    @Excel(name = "电子邮件")
    private String email;
    /**
     * 传真电话
     */
    @Excel(name = "传真电话")
    private String fax;
    /**
     * 邮政编码
     */
    @Excel(name = "邮政编码")
    private String postcode;
    /**
     * 管家姓名
     */
    @Excel(name = "管家姓名")
    private String stewardName;
    /**
     * 管家电话
     */
    @Excel(name = "管家电话")
    private String stewardTelephone;
    /**
     * 国籍
     */
    @Excel(name = "国籍")
    private String national;
    /**
     * 证件名称
     */
    @Excel(name = "证件类型")
    private String certificateName;
    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    private String certificateNum;
    /**
     * 出生年月日
     */
    @Excel(name = "出生日期")
    private String birthday;
    /**
     * 工作单位
     */
    @Excel(name = "工作单位")
    private String workUnit;
    /**
     * 职业
     */
    @Excel(name = "职业")
    private String profession;
    /**
     * 年收入
     */
    @Excel(name = "年收入")
    private String yearsReceive;
    /**
     * 福州金卡用户
     */
    @Excel(name = "福州金卡用户")
    private String isFZgoldCard;
    /**
     * 泰禾拥有多套房屋
     */
    @Excel(name = "泰禾拥有多套房屋")
    private String isHasMoreHouse;
    /**
     * 医疗板块客户
     */
    @Excel(name = "医疗板块客户")
    @TableField("is_Medical_Care_User")
    private String isMedicalCareUser;
    /**
     * 金融板块客户
     */
    @Excel(name = "金融板块客户")
    private String isFinanceUser;
    /**
     * 地产板块客户
     */
    @Excel(name = "地产板块客户")
    private String isRealEstateUser;
    /**
     * 教育板块客户
     */
    @Excel(name = "教育板块客户")
    private String isEducationUser;
    /**
     * 影院教育板块客户
     */
    @Excel(name = "影院教育板块客户")
    private String isCinemaUser;
    /**
     * 拥有车辆
     */
    @Excel(name = "拥有车辆")
    private String hasCar;
    /**
     * 兴趣爱好
     */
    @Excel(name = "兴趣爱好")
    private String hobbies;
    /*====================导出所需字段，顺序不可改动========================*/
    
    
    
    /**
     * 房屋编号
     */
    private String houseNum;
    /**
     * 是否为其他版块会员
     */
    private String otherBoardMember;
    /**
     * 是否为特殊客户
     */
    private String specialCustomer;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 来源：1.物业2.明源
     */
    private Integer source;
    
    private String contactTel;
    
    /**
     * 微信
     */
    private String weChat;
    
    private String openId;
    private String labelId;//标签
    private String labelName;//标签
    private String integral;//客户积分
    private String headPortrait;//客户头像

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getHouseNum() {
        return houseNum;
    }

    public void setHouseNum(String houseNum) {
        this.houseNum = houseNum;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getFixedTelephone() {
        return fixedTelephone;
    }

    public void setFixedTelephone(String fixedTelephone) {
        this.fixedTelephone = fixedTelephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBelong() {
        return belong;
    }

    public void setBelong(String belong) {
        this.belong = belong;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNational() {
        return national;
    }

    public void setNational(String national) {
        this.national = national;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getHobbies() {
        return hobbies;
    }

    public void setHobbies(String hobbies) {
        this.hobbies = hobbies;
    }

    public String getStewardName() {
        return stewardName;
    }

    public void setStewardName(String stewardName) {
        this.stewardName = stewardName;
    }

    public String getStewardTelephone() {
        return stewardTelephone;
    }

    public void setStewardTelephone(String stewardTelephone) {
        this.stewardTelephone = stewardTelephone;
    }

    public String getOtherBoardMember() {
        return otherBoardMember;
    }

    public void setOtherBoardMember(String otherBoardMember) {
        this.otherBoardMember = otherBoardMember;
    }

    public String getSpecialCustomer() {
        return specialCustomer;
    }

    public void setSpecialCustomer(String specialCustomer) {
        this.specialCustomer = specialCustomer;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getCusIdentity() {
        return cusIdentity;
    }

    public void setCusIdentity(String cusIdentity) {
        this.cusIdentity = cusIdentity;
    }

    public String getIsVip() {
        return isVip;
    }

    public void setIsVip(String isVip) {
        this.isVip = isVip;
    }

    public String getSugLeader() {
        return sugLeader;
    }

    public void setSugLeader(String sugLeader) {
        this.sugLeader = sugLeader;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getYearsReceive() {
        return yearsReceive;
    }

    public void setYearsReceive(String yearsReceive) {
        this.yearsReceive = yearsReceive;
    }

    public String getIsFZgoldCard() {
        return isFZgoldCard;
    }

    public void setIsFZgoldCard(String isFZgoldCard) {
        this.isFZgoldCard = isFZgoldCard;
    }

    public String getIsHasMoreHouse() {
        return isHasMoreHouse;
    }

    public void setIsHasMoreHouse(String isHasMoreHouse) {
        this.isHasMoreHouse = isHasMoreHouse;
    }

    public String getIsMedicalCareUser() {
        return isMedicalCareUser;
    }

    public void setIsMedicalCareUser(String isMedicalCareUser) {
        this.isMedicalCareUser = isMedicalCareUser;
    }

    public String getIsFinanceUser() {
        return isFinanceUser;
    }

    public void setIsFinanceUser(String isFinanceUser) {
        this.isFinanceUser = isFinanceUser;
    }

    public String getIsRealEstateUser() {
        return isRealEstateUser;
    }

    public void setIsRealEstateUser(String isRealEstateUser) {
        this.isRealEstateUser = isRealEstateUser;
    }

    public String getIsEducationUser() {
        return isEducationUser;
    }

    public void setIsEducationUser(String isEducationUser) {
        this.isEducationUser = isEducationUser;
    }

    public String getIsCinemaUser() {
        return isCinemaUser;
    }

    public void setIsCinemaUser(String isCinemaUser) {
        this.isCinemaUser = isCinemaUser;
    }

    public String getHasCar() {
        return hasCar;
    }

    public void setHasCar(String hasCar) {
        this.hasCar = hasCar;
    }

	public String getContactTel() {
		return contactTel;
	}

	public void setContactTel(String contactTel) {
		this.contactTel = contactTel;
	}
	
	public String getWeChat() {
		return weChat;
	}

	public void setWeChat(String weChat) {
		this.weChat = weChat;
	}
	
	public String getOpenId() {
		return openId;
	}
	
	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getLabelId() {
		return labelId;
	}

	public void setLabelId(String labelId) {
		this.labelId = labelId;
	}

	public String getLabelName() {
		return labelName;
	}

	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}

	public String getIntegral() {
		return integral;
	}

	public void setIntegral(String integral) {
		this.integral = integral;
	}

	public String getHeadPortrait() {
		return headPortrait;
	}

	public void setHeadPortrait(String headPortrait) {
		this.headPortrait = headPortrait;
	}

	@Override
    public String toString() {
        return "CsCustInfo{" +
        "id=" + id +
        ", custId=" + custId +
        ", custName=" + custName +
        ", houseNum=" + houseNum +
        ", certificateNum=" + certificateNum +
        ", certificateName=" + certificateName +
        ", fixedTelephone=" + fixedTelephone +
        ", telephone=" + telephone +
        ", fax=" + fax +
        ", contactAddress=" + contactAddress +
        ", postcode=" + postcode +
        ", email=" + email +
        ", belong=" + belong +
        ", sex=" + sex +
        ", national=" + national +
        ", birthday=" + birthday +
        ", workUnit=" + workUnit +
        ", profession=" + profession +
        ", hobbies=" + hobbies +
        ", stewardName=" + stewardName +
        ", stewardTelephone=" + stewardTelephone +
        ", otherBoardMember=" + otherBoardMember +
        ", specialCustomer=" + specialCustomer +
        ", createDate=" + createDate +
        ", updateDate=" + updateDate +
        ", source=" + source +
        ", cusIdentity=" + cusIdentity +
        ", isVip=" + isVip +
        ", sugLeader=" + sugLeader +
        ", province=" + province +
        ", city=" + city +
        ", area=" + area +
        ", yearsReceive=" + yearsReceive +
        ", isFZgoldCard=" + isFZgoldCard +
        ", isHasMoreHouse=" + isHasMoreHouse +
        ", isMedicalCareUser=" + isMedicalCareUser +
        ", isFinanceUser=" + isFinanceUser +
        ", isRealEstateUser=" + isRealEstateUser +
        ", isEducationUser=" + isEducationUser +
        ", isCinemaUser=" + isCinemaUser +
        ", hasCar=" + hasCar +
        ", weChat=" + weChat +
        ", openId=" + openId +
        ", labelId=" + labelId +
        ", labelName=" + labelName +
        ", integral=" + integral +
        ", headPortrait=" + headPortrait +
        "}";
    }
}
