package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 字典组id
     */
    private Long dictId;
    /**
     * 字典组编码
     */
    private String dictCode;
    /**
     * 字典组名称
     */
    private String dictName;
    /**
     * 字典项编码
     */
    private String itemCode;
    /**
     * 字典项名称
     */
    private String itemValue;
    /**
     * 显示顺序
     */
    private Long displayOrder;
    /**
     * 可用状态
     */
    private String status;
    /**
     * 字典组描述
     */
    private String remarks;
    /**
     * 创建日期
     */
    private Date creationDate;
    /**
     * 最后更新日期
     */
    private Date lastUpdateDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDictId() {
        return dictId;
    }

    public void setDictId(Long dictId) {
        this.dictId = dictId;
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public Long getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Long displayOrder) {
        this.displayOrder = displayOrder;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    @Override
    public String toString() {
        return "CsDictItem{" +
        "id=" + id +
        ", dictId=" + dictId +
        ", dictCode=" + dictCode +
        ", dictName=" + dictName +
        ", itemCode=" + itemCode +
        ", itemValue=" + itemValue +
        ", displayOrder=" + displayOrder +
        ", status=" + status +
        ", remarks=" + remarks +
        ", creationDate=" + creationDate +
        ", lastUpdateDate=" + lastUpdateDate +
        "}";
    }
}
