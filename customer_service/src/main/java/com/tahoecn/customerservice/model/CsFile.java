package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

/**
 * <p>
 * 附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * [表单编码,BX2018……]
     */
    private String formGenerateId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件类型 csv excel等等
     */
    private String fileType;
    /**
     * 文件服务器端路径
     */
    private String serverPath;
    /**
     * 文件客户端路径
     */
    private String clientPath;
    /**
     * 是否删除 1是，-1否
     */
    private String status;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFormGenerateId() {
        return formGenerateId;
    }

    public void setFormGenerateId(String formGenerateId) {
        this.formGenerateId = formGenerateId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getServerPath() {
        return serverPath;
    }

    public void setServerPath(String serverPath) {
        this.serverPath = serverPath;
    }

    public String getClientPath() {
        return clientPath;
    }

    public void setClientPath(String clientPath) {
        this.clientPath = clientPath;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "CsFile{" +
        "id=" + id +
        ", formGenerateId=" + formGenerateId +
        ", fileName=" + fileName +
        ", fileType=" + fileType +
        ", serverPath=" + serverPath +
        ", clientPath=" + clientPath +
        ", status=" + status +
        "}";
    }
}
