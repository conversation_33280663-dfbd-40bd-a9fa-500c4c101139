package com.tahoecn.customerservice.model;

import java.io.Serializable;

/**
 * <p>
 * 物业工单日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsFormEstateInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 表单ID
     */
    private Long formInstId;
    /**
     * 物业工单状态编码
     */
    private Long formStatusCode;
    /**
     * 物业工单状态名称
     */
    private String formStatusName;
    /**
     * 备注
     */
    private String remark;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFormInstId() {
        return formInstId;
    }

    public void setFormInstId(Long formInstId) {
        this.formInstId = formInstId;
    }

    public Long getFormStatusCode() {
        return formStatusCode;
    }

    public void setFormStatusCode(Long formStatusCode) {
        this.formStatusCode = formStatusCode;
    }

    public String getFormStatusName() {
        return formStatusName;
    }

    public void setFormStatusName(String formStatusName) {
        this.formStatusName = formStatusName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "CsFormEstateInfo{" +
        "id=" + id +
        ", formInstId=" + formInstId +
        ", formStatusCode=" + formStatusCode +
        ", formStatusName=" + formStatusName +
        ", remark=" + remark +
        "}";
    }
}
