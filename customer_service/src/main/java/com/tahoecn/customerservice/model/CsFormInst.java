package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableId;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-24
 */
public class CsFormInst implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工单编号
     */
    private String formNo;
    /**
     * 部门类型编码
     */
    private String deptCode;
    /**
     * 部门类型编码
     */
    private String deptName;
    /**
     * 登记类型编码
     */
    private String registCode;
    /**
     * 登记类型编码
     */
    private String registName;
    /**
     * 流程类别编码
     */
    private String processCode;
    /**
     * 流程类别编码
     */
    private String processName;
    /**
     * 一级分类编码
     */
    private String firstSortCode;
    /**
     * 一级分类名称
     */
    private String firstSortName;
    /**
     * 二级分类编码
     */
    private String secSortCode;
    /**
     * 二级分类名称
     */
    private String secSortName;
    /**
     * 三级分类编码
     */
    private String thirdSortCode;
    /**
     * 三级分类名称
     */
    private String thirdSortName;
    /**
     * 四级分类编码
     */
    private String fourthSortCode;
    /**
     * 四级分类名称
     */
    private String fourthSortName;
    /**
     * 问题严重级别编码
     */
    private String problemLevelCode;
    /**
     * 问题严重级别名称
     */
    private String problemLevelName;
    /**
     * 部位编码
     */
    private String problemPositionCode;
    /**
     * 部位名称
     */
    private String problemPositionName;
    /**
     * 报修分类编码
     */
    private String revisionClassificationCode;
    /**
     * 报修分类名称
     */
    private String revisionClassificationName;
    /**
     * 装修阶段编码
     */
    private String decorationStageCode;
    /**
     * 装修阶段名称
     */
    private String decorationStageName;
    /**
     * 维保期编码
     */
    private String maintenancePeriodCode;
    /**
     * 维保期名称
     */
    private String maintenancePeriodName;
    /**
     * 内部责任部门编码
     */
    private String interResDepartmentCode;
    /**
     * 内部责任部门名称
     */
    private String interResDepartmentName;
    /**
     * 业主名称
     */
    private String ownerName;
    /**
     * 业主类型编码1：个人2：单位
     */
    private Integer ownerType;
    /**
     * 其他版块会员-1：否，1：是
     */
    private Integer ortherMember;
    /**
     * 特殊客户-1：否，1：是
     */
    private Integer specialUser;
    /**
     * 移动电话
     */
    private String mobile;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 证件类型编码
     */
    private String idCode;
    /**
     * 证件类型名称
     */
    private String idName;
    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 出生日期
     */
    private Date birthDate;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 爱好
     */
    private String hobby;
    /**
     * 传真电话
     */
    private String faxPhone;
    /**
     * 联系地址
     */
    private String contactAddress;
    /**
     * 固定电话
     */
    private String fixedTelephone;
    /**
     * 电子邮件
     */
    private String eMail;
    /**
     * 邮政编码
     */
    private String postalCode;
    /**
     * 管家姓名
     */
    private String housekeeperName;
    /**
     * 管家电话
     */
    private String housekeeperTel;
    /**
     * 备注
     */
    private String remark;
    /**
     * 常见问题编码
     */
    private String commonProblemCode;
    /**
     * 常见问题名称
     */
    private String commonProblemName;
    /**
     * 客户需求
     */
    private String customerDemand;
    /**
     * 房屋编号
     */
    private String houseNo;
    /**
     * 房屋名称
     */
    private String houseName;
    /**
     * 区域编码
     */
    private String regionCode;
    /**
     * 区域
     */
    private String region;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 城市
     */
    private String city;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 项目
     */
    private String project;
    /**
     * 楼栋
     */
    private String buildingNo;
    /**
     * 单元号
     */
    private String buildingUnit;
    /**
     * 房间号
     */
    private String roomNo;
    /**
     * 使用性质编码
     */
    private String usePropertyCode;
    /**
     * 使用性质名称
     */
    private String usePropertyName;
    /**
     * 合同交房时间
     */
    private Date contractDeliveryTime;
    /**
     * 签约时间
     */
    private Date signingTime;
    /**
     * 预计脱保时间
     */
    private Date estimatedReleaseTime;
    /**
     * 集中交房时间from
     */
    private Date focusDeliveryTimeFrom;
    /**
     * 集中交房时间to
     */
    private Date focusDeliveryTimeTo;
    /**
     * 交付状态1:已交付 -1未交付
     */
    private String deliveryState;
    /**
     * 是否精装1:是 -1：否
     */
    private String hardcoverState;
    /**
     * 实际交房时间
     */
    private Date actualDeliverTime;
    /**
     * 入住时间
     */
    private Date checkInTime;
    /**
     * 分派人
     */
    private String assignId;
    /**
     * 分派人
     */
    private String assignName;
    /**
     * 分派时间
     */
    private Date assignDate;
    /**
     * 创建日期
     */
    private Date creationDate;
    /**
     * 当前处理人
     */
    private String curAssigneeId;
    /**
     * 当前处理人
     */
    private String curAssigneeName;
    /**
     * 升级级别
     */
    private Long upgradeLevel;
    /**
     * 业务步骤
     */
    private String processStateCode;
    /**
     * 业务步骤
     */
    private String processStateName;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 升级原因
     */
    private String updradeReason;
    /**
     * 业主ID
     */
    private String ownerId;
    /**
     * 满意度名称
     */
    private String satisfactionCode;
    private String satisfactionName;
    /**
     * 升级标识1：是
     */
    private Long upgradeFlag;
    /**
     * 返工标识1:是
     */
    private Long reworkFlag;
    /**
     * 退回标识1:是
     */
    private Long rejectFlag;
    /**
     * 性别编码
     */
    private String genderCode;
    /**
     * 性别名称
     */
    private String genderName;
    /**
     * 锁用户
     */
    private String lockUserId;
    /**
     * 锁时间
     */
    private Date lockTime;
    /**
     * 房屋ID
     */
    private String houseInfoId;
    private String createUserId;
    private String createUserName;
    /**
     * 处理记录
     */
    private String handleRecord;
    private String formGenerateId;
    /**
     * 报事渠道编码
     */
    private String reportChannelCode;
    /**
     * 报事渠道名称
     */
    private String reportChannelName;
    /**
     * 受理渠道编码
     */
    private String acceptChannelCode;
    /**
     * 受理渠道名称
     */
    private String acceptChannelName;
    private Date submitDate;
    /**
     * 主责任单位
     */
    private String mainResUnit;
    /**
     * 维修单位
     */
    private String repairUnit;
    /**
     * 是否启用第三方 -1否 1是
     */
    private String thirdPartyFlag;
    /**
     * 第三方名称
     */
    private String thirdPartyName;
    /**
     * 第三方原因
     */
    private String thirdPartyReason;

    /**
     * 变更记录
     */
    private String changeRecord;

    /**
     * 定时计算出的升级级别
     */
    private Long schedualUpgradeLevel;
    
    /**
     * 投诉标题
     */
    private String complaintHeadlines;

    /**
     * 物业工单编号
     * */
    private String wyFormNo;

    /**
     * 工单来源（仅用于外部系统）
     * */
    private String source;
    
    private String publicArea;
    
    
    //新增字段 4/11 联系电话
    private String contactTel;
    
    /**
     * 是否为业主报事 1：是
     */
    private String isOwner;
    
    /**
     * 千丁工单编号
     * */
    private String qdFormNo;
    /**
     * 千丁报事人id
     * */
    private String qdCreateUserId;
    /**
     * 千丁原因大类id
     * */
    private String qdProblemTypeId;
    /**
     * 千丁项目id
     * */
    private String qdRegionId;
    /**
     * 千丁项目名称
     * */
    private String qdRegionName;
    /**
     * 回调千丁返回信息
     * */
    private String qdBackMes;
    /**
     * openid
     * */
    private String openId;
    /**
     * 报事人
     * */
    private String formUserName;
    /**
     * 报事人电话
     * */
    private String formUserTel;
    /**
     * 是否抢单1是0否
     * */
    private String isGrab;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFormNo() {
        return formNo;
    }

    public void setFormNo(String formNo) {
        this.formNo = formNo;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getRegistCode() {
        return registCode;
    }

    public void setRegistCode(String registCode) {
        this.registCode = registCode;
    }

    public String getRegistName() {
        return registName;
    }

    public void setRegistName(String registName) {
        this.registName = registName;
    }

    public String getProcessCode() {
        return processCode;
    }

    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getFirstSortCode() {
        return firstSortCode;
    }

    public void setFirstSortCode(String firstSortCode) {
        this.firstSortCode = firstSortCode;
    }

    public String getFirstSortName() {
        return firstSortName;
    }

    public void setFirstSortName(String firstSortName) {
        this.firstSortName = firstSortName;
    }

    public String getSecSortCode() {
        return secSortCode;
    }

    public void setSecSortCode(String secSortCode) {
        this.secSortCode = secSortCode;
    }

    public String getSecSortName() {
        return secSortName;
    }

    public void setSecSortName(String secSortName) {
        this.secSortName = secSortName;
    }

    public String getThirdSortCode() {
        return thirdSortCode;
    }

    public void setThirdSortCode(String thirdSortCode) {
        this.thirdSortCode = thirdSortCode;
    }

    public String getThirdSortName() {
        return thirdSortName;
    }

    public void setThirdSortName(String thirdSortName) {
        this.thirdSortName = thirdSortName;
    }

    public String getFourthSortCode() {
        return fourthSortCode;
    }

    public void setFourthSortCode(String fourthSortCode) {
        this.fourthSortCode = fourthSortCode;
    }

    public String getFourthSortName() {
        return fourthSortName;
    }

    public void setFourthSortName(String fourthSortName) {
        this.fourthSortName = fourthSortName;
    }

    public String getProblemLevelCode() {
        return problemLevelCode;
    }

    public void setProblemLevelCode(String problemLevelCode) {
        this.problemLevelCode = problemLevelCode;
    }

    public String getProblemLevelName() {
        return problemLevelName;
    }

    public void setProblemLevelName(String problemLevelName) {
        this.problemLevelName = problemLevelName;
    }

    public String getProblemPositionCode() {
        return problemPositionCode;
    }

    public void setProblemPositionCode(String problemPositionCode) {
        this.problemPositionCode = problemPositionCode;
    }

    public String getProblemPositionName() {
        return problemPositionName;
    }

    public void setProblemPositionName(String problemPositionName) {
        this.problemPositionName = problemPositionName;
    }

    public String getRevisionClassificationCode() {
        return revisionClassificationCode;
    }

    public void setRevisionClassificationCode(String revisionClassificationCode) {
        this.revisionClassificationCode = revisionClassificationCode;
    }

    public String getRevisionClassificationName() {
        return revisionClassificationName;
    }

    public void setRevisionClassificationName(String revisionClassificationName) {
        this.revisionClassificationName = revisionClassificationName;
    }

    public String getDecorationStageCode() {
        return decorationStageCode;
    }

    public void setDecorationStageCode(String decorationStageCode) {
        this.decorationStageCode = decorationStageCode;
    }

    public String getDecorationStageName() {
        return decorationStageName;
    }

    public void setDecorationStageName(String decorationStageName) {
        this.decorationStageName = decorationStageName;
    }

    public String getMaintenancePeriodCode() {
        return maintenancePeriodCode;
    }

    public void setMaintenancePeriodCode(String maintenancePeriodCode) {
        this.maintenancePeriodCode = maintenancePeriodCode;
    }

    public String getMaintenancePeriodName() {
        return maintenancePeriodName;
    }

    public void setMaintenancePeriodName(String maintenancePeriodName) {
        this.maintenancePeriodName = maintenancePeriodName;
    }

    public String getInterResDepartmentCode() {
        return interResDepartmentCode;
    }

    public void setInterResDepartmentCode(String interResDepartmentCode) {
        this.interResDepartmentCode = interResDepartmentCode;
    }

    public String getInterResDepartmentName() {
        return interResDepartmentName;
    }

    public void setInterResDepartmentName(String interResDepartmentName) {
        this.interResDepartmentName = interResDepartmentName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getOrtherMember() {
        return ortherMember;
    }

    public void setOrtherMember(Integer ortherMember) {
        this.ortherMember = ortherMember;
    }

    public Integer getSpecialUser() {
        return specialUser;
    }

    public void setSpecialUser(Integer specialUser) {
        this.specialUser = specialUser;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getIdName() {
        return idName;
    }

    public void setIdName(String idName) {
        this.idName = idName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getHobby() {
        return hobby;
    }

    public void setHobby(String hobby) {
        this.hobby = hobby;
    }

    public String getFaxPhone() {
        return faxPhone;
    }

    public void setFaxPhone(String faxPhone) {
        this.faxPhone = faxPhone;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getFixedTelephone() {
        return fixedTelephone;
    }

    public void setFixedTelephone(String fixedTelephone) {
        this.fixedTelephone = fixedTelephone;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getHousekeeperName() {
        return housekeeperName;
    }

    public void setHousekeeperName(String housekeeperName) {
        this.housekeeperName = housekeeperName;
    }

    public String getHousekeeperTel() {
        return housekeeperTel;
    }

    public void setHousekeeperTel(String housekeeperTel) {
        this.housekeeperTel = housekeeperTel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCommonProblemCode() {
        return commonProblemCode;
    }

    public void setCommonProblemCode(String commonProblemCode) {
        this.commonProblemCode = commonProblemCode;
    }

    public String getCommonProblemName() {
        return commonProblemName;
    }

    public void setCommonProblemName(String commonProblemName) {
        this.commonProblemName = commonProblemName;
    }

    public String getCustomerDemand() {
        return customerDemand;
    }

    public void setCustomerDemand(String customerDemand) {
        this.customerDemand = customerDemand;
    }

    public String getHouseNo() {
        return houseNo;
    }

    public void setHouseNo(String houseNo) {
        this.houseNo = houseNo;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    public String getBuildingUnit() {
        return buildingUnit;
    }

    public void setBuildingUnit(String buildingUnit) {
        this.buildingUnit = buildingUnit;
    }

    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public String getUsePropertyCode() {
        return usePropertyCode;
    }

    public void setUsePropertyCode(String usePropertyCode) {
        this.usePropertyCode = usePropertyCode;
    }

    public String getUsePropertyName() {
        return usePropertyName;
    }

    public void setUsePropertyName(String usePropertyName) {
        this.usePropertyName = usePropertyName;
    }

    public Date getContractDeliveryTime() {
        return contractDeliveryTime;
    }

    public void setContractDeliveryTime(Date contractDeliveryTime) {
        this.contractDeliveryTime = contractDeliveryTime;
    }

    public Date getSigningTime() {
        return signingTime;
    }

    public void setSigningTime(Date signingTime) {
        this.signingTime = signingTime;
    }

    public Date getEstimatedReleaseTime() {
        return estimatedReleaseTime;
    }

    public void setEstimatedReleaseTime(Date estimatedReleaseTime) {
        this.estimatedReleaseTime = estimatedReleaseTime;
    }

    public Date getFocusDeliveryTimeFrom() {
        return focusDeliveryTimeFrom;
    }

    public void setFocusDeliveryTimeFrom(Date focusDeliveryTimeFrom) {
        this.focusDeliveryTimeFrom = focusDeliveryTimeFrom;
    }

    public Date getFocusDeliveryTimeTo() {
        return focusDeliveryTimeTo;
    }

    public void setFocusDeliveryTimeTo(Date focusDeliveryTimeTo) {
        this.focusDeliveryTimeTo = focusDeliveryTimeTo;
    }

    public String getDeliveryState() {
        return deliveryState;
    }

    public void setDeliveryState(String deliveryState) {
        this.deliveryState = deliveryState;
    }

    public String getHardcoverState() {
        return hardcoverState;
    }

    public void setHardcoverState(String hardcoverState) {
        this.hardcoverState = hardcoverState;
    }

    public Date getActualDeliverTime() {
        return actualDeliverTime;
    }

    public void setActualDeliverTime(Date actualDeliverTime) {
        this.actualDeliverTime = actualDeliverTime;
    }

    public Date getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
    }

    public String getAssignId() {
        return assignId;
    }

    public void setAssignId(String assignId) {
        this.assignId = assignId;
    }

    public String getAssignName() {
        return assignName;
    }

    public void setAssignName(String assignName) {
        this.assignName = assignName;
    }

    public Date getAssignDate() {
        return assignDate;
    }

    public void setAssignDate(Date assignDate) {
        this.assignDate = assignDate;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getCurAssigneeId() {
        return curAssigneeId;
    }

    public void setCurAssigneeId(String curAssigneeId) {
        this.curAssigneeId = curAssigneeId;
    }

    public String getCurAssigneeName() {
        return curAssigneeName;
    }

    public void setCurAssigneeName(String curAssigneeName) {
        this.curAssigneeName = curAssigneeName;
    }

    public Long getUpgradeLevel() {
        return upgradeLevel;
    }

    public void setUpgradeLevel(Long upgradeLevel) {
        this.upgradeLevel = upgradeLevel;
    }

    public String getProcessStateCode() {
        return processStateCode;
    }

    public void setProcessStateCode(String processStateCode) {
        this.processStateCode = processStateCode;
    }

    public String getProcessStateName() {
        return processStateName;
    }

    public void setProcessStateName(String processStateName) {
        this.processStateName = processStateName;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getUpdradeReason() {
        return updradeReason;
    }

    public void setUpdradeReason(String updradeReason) {
        this.updradeReason = updradeReason;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getSatisfactionCode() {
        return satisfactionCode;
    }

    public void setSatisfactionCode(String satisfactionCode) {
        this.satisfactionCode = satisfactionCode;
    }

    public String getSatisfactionName() {
        return satisfactionName;
    }

    public void setSatisfactionName(String satisfactionName) {
        this.satisfactionName = satisfactionName;
    }

    public Long getUpgradeFlag() {
        return upgradeFlag;
    }

    public void setUpgradeFlag(Long upgradeFlag) {
        this.upgradeFlag = upgradeFlag;
    }

    public Long getReworkFlag() {
        return reworkFlag;
    }

    public void setReworkFlag(Long reworkFlag) {
        this.reworkFlag = reworkFlag;
    }

    public Long getRejectFlag() {
        return rejectFlag;
    }

    public void setRejectFlag(Long rejectFlag) {
        this.rejectFlag = rejectFlag;
    }

    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getLockUserId() {
        return lockUserId;
    }

    public void setLockUserId(String lockUserId) {
        this.lockUserId = lockUserId;
    }

    public Date getLockTime() {
        return lockTime;
    }

    public void setLockTime(Date lockTime) {
        this.lockTime = lockTime;
    }

    public String getHouseInfoId() {
        return houseInfoId;
    }

    public void setHouseInfoId(String houseInfoId) {
        this.houseInfoId = houseInfoId;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getHandleRecord() {
        return handleRecord;
    }

    public void setHandleRecord(String handleRecord) {
        this.handleRecord = handleRecord;
    }

    public String getFormGenerateId() {
        return formGenerateId;
    }

    public void setFormGenerateId(String formGenerateId) {
        this.formGenerateId = formGenerateId;
    }

    public String getReportChannelCode() {
        return reportChannelCode;
    }

    public void setReportChannelCode(String reportChannelCode) {
        this.reportChannelCode = reportChannelCode;
    }

    public String getReportChannelName() {
        return reportChannelName;
    }

    public void setReportChannelName(String reportChannelName) {
        this.reportChannelName = reportChannelName;
    }

    public String getAcceptChannelCode() {
        return acceptChannelCode;
    }

    public void setAcceptChannelCode(String acceptChannelCode) {
        this.acceptChannelCode = acceptChannelCode;
    }

    public String getAcceptChannelName() {
        return acceptChannelName;
    }

    public void setAcceptChannelName(String acceptChannelName) {
        this.acceptChannelName = acceptChannelName;
    }

    public Date getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
    }

    public String getMainResUnit() {
        return mainResUnit;
    }

    public void setMainResUnit(String mainResUnit) {
        this.mainResUnit = mainResUnit;
    }

    public String getRepairUnit() {
        return repairUnit;
    }

    public void setRepairUnit(String repairUnit) {
        this.repairUnit = repairUnit;
    }

    public String getThirdPartyFlag() {
        return thirdPartyFlag;
    }

    public void setThirdPartyFlag(String thirdPartyFlag) {
        this.thirdPartyFlag = thirdPartyFlag;
    }

    public String getThirdPartyName() {
        return thirdPartyName;
    }

    public void setThirdPartyName(String thirdPartyName) {
        this.thirdPartyName = thirdPartyName;
    }

    public String getThirdPartyReason() {
        return thirdPartyReason;
    }

    public void setThirdPartyReason(String thirdPartyReason) {
        this.thirdPartyReason = thirdPartyReason;
    }

    public String getChangeRecord() {
        return changeRecord;
    }

    public void setChangeRecord(String changeRecord) {
        this.changeRecord = changeRecord;
    }

    public Long getSchedualUpgradeLevel() {
        return schedualUpgradeLevel;
    }

    public void setSchedualUpgradeLevel(Long schedualUpgradeLevel) {
        this.schedualUpgradeLevel = schedualUpgradeLevel;
    }

    public String getWyFormNo() {
        return wyFormNo;
    }

    public void setWyFormNo(String wyFormNo) {
        this.wyFormNo = wyFormNo;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "CsFormInst{" +
                "id=" + id +
                ", formNo=" + formNo +
                ", deptCode=" + deptCode +
                ", deptName=" + deptName +
                ", registCode=" + registCode +
                ", registName=" + registName +
                ", processCode=" + processCode +
                ", processName=" + processName +
                ", firstSortCode=" + firstSortCode +
                ", firstSortName=" + firstSortName +
                ", secSortCode=" + secSortCode +
                ", secSortName=" + secSortName +
                ", thirdSortCode=" + thirdSortCode +
                ", thirdSortName=" + thirdSortName +
                ", fourthSortCode=" + fourthSortCode +
                ", fourthSortName=" + fourthSortName +
                ", problemLevelCode=" + problemLevelCode +
                ", problemLevelName=" + problemLevelName +
                ", problemPositionCode=" + problemPositionCode +
                ", problemPositionName=" + problemPositionName +
                ", revisionClassificationCode=" + revisionClassificationCode +
                ", revisionClassificationName=" + revisionClassificationName +
                ", decorationStageCode=" + decorationStageCode +
                ", decorationStageName=" + decorationStageName +
                ", maintenancePeriodCode=" + maintenancePeriodCode +
                ", maintenancePeriodName=" + maintenancePeriodName +
                ", interResDepartmentCode=" + interResDepartmentCode +
                ", interResDepartmentName=" + interResDepartmentName +
                ", ownerName=" + ownerName +
                ", ownerType=" + ownerType +
                ", ortherMember=" + ortherMember +
                ", specialUser=" + specialUser +
                ", mobile=" + mobile +
                ", nationality=" + nationality +
                ", idCode=" + idCode +
                ", idName=" + idName +
                ", idNo=" + idNo +
                ", birthDate=" + birthDate +
                ", workUnit=" + workUnit +
                ", occupation=" + occupation +
                ", hobby=" + hobby +
                ", faxPhone=" + faxPhone +
                ", contactAddress=" + contactAddress +
                ", fixedTelephone=" + fixedTelephone +
                ", eMail=" + eMail +
                ", postalCode=" + postalCode +
                ", housekeeperName=" + housekeeperName +
                ", housekeeperTel=" + housekeeperTel +
                ", remark=" + remark +
                ", commonProblemCode=" + commonProblemCode +
                ", commonProblemName=" + commonProblemName +
                ", customerDemand=" + customerDemand +
                ", houseNo=" + houseNo +
                ", houseName=" + houseName +
                ", regionCode=" + regionCode +
                ", region=" + region +
                ", cityCode=" + cityCode +
                ", city=" + city +
                ", projectCode=" + projectCode +
                ", project=" + project +
                ", buildingNo=" + buildingNo +
                ", buildingUnit=" + buildingUnit +
                ", roomNo=" + roomNo +
                ", usePropertyCode=" + usePropertyCode +
                ", usePropertyName=" + usePropertyName +
                ", contractDeliveryTime=" + contractDeliveryTime +
                ", signingTime=" + signingTime +
                ", estimatedReleaseTime=" + estimatedReleaseTime +
                ", focusDeliveryTimeFrom=" + focusDeliveryTimeFrom +
                ", focusDeliveryTimeTo=" + focusDeliveryTimeTo +
                ", deliveryState=" + deliveryState +
                ", hardcoverState=" + hardcoverState +
                ", actualDeliverTime=" + actualDeliverTime +
                ", checkInTime=" + checkInTime +
                ", assignId=" + assignId +
                ", assignName=" + assignName +
                ", assignDate=" + assignDate +
                ", creationDate=" + creationDate +
                ", curAssigneeId=" + curAssigneeId +
                ", curAssigneeName=" + curAssigneeName +
                ", upgradeLevel=" + upgradeLevel +
                ", processStateCode=" + processStateCode +
                ", processStateName=" + processStateName +
                ", lastUpdateDate=" + lastUpdateDate +
                ", updradeReason=" + updradeReason +
                ", ownerId=" + ownerId +
                ", satisfactionCode=" + satisfactionCode +
                ", satisfactionName=" + satisfactionName +
                ", upgradeFlag=" + upgradeFlag +
                ", reworkFlag=" + reworkFlag +
                ", rejectFlag=" + rejectFlag +
                ", genderCode=" + genderCode +
                ", genderName=" + genderName +
                ", lockUserId=" + lockUserId +
                ", lockTime=" + lockTime +
                ", houseInfoId=" + houseInfoId +
                ", createUserId=" + createUserId +
                ", createUserName=" + createUserName +
                ", handleRecord=" + handleRecord +
                ", formGenerateId=" + formGenerateId +
                ", reportChannelCode=" + reportChannelCode +
                ", reportChannelName=" + reportChannelName +
                ", acceptChannelCode=" + acceptChannelCode +
                ", acceptChannelName=" + acceptChannelName +
                ", submitDate=" + submitDate +
                ", mainResUnit=" + mainResUnit +
                ", repairUnit=" + repairUnit +
                ", thirdPartyFlag=" + thirdPartyFlag +
                ", thirdPartyName=" + thirdPartyName +
                ", thirdPartyReason=" + thirdPartyReason +
                ", changeRecord=" + changeRecord +
                ", schedualUpgradeLevel=" + schedualUpgradeLevel +
                ", wyFormNo=" + wyFormNo +
                ", source=" + source +
                ", isOwner = " + isOwner +
                ", qdFormNo = " + qdFormNo +
                ", qdCreateUserId = " + qdCreateUserId +
                ", qdProblemTypeId = " + qdProblemTypeId +
                ", qdRegionId = " + qdRegionId +
                ", qdRegionName = " + qdRegionName +
                ", qdBackMes = " + qdBackMes +
                ", openId = " + openId +
                ", formUserName = " + formUserName +
                ", formUserTel = " + formUserTel +
                ", isGrab = " + isGrab +
                "}";
    }
	public String getComplaintHeadlines() {
		return complaintHeadlines;
	}

	public void setComplaintHeadlines(String complaintHeadlines) {
		this.complaintHeadlines = complaintHeadlines;
	}

	public String getPublicArea() {
		return publicArea;
	}

	public void setPublicArea(String publicArea) {
		this.publicArea = publicArea;
	}

	public String getContactTel() {
		return contactTel;
	}

	public void setContactTel(String contactTel) {
		this.contactTel = contactTel;
	}
	
	public String getIsOwner() {
		return isOwner;
	}

	public void setIsOwner(String isOwner) {
		this.isOwner = isOwner;
	}

	public String getQdFormNo() {
		return qdFormNo;
	}

	public void setQdFormNo(String qdFormNo) {
		this.qdFormNo = qdFormNo;
	}

	public String getQdCreateUserId() {
		return qdCreateUserId;
	}

	public void setQdCreateUserId(String qdCreateUserId) {
		this.qdCreateUserId = qdCreateUserId;
	}

	public String getQdProblemTypeId() {
		return qdProblemTypeId;
	}

	public void setQdProblemTypeId(String qdProblemTypeId) {
		this.qdProblemTypeId = qdProblemTypeId;
	}

	public String getQdRegionId() {
		return qdRegionId;
	}

	public void setQdRegionId(String qdRegionId) {
		this.qdRegionId = qdRegionId;
	}

	public String getQdRegionName() {
		return qdRegionName;
	}

	public void setQdRegionName(String qdRegionName) {
		this.qdRegionName = qdRegionName;
	}

	public String getQdBackMes() {
		return qdBackMes;
	}

	public void setQdBackMes(String qdBackMes) {
		this.qdBackMes = qdBackMes;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getFormUserName() {
		return formUserName;
	}

	public void setFormUserName(String formUserName) {
		this.formUserName = formUserName;
	}

	public String getFormUserTel() {
		return formUserTel;
	}

	public void setFormUserTel(String formUserTel) {
		this.formUserTel = formUserTel;
	}

	public String getIsGrab() {
		return isGrab;
	}

	public void setIsGrab(String isGrab) {
		this.isGrab = isGrab;
	}
	
}
