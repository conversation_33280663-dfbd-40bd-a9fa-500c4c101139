package com.tahoecn.customerservice.model;

import java.io.Serializable;

/**
 * <p>
 * 抢单配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-17
 */
public class CsGrab implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 一级分类编码
     */
    private String firstSortCode;
    /**
     * 二级分类编码
     */
    private String secSortCode;
    /**
     * 三级分类编码
     */
    private String thirdSortCode;
    /**
     * 四级分类编码
     */
    private String fourthSortCode;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getFirstSortCode() {
        return firstSortCode;
    }

    public void setFirstSortCode(String firstSortCode) {
        this.firstSortCode = firstSortCode;
    }

    public String getSecSortCode() {
        return secSortCode;
    }

    public void setSecSortCode(String secSortCode) {
        this.secSortCode = secSortCode;
    }

    public String getThirdSortCode() {
        return thirdSortCode;
    }

    public void setThirdSortCode(String thirdSortCode) {
        this.thirdSortCode = thirdSortCode;
    }

    public String getFourthSortCode() {
        return fourthSortCode;
    }

    public void setFourthSortCode(String fourthSortCode) {
        this.fourthSortCode = fourthSortCode;
    }

    @Override
    public String toString() {
        return "CsGrab{" +
        "id=" + id +
        ", projectCode=" + projectCode +
        ", firstSortCode=" + firstSortCode +
        ", secSortCode=" + secSortCode +
        ", thirdSortCode=" + thirdSortCode +
        ", fourthSortCode=" + fourthSortCode +
        "}";
    }
}
