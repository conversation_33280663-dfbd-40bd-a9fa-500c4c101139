package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableId;
import java.io.Serializable;

/**
 * <p>
 * 房屋信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsHouseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 房屋ID
     */
    private String houseId;
    /**
     * 房屋名称
     */
    private String houseName;
    
    
    /*=========================导出需要字段，顺序不可更改 start==========================*/
    /**
     * 房屋编号
     */
    @Excel(name = "房屋编号")
    private String houseNum;
    /**
     * 区域
     */
    @Excel(name = "区域")
    private String area;
    /**
     * 城市
     */
    @Excel(name = "城市")
    private String city;
    /**
     * 项目
     */
    @Excel(name = "项目")
    private String project;
    /**
     * 楼栋
     */
    @Excel(name = "楼栋")
    private String building;
    /**
     * 单元号
     */
    @Excel(name = "单元号")
    private String unit;
    /**
     * 房间号
     */
    @Excel(name = "房间号")
    private String roomNum;
    /**
     * 使用性质
     */
    @Excel(name = "使用性质")
    private String useProperty;
    /**
     * 交付状态
     */
    @Excel(name = "交付状态")
    private Integer deliveryStatus;
    /**
     * 合同交房时间
     */
    @Excel(name = "合同交房时间")
    private String deliveryDate;
    /**
     * 签约时间
     */
    @Excel(name = "签约时间")
    private String signDate;
    /**
     * 预计脱保时间
     */
    @Excel(name = "预计脱保时间")
    private Date offAidDate;
    /**
     * 精装情况
     */
    @Excel(name = "是否精装")
    private String fitment;
    /**
     * 集中交房时间从
     */
    @Excel(name = "集中交房开始时间")
    private Date focusStartDate;
    /**
     * 集中交房时间到
     */
    @Excel(name = "集中交房结束时间")
    private Date focusEndDate;
    /**
     * 实际交房时间
     */
    @Excel(name = "实际交房时间")
    private Date actualDeliveryDate;
    /**
     * 入住时间
     */
    @Excel(name = "入住时间")
    private Date stayTime;
    /*=========================导出需要字段，顺序不可更改 end==========================*/
    
    
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 来源：1.物业2.明源
     */
    private Integer source;
    /**
     * 取证时间
     */
    private String obtainTime;
    /**
     * 管家姓名
     */
    private String stewardName;
    /**
     * 管家电话
     */
    private String stewardTelephone;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getHouseNum() {
        return houseNum;
    }

    public void setHouseNum(String houseNum) {
        this.houseNum = houseNum;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getUseProperty() {
        return useProperty;
    }

    public void setUseProperty(String useProperty) {
        this.useProperty = useProperty;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getSignDate() {
        return signDate;
    }

    public void setSignDate(String signDate) {
        this.signDate = signDate;
    }

    public Date getFocusStartDate() {
        return focusStartDate;
    }

    public void setFocusStartDate(Date focusStartDate) {
        this.focusStartDate = focusStartDate;
    }

    public Date getFocusEndDate() {
        return focusEndDate;
    }

    public void setFocusEndDate(Date focusEndDate) {
        this.focusEndDate = focusEndDate;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getFitment() {
        return fitment;
    }

    public void setFitment(String fitment) {
        this.fitment = fitment;
    }

    public Date getActualDeliveryDate() {
        return actualDeliveryDate;
    }

    public void setActualDeliveryDate(Date actualDeliveryDate) {
        this.actualDeliveryDate = actualDeliveryDate;
    }

    public Date getStayTime() {
        return stayTime;
    }

    public void setStayTime(Date stayTime) {
        this.stayTime = stayTime;
    }

    public Date getOffAidDate() {
        return offAidDate;
    }

    public void setOffAidDate(Date offAidDate) {
        this.offAidDate = offAidDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public String getObtainTime() {
		return obtainTime;
	}

	public void setObtainTime(String obtainTime) {
		this.obtainTime = obtainTime;
	}

	public String getStewardName() {
		return stewardName;
	}

	public void setStewardName(String stewardName) {
		this.stewardName = stewardName;
	}

	public String getStewardTelephone() {
		return stewardTelephone;
	}

	public void setStewardTelephone(String stewardTelephone) {
		this.stewardTelephone = stewardTelephone;
	}

	@Override
    public String toString() {
        return "CsHouseInfo{" +
        "id=" + id +
        ", projectId=" + projectId +
        ", houseId=" + houseId +
        ", houseNum=" + houseNum +
        ", houseName=" + houseName +
        ", area=" + area +
        ", city=" + city +
        ", project=" + project +
        ", unit=" + unit +
        ", building=" + building +
        ", roomNum=" + roomNum +
        ", useProperty=" + useProperty +
        ", deliveryDate=" + deliveryDate +
        ", signDate=" + signDate +
        ", focusStartDate=" + focusStartDate +
        ", focusEndDate=" + focusEndDate +
        ", deliveryStatus=" + deliveryStatus +
        ", fitment=" + fitment +
        ", actualDeliveryDate=" + actualDeliveryDate +
        ", stayTime=" + stayTime +
        ", offAidDate=" + offAidDate +
        ", createDate=" + createDate +
        ", updateDate=" + updateDate +
        ", source=" + source +
        ", obtainTime=" + obtainTime +
        "}";
    }
}
