package com.tahoecn.customerservice.model;

import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * <p>
 * 标签
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-09
 */
public class CsLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 父id
     */
    private String fatherId;
    /**
     * 父id
     */
    private String fatherName;
    /**
     * 标签名称
     */
    private String itemName;
    /**
     * 状态1启用0不启用
     */
    private String state;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 所属层级
     */
    private int hierarchy;
    /**
     * 收集渠道
     */
    private String collection;
    /**
     * 分值
     */
    private String score;
    /**
     * 定义
     */
    private String definition;
    /**
     * 客户活动标志cust客户activity
     */
    private String type;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFatherId() {
        return fatherId;
    }

    public void setFatherId(String fatherId) {
        this.fatherId = fatherId;
    }

    public String getFatherName() {
		return fatherName;
	}

	public void setFatherName(String fatherName) {
		this.fatherName = fatherName;
	}

	public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public int getHierarchy() {
        return hierarchy;
    }

    public void setHierarchy(int hierarchy) {
        this.hierarchy = hierarchy;
    }

    public String getCollection() {
        return collection;
    }

    public void setCollection(String collection) {
        this.collection = collection;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getDefinition() {
        return definition;
    }

    public void setDefinition(String definition) {
        this.definition = definition;
    }

    public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
    public String toString() {
        return "CsLabel{" +
        "id=" + id +
        ", fatherId=" + fatherId +
        ", itemName=" + itemName +
        ", state=" + state +
        ", createDate=" + createDate +
        ", lastUpdateDate=" + lastUpdateDate +
        ", hierarchy=" + hierarchy +
        ", collection=" + collection +
        ", score=" + score +
        ", definition=" + definition +
        ", type=" + type +
        "}";
    }
}
