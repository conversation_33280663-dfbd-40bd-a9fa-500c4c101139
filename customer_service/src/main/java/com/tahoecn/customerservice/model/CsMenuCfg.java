package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 菜单URL配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsMenuCfg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 菜单编码
     */
    private String menuCode;
    /**
     * 菜单url
     */
    private String menuUrl;
    /**
     * 创建日期
     */
    private Date creationDate;

    private Integer orderNum;

    private String name;

    private String icoCode;
    
    /**
     * 菜单编码
     */
    private String menuPcode;

    public String getMenuPcode() {
		return menuPcode;
	}

	public void setMenuPcode(String menuPcode) {
		this.menuPcode = menuPcode;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getMenuUrl() {
        return menuUrl;
    }

    public void setMenuUrl(String menuUrl) {
        this.menuUrl = menuUrl;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcoCode() {
        return icoCode;
    }

    public void setIcoCode(String icoCode) {
        this.icoCode = icoCode;
    }
}
