package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 待办表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsProcessWorkitem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 表单id
     */
    private Long formInstId;
    /**
     * 审批状态(20:待办,30:已办)
     */
    private Long taskStatus;
    /**
     * 处理人
     */
    private String assignId;
    /**
     * 审批开始时间
     */
    private Date taskStartTime;
    /**
     * 审批结束时间
     */
    private Date taskEndTime;
    /**
     * 创建人
     */
    private String createByUserId;
    /**
     * 审批意见
     */
    private String comment;
    /**
     * 创建日期
     */
    private Date creationDate;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 处理人名称
     */
    private String assignName;
    /**
     * 业务步骤编码
     */
    private String processStateCode;
    /**
     * 业务步骤姓名
     */
    private String processStateName;
    /**
     * 业务步骤姓名
     */
    private String assignMobile;
    /**
     * app 处理记录
     */
    private String thPlatform;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFormInstId() {
        return formInstId;
    }

    public void setFormInstId(Long formInstId) {
        this.formInstId = formInstId;
    }

    public Long getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Long taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getAssignId() {
        return assignId;
    }

    public void setAssignId(String assignId) {
        this.assignId = assignId;
    }

    public Date getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(Date taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public Date getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(Date taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public String getCreateByUserId() {
        return createByUserId;
    }

    public void setCreateByUserId(String createByUserId) {
        this.createByUserId = createByUserId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getAssignName() {
        return assignName;
    }

    public void setAssignName(String assignName) {
        this.assignName = assignName;
    }

    public String getProcessStateCode() {
        return processStateCode;
    }

    public void setProcessStateCode(String processStateCode) {
        this.processStateCode = processStateCode;
    }

    public String getProcessStateName() {
        return processStateName;
    }

    public void setProcessStateName(String processStateName) {
        this.processStateName = processStateName;
    }

    public String getAssignMobile() {
        return assignMobile;
    }

    public void setAssignMobile(String assignMobile) {
        this.assignMobile = assignMobile;
    }

    @Override
    public String toString() {
        return "CsProcessWorkitem{" +
        "id=" + id +
        ", formInstId=" + formInstId +
        ", taskStatus=" + taskStatus +
        ", assignId=" + assignId +
        ", taskStartTime=" + taskStartTime +
        ", taskEndTime=" + taskEndTime +
        ", createByUserId=" + createByUserId +
        ", comment=" + comment +
        ", creationDate=" + creationDate +
        ", lastUpdateDate=" + lastUpdateDate +
        ", assignName=" + assignName +
        ", processStateCode=" + processStateCode +
        ", processStateName=" + processStateName +
        ", assignMobile=" + assignMobile +
        "}";
    }

	public String getThPlatform() {
		return thPlatform;
	}

	public void setThPlatform(String thPlatform) {
		this.thPlatform = thPlatform;
	}
}
