package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;
import com.baomidou.mybatisplus.annotations.TableId;
import java.io.Serializable;

/**
 * <p>
 * 初始化项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsProjectInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 区域编码
     */
    private String regionCode;
    /**
     * 区域
     */
    private String region;
    /**
     * 城市公司编码
     */
    private String cityCompanyCode;
    /**
     * 城市公司
     */
    private String cityCompany;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 城市
     */
    private String city;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 项目
     */
    private String project;
    /**
     * 项目来源
     */
    private String projectSource;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCityCompanyCode() {
        return cityCompanyCode;
    }

    public void setCityCompanyCode(String cityCompanyCode) {
        this.cityCompanyCode = cityCompanyCode;
    }

    public String getCityCompany() {
        return cityCompany;
    }

    public void setCityCompany(String cityCompany) {
        this.cityCompany = cityCompany;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getProjectSource() {
        return projectSource;
    }

    public void setProjectSource(String projectSource) {
        this.projectSource = projectSource;
    }

    @Override
    public String toString() {
        return "CsProjectInfo{" +
        "id=" + id +
        ", regionCode=" + regionCode +
        ", region=" + region +
        ", cityCompanyCode=" + cityCompanyCode +
        ", cityCompany=" + cityCompany +
        ", cityCode=" + cityCode +
        ", city=" + city +
        ", projectCode=" + projectCode +
        ", project=" + project +
        ", projectSource=" + projectSource +
        "}";
    }
}
