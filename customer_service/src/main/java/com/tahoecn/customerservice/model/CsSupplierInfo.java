package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 供方数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-18
 */
public class CsSupplierInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商联系人
     */
    private String supplierContactName;
    /**
     * 供应商联系人电话
     */
    private String supplierContactMobile;
    /**
     * 供应商分类
     */
    private String supplierClassification;
    /**
     * 区域（隶属多个区域用","分隔）
     */
    private String supplierAreas;
    /**
     * 创建日期
     */
    private Date creationDate;
    /**
     * 最后更新日期
     */
    private Date lastUpdateDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierContactName() {
        return supplierContactName;
    }

    public void setSupplierContactName(String supplierContactName) {
        this.supplierContactName = supplierContactName;
    }

    public String getSupplierContactMobile() {
        return supplierContactMobile;
    }

    public void setSupplierContactMobile(String supplierContactMobile) {
        this.supplierContactMobile = supplierContactMobile;
    }

    public String getSupplierClassification() {
        return supplierClassification;
    }

    public void setSupplierClassification(String supplierClassification) {
        this.supplierClassification = supplierClassification;
    }

    public String getSupplierAreas() {
        return supplierAreas;
    }

    public void setSupplierAreas(String supplierAreas) {
        this.supplierAreas = supplierAreas;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    @Override
    public String toString() {
        return "CsSupplierInfo{" +
        "id=" + id +
        ", supplierName=" + supplierName +
        ", supplierContactName=" + supplierContactName +
        ", supplierContactMobile=" + supplierContactMobile +
        ", supplierClassification=" + supplierClassification +
        ", supplierAreas=" + supplierAreas +
        ", creationDate=" + creationDate +
        ", lastUpdateDate=" + lastUpdateDate +
        "}";
    }
}
