package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public class CsSyncCust2house implements Serializable {

    private static final long serialVersionUID = 1L;

    private String houseCode;
    private String certificateNum;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 来源：1.物业2.明源
     */
    private Integer source;


    public String getHouseCode() {
        return houseCode;
    }

    public void setHouseCode(String houseCode) {
        this.houseCode = houseCode;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "CsSyncCust2house{" +
        "houseCode=" + houseCode +
        ", certificateNum=" + certificateNum +
        ", updateDate=" + updateDate +
        ", source=" + source +
        "}";
    }
}
