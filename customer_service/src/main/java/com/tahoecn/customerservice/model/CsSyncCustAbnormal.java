package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableId;
import java.io.Serializable;

/**
 * <p>
 * 客户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-08
 */
public class CsSyncCustAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客户ID
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 证件号码
     */
    private String certificateNum;
    /**
     * 证件名称
     */
    private String certificateName;
    /**
     * 固定电话
     */
    private String fixedTelephone;
    /**
     * 移动电话
     */
    private String telephone;
    /**
     * 传真电话
     */
    private String fax;
    /**
     * 联系地址
     */
    private String contactAddress;
    /**
     * 邮政编码
     */
    private String postcode;
    /**
     * 电子邮件
     */
    private String email;
    /**
     * 个人/单位
     */
    private String belong;
    /**
     * 性别
     */
    private String sex;
    /**
     * 国籍
     */
    private String national;
    /**
     * 出生年月日
     */
    private String birthday;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 职业
     */
    private String profession;
    /**
     * 兴趣爱好
     */
    private String hobbies;
    /**
     * 物业删除
     */
    private String isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 来源：1.物业2.明源
     */
    private Integer source;
    /**
     * 异常数据
     */
    private Integer abnormal;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getFixedTelephone() {
        return fixedTelephone;
    }

    public void setFixedTelephone(String fixedTelephone) {
        this.fixedTelephone = fixedTelephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBelong() {
        return belong;
    }

    public void setBelong(String belong) {
        this.belong = belong;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNational() {
        return national;
    }

    public void setNational(String national) {
        this.national = national;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getHobbies() {
        return hobbies;
    }

    public void setHobbies(String hobbies) {
        this.hobbies = hobbies;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getAbnormal() {
        return abnormal;
    }

    public void setAbnormal(Integer abnormal) {
        this.abnormal = abnormal;
    }

    @Override
    public String toString() {
        return "CsSyncCustAbnormal{" +
        "id=" + id +
        ", custId=" + custId +
        ", custName=" + custName +
        ", certificateNum=" + certificateNum +
        ", certificateName=" + certificateName +
        ", fixedTelephone=" + fixedTelephone +
        ", telephone=" + telephone +
        ", fax=" + fax +
        ", contactAddress=" + contactAddress +
        ", postcode=" + postcode +
        ", email=" + email +
        ", belong=" + belong +
        ", sex=" + sex +
        ", national=" + national +
        ", birthday=" + birthday +
        ", workUnit=" + workUnit +
        ", profession=" + profession +
        ", hobbies=" + hobbies +
        ", isDelete=" + isDelete +
        ", createDate=" + createDate +
        ", updateDate=" + updateDate +
        ", source=" + source +
        ", abnormal=" + abnormal +
        "}";
    }
}
