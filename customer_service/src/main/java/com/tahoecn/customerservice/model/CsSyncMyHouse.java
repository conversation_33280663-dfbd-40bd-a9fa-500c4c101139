package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.annotations.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-09
 */
public class CsSyncMyHouse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String projectId;
    private String houseCode;
    private String houseId;
    private String houseNum;
    private String roomCode;
    private String area;
    private String city;
    private String project;
    private String building;
    @TableField("Unit")
    private String Unit;
    private String roomNum;
    private String useProperty;
    private String fitment;
    private String updateDate;
    private String projid;
    private String obtainTime;
    @TableField("Status")
    private String Status;


    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getHouseCode() {
        return houseCode;
    }

    public void setHouseCode(String houseCode) {
        this.houseCode = houseCode;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getHouseNum() {
        return houseNum;
    }

    public void setHouseNum(String houseNum) {
        this.houseNum = houseNum;
    }

    public String getRoomCode() {
		return roomCode;
	}

	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}

	public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }

    public String getUnit() {
        return Unit;
    }

    public void setUnit(String Unit) {
        this.Unit = Unit;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getUseProperty() {
        return useProperty;
    }

    public void setUseProperty(String useProperty) {
        this.useProperty = useProperty;
    }

    public String getFitment() {
        return fitment;
    }

    public void setFitment(String fitment) {
        this.fitment = fitment;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getProjid() {
        return projid;
    }

    public void setProjid(String projid) {
        this.projid = projid;
    }

    public String getObtainTime() {
        return obtainTime;
    }

    public void setObtainTime(String obtainTime) {
        this.obtainTime = obtainTime;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    @Override
    public String toString() {
        return "CsSyncMyHouse{" +
        "projectId=" + projectId +
        ", houseCode=" + houseCode +
        ", houseId=" + houseId +
        ", houseNum=" + houseNum +
        ", area=" + area +
        ", city=" + city +
        ", project=" + project +
        ", building=" + building +
        ", Unit=" + Unit +
        ", roomNum=" + roomNum +
        ", useProperty=" + useProperty +
        ", fitment=" + fitment +
        ", updateDate=" + updateDate +
        ", projid=" + projid +
        ", obtainTime=" + obtainTime +
        ", Status=" + Status +
        "}";
    }
}
