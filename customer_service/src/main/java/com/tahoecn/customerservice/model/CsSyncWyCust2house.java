package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-08
 */
public class CsSyncWyCust2house implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("LiveID")
    private String LiveID;
    @TableField("RoomID")
    private String RoomID;
    @TableField("RoomSign")
    private String RoomSign;
    @TableField("CustID")
    private String CustID;
    @TableField("CustName")
    private String CustName;
    @TableField("PaperCode")
    private String PaperCode;
    @TableField("IsActive")
    private String IsActive;
    @TableField("ChargeTime")
    private String ChargeTime;


    public String getLiveID() {
        return LiveID;
    }

    public void setLiveID(String LiveID) {
        this.LiveID = LiveID;
    }

    public String getRoomID() {
        return RoomID;
    }

    public void setRoomID(String RoomID) {
        this.RoomID = RoomID;
    }

    public String getRoomSign() {
        return RoomSign;
    }

    public void setRoomSign(String RoomSign) {
        this.RoomSign = RoomSign;
    }

    public String getCustID() {
        return CustID;
    }

    public void setCustID(String CustID) {
        this.CustID = CustID;
    }

    public String getCustName() {
        return CustName;
    }

    public void setCustName(String CustName) {
        this.CustName = CustName;
    }

    public String getPaperCode() {
        return PaperCode;
    }

    public void setPaperCode(String PaperCode) {
        this.PaperCode = PaperCode;
    }

    public String getIsActive() {
        return IsActive;
    }

    public void setIsActive(String IsActive) {
        this.IsActive = IsActive;
    }

    public String getChargeTime() {
        return ChargeTime;
    }

    public void setChargeTime(String ChargeTime) {
        this.ChargeTime = ChargeTime;
    }

    @Override
    public String toString() {
        return "CsSyncWyCust2house{" +
        "LiveID=" + LiveID +
        ", RoomID=" + RoomID +
        ", RoomSign=" + RoomSign +
        ", CustID=" + CustID +
        ", CustName=" + CustName +
        ", PaperCode=" + PaperCode +
        ", IsActive=" + IsActive +
        ", ChargeTime=" + ChargeTime +
        "}";
    }
}
