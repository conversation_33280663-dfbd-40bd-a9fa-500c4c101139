package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-08
 */
public class CsSyncWyHouse implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("OrganName")
    private String OrganName;
    @TableField("City")
    private String City;
    @TableField("CommID")
    private String CommID;
    @TableField("CommName")
    private String CommName;
    @TableId("RoomID")
    private String RoomID;
    @TableField("RoomSign")
    private String RoomSign;
    @TableField("RoomName")
    private String RoomName;
    @TableField("UnitSNum")
    private String UnitSNum;
    @TableField("BuildName")
    private String BuildName;
    @TableField("BuildSNum")
    private String BuildSNum;
    @TableField("PropertyUses")
    private String PropertyUses;
    @TableField("ContSubDate")
    private String ContSubDate;
    @TableField("getHouseStartDate")
    private String getHouseStartDate;
    @TableField("getHouseEndDate")
    private String getHouseEndDate;
    @TableField("PayState")
    private String PayState;
    @TableField("BuildsRenovation")
    private String BuildsRenovation;
    @TableField("ActualSubDate")
    private String ActualSubDate;
    @TableField("StayTime")
    private String StayTime;
    @TableField("UpdateTime")
    private String UpdateTime;
    @TableField("IsDelete")
    private String IsDelete;
    @TableField("UserName")
    private String UserName;
    @TableField("MobileTel")
    private String MobileTel;


    public String getOrganName() {
        return OrganName;
    }

    public void setOrganName(String OrganName) {
        this.OrganName = OrganName;
    }

    public String getCity() {
        return City;
    }

    public void setCity(String City) {
        this.City = City;
    }

    public String getCommID() {
        return CommID;
    }

    public void setCommID(String CommID) {
        this.CommID = CommID;
    }

    public String getCommName() {
        return CommName;
    }

    public void setCommName(String CommName) {
        this.CommName = CommName;
    }

    public String getRoomID() {
        return RoomID;
    }

    public void setRoomID(String RoomID) {
        this.RoomID = RoomID;
    }

    public String getRoomSign() {
        return RoomSign;
    }

    public void setRoomSign(String RoomSign) {
        this.RoomSign = RoomSign;
    }

    public String getRoomName() {
        return RoomName;
    }

    public void setRoomName(String RoomName) {
        this.RoomName = RoomName;
    }

    public String getUnitSNum() {
        return UnitSNum;
    }

    public void setUnitSNum(String UnitSNum) {
        this.UnitSNum = UnitSNum;
    }

    public String getBuildName() {
        return BuildName;
    }

    public void setBuildName(String BuildName) {
        this.BuildName = BuildName;
    }

    public String getBuildSNum() {
        return BuildSNum;
    }

    public void setBuildSNum(String BuildSNum) {
        this.BuildSNum = BuildSNum;
    }

    public String getPropertyUses() {
        return PropertyUses;
    }

    public void setPropertyUses(String PropertyUses) {
        this.PropertyUses = PropertyUses;
    }

    public String getContSubDate() {
        return ContSubDate;
    }

    public void setContSubDate(String ContSubDate) {
        this.ContSubDate = ContSubDate;
    }

    public String getGetHouseStartDate() {
        return getHouseStartDate;
    }

    public void setGetHouseStartDate(String getHouseStartDate) {
        this.getHouseStartDate = getHouseStartDate;
    }

    public String getGetHouseEndDate() {
        return getHouseEndDate;
    }

    public void setGetHouseEndDate(String getHouseEndDate) {
        this.getHouseEndDate = getHouseEndDate;
    }

    public String getPayState() {
        return PayState;
    }

    public void setPayState(String PayState) {
        this.PayState = PayState;
    }

    public String getBuildsRenovation() {
        return BuildsRenovation;
    }

    public void setBuildsRenovation(String BuildsRenovation) {
        this.BuildsRenovation = BuildsRenovation;
    }

    public String getActualSubDate() {
        return ActualSubDate;
    }

    public void setActualSubDate(String ActualSubDate) {
        this.ActualSubDate = ActualSubDate;
    }

    public String getStayTime() {
        return StayTime;
    }

    public void setStayTime(String StayTime) {
        this.StayTime = StayTime;
    }

    public String getUpdateTime() {
        return UpdateTime;
    }

    public void setUpdateTime(String UpdateTime) {
        this.UpdateTime = UpdateTime;
    }

    public String getIsDelete() {
        return IsDelete;
    }

    public void setIsDelete(String IsDelete) {
        this.IsDelete = IsDelete;
    }

    public String getUserName() {
        return UserName;
    }

    public void setUserName(String UserName) {
        this.UserName = UserName;
    }

    public String getMobileTel() {
        return MobileTel;
    }

    public void setMobileTel(String MobileTel) {
        this.MobileTel = MobileTel;
    }

    @Override
    public String toString() {
        return "CsSyncWyHouse{" +
        "OrganName=" + OrganName +
        ", City=" + City +
        ", CommID=" + CommID +
        ", CommName=" + CommName +
        ", RoomID=" + RoomID +
        ", RoomSign=" + RoomSign +
        ", RoomName=" + RoomName +
        ", UnitSNum=" + UnitSNum +
        ", BuildName=" + BuildName +
        ", BuildSNum=" + BuildSNum +
        ", PropertyUses=" + PropertyUses +
        ", ContSubDate=" + ContSubDate +
        ", getHouseStartDate=" + getHouseStartDate +
        ", getHouseEndDate=" + getHouseEndDate +
        ", PayState=" + PayState +
        ", BuildsRenovation=" + BuildsRenovation +
        ", ActualSubDate=" + ActualSubDate +
        ", StayTime=" + StayTime +
        ", UpdateTime=" + UpdateTime +
        ", IsDelete=" + IsDelete +
        ", UserName=" + UserName +
        ", MobileTel=" + MobileTel +
        "}";
    }
}
