package com.tahoecn.customerservice.model;

import com.baomidou.mybatisplus.enums.IdType;
import com.baomidou.mybatisplus.annotations.TableId;
import java.io.Serializable;

/**
 * <p>
 * 第三方评分
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-06
 */
public class CsThirdScore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 评分
     */
    private Integer score;
    /**
     * 年
     */
    private Integer annum;
    /**
     * 阶段:1.上半年2.下半年
     */
    private Integer phase;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getAnnum() {
        return annum;
    }

    public void setAnnum(Integer annum) {
        this.annum = annum;
    }

    public Integer getPhase() {
        return phase;
    }

    public void setPhase(Integer phase) {
        this.phase = phase;
    }

    @Override
    public String toString() {
        return "CsThirdScore{" +
        "id=" + id +
        ", name=" + name +
        ", score=" + score +
        ", annum=" + annum +
        ", phase=" + phase +
        "}";
    }
}
