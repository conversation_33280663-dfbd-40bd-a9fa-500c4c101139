package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 表单跟踪表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsTrackRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 表单id
     */
    private Long formInstId;
    /**
     * 创建人
     */
    private String createbyUserId;
    /**
     * 跟进描述
     */
    private String comment;
    /**
     * 创建日期
     */
    private Date creationDate;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 业务步骤编码
     */
    private Integer processStateCode;
    /**
     * 业务步骤姓名
     */
    private Integer processStateName;
    private String createbyUserName;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFormInstId() {
        return formInstId;
    }

    public void setFormInstId(Long formInstId) {
        this.formInstId = formInstId;
    }

    public String getCreatebyUserId() {
        return createbyUserId;
    }

    public void setCreatebyUserId(String createbyUserId) {
        this.createbyUserId = createbyUserId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Integer getProcessStateCode() {
        return processStateCode;
    }

    public void setProcessStateCode(Integer processStateCode) {
        this.processStateCode = processStateCode;
    }

    public Integer getProcessStateName() {
        return processStateName;
    }

    public void setProcessStateName(Integer processStateName) {
        this.processStateName = processStateName;
    }

    public String getCreatebyUserName() {
        return createbyUserName;
    }

    public void setCreatebyUserName(String createbyUserName) {
        this.createbyUserName = createbyUserName;
    }

    @Override
    public String toString() {
        return "CsTrackRecord{" +
        "id=" + id +
        ", formInstId=" + formInstId +
        ", createbyUserId=" + createbyUserId +
        ", comment=" + comment +
        ", creationDate=" + creationDate +
        ", lastUpdateDate=" + lastUpdateDate +
        ", processStateCode=" + processStateCode +
        ", processStateName=" + processStateName +
        ", createbyUserName=" + createbyUserName +
        "}";
    }
}
