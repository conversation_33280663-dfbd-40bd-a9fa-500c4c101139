package com.tahoecn.customerservice.model;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 升级配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public class CsUpgradeCfg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 工单类型（房产，物业，等等）
     */
    private String formTypeCode;
    /**
     * 工单类型（房产，物业，等等）
     */
    private String formTypeName;
    /**
     * 分类（投诉，报修等等）
     */
    private String sortTypeCode;
    /**
     * 分类（投诉，报修等等）
     */
    private String sortTypeName;
    /**
     * 升级级别
     */
    private Long levelCode;
    /**
     * 天数
     */
    private Long days;
    /**
     * 创建时间
     */
    private Date creationDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFormTypeCode() {
        return formTypeCode;
    }

    public void setFormTypeCode(String formTypeCode) {
        this.formTypeCode = formTypeCode;
    }

    public String getFormTypeName() {
        return formTypeName;
    }

    public void setFormTypeName(String formTypeName) {
        this.formTypeName = formTypeName;
    }

    public String getSortTypeCode() {
        return sortTypeCode;
    }

    public void setSortTypeCode(String sortTypeCode) {
        this.sortTypeCode = sortTypeCode;
    }

    public String getSortTypeName() {
        return sortTypeName;
    }

    public void setSortTypeName(String sortTypeName) {
        this.sortTypeName = sortTypeName;
    }

    public Long getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(Long levelCode) {
        this.levelCode = levelCode;
    }

    public Long getDays() {
        return days;
    }

    public void setDays(Long days) {
        this.days = days;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Override
    public String toString() {
        return "CsUpgradeCfg{" +
        "id=" + id +
        ", formTypeCode=" + formTypeCode +
        ", formTypeName=" + formTypeName +
        ", sortTypeCode=" + sortTypeCode +
        ", sortTypeName=" + sortTypeName +
        ", levelCode=" + levelCode +
        ", days=" + days +
        ", creationDate=" + creationDate +
        "}";
    }
}
