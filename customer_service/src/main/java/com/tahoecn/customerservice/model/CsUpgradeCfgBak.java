package com.tahoecn.customerservice.model;

import java.io.Serializable;

/**
 * @ClassName CsUpgradeCfgBak
 * @Description TODO
 * <AUTHOR>
 * @Date 2018/12/21 10:44
 * Version 1.0
 **/
public class CsUpgradeCfgBak implements Serializable {

    private static final long serialVersionUID = 1L;

    private long id;

    private String firstSortCode;

    private String firstSortName;

    private String secSortCode;

    private String secSortName;

    private String thirdSortCode;

    private String thirdSortName;

    private String fourthSortCode;

    private String fourthSortName;

    private Long cityDays;

    private Long regionDays;

    private Long groupDasy;

    public long getId() {
        return id;
    }

    public String getFirstSortCode() {
        return firstSortCode;
    }

    public String getFirstSortName() {
        return firstSortName;
    }

    public String getSecSortCode() {
        return secSortCode;
    }

    public String getSecSortName() {
        return secSortName;
    }

    public String getThirdSortCode() {
        return thirdSortCode;
    }

    public String getThirdSortName() {
        return thirdSortName;
    }

    public String getFourthSortCode() {
        return fourthSortCode;
    }

    public String getFourthSortName() {
        return fourthSortName;
    }

    public Long getCityDays() {
        return cityDays;
    }

    public Long getRegionDays() {
        return regionDays;
    }

    public Long getGroupDasy() {
        return groupDasy;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setFirstSortCode(String firstSortCode) {
        this.firstSortCode = firstSortCode;
    }

    public void setFirstSortName(String firstSortName) {
        this.firstSortName = firstSortName;
    }

    public void setSecSortCode(String secSortCode) {
        this.secSortCode = secSortCode;
    }

    public void setSecSortName(String secSortName) {
        this.secSortName = secSortName;
    }

    public void setThirdSortCode(String thirdSortCode) {
        this.thirdSortCode = thirdSortCode;
    }

    public void setThirdSortName(String thirdSortName) {
        this.thirdSortName = thirdSortName;
    }

    public void setFourthSortCode(String fourthSortCode) {
        this.fourthSortCode = fourthSortCode;
    }

    public void setFourthSortName(String fourthSortName) {
        this.fourthSortName = fourthSortName;
    }

    public void setCityDays(Long cityDays) {
        this.cityDays = cityDays;
    }

    public void setRegionDays(Long regionDays) {
        this.regionDays = regionDays;
    }

    public void setGroupDasy(Long groupDasy) {
        this.groupDasy = groupDasy;
    }
}
