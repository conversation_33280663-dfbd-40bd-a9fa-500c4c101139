package com.tahoecn.customerservice.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018-08-16
 */
@TableName("User")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "用户编号",width = 20, isImportField = "true_st")
    private Integer id;

    @NotEmpty(message = "用户名不能为空")
    @Length(min=2,max=10)
    @Excel(name = "用户姓名", width = 30, isImportField = "true_st")
    private String name;

    @Min(value = 18,message = "用户年龄最小不能低于18岁")
    @Max(value = 60,message = "用户年龄最大不能超过60岁")
    @Excel(name = "用户年龄", width = 20, isImportField = "true_st")
    private Integer age;

    @Excel(name = "用户类型",width = 20, isImportField = "true_st")
    private Integer type;
    @Excel(name = "启用状态",width = 20, isImportField = "true_st")
    private Integer status;
    @Excel(name = "创建日期",width = 50, isImportField = "true_st",databaseFormat="yyyyMMddHHmmss",format="yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }



    @Override
    public String toString() {
        return "User{" +
        "id=" + id +
        ", name=" + name +
        ", age=" + age +
        ", type=" + type +
        ", status=" + status +
        ", createdTime=" + createdTime +
        "}";
    }
}
