package com.tahoecn.customerservice.model;

import com.tahoecn.customerservice.model.dto.MenuDO;
import com.tahoecn.customerservice.model.dto.Tree;
import io.swagger.models.auth.In;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhanghw on 2018/11/21.
 */
public class UserPackage implements Serializable {
    /**
     * 用户全局id
     */
    private String sid;
    /**
     * 用户姓名
     */
    private String name;
    /**
     * 登录用户名
     */
    private String username;
    /**
     * 所属部门id全路径
     */
    private String orgIdTree;
    /**
     * 所属部门名称全路径
     */
    private String orgNameTree;
    /**
     * 部门id
     */
    private String orgId;
    /**
     * 部门名称
     */
    private String orgName;

    /**
     * 角色：1:400坐席2:房修录入员,其他为空
     */
    private Integer flag;
    
    /**
     * 是否是管理员
     */
    private Integer isAdmin;
    /**
     * 是否是400坐席 1是
     */
    private Integer isSiBaiSeats;
    /**
     * 权限菜单树
     */
    private List<Tree<MenuDO>> menuTree;

    private CsCustomerExtension seats;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getOrgIdTree() {
        return orgIdTree;
    }

    public void setOrgIdTree(String orgIdTree) {
        this.orgIdTree = orgIdTree;
    }

    public String getOrgNameTree() {
        return orgNameTree;
    }

    public void setOrgNameTree(String orgNameTree) {
        this.orgNameTree = orgNameTree;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public List<Tree<MenuDO>> getMenuTree() {
        return menuTree;
    }

    public void setMenuTree(List<Tree<MenuDO>> menuTree) {
        this.menuTree = menuTree;
    }

    public CsCustomerExtension getSeats() {
        return seats;
    }

    public void setSeats(CsCustomerExtension seats) {
        this.seats = seats;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

	public Integer getIsAdmin() {
		return isAdmin;
	}

	public void setIsAdmin(Integer isAdmin) {
		this.isAdmin = isAdmin;
	}

	public Integer getIsSiBaiSeats() {
		return isSiBaiSeats;
	}

	public void setIsSiBaiSeats(Integer isSiBaiSeats) {
		this.isSiBaiSeats = isSiBaiSeats;
	}
	
}
