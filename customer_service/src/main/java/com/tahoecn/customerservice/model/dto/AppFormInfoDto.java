package com.tahoecn.customerservice.model.dto;

public class AppFormInfoDto{
	
	private String formId;//工单id
	private String houseNo;//房屋编号
	private String ownerId;//业主id
	private String ownerName;//业主名称
	private String ownerType;//个人/单位
	private String genderName;//性别
	private String mobile;//电话
	private String idNo;//身份证号
	private String formUserName;//报事人
	private String formUserTel;//报事人电话
	private String customerDemand;//客户诉求
	private String deptCode;//部门
	private String deptName;//
	private String processCode;//流程类别
	private String processName;//
	private String firstSortCode;//一级分类
	private String firstSortName;//
	private String secSortCode;//二级分类
	private String secSortName;//
	private String thirdSortCode;//三级分类
	private String thirdSortName;//
	private String fourthSortCode;//四级分类
	private String fourthSortName;//
	private String problemLevelCode;//问题严重级别
	private String problemLevelName;//
	private String problemPositionCode;//报修部位
	private String problemPositionName;//
	private String revisionClassificationCode;//报修分类
	private String revisionClassificationName;//
	private String decorationStageCode;//装修阶段
	private String decorationStageName;//
	private String maintenancePeriodCode;//维保期
	private String maintenancePeriodName;//
	public String getFormId() {
		return formId;
	}
	public void setFormId(String formId) {
		this.formId = formId;
	}
	public String getHouseNo() {
		return houseNo;
	}
	public void setHouseNo(String houseNo) {
		this.houseNo = houseNo;
	}
	public String getOwnerId() {
		return ownerId;
	}
	public void setOwnerId(String ownerId) {
		this.ownerId = ownerId;
	}
	public String getOwnerName() {
		return ownerName;
	}
	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}
	public String getOwnerType() {
		return ownerType;
	}
	public void setOwnerType(String ownerType) {
		this.ownerType = ownerType;
	}
	public String getGenderName() {
		return genderName;
	}
	public void setGenderName(String genderName) {
		this.genderName = genderName;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	public String getFormUserName() {
		return formUserName;
	}
	public void setFormUserName(String formUserName) {
		this.formUserName = formUserName;
	}
	public String getFormUserTel() {
		return formUserTel;
	}
	public void setFormUserTel(String formUserTel) {
		this.formUserTel = formUserTel;
	}
	public String getCustomerDemand() {
		return customerDemand;
	}
	public void setCustomerDemand(String customerDemand) {
		this.customerDemand = customerDemand;
	}
	public String getDeptCode() {
		return deptCode;
	}
	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getProcessCode() {
		return processCode;
	}
	public void setProcessCode(String processCode) {
		this.processCode = processCode;
	}
	public String getProcessName() {
		return processName;
	}
	public void setProcessName(String processName) {
		this.processName = processName;
	}
	public String getFirstSortCode() {
		return firstSortCode;
	}
	public void setFirstSortCode(String firstSortCode) {
		this.firstSortCode = firstSortCode;
	}
	public String getFirstSortName() {
		return firstSortName;
	}
	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}
	public String getSecSortCode() {
		return secSortCode;
	}
	public void setSecSortCode(String secSortCode) {
		this.secSortCode = secSortCode;
	}
	public String getSecSortName() {
		return secSortName;
	}
	public void setSecSortName(String secSortName) {
		this.secSortName = secSortName;
	}
	public String getThirdSortCode() {
		return thirdSortCode;
	}
	public void setThirdSortCode(String thirdSortCode) {
		this.thirdSortCode = thirdSortCode;
	}
	public String getThirdSortName() {
		return thirdSortName;
	}
	public void setThirdSortName(String thirdSortName) {
		this.thirdSortName = thirdSortName;
	}
	public String getFourthSortCode() {
		return fourthSortCode;
	}
	public void setFourthSortCode(String fourthSortCode) {
		this.fourthSortCode = fourthSortCode;
	}
	public String getFourthSortName() {
		return fourthSortName;
	}
	public void setFourthSortName(String fourthSortName) {
		this.fourthSortName = fourthSortName;
	}
	public String getProblemLevelCode() {
		return problemLevelCode;
	}
	public void setProblemLevelCode(String problemLevelCode) {
		this.problemLevelCode = problemLevelCode;
	}
	public String getProblemLevelName() {
		return problemLevelName;
	}
	public void setProblemLevelName(String problemLevelName) {
		this.problemLevelName = problemLevelName;
	}
	public String getProblemPositionCode() {
		return problemPositionCode;
	}
	public void setProblemPositionCode(String problemPositionCode) {
		this.problemPositionCode = problemPositionCode;
	}
	public String getProblemPositionName() {
		return problemPositionName;
	}
	public void setProblemPositionName(String problemPositionName) {
		this.problemPositionName = problemPositionName;
	}
	public String getRevisionClassificationCode() {
		return revisionClassificationCode;
	}
	public void setRevisionClassificationCode(String revisionClassificationCode) {
		this.revisionClassificationCode = revisionClassificationCode;
	}
	public String getRevisionClassificationName() {
		return revisionClassificationName;
	}
	public void setRevisionClassificationName(String revisionClassificationName) {
		this.revisionClassificationName = revisionClassificationName;
	}
	public String getDecorationStageCode() {
		return decorationStageCode;
	}
	public void setDecorationStageCode(String decorationStageCode) {
		this.decorationStageCode = decorationStageCode;
	}
	public String getDecorationStageName() {
		return decorationStageName;
	}
	public void setDecorationStageName(String decorationStageName) {
		this.decorationStageName = decorationStageName;
	}
	public String getMaintenancePeriodCode() {
		return maintenancePeriodCode;
	}
	public void setMaintenancePeriodCode(String maintenancePeriodCode) {
		this.maintenancePeriodCode = maintenancePeriodCode;
	}
	public String getMaintenancePeriodName() {
		return maintenancePeriodName;
	}
	public void setMaintenancePeriodName(String maintenancePeriodName) {
		this.maintenancePeriodName = maintenancePeriodName;
	}
}
