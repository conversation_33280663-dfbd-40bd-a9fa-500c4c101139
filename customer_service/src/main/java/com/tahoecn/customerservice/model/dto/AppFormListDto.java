package com.tahoecn.customerservice.model.dto;

public class AppFormListDto{
	
	private String formId;//工单id
	private String formNo;//工单编号
	private String classification;//分类
	private String formUserName;//报事人
	private String submitDate;//报事时间
	private String customerDemand;//客户诉求
	private String isGrab;//是否抢单
	private String upgradeFlag;//是否升级
	private String rejectFlag;//是否退回
	private String reworkFlag;//是否返工
	private String formUserTel;//报事电话
	private String project;//所属项目
	private String assignName;//分派人
	private String curAssigneeName;//处理人
	private String creationDate;//创建时间
	private String houseInfo;//房屋信息
	private String mobile;//业主电话
	public String getFormId() {
		return formId;
	}
	public void setFormId(String formId) {
		this.formId = formId;
	}
	public String getFormNo() {
		return formNo;
	}
	public void setFormNo(String formNo) {
		this.formNo = formNo;
	}
	public String getClassification() {
		return classification;
	}
	public void setClassification(String classification) {
		this.classification = classification;
	}
	public String getFormUserName() {
		return formUserName;
	}
	public void setFormUserName(String formUserName) {
		this.formUserName = formUserName;
	}
	public String getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(String submitDate) {
		this.submitDate = submitDate;
	}
	public String getCustomerDemand() {
		return customerDemand;
	}
	public void setCustomerDemand(String customerDemand) {
		this.customerDemand = customerDemand;
	}
	public String getIsGrab() {
		return isGrab;
	}
	public void setIsGrab(String isGrab) {
		this.isGrab = isGrab;
	}
	public String getUpgradeFlag() {
		return upgradeFlag;
	}
	public void setUpgradeFlag(String upgradeFlag) {
		this.upgradeFlag = upgradeFlag;
	}
	public String getRejectFlag() {
		return rejectFlag;
	}
	public void setRejectFlag(String rejectFlag) {
		this.rejectFlag = rejectFlag;
	}
	public String getReworkFlag() {
		return reworkFlag;
	}
	public void setReworkFlag(String reworkFlag) {
		this.reworkFlag = reworkFlag;
	}
	public String getFormUserTel() {
		return formUserTel;
	}
	public void setFormUserTel(String formUserTel) {
		this.formUserTel = formUserTel;
	}
	public String getProject() {
		return project;
	}
	public void setProject(String project) {
		this.project = project;
	}
	public String getAssignName() {
		return assignName;
	}
	public void setAssignName(String assignName) {
		this.assignName = assignName;
	}
	public String getCurAssigneeName() {
		return curAssigneeName;
	}
	public void setCurAssigneeName(String curAssigneeName) {
		this.curAssigneeName = curAssigneeName;
	}
	public String getCreationDate() {
		return creationDate;
	}
	public void setCreationDate(String creationDate) {
		this.creationDate = creationDate;
	}
	public String getHouseInfo() {
		return houseInfo;
	}
	public void setHouseInfo(String houseInfo) {
		this.houseInfo = houseInfo;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
}
