package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

/**
 * 物业  =》家庭成员信息数据 
 * <AUTHOR>  2018年11月15日
 */
public class FamilyInfoDto implements Serializable{
	private String holdID;
	private String custID;
	private String relationship;
	private String memberName;
	private String paperName;
	private String paperCode;
	private String sex;
	private String birthday;
	private String nationality;
	private String mobilePhone ;
	private String holdIDInterests;
	private String isOtherMembers;
	private String isSpecialCustomer;
	private String RN;
	public String getHoldID() {
		return holdID;
	}
	public void setHoldID(String holdID) {
		this.holdID = holdID;
	}
	public String getRelationship() {
		return relationship;
	}
	public void setRelationship(String relationship) {
		this.relationship = relationship;
	}
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	public String getPaperName() {
		return paperName;
	}
	public void setPaperName(String paperName) {
		this.paperName = paperName;
	}
	public String getPaperCode() {
		return paperCode;
	}
	public void setPaperCode(String paperCode) {
		this.paperCode = paperCode;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getNationality() {
		return nationality;
	}
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}
	public String getMobilePhone() {
		return mobilePhone;
	}
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	public String getIsOtherMembers() {
		return isOtherMembers;
	}
	public void setIsOtherMembers(String isOtherMembers) {
		this.isOtherMembers = isOtherMembers;
	}
	public String getIsSpecialCustomer() {
		return isSpecialCustomer;
	}
	public void setIsSpecialCustomer(String isSpecialCustomer) {
		this.isSpecialCustomer = isSpecialCustomer;
	}
	public String getRN() {
		return RN;
	}
	public void setRN(String rN) {
		RN = rN;
	}

	public String getCustID() {
		return custID;
	}

	public void setCustID(String custID) {
		this.custID = custID;
	}

	public String getHoldIDInterests() {
		return holdIDInterests;
	}

	public void setHoldIDInterests(String holdIDInterests) {
		this.holdIDInterests = holdIDInterests;
	}

	@Override
	public String toString() {
		return "FamilyInfoDto [holdID=" + holdID + ", relationship=" + relationship + ", memberName=" + memberName
				+ ", paperName=" + paperName + ", paperCode=" + paperCode + ", sex=" + sex + ", birthday=" + birthday
				+ ", nationality=" + nationality + ", mobilePhone=" + mobilePhone + ", holdIDInterests=" + holdIDInterests
				+ ", isOtherMembers=" + isOtherMembers + ", isSpecialCustomer=" + isSpecialCustomer + ", RN=" + RN
				+ "]";
	}
}
