/**
 * 
 */
package com.tahoecn.customerservice.model.dto;

import com.tahoecn.customerservice.model.CsProcessWorkitem;
import java.util.List;

import com.tahoecn.customerservice.model.CsCustFamily;
import com.tahoecn.customerservice.model.CsFormInst;

/**
 *
 */
public class FamilyListDto {
	/**
	 * 工单信息
	 */
	private CsFormInst csFormInst;
	/**
	 * 家庭成员
	 */
	private List<CsCustFamily> formCustFamilies;
	/**
	 * 操作记录
	 */
	private String comment;

	/**
	 * 查询详情得来源
	 */
	private String source;
	/**
	 * 操作类型 draft,update,submit
	 */
	private String operateType;

	/**
	 * 跟蹤記錄
	 */
	private List<CsProcessWorkitem> processWorkitem;

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public List<CsCustFamily> getFormCustFamilies() {
		return formCustFamilies;
	}

	public void setFormCustFamilies(List<CsCustFamily> formCustFamilies) {
		this.formCustFamilies = formCustFamilies;
	}

	public CsFormInst getCsFormInst() {
		return csFormInst;
	}

	public void setCsFormInst(CsFormInst csFormInst) {
		this.csFormInst = csFormInst;
	}

	public String getOperateType() {
		return operateType;
	}

	public void setOperateType(String operateType) {
		this.operateType = operateType;
	}

	public List<CsProcessWorkitem> getProcessWorkitem() {
		return processWorkitem;
	}

	public void setProcessWorkitem(List<CsProcessWorkitem> processWorkitem) {
		this.processWorkitem = processWorkitem;
	}
}
