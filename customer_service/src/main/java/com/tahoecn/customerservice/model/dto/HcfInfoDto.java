/**
 * 
 */
package com.tahoecn.customerservice.model.dto;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2018年11月17日 上午11:56:15
 * @desc 房人工单组合查询
 */
@ApiModel(value = "csHouseInfoDto", description = "房人工单 联查实体")
public class HcfInfoDto {

	/**
	 * 区域
	 */
	@ApiModelProperty(name = "area", value = "区域")
	private String area;
	/**
	 * 城市
	 */
	@ApiModelProperty(name = "city", value = "城市")
	private String city;
	/**
	 * 项目
	 */
	@ApiModelProperty(name = "project", value = "项目")
	private String project;
	/**
	 * 楼栋
	 */
	@ApiModelProperty(name = "building", value = "楼号")
	private String building;
	/**
	 * 单元号
	 */
	@ApiModelProperty(name = "unit", value = "单元号")
	private String unit;
	/**
	 * 移动电话
	 */
	@ApiModelProperty(name = "telephone", value = "电话")
	private String telephone;
	/**
	 * 创建时间 开始
	 */
	@ApiModelProperty(name = "createDateStart", value = "创建时间 开始")
	private Date createDateStart;
	/**
	 * 创建时间 结束
	 */
	@ApiModelProperty(name = "createDateEnd", value = "创建时间 结束")
	private Date createDateEnd;
	/**
	 * 业务步骤
	 */
	@ApiModelProperty(name = "processStateCode", value = "业务步骤")
	private String processStateCode;
	/**
	 * 模糊查询——房屋编号
	 */
	@ApiModelProperty(name = "sHouseNum", value = "模糊查询——房屋编号")
	private String sHouseNum;
	/**
	 * 模糊查询——房屋编号
	 */
	@ApiModelProperty(name = "sHouseName", value = "模糊查询——房屋编号")
	private String sHouseName;
	/**
	 * 模糊查询——房号
	 */
	@ApiModelProperty(name = "roomNum", value = "模糊查询——房号")
	private String roomNum;
	/**
	 * 房屋编号
	 */
	@ApiModelProperty(name = "houseNum", value = "级联查询——房屋编号")
	private String houseNum;
	/**
	 * 客户ID
	 */
	@ApiModelProperty(name = "custId", value = "级联查询——客户ID")
	private String custId;
	
	@ApiModelProperty(name = "deptCode", value = "部门")
	private String deptCode;

	@ApiModelProperty(name = "pageSize", value = "每页数量")
	private Integer pageSize = 20;

	@ApiModelProperty(name = "pageNum", value = "页码")
	private Integer pageNum = 1;

    @ApiModelProperty(name = "orderBy", value = "排序")
    private String orderBy = "ASC";
	/**
	 * 当前处理人ID,前台不需要传
	 */
	private String curAssigneeId;
	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getBuilding() {
		return building;
	}

	public void setBuilding(String building) {
		this.building = building;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public void setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}

	public void setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
	}

	public String getProcessStateCode() {
		return processStateCode;
	}

	public void setProcessStateCode(String processStateCode) {
		this.processStateCode = processStateCode;
	}
	
	public String getsHouseNum() {
		return sHouseNum;
	}

	public void setsHouseNum(String sHouseNum) {
		this.sHouseNum = sHouseNum;
	}
	
	public String getsHouseName() {
		return sHouseName;
	}

	public void setsHouseName(String sHouseName) {
		this.sHouseName = sHouseName;
	}

	public String getRoomNum() {
		return roomNum;
	}

	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}

	public String getHouseNum() {
		return houseNum;
	}

	public void setHouseNum(String houseNum) {
		this.houseNum = houseNum;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}
	
	public String getDeptCode() {
		return deptCode;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public boolean paramsIsNotNull() {
		return StringUtils.isNotBlank(area) 
				|| StringUtils.isNotBlank(city) 
				|| StringUtils.isNotBlank(project)
				|| StringUtils.isNotBlank(building) 
				|| StringUtils.isNotBlank(unit) 
				|| StringUtils.isNotBlank(telephone) 
				|| createDateStart != null
				|| createDateEnd != null 
				|| StringUtils.isNotBlank(processStateCode)
				|| StringUtils.isNotBlank(sHouseNum)
				|| StringUtils.isNotBlank(sHouseName)
				|| StringUtils.isNotBlank(roomNum)
				|| StringUtils.isNotBlank(houseNum)
				|| StringUtils.isNotBlank(custId);
	}

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getCurAssigneeId() {
		return curAssigneeId;
	}

	public void setCurAssigneeId(String curAssigneeId) {
		this.curAssigneeId = curAssigneeId;
	}
}
