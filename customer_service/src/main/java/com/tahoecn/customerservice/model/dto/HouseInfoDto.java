package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

/**
 * 物业  =》房屋信息数据 
 * <AUTHOR>  2018年11月15日
 */
public class HouseInfoDto  implements Serializable{
	
	private String commID;
	private String roomID;
	private String roomSign;
	private String roomName;
	private String regionName;
	private String city;
	private String commName;
	private String buildName;
	private String unitSNum;
	private String roomNumber;
	private String propertyUses;
	private String contSubDate;
	private String signingTime;
	private String getHouseStartDate;
	private String getHouseEndDate;
	private String payState;
	private String buildsRenovation;
	private String actualSubDate;
	private String stayTime;
	private String withdrawalTime;
	private String RN;
	private String OrganName;
	public String getCommID() {
		return commID;
	}
	public void setCommID(String commID) {
		this.commID = commID;
	}
	public String getRoomID() {
		return roomID;
	}
	public void setRoomID(String roomID) {
		this.roomID = roomID;
	}
	public String getRoomSign() {
		return roomSign;
	}
	public void setRoomSign(String roomSign) {
		this.roomSign = roomSign;
	}
	public String getRoomName() {
		return roomName;
	}
	public void setRoomName(String roomName) {
		this.roomName = roomName;
	}
	public String getRegionName() {
		return regionName;
	}
	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCommName() {
		return commName;
	}
	public void setCommName(String commName) {
		this.commName = commName;
	}
	public String getBuildName() {
		return buildName;
	}
	public void setBuildName(String buildName) {
		this.buildName = buildName;
	}
	public String getUnitSNum() {
		return unitSNum;
	}
	public void setUnitSNum(String unitSNum) {
		this.unitSNum = unitSNum;
	}
	public String getRoomNumber() {
		return roomNumber;
	}
	public void setRoomNumber(String roomNumber) {
		this.roomNumber = roomNumber;
	}
	public String getPropertyUses() {
		return propertyUses;
	}
	public void setPropertyUses(String propertyUses) {
		this.propertyUses = propertyUses;
	}
	public String getContSubDate() {
		return contSubDate;
	}
	public void setContSubDate(String contSubDate) {
		this.contSubDate = contSubDate;
	}
	public String getSigningTime() {
		return signingTime;
	}
	public void setSigningTime(String signingTime) {
		this.signingTime = signingTime;
	}
	public String getGetHouseStartDate() {
		return getHouseStartDate;
	}
	public void setGetHouseStartDate(String getHouseStartDate) {
		this.getHouseStartDate = getHouseStartDate;
	}
	public String getGetHouseEndDate() {
		return getHouseEndDate;
	}
	public void setGetHouseEndDate(String getHouseEndDate) {
		this.getHouseEndDate = getHouseEndDate;
	}
	public String getPayState() {
		return payState;
	}
	public void setPayState(String payState) {
		this.payState = payState;
	}
	public String getBuildsRenovation() {
		return buildsRenovation;
	}
	public void setBuildsRenovation(String buildsRenovation) {
		this.buildsRenovation = buildsRenovation;
	}
	public String getActualSubDate() {
		return actualSubDate;
	}
	public void setActualSubDate(String actualSubDate) {
		this.actualSubDate = actualSubDate;
	}
	public String getStayTime() {
		return stayTime;
	}
	public void setStayTime(String stayTime) {
		this.stayTime = stayTime;
	}
	public String getWithdrawalTime() {
		return withdrawalTime;
	}
	public void setWithdrawalTime(String withdrawalTime) {
		this.withdrawalTime = withdrawalTime;
	}
	public String getRN() {
		return RN;
	}
	public void setRN(String rN) {
		RN = rN;
	}
	@Override
	public String toString() {
		return "HouseInfoDto [commID=" + commID + ", roomID=" + roomID + ", roomSign=" + roomSign + ", roomName="
				+ roomName + ", regionName=" + regionName + ", city=" + city + ", commName=" + commName + ", buildName="
				+ buildName + ", unitSNum=" + unitSNum + ", roomNumber=" + roomNumber + ", propertyUses=" + propertyUses
				+ ", contSubDate=" + contSubDate + ", signingTime=" + signingTime + ", getHouseStartDate="
				+ getHouseStartDate + ", getHouseEndDate=" + getHouseEndDate + ", payState=" + payState
				+ ", buildsRenovation=" + buildsRenovation + ", actualSubDate=" + actualSubDate + ", stayTime="
				+ stayTime + ", withdrawalTime=" + withdrawalTime + ", RN=" + RN + "]";
	}

	public String getOrganName() {
		return OrganName;
	}

	public void setOrganName(String organName) {
		OrganName = organName;
	}
}
