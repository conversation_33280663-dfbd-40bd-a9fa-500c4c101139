package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

public class MatterStatisticalParentDto implements Serializable {
	/**
	* 
	*/
	private static final long serialVersionUID = -6626566753070663172L;
	private String id;
	private String masterSlaveName;
	private String masterCode;
	private int totalNum;
	private int finishNum;

	public String getMasterSlaveName() {
		return masterSlaveName;
	}

	public void setMasterSlaveName(String masterSlaveName) {
		this.masterSlaveName = masterSlaveName;
	}

	public int getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(int totalNum) {
		this.totalNum = totalNum;
	}

	public int getFinishNum() {
		return finishNum;
	}

	public void setFinishNum(int finishNum) {
		this.finishNum = finishNum;
	}

	public int getDoingNum() {
		return doingNum;
	}

	public void setDoingNum(int doingNum) {
		this.doingNum = doingNum;
	}

	public int getLevelUpNum() {
		return levelUpNum;
	}

	public void setLevelUpNum(int levelUpNum) {
		this.levelUpNum = levelUpNum;
	}

	public int getReworkNum() {
		return reworkNum;
	}

	public void setReworkNum(int reworkNum) {
		this.reworkNum = reworkNum;
	}

	public String getLevelUpRate() {
		return levelUpRate;
	}

	public void setLevelUpRate(String levelUpRate) {
		this.levelUpRate = levelUpRate;
	}

	public String getFinsihRate() {
		return finsihRate;
	}

	public void setFinsihRate(String finsihRate) {
		this.finsihRate = finsihRate;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMasterCode() {
		return masterCode;
	}

	public void setMasterCode(String masterCode) {
		this.masterCode = masterCode;
	}

	private int doingNum;
	private int levelUpNum;
	private int reworkNum;
	private String finsihRate;
	private String levelUpRate;
}
