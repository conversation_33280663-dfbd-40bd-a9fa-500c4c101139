package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

/**
 * 物业 =》项目信息数据  
 * <AUTHOR>  2018年11月15日
 */
public class ProjectInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String organName;
	private String organ;
	private String city;
	private String commId;
	private String commName;
	private String commAddress;
	private String updateTime;
	private String RN;
	public String getOrganName() {
		return organName;
	}
	public void setOrganName(String organName) {
		this.organName = organName;
	}
	public String getOrgan() {
		return organ;
	}
	public void setOrgan(String organ) {
		this.organ = organ;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCommId() {
		return commId;
	}
	public void setCommID(String commId) {
		this.commId = commId;
	}
	public String getCommName() {
		return commName;
	}
	public void setCommName(String commName) {
		this.commName = commName;
	}
	public String getCommAddress() {
		return commAddress;
	}
	public void setCommAddress(String commAddress) {
		this.commAddress = commAddress;
	}
	
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getRN() {
		return RN;
	}
	public void setRN(String rN) {
		RN = rN;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	@Override
	public String toString() {
		return "ProjectInfoDto [organName=" + organName + ", organ=" + organ + ", city=" + city + ", commID=" + commId
				+ ", commName=" + commName + ", commAddress=" + commAddress + ", RN=" + RN + "]";
	}
	
	
}
