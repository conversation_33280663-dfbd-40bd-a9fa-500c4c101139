package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

/**
 * Created by zhanghw on 2018/12/17.
 */
public class ProjectRoleDO implements Serializable {
    private String menuCode;
    private String parentCode;
    private String menuName;
    private Integer orderNum;
    /**
     * 区分类型
     * 1.泰禾集团
     * 2.区域
     * 3.城市
     * 4.项目
     */
    private String type;
    /**
     * 类型对应的名称
     */
    private String typeName;
    /**
     * 树路径
     */
    private String treePath;

    private ProjectRoleAttrDO projectRoleAttrDO;

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public ProjectRoleAttrDO getProjectRoleAttrDO() {
        return projectRoleAttrDO;
    }

    public void setProjectRoleAttrDO(ProjectRoleAttrDO projectRoleAttrDO) {
        this.projectRoleAttrDO = projectRoleAttrDO;
    }

    public String getTreePath() {
        return treePath;
    }

    public void setTreePath(String treePath) {
        this.treePath = treePath;
    }
}
