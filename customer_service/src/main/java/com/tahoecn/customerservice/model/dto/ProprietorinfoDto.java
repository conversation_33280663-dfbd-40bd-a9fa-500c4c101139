package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

/**
 * 物业 =》业主信息数据  
 * <AUTHOR>  2018年11月15日
 */
public class ProprietorinfoDto implements Serializable{
	
	private String custID;
	private String custName;
	private String roomSigns;
	private String paperName;
	private String paperCode;
	private String fixedTel;  
	private String mobilePhone;
	private String faxTel;        
	private String address;
	private String postCode;       
	private String eMail;          
	private String isUnit;       
	private String sex;         
	private String nationality;   
	private String birthday;
	private String workUnit;     
	private String job;
	private String custInterests;
	private String housekeeperInformation;
	private String housekeepingTelephone;
	private String isOtherMembers;
	private String isSpecialCustomer;
	private String RN;
	public String getCustID() {
		return custID;
	}
	public void setCustID(String custID) {
		this.custID = custID;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getRoomSigns() {
		return roomSigns;
	}
	public void setRoomSigns(String roomSigns) {
		this.roomSigns = roomSigns;
	}
	public String getPaperName() {
		return paperName;
	}
	public void setPaperName(String paperName) {
		this.paperName = paperName;
	}
	public String getPaperCode() {
		return paperCode;
	}
	public void setPaperCode(String paperCode) {
		this.paperCode = paperCode;
	}
	public String getFixedTel() {
		return fixedTel;
	}
	public void setFixedTel(String fixedTel) {
		this.fixedTel = fixedTel;
	}
	public String getMobilePhone() {
		return mobilePhone;
	}
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	public String getFaxTel() {
		return faxTel;
	}
	public void setFaxTel(String faxTel) {
		this.faxTel = faxTel;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getPostCode() {
		return postCode;
	}
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	public String geteMail() {
		return eMail;
	}
	public void seteMail(String eMail) {
		this.eMail = eMail;
	}
	public String getIsUnit() {
		return isUnit;
	}
	public void setIsUnit(String isUnit) {
		this.isUnit = isUnit;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getNationality() {
		return nationality;
	}
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getWorkUnit() {
		return workUnit;
	}
	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getJob() {
		return job;
	}

	public void setJob(String job) {
		this.job = job;
	}

	public String getCustInterests() {
		return custInterests;
	}

	public void setCustInterests(String custInterests) {
		this.custInterests = custInterests;
	}

	public String getHousekeeperInformation() {
		return housekeeperInformation;
	}
	public void setHousekeeperInformation(String housekeeperInformation) {
		this.housekeeperInformation = housekeeperInformation;
	}
	public String getHousekeepingTelephone() {
		return housekeepingTelephone;
	}
	public void setHousekeepingTelephone(String housekeepingTelephone) {
		this.housekeepingTelephone = housekeepingTelephone;
	}
	public String getIsOtherMembers() {
		return isOtherMembers;
	}
	public void setIsOtherMembers(String isOtherMembers) {
		this.isOtherMembers = isOtherMembers;
	}
	public String getIsSpecialCustomer() {
		return isSpecialCustomer;
	}
	public void setIsSpecialCustomer(String isSpecialCustomer) {
		this.isSpecialCustomer = isSpecialCustomer;
	}
	public String getRN() {
		return RN;
	}
	public void setRN(String rN) {
		RN = rN;
	}
	@Override
	public String toString() {
		return "ProprietorinfoDto [custID=" + custID + ", custName=" + custName + ", roomSigns=" + roomSigns
				+ ", paperName=" + paperName + ", paperCode=" + paperCode + ", fixedTel=" + fixedTel + ", mobilePhone="
				+ mobilePhone + ", faxTel=" + faxTel + ", address=" + address + ", postCode=" + postCode + ", eMail="
				+ eMail + ", isUnit=" + isUnit + ", sex=" + sex + ", nationality=" + nationality + ", birthday="
				+ birthday + ", workUnit=" + workUnit + ", custInterests=" + custInterests + ", job=" + job
				+ ", housekeeperInformation=" + housekeeperInformation + ", housekeepingTelephone="
				+ housekeepingTelephone + ", isOtherMembers=" + isOtherMembers + ", isSpecialCustomer="
				+ isSpecialCustomer + ", RN=" + RN + "]";
	}    
}
