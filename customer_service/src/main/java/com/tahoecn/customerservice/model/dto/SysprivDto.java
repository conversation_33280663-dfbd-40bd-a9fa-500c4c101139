package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

public class SysprivDto implements Serializable  {
/**
	 * 
	 */
private static final long serialVersionUID = 6334660998214021494L;
private String 	fsid;
private String 	fdCode;
private String 	fdPname;
private String 	fdUpdateTime;
private String 	fdPsid;
private String 	fdNameTree;
private String 	fdSidTree;
private String 	fdCreateTime;
private String 	fdSid;
public String getFsid() {
	return fsid;
}
public void setFsid(String fsid) {
	this.fsid = fsid;
}
public String getFdCode() {
	return fdCode;
}
public void setFdCode(String fdCode) {
	this.fdCode = fdCode;
}
public String getFdPname() {
	return fdPname;
}
public void setFdPname(String fdPname) {
	this.fdPname = fdPname;
}
public String getFdUpdateTime() {
	return fdUpdateTime;
}
public void setFdUpdateTime(String fdUpdateTime) {
	this.fdUpdateTime = fdUpdateTime;
}
public String getFdPsid() {
	return fdPsid;
}
public void setFdPsid(String fdPsid) {
	this.fdPsid = fdPsid;
}
public String getFdNameTree() {
	return fdNameTree;
}
public void setFdNameTree(String fdNameTree) {
	this.fdNameTree = fdNameTree;
}
public String getFdSidTree() {
	return fdSidTree;
}
public void setFdSidTree(String fdSidTree) {
	this.fdSidTree = fdSidTree;
}
public String getFdCreateTime() {
	return fdCreateTime;
}
public void setFdCreateTime(String fdCreateTime) {
	this.fdCreateTime = fdCreateTime;
}
public String getFdSid() {
	return fdSid;
}
public void setFdSid(String fdSid) {
	this.fdSid = fdSid;
}
public String getFdPnameTree() {
	return fdPnameTree;
}
public void setFdPnameTree(String fdPnameTree) {
	this.fdPnameTree = fdPnameTree;
}
public String getFdName() {
	return fdName;
}
public void setFdName(String fdName) {
	this.fdName = fdName;
}
public String getFdOrder() {
	return fdOrder;
}
public void setFdOrder(String fdOrder) {
	this.fdOrder = fdOrder;
}
private String 	fdPnameTree;
private String 	fdName;
private String 	fdOrder;
}
