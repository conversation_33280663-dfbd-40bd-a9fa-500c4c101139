package com.tahoecn.customerservice.model.dto;

import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by zhanghw on 2018/12/19.
 */
public class TreeExt<T> {
    /**
     * 区分类型
     * 1.泰禾集团
     * 2.区域
     * 3.城市
     * 4.项目
     */
    private String type;
    /**
     * 类型对应的名称
     */
    private String typeName;
    /**
     * 树路径
     */
    private String treePath;
    /**
     * 客服负责人
     */
    private String serviceLeaders;
    /**
     * 客服处理人
     */
    private String servicesWorks;
    /**
     * 报修负责人
     */
    private String repairsLeaders;
    /**
     * 报修处理人
     */
    private String repairsWorks;

    /**
     * 报修录入员
     */
    private String fixInputUsers;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTreePath() {
        return treePath;
    }

    public void setTreePath(String treePath) {
        this.treePath = treePath;
    }

    public String getServiceLeaders() {
        return serviceLeaders;
    }

    public void setServiceLeaders(String serviceLeaders) {
        this.serviceLeaders = serviceLeaders;
    }

    public String getServicesWorks() {
        return servicesWorks;
    }

    public void setServicesWorks(String servicesWorks) {
        this.servicesWorks = servicesWorks;
    }

    public String getRepairsLeaders() {
        return repairsLeaders;
    }

    public void setRepairsLeaders(String repairsLeaders) {
        this.repairsLeaders = repairsLeaders;
    }

    public String getRepairsWorks() {
        return repairsWorks;
    }

    public void setRepairsWorks(String repairsWorks) {
        this.repairsWorks = repairsWorks;
    }

    public String getFixInputUsers() {
        return fixInputUsers;
    }

    public void setFixInputUsers(String fixInputUsers) {
        this.fixInputUsers = fixInputUsers;
    }

    /**
     * 节点ID
     */
    protected String id;
    /**
     * 显示节点文本
     */
    protected String text;
    /**
     * 节点状态，open closed
     */
    protected Map<String, Object> state;
    /**
     * 节点是否被选中 true false
     */
    protected boolean checked = false;
    /**
     * 节点属性
     */
    protected Map<String, Object> attributes;

    /**
     * 节点的子节点
     */
    protected List<TreeExt<T>> children = new ArrayList<TreeExt<T>>();

    /**
     * 父ID
     */
    protected String parentId;
    /**
     * 是否有父节点
     */
    protected boolean hasParent = false;
    /**
     * 是否有子节点
     */
    protected boolean hasChildren = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Map<String, Object> getState() {
        return state;
    }

    public void setState(Map<String, Object> state) {
        this.state = state;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    public List<TreeExt<T>> getChildren() {
        return children;
    }

    public void setChildren(List<TreeExt<T>> children) {
        this.children = children;
    }

    public boolean isHasParent() {
        return hasParent;
    }

    public void setHasParent(boolean isParent) {
        this.hasParent = isParent;
    }

    public boolean isHasChildren() {
        return hasChildren;
    }

    public void setChildren(boolean isChildren) {
        this.hasChildren = isChildren;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public TreeExt(String id, String text, Map<String, Object> state, boolean checked, Map<String, Object> attributes,
                   List<TreeExt<T>> children, boolean isParent, boolean isChildren, String parentID) {
        super();
        this.id = id;
        this.text = text;
        this.state = state;
        this.checked = checked;
        this.attributes = attributes;
        this.children = children;
        this.hasParent = isParent;
        this.hasChildren = isChildren;
        this.parentId = parentID;
    }

    public TreeExt() {
        super();
    }

    @Override
    public String toString() {

        return JSON.toJSONString(this);
    }
}
