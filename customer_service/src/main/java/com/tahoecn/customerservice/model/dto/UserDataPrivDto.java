package com.tahoecn.customerservice.model.dto;

import java.util.List;

public class UserDataPrivDto {
    private String fdSysSid;
    private String fdSysName;
    private String fdSid;
    private String fdName;
    private String fdCode;
    private String fdPrivRangeSid;
    private String fdPrivRangeName;
    private String fdPrivRangeCode;
    private String fdDataRangeSid;
    private String fdDataRangeName;
    private String fdDataRangeCode;
    private String fdDataRangePcode;
    private List<FdDataPrivListDto> fdDataPrivList;
    private String fdOrder;
    private String fdCreateTime;
    private String fdUpdateTime;
    private String[] fdStandardRoleSids;

    public String getFdSysSid() {
        return fdSysSid;
    }

    public void setFdSysSid(String fdSysSid) {
        this.fdSysSid = fdSysSid;
    }

    public String getFdSysName() {
        return fdSysName;
    }

    public void setFdSysName(String fdSysName) {
        this.fdSysName = fdSysName;
    }

    public String getFdSid() {
        return fdSid;
    }

    public void setFdSid(String fdSid) {
        this.fdSid = fdSid;
    }

    public String getFdName() {
        return fdName;
    }

    public void setFdName(String fdName) {
        this.fdName = fdName;
    }

    public String getFdCode() {
        return fdCode;
    }

    public void setFdCode(String fdCode) {
        this.fdCode = fdCode;
    }

    public String getFdPrivRangeSid() {
        return fdPrivRangeSid;
    }

    public void setFdPrivRangeSid(String fdPrivRangeSid) {
        this.fdPrivRangeSid = fdPrivRangeSid;
    }

    public String getFdPrivRangeName() {
        return fdPrivRangeName;
    }

    public void setFdPrivRangeName(String fdPrivRangeName) {
        this.fdPrivRangeName = fdPrivRangeName;
    }

    public String getFdPrivRangeCode() {
        return fdPrivRangeCode;
    }

    public void setFdPrivRangeCode(String fdPrivRangeCode) {
        this.fdPrivRangeCode = fdPrivRangeCode;
    }

    public String getFdDataRangeSid() {
        return fdDataRangeSid;
    }

    public void setFdDataRangeSid(String fdDataRangeSid) {
        this.fdDataRangeSid = fdDataRangeSid;
    }

    public String getFdDataRangeName() {
        return fdDataRangeName;
    }

    public void setFdDataRangeName(String fdDataRangeName) {
        this.fdDataRangeName = fdDataRangeName;
    }

    public String getFdDataRangeCode() {
        return fdDataRangeCode;
    }

    public void setFdDataRangeCode(String fdDataRangeCode) {
        this.fdDataRangeCode = fdDataRangeCode;
    }

    public String getFdDataRangePcode() {
        return fdDataRangePcode;
    }

    public void setFdDataRangePcode(String fdDataRangePcode) {
        this.fdDataRangePcode = fdDataRangePcode;
    }

    public String getFdOrder() {
        return fdOrder;
    }

    public void setFdOrder(String fdOrder) {
        this.fdOrder = fdOrder;
    }

    public String getFdCreateTime() {
        return fdCreateTime;
    }

    public void setFdCreateTime(String fdCreateTime) {
        this.fdCreateTime = fdCreateTime;
    }

    public String getFdUpdateTime() {
        return fdUpdateTime;
    }

    public void setFdUpdateTime(String fdUpdateTime) {
        this.fdUpdateTime = fdUpdateTime;
    }

    public List<FdDataPrivListDto> getFdDataPrivList() {
        return fdDataPrivList;
    }

    public void setFdDataPrivList(List<FdDataPrivListDto> fdDataPrivList) {
        this.fdDataPrivList = fdDataPrivList;
    }

    public String[] getFdStandardRoleSids() {
        return fdStandardRoleSids;
    }

    public void setFdStandardRoleSids(String[] fdStandardRoleSids) {
        this.fdStandardRoleSids = fdStandardRoleSids;
    }
}