package com.tahoecn.customerservice.model.dto;

import java.io.Serializable;

public class UserStandardRoleDto implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String fdOrgSid;
    private String fdOrgType;
    private String fdOrgSidTree;
    private String fdOrgNameTree;
    private String fdOrgName;
    private String fdSid;
    private String fdName;
    private String fdCode;
    private String fdPsid;
    private String fdLevel;
    private String fdOrder;
    private String fdInitiatingProcess;
    private String fdIsOrg;
    private String fdCreateTime;
    private String fdUpdateTime;
    
    
	public String getFdOrgSid() {
		return fdOrgSid;
	}
	public void setFdOrgSid(String fdOrgSid) {
		this.fdOrgSid = fdOrgSid;
	}
	public String getFdOrgType() {
		return fdOrgType;
	}
	public void setFdOrgType(String fdOrgType) {
		this.fdOrgType = fdOrgType;
	}
	public String getFdOrgSidTree() {
		return fdOrgSidTree;
	}
	public void setFdOrgSidTree(String fdOrgSidTree) {
		this.fdOrgSidTree = fdOrgSidTree;
	}
	public String getFdOrgNameTree() {
		return fdOrgNameTree;
	}
	public void setFdOrgNameTree(String fdOrgNameTree) {
		this.fdOrgNameTree = fdOrgNameTree;
	}
	public String getFdOrgName() {
		return fdOrgName;
	}
	public void setFdOrgName(String fdOrgName) {
		this.fdOrgName = fdOrgName;
	}
	public String getFdSid() {
		return fdSid;
	}
	public void setFdSid(String fdSid) {
		this.fdSid = fdSid;
	}
	public String getFdName() {
		return fdName;
	}
	public void setFdName(String fdName) {
		this.fdName = fdName;
	}
	public String getFdCode() {
		return fdCode;
	}
	public void setFdCode(String fdCode) {
		this.fdCode = fdCode;
	}
	public String getFdPsid() {
		return fdPsid;
	}
	public void setFdPsid(String fdPsid) {
		this.fdPsid = fdPsid;
	}
	public String getFdLevel() {
		return fdLevel;
	}
	public void setFdLevel(String fdLevel) {
		this.fdLevel = fdLevel;
	}
	public String getFdOrder() {
		return fdOrder;
	}
	public void setFdOrder(String fdOrder) {
		this.fdOrder = fdOrder;
	}
	public String getFdInitiatingProcess() {
		return fdInitiatingProcess;
	}
	public void setFdInitiatingProcess(String fdInitiatingProcess) {
		this.fdInitiatingProcess = fdInitiatingProcess;
	}
	public String getFdIsOrg() {
		return fdIsOrg;
	}
	public void setFdIsOrg(String fdIsOrg) {
		this.fdIsOrg = fdIsOrg;
	}
	public String getFdCreateTime() {
		return fdCreateTime;
	}
	public void setFdCreateTime(String fdCreateTime) {
		this.fdCreateTime = fdCreateTime;
	}
	public String getFdUpdateTime() {
		return fdUpdateTime;
	}
	public void setFdUpdateTime(String fdUpdateTime) {
		this.fdUpdateTime = fdUpdateTime;
	}
}
