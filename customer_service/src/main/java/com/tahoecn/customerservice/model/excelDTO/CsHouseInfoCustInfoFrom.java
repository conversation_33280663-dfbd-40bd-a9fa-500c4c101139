package com.tahoecn.customerservice.model.excelDTO;

import java.util.Date;

import cn.afterturn.easypoi.excel.annotation.Excel;

public class CsHouseInfoCustInfoFrom {
	
	@Excel(name = "房屋编号")
	private String houseNum;
	@Excel(name = "房屋名称")
	private String houseName;
	@Excel(name = "区域")
	private String area;
	@Excel(name = "城市")
	private String city;
	@Excel(name = "项目")
	private String project;
/*	@Excel(name = "项目分期")
	private String */
	@Excel(name = "楼号")
	private String building;
	@Excel(name = "单元号")
	private String unit;
	@Excel(name = "房号")
	private String roomNum;
	@Excel(name = "房屋装修类型")
	private String fitment;
	@Excel(name = "产品类型")
	private String useProperty;
	@Excel(name = "签约时间" ,format = "yyyy-MM-dd HH:mm:ss")
	private Date signDate;
	@Excel(name = "合同交付时间",format = "yyyy-MM-dd HH:mm:ss")
	private Date deliveryDate;
	@Excel(name = "集中交房时间从",format = "yyyy-MM-dd HH:mm:ss")
	private Date  focusStartDate;
	@Excel(name = "集中交房时间到 ",format = "yyyy-MM-dd HH:mm:ss")
	private Date focusEndDate;
	@Excel(name = "实际交付时间",format = "yyyy-MM-dd HH:mm:ss")
	private Date actualDeliveryDate;
	@Excel(name = "脱保时间",format = "yyyy-MM-dd HH:mm:ss")
	private Date offAidDate;
	@Excel(name = "取证时间",format = "yyyy-MM-dd HH:mm:ss")
	private Date obtainTime;
	@Excel(name = "入住时间",format = "yyyy-MM-dd HH:mm:ss")
	private Date stayTime;
	@Excel(name = "交付状态")
	private String deliveryStatus;
	
	//客户表部分
	@Excel(name = "业主姓名")
	private String custName;
	@Excel(name = "移动电话")
	private String telephone;
	@Excel(name = "固定电话")
	private String fixed_telephone;
	@Excel(name = "传真电话")
	private String fax;
	@Excel(name = "证件类型")
	private String certificateName;
	@Excel(name = "证件号码")
	private String certificateNum;
	@Excel(name = "客户身份")
	private String cusIdentity;
	@Excel(name = "个人/单位")
	private String belong;
	@Excel(name = "性别")
	private String sex;
	@Excel(name = "VIP")
	private String is_vip;
	@Excel(name = "意见领袖")
	private String sugLeader;
	@Excel(name = "省(直辖市、自治区)")
	private String province;
	/**
	 * 表中别名，防止上下重复
	 */
	@Excel(name = "市")
	private String custCity;
	/**
	 * 表中别名，防止上下重复
	 */
	@Excel(name = "区(县)")
	private String custArea;
	@Excel(name = "联系地址")
	private String contactAddress;
	@Excel(name = "邮政编码")
	private String postcode;
	@Excel(name = "电子邮件")
	private String email;
	@Excel(name = "微信")
	private String wx;
	@Excel(name = "管家姓名")
	private String stewardName;
	@Excel(name = "管家电话")
	private String stewardTelephone;
	@Excel(name = "国籍")
	private String national;
	@Excel(name = "出生日期")
	private String birthday;
	@Excel(name = "工作单位")
	private String workUnit;
	@Excel(name = "职业")
	private String profession;
	@Excel(name = "年收入")
	private String yearsReceive;
	@Excel(name = "福州金卡用户")
	private String isFZgoldCard;
	@Excel(name = "泰禾拥有多套房屋")
	private String isHasMoreHouse;
	@Excel(name = "医疗板块客户")
	private String isMedicalCareUser;
	@Excel(name = "金融板块客户")
	private String isFinanceUser;
	@Excel(name = "地产板块客户")
	private String isRealEstateUser;
	@Excel(name = "教育板块客户")
	private String isEducationUser;
	@Excel(name = "影院教育板块客户")
	private String  isCinemaUser;
	@Excel(name = "拥有车辆")
	private String hasCar;
	@Excel(name = "兴趣爱好")
	private String hobbies;
	@Excel(name = "客户积分")
	private String jf;
	@Excel(name = "是否为其他版块会员")
	private String otherBoardMember;
	@Excel(name = "是否为特殊客户")
	private String specialCustomer;
	public String getHouseNum() {
		return houseNum;
	}
	public void setHouseNum(String houseNum) {
		this.houseNum = houseNum;
	}
	public String getHouseName() {
		return houseName;
	}
	public void setHouseName(String houseName) {
		this.houseName = houseName;
	}
	public String getArea() {
		return area;
	}
	public void setArea(String area) {
		this.area = area;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getProject() {
		return project;
	}
	public void setProject(String project) {
		this.project = project;
	}
	public String getBuilding() {
		return building;
	}
	public void setBuilding(String building) {
		this.building = building;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getRoomNum() {
		return roomNum;
	}
	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}
	public String getFitment() {
		return fitment;
	}
	public void setFitment(String fitment) {
		this.fitment = fitment;
	}
	public String getUseProperty() {
		return useProperty;
	}
	public void setUseProperty(String useProperty) {
		this.useProperty = useProperty;
	}
	public Date getSignDate() {
		return signDate;
	}
	public void setSignDate(Date signDate) {
		this.signDate = signDate;
	}
	public Date getDeliveryDate() {
		return deliveryDate;
	}
	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}
	public Date getFocusStartDate() {
		return focusStartDate;
	}
	public void setFocusStartDate(Date focusStartDate) {
		this.focusStartDate = focusStartDate;
	}
	public Date getFocusEndDate() {
		return focusEndDate;
	}
	public void setFocusEndDate(Date focusEndDate) {
		this.focusEndDate = focusEndDate;
	}
	public Date getActualDeliveryDate() {
		return actualDeliveryDate;
	}
	public void setActualDeliveryDate(Date actualDeliveryDate) {
		this.actualDeliveryDate = actualDeliveryDate;
	}
	public Date getOffAidDate() {
		return offAidDate;
	}
	public void setOffAidDate(Date offAidDate) {
		this.offAidDate = offAidDate;
	}
	public Date getObtainTime() {
		return obtainTime;
	}
	public void setObtainTime(Date obtainTime) {
		this.obtainTime = obtainTime;
	}
	public Date getStayTime() {
		return stayTime;
	}
	public void setStayTime(Date stayTime) {
		this.stayTime = stayTime;
	}
	public String getDeliveryStatus() {
		return deliveryStatus;
	}
	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getTelephone() {
		return telephone;
	}
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
	public String getFixed_telephone() {
		return fixed_telephone;
	}
	public void setFixed_telephone(String fixed_telephone) {
		this.fixed_telephone = fixed_telephone;
	}
	public String getFax() {
		return fax;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	public String getCertificateName() {
		return certificateName;
	}
	public void setCertificateName(String certificateName) {
		this.certificateName = certificateName;
	}
	public String getCertificateNum() {
		return certificateNum;
	}
	public void setCertificateNum(String certificateNum) {
		this.certificateNum = certificateNum;
	}
	public String getCusIdentity() {
		return cusIdentity;
	}
	public void setCusIdentity(String cusIdentity) {
		this.cusIdentity = cusIdentity;
	}
	public String getBelong() {
		return belong;
	}
	public void setBelong(String belong) {
		this.belong = belong;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getIs_vip() {
		return is_vip;
	}
	public void setIs_vip(String is_vip) {
		this.is_vip = is_vip;
	}
	public String getSugLeader() {
		return sugLeader;
	}
	public void setSugLeader(String sugLeader) {
		this.sugLeader = sugLeader;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCustCity() {
		return custCity;
	}
	public void setCustCity(String custCity) {
		this.custCity = custCity;
	}
	public String getCustArea() {
		return custArea;
	}
	public void setCustArea(String custArea) {
		this.custArea = custArea;
	}
	public String getContactAddress() {
		return contactAddress;
	}
	public void setContactAddress(String contactAddress) {
		this.contactAddress = contactAddress;
	}
	public String getPostcode() {
		return postcode;
	}
	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getStewardName() {
		return stewardName;
	}
	public void setStewardName(String stewardName) {
		this.stewardName = stewardName;
	}
	public String getStewardTelephone() {
		return stewardTelephone;
	}
	public void setStewardTelephone(String stewardTelephone) {
		this.stewardTelephone = stewardTelephone;
	}
	public String getNational() {
		return national;
	}
	public void setNational(String national) {
		this.national = national;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getWorkUnit() {
		return workUnit;
	}
	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}
	public String getProfession() {
		return profession;
	}
	public void setProfession(String profession) {
		this.profession = profession;
	}
	public String getYearsReceive() {
		return yearsReceive;
	}
	public void setYearsReceive(String yearsReceive) {
		this.yearsReceive = yearsReceive;
	}
	public String getIsFZgoldCard() {
		return isFZgoldCard;
	}
	public void setIsFZgoldCard(String isFZgoldCard) {
		this.isFZgoldCard = isFZgoldCard;
	}
	public String getIsHasMoreHouse() {
		return isHasMoreHouse;
	}
	public void setIsHasMoreHouse(String isHasMoreHouse) {
		this.isHasMoreHouse = isHasMoreHouse;
	}
	public String getIsMedicalCareUser() {
		return isMedicalCareUser;
	}
	public void setIsMedicalCareUser(String isMedicalCareUser) {
		this.isMedicalCareUser = isMedicalCareUser;
	}
	public String getIsFinanceUser() {
		return isFinanceUser;
	}
	public void setIsFinanceUser(String isFinanceUser) {
		this.isFinanceUser = isFinanceUser;
	}
	public String getIsRealEstateUser() {
		return isRealEstateUser;
	}
	public void setIsRealEstateUser(String isRealEstateUser) {
		this.isRealEstateUser = isRealEstateUser;
	}
	public String getIsEducationUser() {
		return isEducationUser;
	}
	public void setIsEducationUser(String isEducationUser) {
		this.isEducationUser = isEducationUser;
	}
	public String getIsCinemaUser() {
		return isCinemaUser;
	}
	public void setIsCinemaUser(String isCinemaUser) {
		this.isCinemaUser = isCinemaUser;
	}
	public String getHasCar() {
		return hasCar;
	}
	public void setHasCar(String hasCar) {
		this.hasCar = hasCar;
	}
	public String getHobbies() {
		return hobbies;
	}
	public void setHobbies(String hobbies) {
		this.hobbies = hobbies;
	}
	public String getOtherBoardMember() {
		return otherBoardMember;
	}
	public void setOtherBoardMember(String otherBoardMember) {
		this.otherBoardMember = otherBoardMember;
	}
	public String getSpecialCustomer() {
		return specialCustomer;
	}
	public void setSpecialCustomer(String specialCustomer) {
		this.specialCustomer = specialCustomer;
	}
	public String getWx() {
		return wx;
	}
	public void setWx(String wx) {
		this.wx = wx;
	}
	public String getJf() {
		return jf;
	}
	public void setJf(String jf) {
		this.jf = jf;
	}
	
	
}
