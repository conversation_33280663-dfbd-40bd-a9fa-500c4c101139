package com.tahoecn.customerservice.model.excelDTO;

import java.util.Date;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * @ClassName DeliverDto
 * <AUTHOR>
 * @date 2019年7月17日
 */
public class DeliverDto {

	@Excel(name = "组团区域")
	private String attribute1;
	@Excel(name = "楼宇名称")
	private String building;
	@Excel(name = "房屋编号")
	private String houseNum;
	@Excel(name = "房屋名称")
	private String houseName;
	@Excel(name = "单元")
	private String unit;
	@Excel(name = "楼层")
	private String attribute2;
	@Excel(name = "面积类型")
	private String attribute3;
	@Excel(name = "建筑面积", format = "#,###.00")
	private String attribute4;
	@Excel(name = "套内面积（平米）", format = "#,###.00")
	private String attribute5;
	@Excel(name = "公摊面积（平米）", format = "#,###.00")
	private String attribute6;
	@Excel(name = "花园面积（平米）", format = "#,###.00")
	private String attribute7;
	@Excel(name = "庭院面积（平米）", format = "#,###.00")
	private String attribute8;
	@Excel(name = "产权性质")
	private String attribute9;
	@Excel(name = "使用性质")
	private String useProperty;
	@Excel(name = "使用状态")
	private String attribute10;
	@Excel(name = "房屋状态")
	private String myStatus;
	@Excel(name = "合同交房时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date deliveryDate;
	@Excel(name = "物业接管时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date attribute11;
	@Excel(name = "集中交房时间从", format = "yyyy-MM-dd HH:mm:ss")
	private Date focusStartDate;
	@Excel(name = "到", format = "yyyy-MM-dd HH:mm:ss")
	private Date focusEndDate;
	@Excel(name = "实际交房时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date actualDeliveryDate;
	@Excel(name = "装修时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date attribute12;
	@Excel(name = "是否精装")
	private String fitment;
	@Excel(name = "交付状态")
	private String deliveryStatus;
	@Excel(name = "业主名称")
	private String custName;
	@Excel(name = "性别")
	private String sex;
	@Excel(name = "国籍")
	private String national;
	@Excel(name = "出生日期")
	private String birthday;
	@Excel(name = "工作单位")
	private String workUnit;
	@Excel(name = "职业")
	private String profession;
	@Excel(name = "证件名称")
	private String certificateName;
	@Excel(name = "证件号码")
	private String certificateNum;
	@Excel(name = "固定电话")
	private String fixedTelephone;
	@Excel(name = "移动电话")
	private String telephone;
	@Excel(name = "传真电话")
	private String fax;
	@Excel(name = "联系地址")
	private String contactAddress;
	@Excel(name = "邮政编码")
	private String postcode;
	@Excel(name = "电子邮件")
	private String email;
	
	public String getAttribute1() {
		return attribute1;
	}
	public void setAttribute1(String attribute1) {
		this.attribute1 = attribute1;
	}
	public String getBuilding() {
		return building;
	}
	public void setBuilding(String building) {
		this.building = building;
	}
	public String getHouseNum() {
		return houseNum;
	}
	public void setHouseNum(String houseNum) {
		this.houseNum = houseNum;
	}
	public String getHouseName() {
		return houseName;
	}
	public void setHouseName(String houseName) {
		this.houseName = houseName;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getAttribute2() {
		return attribute2;
	}
	public void setAttribute2(String attribute2) {
		this.attribute2 = attribute2;
	}
	public String getAttribute3() {
		return attribute3;
	}
	public void setAttribute3(String attribute3) {
		this.attribute3 = attribute3;
	}
	public String getAttribute4() {
		return attribute4;
	}
	public void setAttribute4(String attribute4) {
		this.attribute4 = attribute4;
	}
	public String getAttribute5() {
		return attribute5;
	}
	public void setAttribute5(String attribute5) {
		this.attribute5 = attribute5;
	}
	public String getAttribute6() {
		return attribute6;
	}
	public void setAttribute6(String attribute6) {
		this.attribute6 = attribute6;
	}
	public String getAttribute7() {
		return attribute7;
	}
	public void setAttribute7(String attribute7) {
		this.attribute7 = attribute7;
	}
	public String getAttribute8() {
		return attribute8;
	}
	public void setAttribute8(String attribute8) {
		this.attribute8 = attribute8;
	}
	public String getAttribute9() {
		return attribute9;
	}
	public void setAttribute9(String attribute9) {
		this.attribute9 = attribute9;
	}
	public String getUseProperty() {
		return useProperty;
	}
	public void setUseProperty(String useProperty) {
		this.useProperty = useProperty;
	}
	public String getAttribute10() {
		return attribute10;
	}
	public void setAttribute10(String attribute10) {
		this.attribute10 = attribute10;
	}
	public String getMyStatus() {
		return myStatus;
	}
	public void setMyStatus(String myStatus) {
		this.myStatus = myStatus;
	}
	public Date getDeliveryDate() {
		return deliveryDate;
	}
	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}
	public Date getAttribute11() {
		return attribute11;
	}
	public void setAttribute11(Date attribute11) {
		this.attribute11 = attribute11;
	}
	public Date getFocusStartDate() {
		return focusStartDate;
	}
	public void setFocusStartDate(Date focusStartDate) {
		this.focusStartDate = focusStartDate;
	}
	public Date getFocusEndDate() {
		return focusEndDate;
	}
	public void setFocusEndDate(Date focusEndDate) {
		this.focusEndDate = focusEndDate;
	}
	public Date getActualDeliveryDate() {
		return actualDeliveryDate;
	}
	public void setActualDeliveryDate(Date actualDeliveryDate) {
		this.actualDeliveryDate = actualDeliveryDate;
	}
	public Date getAttribute12() {
		return attribute12;
	}
	public void setAttribute12(Date attribute12) {
		this.attribute12 = attribute12;
	}
	public String getFitment() {
		return fitment;
	}
	public void setFitment(String fitment) {
		this.fitment = fitment;
	}
	public String getDeliveryStatus() {
		return "1".equals(deliveryStatus)?"已交付":"未交付";
	}
	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getNational() {
		return national;
	}
	public void setNational(String national) {
		this.national = national;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getWorkUnit() {
		return workUnit;
	}
	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}
	public String getProfession() {
		return profession;
	}
	public void setProfession(String profession) {
		this.profession = profession;
	}
	public String getCertificateName() {
		return certificateName;
	}
	public void setCertificateName(String certificateName) {
		this.certificateName = certificateName;
	}
	public String getCertificateNum() {
		return certificateNum;
	}
	public void setCertificateNum(String certificateNum) {
		this.certificateNum = certificateNum;
	}
	public String getFixedTelephone() {
		return fixedTelephone;
	}
	public void setFixedTelephone(String fixedTelephone) {
		this.fixedTelephone = fixedTelephone;
	}
	public String getTelephone() {
		return telephone;
	}
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
	public String getFax() {
		return fax;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	public String getContactAddress() {
		return contactAddress;
	}
	public void setContactAddress(String contactAddress) {
		this.contactAddress = contactAddress;
	}
	public String getPostcode() {
		return postcode;
	}
	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}

}
