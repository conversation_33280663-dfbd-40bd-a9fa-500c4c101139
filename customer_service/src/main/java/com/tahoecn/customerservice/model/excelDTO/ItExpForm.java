/**
 * 
 */
package com.tahoecn.customerservice.model.excelDTO;

import java.util.Date;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * <AUTHOR>
 *
 */
public class ItExpForm {

	private String id;
	@Excel(name = "工单编号")
	private String formNo;
	@Excel(name = "项目")
	private String project;
	@Excel(name = "用户名")
	private String ownerName;
	@Excel(name = "电话")
	private String mobile;
	@Excel(name = "报事渠道")
	private String reportChannelName;
	@Excel(name = "业务步骤")
	private String processStateName;
	@Excel(name = "流程类别")
	private String processName;
	@Excel(name = "部门")
	private String deptName;
	@Excel(name = "一级分类")
	private String firstSortName;
	@Excel(name = "二级分类")
	private String secSortName;
	@Excel(name = "客户诉求")
	private String customerDemand;
	@Excel(name = "处理结果")
	private String handleRecord;
	@Excel(name = "创建时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date creationDate;
	@Excel(name = "分派时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date assignDate;
//	@Excel(name = "处理时间", format = "yyyy-MM-dd HH:mm:ss")
//	private Date submitDate;
	@Excel(name = "处理时间", format = "yyyy-MM-dd HH:mm:ss")
	private Date runTime;
	@Excel(name = "录入人")
	private String createUserName;
	@Excel(name = "当前处理人")
	private String curAssigneeName;

	private String houseNo;
	private String region;
	private String city;
	private String buildingNo;
	private String buildingUnit;
	private String roomNo;
	private String housekeeperName;
	private String housekeeperTel;
	private String acceptChannelCode;
	private String thirdSortName;
	private String fourthSortName;
	private String problemLevelName;
	private String problemPositionName;
	private String revisionClassificationName;
	private String decorationStageName;
	private String interResDepartmentName;
	private String maintenancePeriodName;
	private String repairUnit;
	private String thirdPartyFlag;
	private String thirdPartyReason;
	private String thirdPartyName;
	private String reworkFlag;
	private String upgradeFlag;
	private Date doTime;
	private Date upTime;
	private String upUser;
	private String doUser;

	public String getFormNo() {
		return formNo;
	}

	public void setFormNo(String formNo) {
		this.formNo = formNo;
	}

	public String getHouseNo() {
		return houseNo;
	}

	public void setHouseNo(String houseNo) {
		this.houseNo = houseNo;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getBuildingNo() {
		return buildingNo;
	}

	public void setBuildingNo(String buildingNo) {
		this.buildingNo = buildingNo;
	}

	public String getBuildingUnit() {
		return buildingUnit;
	}

	public void setBuildingUnit(String buildingUnit) {
		this.buildingUnit = buildingUnit;
	}

	public String getRoomNo() {
		return roomNo;
	}

	public void setRoomNo(String roomNo) {
		this.roomNo = roomNo;
	}

	public String getOwnerName() {
		return ownerName;
	}

	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getHousekeeperName() {
		return housekeeperName;
	}

	public void setHousekeeperName(String housekeeperName) {
		this.housekeeperName = housekeeperName;
	}

	public String getHousekeeperTel() {
		return housekeeperTel;
	}

	public void setHousekeeperTel(String housekeeperTel) {
		this.housekeeperTel = housekeeperTel;
	}

	public String getReportChannelName() {
		return reportChannelName;
	}

	public void setReportChannelName(String reportChannelName) {
		this.reportChannelName = reportChannelName;
	}

	public String getAcceptChannelCode() {
		return acceptChannelCode;
	}

	public void setAcceptChannelCode(String acceptChannelCode) {
		this.acceptChannelCode = acceptChannelCode;
	}

	public String getProcessStateName() {
		return processStateName;
	}

	public void setProcessStateName(String processStateName) {
		this.processStateName = processStateName;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getFirstSortName() {
		return firstSortName;
	}

	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}

	public String getSecSortName() {
		return secSortName;
	}

	public void setSecSortName(String secSortName) {
		this.secSortName = secSortName;
	}

	public String getThirdSortName() {
		return thirdSortName;
	}

	public void setThirdSortName(String thirdSortName) {
		this.thirdSortName = thirdSortName;
	}

	public String getFourthSortName() {
		return fourthSortName;
	}

	public void setFourthSortName(String fourthSortName) {
		this.fourthSortName = fourthSortName;
	}

	public String getProblemLevelName() {
		return problemLevelName;
	}

	public void setProblemLevelName(String problemLevelName) {
		this.problemLevelName = problemLevelName;
	}

	public String getProblemPositionName() {
		return problemPositionName;
	}

	public void setProblemPositionName(String problemPositionName) {
		this.problemPositionName = problemPositionName;
	}

	public String getRevisionClassificationName() {
		return revisionClassificationName;
	}

	public void setRevisionClassificationName(String revisionClassificationName) {
		this.revisionClassificationName = revisionClassificationName;
	}

	public String getDecorationStageName() {
		return decorationStageName;
	}

	public void setDecorationStageName(String decorationStageName) {
		this.decorationStageName = decorationStageName;
	}

	public String getInterResDepartmentName() {
		return interResDepartmentName;
	}

	public void setInterResDepartmentName(String interResDepartmentName) {
		this.interResDepartmentName = interResDepartmentName;
	}

	public String getMaintenancePeriodName() {
		return maintenancePeriodName;
	}

	public void setMaintenancePeriodName(String maintenancePeriodName) {
		this.maintenancePeriodName = maintenancePeriodName;
	}

	public String getCustomerDemand() {
		return customerDemand;
	}

	public void setCustomerDemand(String customerDemand) {
		this.customerDemand = customerDemand;
	}

	public String getHandleRecord() {
		return handleRecord;
	}

	public void setHandleRecord(String handleRecord) {
		this.handleRecord = handleRecord;
	}

	public String getRepairUnit() {
		return repairUnit;
	}

	public void setRepairUnit(String repairUnit) {
		this.repairUnit = repairUnit;
	}

	public String getThirdPartyFlag() {
		return "1".equals(thirdPartyFlag)?"是":"否";
	}

	public void setThirdPartyFlag(String thirdPartyFlag) {
		this.thirdPartyFlag = thirdPartyFlag;
	}

	public String getThirdPartyReason() {
		return thirdPartyReason;
	}

	public void setThirdPartyReason(String thirdPartyReason) {
		this.thirdPartyReason = thirdPartyReason;
	}

	public String getThirdPartyName() {
		return thirdPartyName;
	}

	public void setThirdPartyName(String thirdPartyName) {
		this.thirdPartyName = thirdPartyName;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Date getAssignDate() {
		return assignDate;
	}

	public void setAssignDate(Date assignDate) {
		this.assignDate = assignDate;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getCurAssigneeName() {
		return curAssigneeName;
	}

	public void setCurAssigneeName(String curAssigneeName) {
		this.curAssigneeName = curAssigneeName;
	}

	public String getReworkFlag() {
		return "1".equals(reworkFlag)?"是":"否";
	}

	public void setReworkFlag(String reworkFlag) {
		this.reworkFlag = reworkFlag;
	}

	public String getUpgradeFlag() {
		return "1".equals(upgradeFlag)?"是":"否";
	}

	public void setUpgradeFlag(String upgradeFlag) {
		this.upgradeFlag = upgradeFlag;
	}

	public String getUpUser() {
		return upUser;
	}

	public void setUpUser(String upUser) {
		this.upUser = upUser;
	}

	public Date getUpTime() {
		return upTime;
	}

	public void setUpTime(Date upTime) {
		this.upTime = upTime;
	}

	public String getDoUser() {
		return doUser;
	}

	public void setDoUser(String doUser) {
		this.doUser = doUser;
	}

	public Date getDoTime() {
		return doTime;
	}

	public void setDoTime(Date doTime) {
		this.doTime = doTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getRunTime() {
		return runTime;
	}

	public void setRunTime(Date runTime) {
		this.runTime = runTime;
	}
	
}
