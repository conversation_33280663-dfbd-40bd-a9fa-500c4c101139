package com.tahoecn.customerservice.model.my;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
@TableName("View_kf_Customer_new")
public class SyncMyCust implements Serializable {

    private static final long serialVersionUID = 1L;

    private String custId;
    private String custName;
    private String certificateName;
    private String certificateNum;
    private String fixedTelephone;
    private String telephone;
    private String faxTelephone;
    private String contactAddress;
    @TableField("PostCode")
    private String PostCode;
    @TableField("Email")
    private String Email;
    private String belong;
    private String sex;
    private String national;
    private String birthday;
    private String workUnit;
    private String profession;
    private String updateDate;
    private String projid;


    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public String getFixedTelephone() {
        return fixedTelephone;
    }

    public void setFixedTelephone(String fixedTelephone) {
        this.fixedTelephone = fixedTelephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFaxTelephone() {
        return faxTelephone;
    }

    public void setFaxTelephone(String faxTelephone) {
        this.faxTelephone = faxTelephone;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getPostCode() {
        return PostCode;
    }

    public void setPostCode(String PostCode) {
        this.PostCode = PostCode;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getBelong() {
        return belong;
    }

    public void setBelong(String belong) {
        this.belong = belong;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNational() {
        return national;
    }

    public void setNational(String national) {
        this.national = national;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getProjid() {
        return projid;
    }

    public void setProjid(String projid) {
        this.projid = projid;
    }

    @Override
    public String toString() {
        return "CsSyncMyCust{" +
        "custId=" + custId +
        ", custName=" + custName +
        ", certificateName=" + certificateName +
        ", certificateNum=" + certificateNum +
        ", fixedTelephone=" + fixedTelephone +
        ", telephone=" + telephone +
        ", faxTelephone=" + faxTelephone +
        ", contactAddress=" + contactAddress +
        ", PostCode=" + PostCode +
        ", Email=" + Email +
        ", belong=" + belong +
        ", sex=" + sex +
        ", national=" + national +
        ", birthday=" + birthday +
        ", workUnit=" + workUnit +
        ", profession=" + profession +
        ", updateDate=" + updateDate +
        ", projid=" + projid +
        "}";
    }
}
