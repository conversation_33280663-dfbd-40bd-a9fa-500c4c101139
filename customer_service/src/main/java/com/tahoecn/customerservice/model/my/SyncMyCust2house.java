package com.tahoecn.customerservice.model.my;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotations.TableName;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
@TableName("View_kf_room2customer")
public class SyncMyCust2house implements Serializable {

    private static final long serialVersionUID = 1L;

    private String houseId;
    private String roomCode;
    private String custId;
    private String certificateNum;
    private String cqDate;


    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getRoomCode() {
		return roomCode;
	}

	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}

	public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public String getCqDate() {
        return cqDate;
    }

    public void setCqDate(String cqDate) {
        this.cqDate = cqDate;
    }

    @Override
    public String toString() {
        return "CsSyncMyCust2house{" +
        "houseId=" + houseId +
        ", roomCode=" + roomCode +
        ", custId=" + custId +
        ", certificateNum=" + certificateNum +
        ", cqDate=" + cqDate +
        "}";
    }
}
