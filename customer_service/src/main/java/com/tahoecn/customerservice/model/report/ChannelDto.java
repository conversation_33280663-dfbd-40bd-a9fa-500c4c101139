/**
 * 
 */
package com.tahoecn.customerservice.model.report;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 报事渠道统计
 * 
 * @ClassName ChannelDto
 * <AUTHOR>
 * @date 2019年1月9日
 */
public class ChannelDto {

	/**
	 * 区域CODE
	 */
	private String regionCode;

	@Excel(name = "区域")
	private String region;

	/**
	 * 城市CODE
	 */
	private String cityCode;

	@Excel(name = "城市")
	private String city;

	/**
	 * 项目CODE
	 */
	private String projectCode;

	@Excel(name = "项目")
	private String project;

	/**
	 * 一级分类CODE
	 */
	private String firstSortCode;

	@Excel(name = "一级分类")
	private String firstSortName;

	/**
	 * 报事渠道
	 */
	private String reportChannelCode;

	@Excel(name = "报事渠道")
	private String reportChannelName;
	@Excel(name = "数量")
	private String reportChannelNum;

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getFirstSortCode() {
		return firstSortCode;
	}

	public void setFirstSortCode(String firstSortCode) {
		this.firstSortCode = firstSortCode;
	}

	public String getFirstSortName() {
		return firstSortName;
	}

	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}

	public String getReportChannelCode() {
		return reportChannelCode;
	}

	public void setReportChannelCode(String reportChannelCode) {
		this.reportChannelCode = reportChannelCode;
	}

	public String getReportChannelName() {
		return reportChannelName;
	}

	public void setReportChannelName(String reportChannelName) {
		this.reportChannelName = reportChannelName;
	}

	public String getReportChannelNum() {
		return reportChannelNum;
	}

	public void setReportChannelNum(String reportChannelNum) {
		this.reportChannelNum = reportChannelNum;
	}

}
