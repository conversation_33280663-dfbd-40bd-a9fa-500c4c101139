/**
 * 
 */
package com.tahoecn.customerservice.model.report;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * @ClassName ClassifyDto
 * <AUTHOR>
 * @date 2019年1月10日
 */
public class ClassifyDto {
	

	/**
	 * 区域CODE
	 */
	private String regionCode;

	@Excel(name = "区域")
	private String region;

	/**
	 * 城市CODE
	 */
	private String cityCode;

	@Excel(name = "城市")
	private String city;

	/**
	 * 项目CODE
	 */
	private String projectCode;

	@Excel(name = "项目")
	private String project;

	/**
	 * 一级分类CODE
	 */
	private String firstSortCode;

	@Excel(name = "一级分类")
	private String firstSortName;
	
	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer firstTotal;

	/**
	 * 二级分类CODE
	 */
	private String secSortCode;

	@Excel(name = "二级分类")
	private String secSortName;
	
	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer secTotal;

	/**
	 * 三级分类CODE
	 */
	private String thirdSortCode;

	@Excel(name = "三级分类")
	private String thirdSortName;
	
	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer thirdTotal;

	/**
	 * 四级分类CODE
	 */
	private String fourthSortCode;

	@Excel(name = "四级分类")
	private String fourthSortName;
	
	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer fourthTotal;
	
	public ClassifyDto() {
		super();
	}
	
	public ClassifyDto(SortTreeDto f,SortTreeDto s,SortTreeDto t,SortTreeDto fo) {
		super();
		// 一级分类
		this.setRegion(f.getRegion());
		this.setRegionCode(f.getRegionCode());
		this.setCity(f.getCity());
		this.setCityCode(f.getCityCode());
		this.setProject(f.getProject());
		this.setProjectCode(f.getProjectCode());
		this.setFirstSortCode(f.getFirstSortCode());
		this.setFirstSortName(f.getFirstSortName());
		this.setFirstTotal(f.getTotal());
		
		// 二级分类
		this.setSecSortCode(s.getSecSortCode());
		this.setSecSortName(s.getSecSortName());
		this.setSecTotal(s.getTotal());
		
		// 三级分类
		if(t != null) {
			this.setThirdSortCode(t.getThirdSortCode());
			this.setThirdSortName(t.getThirdSortName());
			this.setThirdTotal(t.getTotal());
		}
		
		// 四级分类
		if(fo != null) {
			this.setFourthSortCode(fo.getFourthSortCode());
			this.setFourthSortName(fo.getFourthSortName());
			this.setFourthTotal(fo.getTotal());
		}
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getFirstSortCode() {
		return firstSortCode;
	}

	public void setFirstSortCode(String firstSortCode) {
		this.firstSortCode = firstSortCode;
	}

	public String getFirstSortName() {
		return firstSortName;
	}

	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}

	public Integer getFirstTotal() {
		return firstTotal;
	}

	public void setFirstTotal(Integer firstTotal) {
		this.firstTotal = firstTotal;
	}

	public String getSecSortCode() {
		return secSortCode;
	}

	public void setSecSortCode(String secSortCode) {
		this.secSortCode = secSortCode;
	}

	public String getSecSortName() {
		return secSortName;
	}

	public void setSecSortName(String secSortName) {
		this.secSortName = secSortName;
	}

	public Integer getSecTotal() {
		return secTotal;
	}

	public void setSecTotal(Integer secTotal) {
		this.secTotal = secTotal;
	}

	public String getThirdSortCode() {
		return thirdSortCode;
	}

	public void setThirdSortCode(String thirdSortCode) {
		this.thirdSortCode = thirdSortCode;
	}

	public String getThirdSortName() {
		return thirdSortName;
	}

	public void setThirdSortName(String thirdSortName) {
		this.thirdSortName = thirdSortName;
	}

	public Integer getThirdTotal() {
		return thirdTotal;
	}

	public void setThirdTotal(Integer thirdTotal) {
		this.thirdTotal = thirdTotal;
	}

	public String getFourthSortCode() {
		return fourthSortCode;
	}

	public void setFourthSortCode(String fourthSortCode) {
		this.fourthSortCode = fourthSortCode;
	}

	public String getFourthSortName() {
		return fourthSortName;
	}

	public void setFourthSortName(String fourthSortName) {
		this.fourthSortName = fourthSortName;
	}

	public Integer getFourthTotal() {
		return fourthTotal;
	}

	public void setFourthTotal(Integer fourthTotal) {
		this.fourthTotal = fourthTotal;
	}
	
}
