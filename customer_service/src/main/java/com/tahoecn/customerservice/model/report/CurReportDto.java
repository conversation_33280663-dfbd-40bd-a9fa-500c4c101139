/**
 * 
 */
package com.tahoecn.customerservice.model.report;

import java.math.BigDecimal;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * @ClassName CurReportDto
 * <AUTHOR>
 * @date 2019年1月10日
 */
public class CurReportDto {

	/**
	 * 区域CODE
	 */
	private String regionCode;

	@Excel(name = "区域")
	private String region;

	/**
	 * 城市CODE
	 */
	private String cityCode;

	@Excel(name = "城市")
	private String city;

	/**
	 * 项目CODE
	 */
	private String projectCode;

	@Excel(name = "项目")
	private String project;

	/**
	 * 一级分类CODE
	 */
	private String firstSortCode;

	@Excel(name = "一级分类")
	private String firstSortName;

	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer firstTotal;

	/**
	 * 关闭数量
	 */
	@Excel(name = "关闭数量")
	private Integer firstCloseNum;

	/**
	 * 关闭率
	 */
	@Excel(name = "关闭率", suffix = "%")
	private BigDecimal firstCloseRate;

	/**
	 * 返修数量
	 */
	private Integer firstRejectNum;

	/**
	 * 返修率
	 */
	@Excel(name = "返修率", suffix = "%")
	private BigDecimal firstRejectRate;

	/**
	 * 升级数量
	 */
	private Integer firstUpgradeNum;

	/**
	 * 升级率
	 */
	@Excel(name = "升级率", suffix = "%")
	private BigDecimal firstUpgradeRate;

	/**
	 * 二级分类CODE
	 */
	private String secSortCode;

	@Excel(name = "二级分类")
	private String secSortName;

	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer secTotal;

	/**
	 * 关闭数量
	 */
	@Excel(name = "关闭数量")
	private Integer secCloseNum;

	/**
	 * 关闭率
	 */
	@Excel(name = "关闭率", suffix = "%")
	private BigDecimal secCloseRate;

	/**
	 * 返修数量
	 */
	private Integer secRejectNum;

	/**
	 * 返修率
	 */
	@Excel(name = "返修率", suffix = "%")
	private BigDecimal secRejectRate;

	/**
	 * 升级数量
	 */
	private Integer secUpgradeNum;

	/**
	 * 升级率
	 */
	@Excel(name = "升级率", suffix = "%")
	private BigDecimal secUpgradeRate;

	/**
	 * 三级分类CODE
	 */
	private String thirdSortCode;

	@Excel(name = "三级分类")
	private String thirdSortName;

	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer thirdTotal;

	/**
	 * 关闭数量
	 */
	@Excel(name = "关闭数量")
	private Integer thirdCloseNum;

	/**
	 * 关闭率
	 */
	@Excel(name = "关闭率", suffix = "%")
	private BigDecimal thirdCloseRate;

	/**
	 * 返修数量
	 */
	private Integer thirdRejectNum;

	/**
	 * 返修率
	 */
	@Excel(name = "返修率", suffix = "%")
	private BigDecimal thirdRejectRate;

	/**
	 * 升级数量
	 */
	private Integer thirdUpgradeNum;

	/**
	 * 升级率
	 */
	@Excel(name = "升级率", suffix = "%")
	private BigDecimal thirdUpgradeRate;

	/**
	 * 四级分类CODE
	 */
	private String fourthSortCode;

	@Excel(name = "四级分类")
	private String fourthSortName;

	/**
	 * 总数
	 */
	@Excel(name = "数量")
	private Integer fourthTotal;

	/**
	 * 关闭数量
	 */
	@Excel(name = "关闭数量")
	private Integer fourthCloseNum;

	/**
	 * 关闭率
	 */
	@Excel(name = "关闭率", suffix = "%")
	private BigDecimal fourthCloseRate;

	/**
	 * 返修数量
	 */
	private Integer fourthRejectNum;

	/**
	 * 返修率
	 */
	@Excel(name = "返修率", suffix = "%")
	private BigDecimal fourthRejectRate;

	/**
	 * 升级数量
	 */
	private Integer fourthUpgradeNum;

	/**
	 * 升级率
	 */
	@Excel(name = "升级率", suffix = "%")
	private BigDecimal fourthUpgradeRate;

	public CurReportDto() {
		super();
	}

	public CurReportDto(SortTreeDto f, SortTreeDto s, SortTreeDto t, SortTreeDto fo) {
		// 一级分类
		this.setRegion(f.getRegion());
		this.setRegionCode(f.getRegionCode());
		this.setCity(f.getCity());
		this.setCityCode(f.getCityCode());
		this.setProject(f.getProject());
		this.setProjectCode(f.getProjectCode());
		this.setFirstSortCode(f.getFirstSortCode());
		this.setFirstSortName(f.getFirstSortName());
		this.setFirstTotal(f.getTotal());
		this.setFirstCloseNum(f.getCloseNum());
		this.setFirstRejectNum(f.getRejectNum());
		this.setFirstUpgradeNum(f.getUpgradeNum());

		// 二级分类
		this.setSecSortCode(s.getSecSortCode());
		this.setSecSortName(s.getSecSortName());
		this.setSecTotal(s.getTotal());
		this.setSecCloseNum(s.getCloseNum());
		this.setSecRejectNum(s.getRejectNum());
		this.setSecUpgradeNum(s.getUpgradeNum());

		// 三级分类
		if (t != null) {
			this.setThirdSortCode(t.getThirdSortCode());
			this.setThirdSortName(t.getThirdSortName());
			this.setThirdTotal(t.getTotal());
			this.setThirdCloseNum(t.getCloseNum());
			this.setThirdRejectNum(t.getRejectNum());
			this.setThirdUpgradeNum(t.getUpgradeNum());
		}

		// 四级分类
		if (fo != null) {
			this.setFourthSortCode(fo.getFourthSortCode());
			this.setFourthSortName(fo.getFourthSortName());
			this.setFourthTotal(fo.getTotal());
			this.setFourthCloseNum(fo.getCloseNum());
			this.setFourthRejectNum(fo.getRejectNum());
			this.setFourthUpgradeNum(fo.getUpgradeNum());
		}
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getFirstSortCode() {
		return firstSortCode;
	}

	public void setFirstSortCode(String firstSortCode) {
		this.firstSortCode = firstSortCode;
	}

	public String getFirstSortName() {
		return firstSortName;
	}

	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}

	public Integer getFirstTotal() {
		return firstTotal;
	}

	public void setFirstTotal(Integer firstTotal) {
		this.firstTotal = firstTotal;
	}

	public Integer getFirstCloseNum() {
		return firstCloseNum;
	}

	public void setFirstCloseNum(Integer firstCloseNum) {
		this.firstCloseNum = firstCloseNum;
	}

	public BigDecimal getFirstCloseRate() {
		if (firstCloseRate == null) {
			if (firstTotal == null || firstTotal == 0) {
				firstCloseRate = new BigDecimal(0);
			} else {
				firstCloseRate = new BigDecimal(firstCloseNum * 100 / firstTotal).setScale(0);
			}
		}
		return firstCloseRate;
	}

	public void setFirstCloseRate(BigDecimal firstCloseRate) {
		this.firstCloseRate = firstCloseRate;
	}

	public Integer getFirstRejectNum() {
		return firstRejectNum;
	}

	public void setFirstRejectNum(Integer firstRejectNum) {
		this.firstRejectNum = firstRejectNum;
	}

	public BigDecimal getFirstRejectRate() {
		if (firstRejectRate == null) {
			if (firstTotal == null || firstTotal == 0) {
				firstRejectRate = new BigDecimal(0);
			} else {
				firstRejectRate = new BigDecimal(firstRejectNum * 100 / firstTotal).setScale(0);
			}
		}
		return firstRejectRate;
	}

	public void setFirstRejectRate(BigDecimal firstRejectRate) {
		this.firstRejectRate = firstRejectRate;
	}

	public Integer getFirstUpgradeNum() {
		return firstUpgradeNum;
	}

	public void setFirstUpgradeNum(Integer firstUpgradeNum) {
		this.firstUpgradeNum = firstUpgradeNum;
	}

	public BigDecimal getFirstUpgradeRate() {
		if (firstUpgradeRate == null) {
			if (firstTotal == null || firstTotal == 0) {
				firstUpgradeRate = new BigDecimal(0);
			} else {
				firstUpgradeRate = new BigDecimal(firstUpgradeNum * 100 / firstTotal).setScale(0);
			}
		}
		return firstUpgradeRate;
	}

	public void setFirstUpgradeRate(BigDecimal firstUpgradeRate) {
		this.firstUpgradeRate = firstUpgradeRate;
	}

	public String getSecSortCode() {
		return secSortCode;
	}

	public void setSecSortCode(String secSortCode) {
		this.secSortCode = secSortCode;
	}

	public String getSecSortName() {
		return secSortName;
	}

	public void setSecSortName(String secSortName) {
		this.secSortName = secSortName;
	}

	public Integer getSecTotal() {
		return secTotal;
	}

	public void setSecTotal(Integer secTotal) {
		this.secTotal = secTotal;
	}

	public Integer getSecCloseNum() {
		return secCloseNum;
	}

	public void setSecCloseNum(Integer secCloseNum) {
		this.secCloseNum = secCloseNum;
	}

	public BigDecimal getSecCloseRate() {
		if (secCloseRate == null) {
			if (secTotal == null || secTotal == 0) {
				secCloseRate = new BigDecimal(0);
			} else {
				secCloseRate = new BigDecimal(secCloseNum * 100 / secTotal).setScale(0);
			}
		}
		return secCloseRate;
	}

	public void setSecCloseRate(BigDecimal secCloseRate) {
		this.secCloseRate = secCloseRate;
	}

	public Integer getSecRejectNum() {
		return secRejectNum;
	}

	public void setSecRejectNum(Integer secRejectNum) {
		this.secRejectNum = secRejectNum;
	}

	public BigDecimal getSecRejectRate() {
		if (secRejectRate == null) {
			if (secTotal == null || secTotal == 0) {
				secRejectRate = new BigDecimal(0);
			} else {
				secRejectRate = new BigDecimal(secRejectNum * 100 / secTotal).setScale(0);
			}
		}
		return secRejectRate;
	}

	public void setSecRejectRate(BigDecimal secRejectRate) {
		this.secRejectRate = secRejectRate;
	}

	public Integer getSecUpgradeNum() {
		return secUpgradeNum;
	}

	public void setSecUpgradeNum(Integer secUpgradeNum) {
		this.secUpgradeNum = secUpgradeNum;
	}

	public BigDecimal getSecUpgradeRate() {
		if (secUpgradeRate == null) {
			if (secTotal == null || secTotal == 0) {
				secUpgradeRate = new BigDecimal(0);
			} else {
				secUpgradeRate = new BigDecimal(secUpgradeNum * 100 / secTotal).setScale(0);
			}
		}
		return secUpgradeRate;
	}

	public void setSecUpgradeRate(BigDecimal secUpgradeRate) {
		this.secUpgradeRate = secUpgradeRate;
	}

	public String getThirdSortCode() {
		return thirdSortCode;
	}

	public void setThirdSortCode(String thirdSortCode) {
		this.thirdSortCode = thirdSortCode;
	}

	public String getThirdSortName() {
		return thirdSortName;
	}

	public void setThirdSortName(String thirdSortName) {
		this.thirdSortName = thirdSortName;
	}

	public Integer getThirdTotal() {
		return thirdTotal;
	}

	public void setThirdTotal(Integer thirdTotal) {
		this.thirdTotal = thirdTotal;
	}

	public Integer getThirdCloseNum() {
		return thirdCloseNum;
	}

	public void setThirdCloseNum(Integer thirdCloseNum) {
		this.thirdCloseNum = thirdCloseNum;
	}

	public BigDecimal getThirdCloseRate() {
		if (thirdCloseRate == null) {
			if (thirdTotal == null || thirdTotal == 0) {
				thirdCloseRate = null;
			} else {
				thirdCloseRate = new BigDecimal(thirdCloseNum * 100 / thirdTotal).setScale(0);
			}
		}
		return thirdCloseRate;
	}

	public void setThirdCloseRate(BigDecimal thirdCloseRate) {
		this.thirdCloseRate = thirdCloseRate;
	}

	public Integer getThirdRejectNum() {
		return thirdRejectNum;
	}

	public void setThirdRejectNum(Integer thirdRejectNum) {
		this.thirdRejectNum = thirdRejectNum;
	}

	public BigDecimal getThirdRejectRate() {
		if (thirdRejectRate == null) {
			if (thirdTotal == null || thirdTotal == 0) {
				thirdRejectRate = null;
			} else {
				thirdRejectRate = new BigDecimal(thirdRejectNum * 100 / thirdTotal).setScale(0);
			}
		}
		return thirdRejectRate;
	}

	public void setThirdRejectRate(BigDecimal thirdRejectRate) {
		this.thirdRejectRate = thirdRejectRate;
	}

	public Integer getThirdUpgradeNum() {
		return thirdUpgradeNum;
	}

	public void setThirdUpgradeNum(Integer thirdUpgradeNum) {
		this.thirdUpgradeNum = thirdUpgradeNum;
	}

	public BigDecimal getThirdUpgradeRate() {
		if (thirdUpgradeRate == null) {
			if (thirdTotal == null || thirdTotal == 0) {
				thirdUpgradeRate = null;
			} else {
				thirdUpgradeRate = new BigDecimal(thirdUpgradeNum * 100 / thirdTotal).setScale(0);
			}
		}
		return thirdUpgradeRate;
	}

	public void setThirdUpgradeRate(BigDecimal thirdUpgradeRate) {
		this.thirdUpgradeRate = thirdUpgradeRate;
	}

	public String getFourthSortCode() {
		return fourthSortCode;
	}

	public void setFourthSortCode(String fourthSortCode) {
		this.fourthSortCode = fourthSortCode;
	}

	public String getFourthSortName() {
		return fourthSortName;
	}

	public void setFourthSortName(String fourthSortName) {
		this.fourthSortName = fourthSortName;
	}

	public Integer getFourthTotal() {
		return fourthTotal;
	}

	public void setFourthTotal(Integer fourthTotal) {
		this.fourthTotal = fourthTotal;
	}

	public Integer getFourthCloseNum() {
		return fourthCloseNum;
	}

	public void setFourthCloseNum(Integer fourthCloseNum) {
		this.fourthCloseNum = fourthCloseNum;
	}

	public BigDecimal getFourthCloseRate() {
		if (fourthCloseRate == null) {
			if (fourthTotal == null || fourthTotal == 0) {
				fourthCloseRate = null;
			} else {
				fourthCloseRate = new BigDecimal(fourthCloseNum * 100 / fourthTotal).setScale(0);
			}
		}
		return fourthCloseRate;
	}

	public void setFourthCloseRate(BigDecimal fourthCloseRate) {
		this.fourthCloseRate = fourthCloseRate;
	}

	public Integer getFourthRejectNum() {
		return fourthRejectNum;
	}

	public void setFourthRejectNum(Integer fourthRejectNum) {
		this.fourthRejectNum = fourthRejectNum;
	}

	public BigDecimal getFourthRejectRate() {
		if (fourthRejectRate == null) {
			if (fourthTotal == null || fourthTotal == 0) {
				fourthRejectRate = null;
			} else {
				fourthRejectRate = new BigDecimal(fourthRejectNum * 100 / fourthTotal).setScale(0);
			}
		}
		return fourthRejectRate;
	}

	public void setFourthRejectRate(BigDecimal fourthRejectRate) {
		this.fourthRejectRate = fourthRejectRate;
	}

	public Integer getFourthUpgradeNum() {
		return fourthUpgradeNum;
	}

	public void setFourthUpgradeNum(Integer fourthUpgradeNum) {
		this.fourthUpgradeNum = fourthUpgradeNum;
	}

	public BigDecimal getFourthUpgradeRate() {
		if (fourthUpgradeRate == null) {
			if (fourthTotal == null || fourthTotal == 0) {
				fourthUpgradeRate = null;
			} else {
				fourthUpgradeRate = new BigDecimal(fourthUpgradeNum * 100 / fourthTotal).setScale(0);
			}
		}
		return fourthUpgradeRate;
	}

	public void setFourthUpgradeRate(BigDecimal fourthUpgradeRate) {
		this.fourthUpgradeRate = fourthUpgradeRate;
	}

}
