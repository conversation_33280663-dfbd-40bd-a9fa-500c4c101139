/**
 * 
 */
package com.tahoecn.customerservice.model.report;

import java.math.BigDecimal;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 报修整体统计
 * @ClassName RepairWholeDto
 * <AUTHOR>
 * @date 2019年1月9日
 */
/**
 * @ClassName RepairWholeDto
 * <AUTHOR>
 * @date 2019年1月9日
 */
public class RepairWholeDto {

	private String regionCode;
	
	@Excel(name = "区域")
	private String region;
	
	private String cityCode;

	@Excel(name = "城市")
	private String city;

	@Excel(name = "400")
	private Integer fzzNum;

	@Excel(name = "工单总量")
	private Integer total;

	/**
	 * 及时关闭总量
	 */
	private Integer timelyClosureNum;

	/**
	 * 关闭总量
	 */
	private Integer closeNum;

	@Excel(name = "及时关闭率（%）")
	private BigDecimal timelyClosureRate;

	@Excel(name = "名次")
	private Integer closureSort;

	@Excel(name = "特殊关闭单数")
	private Integer specialClosureNum;
	
	/**
	 * 计算回访满意度 单数
	 */
	private Integer satisfactionTotalNum;

	/**
	 * 回访满意度总量
	 */
	private Integer SatisfactionNum;

	@Excel(name = "回访满意度")
	private BigDecimal satisfaction;

	@Excel(name = "名次")
	private Integer satisfactionSort;

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public Integer getFzzNum() {
		return fzzNum;
	}

	public void setFzzNum(Integer fzzNum) {
		this.fzzNum = fzzNum;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	public Integer getTimelyClosureNum() {
		return timelyClosureNum;
	}

	public void setTimelyClosureNum(Integer timelyClosureNum) {
		this.timelyClosureNum = timelyClosureNum;
	}

	public BigDecimal getTimelyClosureRate() {
		if (timelyClosureRate == null) {
			if (closeNum == 0) {
				timelyClosureRate = new BigDecimal(0);
			} else {
				timelyClosureRate = new BigDecimal((timelyClosureNum*100)/closeNum).setScale(0);
			}
		}
		return timelyClosureRate;
	}

	public void setTimelyClosureRate(BigDecimal timelyClosureRate) {
		this.timelyClosureRate = timelyClosureRate;
	}

	public Integer getClosureSort() {
		return closureSort;
	}

	public void setClosureSort(Integer closureSort) {
		this.closureSort = closureSort;
	}

	public Integer getSpecialClosureNum() {
		return specialClosureNum;
	}

	public void setSpecialClosureNum(Integer specialClosureNum) {
		this.specialClosureNum = specialClosureNum;
	}

	public Integer getSatisfactionNum() {
		return SatisfactionNum;
	}

	public void setSatisfactionNum(Integer satisfactionNum) {
		SatisfactionNum = satisfactionNum;
	}

	public Integer getSatisfactionTotalNum() {
		return satisfactionTotalNum;
	}

	public void setSatisfactionTotalNum(Integer satisfactionTotalNum) {
		this.satisfactionTotalNum = satisfactionTotalNum;
	}

	public BigDecimal getSatisfaction() {
		if (satisfaction == null) {
			if (satisfactionTotalNum == 0) {
				satisfaction = new BigDecimal(0);
			} else {
				satisfaction = new BigDecimal((this.SatisfactionNum * 20) / this.satisfactionTotalNum).setScale(0);
			}
		}
		return satisfaction;
	}

	public void setSatisfaction(BigDecimal satisfaction) {
		this.satisfaction = satisfaction;
	}

	public Integer getSatisfactionSort() {
		return satisfactionSort;
	}

	public void setSatisfactionSort(Integer satisfactionSort) {
		this.satisfactionSort = satisfactionSort;
	}
	
	public Integer getCloseNum() {
		return closeNum;
	}

	public void setCloseNum(Integer closeNum) {
		this.closeNum = closeNum;
	}

}
