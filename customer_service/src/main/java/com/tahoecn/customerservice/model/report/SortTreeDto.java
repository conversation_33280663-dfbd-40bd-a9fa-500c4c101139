/**
 * 
 */
package com.tahoecn.customerservice.model.report;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 关闭升级返修报表
 * @ClassName CurReportDto
 * <AUTHOR>
 * @date 2019年1月9日
 */
public class SortTreeDto {
	
	/**
	 * 区域CODE
	 */
	private String regionCode;

	@Excel(name = "区域")
	private String region;

	/**
	 * 城市CODE
	 */
	private String cityCode;

	@Excel(name = "城市")
	private String city;

	/**
	 * 项目CODE
	 */
	private String projectCode;

	@Excel(name = "项目")
	private String project;

	/**
	 * 一级分类CODE
	 */
	private String firstSortCode;

	@Excel(name = "一级分类")
	private String firstSortName;

	/**
	 * 二级分类CODE
	 */
	private String secSortCode;

	@Excel(name = "二级分类")
	private String secSortName;

	/**
	 * 三级分类CODE
	 */
	private String thirdSortCode;

	@Excel(name = "三级分类")
	private String thirdSortName;

	/**
	 * 四级分类CODE
	 */
	private String fourthSortCode;

	@Excel(name = "四级分类")
	private String fourthSortName;
	
	/**
	 * 总数
	 */
	private Integer total;
	
	/**
	 * 关闭数量
	 */
	private Integer closeNum;
	
	/**
	 * 返修数量
	 */
	private Integer rejectNum;
	
	/**
	 * 升级数量
	 */
	private Integer upgradeNum;

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getFirstSortCode() {
		return firstSortCode;
	}

	public void setFirstSortCode(String firstSortCode) {
		this.firstSortCode = firstSortCode;
	}

	public String getFirstSortName() {
		return firstSortName;
	}

	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}

	public String getSecSortCode() {
		return secSortCode;
	}

	public void setSecSortCode(String secSortCode) {
		this.secSortCode = secSortCode;
	}

	public String getSecSortName() {
		return secSortName;
	}

	public void setSecSortName(String secSortName) {
		this.secSortName = secSortName;
	}

	public String getThirdSortCode() {
		return thirdSortCode;
	}

	public void setThirdSortCode(String thirdSortCode) {
		this.thirdSortCode = thirdSortCode;
	}

	public String getThirdSortName() {
		return thirdSortName;
	}

	public void setThirdSortName(String thirdSortName) {
		this.thirdSortName = thirdSortName;
	}

	public String getFourthSortCode() {
		return fourthSortCode;
	}

	public void setFourthSortCode(String fourthSortCode) {
		this.fourthSortCode = fourthSortCode;
	}

	public String getFourthSortName() {
		return fourthSortName;
	}

	public void setFourthSortName(String fourthSortName) {
		this.fourthSortName = fourthSortName;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	public Integer getCloseNum() {
		return closeNum;
	}

	public void setCloseNum(Integer closeNum) {
		this.closeNum = closeNum;
	}

	public Integer getRejectNum() {
		return rejectNum;
	}

	public void setRejectNum(Integer rejectNum) {
		this.rejectNum = rejectNum;
	}

	public Integer getUpgradeNum() {
		return upgradeNum;
	}

	public void setUpgradeNum(Integer upgradeNum) {
		this.upgradeNum = upgradeNum;
	}
	
}
