/**
 * 
 */
package com.tahoecn.customerservice.model.report;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * @ClassName UpgradeDto
 * <AUTHOR>
 * @date 2019年1月10日
 */
public class UpgradeDto {

	/**
	 * 区域CODE
	 */
	private String regionCode;

	@Excel(name = "区域")
	private String region;

	/**
	 * 城市CODE
	 */
	private String cityCode;

	@Excel(name = "城市")
	private String city;

	/**
	 * 项目CODE
	 */
	private String projectCode;

	@Excel(name = "项目")
	private String project;

	/**
	 * 一级分类CODE
	 */
	private String firstSortCode;

	@Excel(name = "一级分类")
	private String firstSortName;
	
	/**
	 * 升级数量
	 */
	@Excel(name = "数量")
	private Integer firstUpgradeNum;

	/**
	 * 二级分类CODE
	 */
	private String secSortCode;

	@Excel(name = "二级分类")
	private String secSortName;
	
	/**
	 * 升级数量
	 */
	@Excel(name = "数量")
	private Integer secUpgradeNum;

	/**
	 * 三级分类CODE
	 */
	private String thirdSortCode;

	@Excel(name = "三级分类")
	private String thirdSortName;
	
	/**
	 * 升级数量
	 */
	@Excel(name = "数量")
	private Integer thirdUpgradeNum;

	/**
	 * 四级分类CODE
	 */
	private String fourthSortCode;

	@Excel(name = "四级分类")
	private String fourthSortName;
	
	/**
	 * 升级数量
	 */
	@Excel(name = "数量")
	private Integer fourthUpgradeNum;
	
	public UpgradeDto() {
		super();
	}
	
	public UpgradeDto(SortTreeDto f,SortTreeDto s,SortTreeDto t,SortTreeDto fo) {
		// 一级分类
		this.setRegion(f.getRegion());
		this.setRegionCode(f.getRegionCode());
		this.setCity(f.getCity());
		this.setCityCode(f.getCityCode());
		this.setProject(f.getProject());
		this.setProjectCode(f.getProjectCode());
		this.setFirstSortCode(f.getFirstSortCode());
		this.setFirstSortName(f.getFirstSortName());
		this.setFirstUpgradeNum(f.getUpgradeNum());
		
		// 二级分类
		this.setSecSortCode(s.getSecSortCode());
		this.setSecSortName(s.getSecSortName());
		this.setSecUpgradeNum(s.getUpgradeNum());
		
		// 三级分类
		if(t != null) {
			this.setThirdSortCode(t.getThirdSortCode());
			this.setThirdSortName(t.getThirdSortName());
			this.setThirdUpgradeNum(t.getUpgradeNum());
		}
		
		// 四级分类
		if(fo != null) {
			this.setFourthSortCode(fo.getFourthSortCode());
			this.setFourthSortName(fo.getFourthSortName());
			this.setFourthUpgradeNum(fo.getUpgradeNum());
		}
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getFirstSortCode() {
		return firstSortCode;
	}

	public void setFirstSortCode(String firstSortCode) {
		this.firstSortCode = firstSortCode;
	}

	public String getFirstSortName() {
		return firstSortName;
	}

	public void setFirstSortName(String firstSortName) {
		this.firstSortName = firstSortName;
	}

	public Integer getFirstUpgradeNum() {
		return firstUpgradeNum;
	}

	public void setFirstUpgradeNum(Integer firstUpgradeNum) {
		this.firstUpgradeNum = firstUpgradeNum;
	}

	public String getSecSortCode() {
		return secSortCode;
	}

	public void setSecSortCode(String secSortCode) {
		this.secSortCode = secSortCode;
	}

	public String getSecSortName() {
		return secSortName;
	}

	public void setSecSortName(String secSortName) {
		this.secSortName = secSortName;
	}

	public Integer getSecUpgradeNum() {
		return secUpgradeNum;
	}

	public void setSecUpgradeNum(Integer secUpgradeNum) {
		this.secUpgradeNum = secUpgradeNum;
	}

	public String getThirdSortCode() {
		return thirdSortCode;
	}

	public void setThirdSortCode(String thirdSortCode) {
		this.thirdSortCode = thirdSortCode;
	}

	public String getThirdSortName() {
		return thirdSortName;
	}

	public void setThirdSortName(String thirdSortName) {
		this.thirdSortName = thirdSortName;
	}

	public Integer getThirdUpgradeNum() {
		return thirdUpgradeNum;
	}

	public void setThirdUpgradeNum(Integer thirdUpgradeNum) {
		this.thirdUpgradeNum = thirdUpgradeNum;
	}

	public String getFourthSortCode() {
		return fourthSortCode;
	}

	public void setFourthSortCode(String fourthSortCode) {
		this.fourthSortCode = fourthSortCode;
	}

	public String getFourthSortName() {
		return fourthSortName;
	}

	public void setFourthSortName(String fourthSortName) {
		this.fourthSortName = fourthSortName;
	}

	public Integer getFourthUpgradeNum() {
		return fourthUpgradeNum;
	}

	public void setFourthUpgradeNum(Integer fourthUpgradeNum) {
		this.fourthUpgradeNum = fourthUpgradeNum;
	}
	
}
