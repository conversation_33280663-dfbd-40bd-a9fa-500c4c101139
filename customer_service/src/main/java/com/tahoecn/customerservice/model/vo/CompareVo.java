package com.tahoecn.customerservice.model.vo;

import com.tahoecn.customerservice.common.utils.NoteAttribute;

/**
 * Created by gaara on 2018/11/24.
 */
public class CompareVo {
    @NoteAttribute(name="二级分类名称")
    private String secSortName;

    @NoteAttribute(name="三级分类名称")
    private String thirdSortName;

    @NoteAttribute(name="四级分类")
    private String fourthSortName;

    @NoteAttribute(name="问题严重级别")
    private String problemLevelName;

    @NoteAttribute(name="报修部位")
    private String problemPositionName;

    @NoteAttribute(name="报修分类")
    private String revisionClassificationName;

    @NoteAttribute(name="装修阶段")
    private String decorationStageName;

    @NoteAttribute(name="维保期")
    private String maintenancePeriodName;

    @NoteAttribute(name="内部责任部门")
    private String interResDepartmentName;

    @NoteAttribute(name="主责任单位")
    private String mainResUnit;

    @NoteAttribute(name="维修单位")
    private String repairUnit;

    @NoteAttribute(name="是否启用第三方")
    private String thirdPartyFlag;

    @NoteAttribute(name="第三方名称")
    private String thirdPartyName;

    @NoteAttribute(name="启用第三方原因")
    private String thirdPartyReason;

    public String getSecSortName() {
        return secSortName;
    }

    public void setSecSortName(String secSortName) {
        this.secSortName = secSortName;
    }

    public String getThirdSortName() {
        return thirdSortName;
    }

    public void setThirdSortName(String thirdSortName) {
        this.thirdSortName = thirdSortName;
    }

    public String getFourthSortName() {
        return fourthSortName;
    }

    public void setFourthSortName(String fourthSortName) {
        this.fourthSortName = fourthSortName;
    }

    public String getProblemLevelName() {
        return problemLevelName;
    }

    public void setProblemLevelName(String problemLevelName) {
        this.problemLevelName = problemLevelName;
    }

    public String getProblemPositionName() {
        return problemPositionName;
    }

    public void setProblemPositionName(String problemPositionName) {
        this.problemPositionName = problemPositionName;
    }

    public String getRevisionClassificationName() {
        return revisionClassificationName;
    }

    public void setRevisionClassificationName(String revisionClassificationName) {
        this.revisionClassificationName = revisionClassificationName;
    }

    public String getDecorationStageName() {
        return decorationStageName;
    }

    public void setDecorationStageName(String decorationStageName) {
        this.decorationStageName = decorationStageName;
    }

    public String getMaintenancePeriodName() {
        return maintenancePeriodName;
    }

    public void setMaintenancePeriodName(String maintenancePeriodName) {
        this.maintenancePeriodName = maintenancePeriodName;
    }

    public String getInterResDepartmentName() {
        return interResDepartmentName;
    }

    public void setInterResDepartmentName(String interResDepartmentName) {
        this.interResDepartmentName = interResDepartmentName;
    }

    public String getMainResUnit() {
        return mainResUnit;
    }

    public void setMainResUnit(String mainResUnit) {
        this.mainResUnit = mainResUnit;
    }

    public String getRepairUnit() {
        return repairUnit;
    }

    public void setRepairUnit(String repairUnit) {
        this.repairUnit = repairUnit;
    }

    public String getThirdPartyFlag() {
        return thirdPartyFlag;
    }

    public void setThirdPartyFlag(String thirdPartyFlag) {
        this.thirdPartyFlag = thirdPartyFlag;
    }

    public String getThirdPartyName() {
        return thirdPartyName;
    }

    public void setThirdPartyName(String thirdPartyName) {
        this.thirdPartyName = thirdPartyName;
    }

    public String getThirdPartyReason() {
        return thirdPartyReason;
    }

    public void setThirdPartyReason(String thirdPartyReason) {
        this.thirdPartyReason = thirdPartyReason;
    }
}
