package com.tahoecn.customerservice.model.vo;

import io.swagger.models.auth.In;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhanghw on 2018/12/5.
 */
public class CustomerStatisticsSatisficingVo implements Serializable {
    private Integer satisfactionSum;
    private Integer satisfactionAll;
    private String satisfactionProportion;

    private String  scoreName;
    private Integer thirdScore;
    private Integer thirdProportion;

    private List<CustomerStatisticsSatisficingGroupVo> satisficingGroupVoList;

    public Integer getSatisfactionSum() {
        return satisfactionSum;
    }

    public void setSatisfactionSum(Integer satisfactionSum) {
        this.satisfactionSum = satisfactionSum;
    }

    public Integer getSatisfactionAll() {
        return satisfactionAll;
    }

    public void setSatisfactionAll(Integer satisfactionAll) {
        this.satisfactionAll = satisfactionAll;
    }

    public String getSatisfactionProportion() {
        return satisfactionProportion;
    }

    public void setSatisfactionProportion(String satisfactionProportion) {
        this.satisfactionProportion = satisfactionProportion;
    }

    public List<CustomerStatisticsSatisficingGroupVo> getSatisficingGroupVoList() {
        return satisficingGroupVoList;
    }

    public void setSatisficingGroupVoList(List<CustomerStatisticsSatisficingGroupVo> satisficingGroupVoList) {
        this.satisficingGroupVoList = satisficingGroupVoList;
    }

    public Integer getThirdScore() {
        return thirdScore;
    }

    public void setThirdScore(Integer thirdScore) {
        this.thirdScore = thirdScore;
    }

    public Integer getThirdProportion() {
        return thirdProportion;
    }

    public void setThirdProportion(Integer thirdProportion) {
        this.thirdProportion = thirdProportion;
    }

    public String getScoreName() {
        return scoreName;
    }

    public void setScoreName(String scoreName) {
        this.scoreName = scoreName;
    }
}
