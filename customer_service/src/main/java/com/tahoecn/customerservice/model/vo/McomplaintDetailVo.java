package com.tahoecn.customerservice.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhanghw on 2018/12/4.
 */
public class McomplaintDetailVo implements Serializable {
    private McomplaintVo mcomplaintVo;
    private List<CsProcessWorkitemDateFormatVo> processList;

    public McomplaintVo getMcomplaintVo() {
        return mcomplaintVo;
    }

    public void setMcomplaintVo(McomplaintVo mcomplaintVo) {
        this.mcomplaintVo = mcomplaintVo;
    }

    public List<CsProcessWorkitemDateFormatVo> getProcessList() {
        return processList;
    }

    public void setProcessList(List<CsProcessWorkitemDateFormatVo> processList) {
        this.processList = processList;
    }
}
