package com.tahoecn.customerservice.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhanghw on 2018/12/3.
 */
public class McomplaintVo implements Serializable {
    /**
     * 工单ID
     */
    @ApiModelProperty(value = "工单ID")
    private Long id;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String formNo;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String complaintHeadlines;

    /**
     * 二级分类名称
     */
    @ApiModelProperty(value = "二级分类名称")
    private String secSortName;
    /**
     * 三级分类名称
     */
    @ApiModelProperty(value = "三级分类名称")
    private String thirdSortName;
    /**
     * 报事人
     */
    @ApiModelProperty(value = "报事人")
    private String ownerName;

    /**
     * 当前处理人
     */
    @ApiModelProperty(value = "当前处理人")
    private String curAssigneeName;
    /**
     * 处理时长
     */
    @ApiModelProperty(value = "处理时长")
    private String doTime;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String region;
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private String project;

    /**
     * 主责任单位
     */
    @ApiModelProperty(value = "主责任单位")
    private String mainResUnit;
    /**
     * 客户需求
     */
    @ApiModelProperty(value = "客户需求")
    private String customerDemand;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    private String comment;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date lastUpdateDate;
    /**
     * 移动电话
     */
    private String mobile;
    /**
     * 电子邮件
     */
    private String eMail;

    /**
     * 楼栋
     */
    private String buildingNo;
    /**
     * 单元号
     */
    private String buildingUnit;
    /**
     * 房间号
     */
    private String roomNo;
    /**
     * 管家姓名
     */
    private String housekeeperName;
    /**
     * 管家电话
     */
    private String housekeeperTel;
    /**
     * 特殊客户-1：否，1：是
     */
    private Integer specialUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplaintHeadlines() {
        return complaintHeadlines;
    }

    public void setComplaintHeadlines(String complaintHeadlines) {
        this.complaintHeadlines = complaintHeadlines;
    }

    public String getSecSortName() {
        return secSortName;
    }

    public void setSecSortName(String secSortName) {
        this.secSortName = secSortName;
    }

    public String getThirdSortName() {
        return thirdSortName;
    }

    public void setThirdSortName(String thirdSortName) {
        this.thirdSortName = thirdSortName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getCurAssigneeName() {
        return curAssigneeName;
    }

    public void setCurAssigneeName(String curAssigneeName) {
        this.curAssigneeName = curAssigneeName;
    }

    public String getDoTime() {
        return doTime;
    }

    public void setDoTime(String doTime) {
        this.doTime = doTime;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getFormNo() {
        return formNo;
    }

    public void setFormNo(String formNo) {
        this.formNo = formNo;
    }

    public String getMainResUnit() {
        return mainResUnit;
    }

    public void setMainResUnit(String mainResUnit) {
        this.mainResUnit = mainResUnit;
    }

    public String getCustomerDemand() {
        return customerDemand;
    }

    public void setCustomerDemand(String customerDemand) {
        this.customerDemand = customerDemand;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    public String getBuildingUnit() {
        return buildingUnit;
    }

    public void setBuildingUnit(String buildingUnit) {
        this.buildingUnit = buildingUnit;
    }

    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public String getHousekeeperName() {
        return housekeeperName;
    }

    public void setHousekeeperName(String housekeeperName) {
        this.housekeeperName = housekeeperName;
    }

    public String getHousekeeperTel() {
        return housekeeperTel;
    }

    public void setHousekeeperTel(String housekeeperTel) {
        this.housekeeperTel = housekeeperTel;
    }

    public Integer getSpecialUser() {
        return specialUser;
    }

    public void setSpecialUser(Integer specialUser) {
        this.specialUser = specialUser;
    }
}
