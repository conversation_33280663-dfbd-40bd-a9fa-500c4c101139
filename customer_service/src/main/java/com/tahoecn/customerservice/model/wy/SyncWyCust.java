package com.tahoecn.customerservice.model.wy;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
@TableName("view_HSPR_Customer_THKF")
public class SyncWyCust implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CommID")
    private String CommID;
    @TableId("CustID")
    private String CustID;
    @TableField("CustName")
    private String CustName;
    @TableField("PaperCode")
    private String PaperCode;
    @TableField("PaperName")
    private String PaperName;
    @TableField("FaxTel")
    private String FaxTel;
    @TableField("MobilePhone")
    private String MobilePhone;
    @TableField("Address")
    private String Address;
    @TableField("PostCode")
    private String PostCode;
    @TableField("EMail")
    private String EMail;
    @TableField("IsUnit")
    private String IsUnit;
    @TableField("Sex")
    private String Sex;
    @TableField("Nationality")
    private String Nationality;
    @TableField("Birthday")
    private String Birthday;
    @TableField("Job")
    private String Job;
    @TableField("Hobbies")
    private String Hobbies;
    private String custupdatetime;
    @TableField("IsDelete")
    private String IsDelete;


    public String getCommID() {
        return CommID;
    }

    public void setCommID(String CommID) {
        this.CommID = CommID;
    }

    public String getCustID() {
        return CustID;
    }

    public void setCustID(String CustID) {
        this.CustID = CustID;
    }

    public String getCustName() {
        return CustName;
    }

    public void setCustName(String CustName) {
        this.CustName = CustName;
    }

    public String getPaperCode() {
        return PaperCode;
    }

    public void setPaperCode(String PaperCode) {
        this.PaperCode = PaperCode;
    }

    public String getPaperName() {
        return PaperName;
    }

    public void setPaperName(String PaperName) {
        this.PaperName = PaperName;
    }

    public String getFaxTel() {
        return FaxTel;
    }

    public void setFaxTel(String FaxTel) {
        this.FaxTel = FaxTel;
    }

    public String getMobilePhone() {
        return MobilePhone;
    }

    public void setMobilePhone(String MobilePhone) {
        this.MobilePhone = MobilePhone;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String Address) {
        this.Address = Address;
    }

    public String getPostCode() {
        return PostCode;
    }

    public void setPostCode(String PostCode) {
        this.PostCode = PostCode;
    }

    public String getEMail() {
        return EMail;
    }

    public void setEMail(String EMail) {
        this.EMail = EMail;
    }

    public String getIsUnit() {
        return IsUnit;
    }

    public void setIsUnit(String IsUnit) {
        this.IsUnit = IsUnit;
    }

    public String getSex() {
        return Sex;
    }

    public void setSex(String Sex) {
        this.Sex = Sex;
    }

    public String getNationality() {
        return Nationality;
    }

    public void setNationality(String Nationality) {
        this.Nationality = Nationality;
    }

    public String getBirthday() {
        return Birthday;
    }

    public void setBirthday(String Birthday) {
        this.Birthday = Birthday;
    }

    public String getJob() {
        return Job;
    }

    public void setJob(String Job) {
        this.Job = Job;
    }

    public String getHobbies() {
        return Hobbies;
    }

    public void setHobbies(String Hobbies) {
        this.Hobbies = Hobbies;
    }

    public String getCustupdatetime() {
        return custupdatetime;
    }

    public void setCustupdatetime(String custupdatetime) {
        this.custupdatetime = custupdatetime;
    }

    public String getIsDelete() {
        return IsDelete;
    }

    public void setIsDelete(String IsDelete) {
        this.IsDelete = IsDelete;
    }

    @Override
    public String toString() {
        return "CsSyncWyCust{" +
        "CommID=" + CommID +
        ", CustID=" + CustID +
        ", CustName=" + CustName +
        ", PaperCode=" + PaperCode +
        ", PaperName=" + PaperName +
        ", FaxTel=" + FaxTel +
        ", MobilePhone=" + MobilePhone +
        ", Address=" + Address +
        ", PostCode=" + PostCode +
        ", EMail=" + EMail +
        ", IsUnit=" + IsUnit +
        ", Sex=" + Sex +
        ", Nationality=" + Nationality +
        ", Birthday=" + Birthday +
        ", Job=" + Job +
        ", Hobbies=" + Hobbies +
        ", custupdatetime=" + custupdatetime +
        ", IsDelete=" + IsDelete +
        "}";
    }
}
