package com.tahoecn.customerservice.schedule;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.core.util.NetUtil;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.CsUserRole;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.CsUserRoleService;
import com.tahoecn.customerservice.service.ProcessService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

/**
 * Created by gaara on 2018/11/26.
 */
@Component("formOvertimeTask")
public class FormOvertimeTask {

    @Autowired
    private CsFormInstService csFormInstService;
    @Autowired
    private ProcessService processService;
    @Autowired
    private CsProcessWorkitemService csProcessWorkitemService;
    
    @Autowired
    private CsUcUserService csUcUserService;
//    @Autowired
//    private CsUserRoleService csUserRoleService;
    @Value("${bind_host}")
    private String bindHost;

    private static final Log log = LogFactory.get();



    private static final String sRepairToBeAssigned3Hours = "sRepair-ToBeAssigned-3Hours";
    private static final String sRepairHandle3Hours = "sRepair-Handle-3Hours";
    private static final String sRepairToBeAssigned3Days = "sRepair-ToBeAssigned-3Day";
    private static final String sRepairHandle3Days = "sRepair-Handle-3Day";

    private static final String sComplaintToBeAssigned3Hours = "sComplaint-ToBeAssigned-3Hours";
    private static final String sComplaintHandle3Hours = "sComplaint-Handle-3Hours";
    private static final String sComplaintToBeAssigned3Days = "sComplaint-ToBeAssigned-3Day";
    private static final String sComplaintHandle3Days = "sComplaint-Handle-3Day";


    private static final String sComplaintPre = "sComplaint-"; //投诉前缀

    private static final String sRepairPre = "sRepair-"; //报修前缀

    //每小时执行一次
//    @Scheduled(cron = "0 0 */1 * * ?")
    public void voerTimeNoticeHours() {
    	if (StringUtils.isBlank(bindHost)) {
    		return;
    	}
    	if (StringUtils.isNotBlank(bindHost)) {
        	Boolean flg = false;
        	for (String ip : NetUtil.localIpv4s()) {
        		log.info("bind host str={} ,localhost str = {}", bindHost, ip);
        		flg = flg ? true:bindHost.equals(ip);
				
			}
        	if(!flg) {
        		log.info("bind host {} != localhost pullUserInfo scheduled exit! ", bindHost);
        		return;
        	}
        }

        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        //3小时提醒
        formWrapper.where("creation_date > '2018-12-25' and submit_date is not null and TIMESTAMPDIFF(HOUR, submit_date, CURRENT_TIMESTAMP())=3 " +
                "and process_state_code in ('toBeAssigned','handle')");
        List overTimeList = csFormInstService.selectList(formWrapper);
        for(int i=0;i<overTimeList.size();i++){
            CsFormInst form = (CsFormInst)overTimeList.get(i);
            Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<>();
            wrapper.eq("form_inst_id", form.getId());
            wrapper.eq("task_status", 20);
            
            CsProcessWorkitem csProcessWorkitem = csProcessWorkitemService.selectOne(wrapper);
            String tel = csProcessWorkitem == null ? null : csProcessWorkitem.getAssignMobile();
            if(StringUtils.isBlank(tel)) {
            	CsUcUser user = csUcUserService.selectByUsername(form.getCurAssigneeId());
            	tel = user.getFdTel();
            }
            if("coBX".equals(form.getFirstSortCode())) {
                if ("toBeAssigned".equals(form.getProcessStateCode())) {
                    processService.sendSms(sRepairToBeAssigned3Hours, form, form.getCurAssigneeName(), tel);
                }
                if ("handle".equals(form.getProcessStateCode())) {
                    processService.sendSms(sRepairHandle3Hours, form, form.getCurAssigneeName(), tel);
                }
            }
            if("coTS".equals(form.getFirstSortCode())) {
                if ("toBeAssigned".equals(form.getProcessStateCode())) {
                    processService.sendSms(sComplaintToBeAssigned3Hours, form, form.getCurAssigneeName(), tel);
                }
                if ("handle".equals(form.getProcessStateCode())) {
                    processService.sendSms(sComplaintHandle3Hours, form, form.getCurAssigneeName(), tel);
                }
            }
        }
    }

    //每天9：30执行
//    @Scheduled(cron = "0 30 9 * * ?")
    public void voerTimeNoticeDays() {
    	if (StringUtils.isBlank(bindHost)) {
    		return;
    	}
    	if (StringUtils.isNotBlank(bindHost)) {
        	Boolean flg = false;
        	for (String ip : NetUtil.localIpv4s()) {
        		log.info("bind host str={} ,localhost str = {}", bindHost, ip);
        		flg = flg ? true:bindHost.equals(ip);
				
			}
        	if(!flg) {
        		log.info("bind host {} != localhost pullUserInfo scheduled exit! ", bindHost);
        		return;
        	}
        }
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        //每隔3天提醒一次
        formWrapper.where("creation_date > '2018-12-25' and submit_date is not null and TIMESTAMPDIFF(DAY, submit_date, CURRENT_TIMESTAMP())>=3 and TIMESTAMPDIFF(DAY, submit_date, CURRENT_TIMESTAMP()) mod 3=0 " +
                "and process_state_code in ('toBeAssigned','handle')");
        List overTimeList = csFormInstService.selectList(formWrapper);
        for(int i=0;i<overTimeList.size();i++){
            CsFormInst form = (CsFormInst)overTimeList.get(i);
            Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<>();
            wrapper.eq("form_inst_id", form.getId());
            wrapper.eq("task_status", 20);
            
            CsProcessWorkitem csProcessWorkitem = csProcessWorkitemService.selectOne(wrapper);
            String tel = csProcessWorkitem == null ? null : csProcessWorkitem.getAssignMobile();
            if(StringUtils.isBlank(tel)) {
            	CsUcUser user = csUcUserService.selectByUsername(form.getCurAssigneeId());
            	tel = user.getFdTel();
            }
            if("coBX".equals(form.getFirstSortCode())) {
                if ("toBeAssigned".equals(form.getProcessStateCode())) {
                    processService.sendSms(sRepairToBeAssigned3Days, form, form.getCurAssigneeName(), tel);
                }
                if ("handle".equals(form.getProcessStateCode())) {
                    processService.sendSms(sRepairHandle3Days, form, form.getCurAssigneeName(), tel);
                }
            }
            if("coTS".equals(form.getFirstSortCode())) {
                if ("toBeAssigned".equals(form.getProcessStateCode())) {
                    processService.sendSms(sComplaintToBeAssigned3Days, form, form.getCurAssigneeName(), tel);
                }
                if ("handle".equals(form.getProcessStateCode())) {
                    processService.sendSms(sComplaintHandle3Days, form, form.getCurAssigneeName(), tel);
                }
            }
        }
    }

    //每天10：00执行升级
    @Scheduled(cron = "0 0 10 * * ?")
    public void upgrade(){
    	if (StringUtils.isBlank(bindHost)) {
    		return;
    	}
    	if (StringUtils.isNotBlank(bindHost)) {
        	Boolean flg = false;
        	for (String ip : NetUtil.localIpv4s()) {
        		log.info("bind host str={} ,localhost str = {}", bindHost, ip);
        		flg = flg ? true:bindHost.equals(ip);
				
			}
        	if(!flg) {
        		log.info("bind host {} != localhost pullUserInfo scheduled exit! ", bindHost);
        		return;
        	}
        }
        List upgradeList = csFormInstService.selectUpgradeList();
        for(int i=0;i<upgradeList.size();i++){
            CsFormInst form = (CsFormInst)upgradeList.get(i);
            if(form.getSchedualUpgradeLevel() - form.getUpgradeLevel() <= 0){
                continue;
            }
            try {
                processService.upgrade(form,getTemplateCode(form),form.getSchedualUpgradeLevel());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String getTemplateCode(CsFormInst form){
        Long schedualUpgradeLevel = form.getSchedualUpgradeLevel();
        String firstSortCode = form.getFirstSortCode();
        if("coBX".equals(firstSortCode)){
            return sRepairPre + "upgrade-"+schedualUpgradeLevel;
        }

        if("coTS".equals(firstSortCode)){
            return sComplaintPre + "upgrade-"+schedualUpgradeLevel;
        }
        return null;
    }
}
