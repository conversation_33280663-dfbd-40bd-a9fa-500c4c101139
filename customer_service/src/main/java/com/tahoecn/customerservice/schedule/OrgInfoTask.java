package com.tahoecn.customerservice.schedule;

import com.tahoecn.core.date.DateUtil;
import com.tahoecn.core.util.JsonUtil;
import com.tahoecn.core.util.NetUtil;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.model.CsUcOrg;
import com.tahoecn.customerservice.model.dto.OrgInfoDto;
import com.tahoecn.customerservice.model.dto.ResponseDto;
import com.tahoecn.customerservice.service.CsUcOrgService;
import com.tahoecn.http.HttpClient;
import com.tahoecn.http.HttpUtil;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by zhanghw on 2018/10/11.
 * 每天凌晨2点拉取更新组织信息
 */
@Component("orgInfoTask")
public class OrgInfoTask {

    private static final Log log = LogFactory.get();
    @Value("${uc_api_url_orglist}")
    private String orgListUrl;
    @Value("${uc_sysId}")
    private String sysId;
    @Value("${uc_priv_key}")
    private String privKey;
    @Value("${bind_host}")
    private String bindHost;
    @Autowired
    private CsUcOrgService csUcOrgService;
    private String fromTime = "2000-01-01 00:00:00";

    @Scheduled(cron = "0 0 2 * * ?")
    public void pullOrgInfo() {
        if (StringUtils.isNotBlank(bindHost)) {
        	Boolean flg = false;
        	for (String ip : NetUtil.localIpv4s()) {
        		log.info("bind host str={} ,localhost str = {}", bindHost, ip);
        		flg = flg ? true:bindHost.equals(ip);
				
			}
        	if(!flg) {
        		log.info("bind host {} != localhost pullUserInfo scheduled exit! ", bindHost);
        		return;
        	}
        }
        try {
            Long timestamp = System.currentTimeMillis();
            String token = SecureUtil.md5(timestamp + privKey);
            String toTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            Integer pageSize = 50;
            Integer pageNo = 1;
            Integer pageTotal = pageNo;
            fromTime=DateUtil.lastWeek().toDateStr();
            while (pageNo < pageTotal + 1) {
                StringBuilder sb = new StringBuilder(orgListUrl);
                HashMap paramMap = new HashMap<>();
                paramMap.put("fromTime", URLEncoder.encode(fromTime, "utf-8"));
                paramMap.put("toTime", URLEncoder.encode(toTime, "utf-8"));
                paramMap.put("sysId", sysId);
                paramMap.put("pageNo", String.valueOf(pageNo));
                paramMap.put("pageSize", String.valueOf(pageSize));
                paramMap.put("timestamp", String.valueOf(timestamp));
                paramMap.put("token", token);
                String url = sb.append("?").append(HttpUtil.mapToUrlParameter(paramMap)).toString();
                log.info("request url = {}", url);
                String result = HttpClient.httpGet(url);
                log.info("orginfo result = {}", result);
                if (StringUtils.isNotBlank(result)) {
                    ResponseDto<List<OrgInfoDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
                    if (responseDto != null && responseDto.getCode() == 0) {
                        pageTotal = responseDto.getTotalPages();
                        if (responseDto.getResult() != null) {
                            for (Object str : responseDto.getResult()) {
                                String info = JsonUtil.convertObjectToJson(str);
                                OrgInfoDto orgInfoDto = JsonUtil.convertJsonToBean(info, OrgInfoDto.class);
                                CsUcOrg orgInfo = convert(orgInfoDto);
                                csUcOrgService.synOrgInfo(orgInfo);
                            }
                        }
                    }
                }
                Thread.sleep(1000);
                pageNo++;
            }
        } catch (Exception e) {
            log.error(e, "pull org info fail! {}", e.getMessage());
        }

    }

    private CsUcOrg convert(OrgInfoDto orgInfoDto) {
        CsUcOrg orgInfo = new CsUcOrg();
        BeanUtils.copyProperties(orgInfoDto, orgInfo);
        orgInfo.setCreateDate(new Date());
        orgInfo.setUpdateDate(new Date());
        return orgInfo;
    }
}
