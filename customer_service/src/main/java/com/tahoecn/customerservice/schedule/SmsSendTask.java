/**
 * 
 */
package com.tahoecn.customerservice.schedule;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.tahoecn.customerservice.model.CsSmsTark;
import com.tahoecn.customerservice.service.CsSendSmsLogService;
import com.tahoecn.customerservice.service.CsSmsTarkService;

/**
 * @ClassName SmsSendTask
 * <AUTHOR>
 * @date 2018年12月21日
 */
@Component("smsSendTask")
public class SmsSendTask {
	
	@Autowired
	private CsSmsTarkService csSmsTarkService;
	@Autowired
	private CsSendSmsLogService csSendSmsLogService;
	
	/**
	 * 每分钟定时执行
	 * 获取待发送任务列表，发送短信
	 */
	@Scheduled(cron = "0 0/1 * * * ?")
	public void csSmsSendTark() {
		EntityWrapper<CsSmsTark> wrapper = new EntityWrapper<>();
		wrapper.eq("status", "待发送").and().eq("type", "定时").and().le("runtime", new Date());
		List<CsSmsTark> list = csSmsTarkService.selectList(wrapper);
		if(list != null && list.size() >= 0) {
			list.forEach(e -> {runSend(e);});
		}
	}
	
	/**
	 * 发送短信
	 */
	public void runSend(CsSmsTark csSmsTark) {
		try {
			csSendSmsLogService.sendSms(csSmsTark.getTel(), csSmsTark.getMsg(), csSmsTark.getCreateUserName());
			csSmsTark.setStatus("发送成功");
		} catch (Exception e) {
			csSmsTark.setStatus("发送失败");
		}
		csSmsTarkService.updateById(csSmsTark);
	}

}
