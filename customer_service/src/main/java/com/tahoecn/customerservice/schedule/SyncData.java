//package com.tahoecn.customerservice.schedule;
//
//import com.tahoecn.customerservice.mapper.CsSyncMyCust2houseMapper;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import com.tahoecn.core.util.NetUtil;
//import com.tahoecn.customerservice.service.CsCustInfoService;
//import com.tahoecn.customerservice.service.CsHouseInfoService;
//import com.tahoecn.customerservice.service.CsSyncCust2houseService;
//import com.tahoecn.customerservice.service.CsSyncCustService;
//import com.tahoecn.customerservice.service.CsSyncDataService;
//import com.tahoecn.log.Log;
//import com.tahoecn.log.LogFactory;
//
//@Component
//public class SyncData {
//	private static final Log log = LogFactory.get();
//
//	@Value("${bind_host}")
//	private String bindHost;
//
//	@Autowired
//	private CsSyncDataService csSyncDataService;
//	
//	@Autowired
//	private CsSyncCustService csSyncCustService;
//
//	@Autowired
//	private CsSyncMyCust2houseMapper  syncMyCust2houseMapper;
//	
//	@Autowired
//	private CsCustInfoService csCustInfoService;
//	@Autowired
//	private CsHouseInfoService csHouseInfoService;
//	@Autowired
//	private CsSyncCust2houseService cust2houseService;
//
//	/**
//	 * 同步数据
//	 *
//	 * @throws Exception
//	 */
//	@Scheduled(cron = "0 0 0 * * ?")
//	public void syncData() throws Exception {
//		// 判断执行IP是否匹配当前服务节点
//		if (StringUtils.isNotBlank(bindHost)) {
//			Boolean flg = false;
//			for (String ip : NetUtil.localIpv4s()) {
//				log.info("bind host str={} ,localhost str = {}", bindHost, ip);
//				flg = flg ? true : bindHost.equals(ip);
//			}
//			if (!flg) {
//				log.info("bind host {} != localhost pullUserInfo scheduled exit! ", bindHost);
//				return;
//			}
//		}
//
//		cust2houseService.delete(null);
//
//		syncMyCust2houseMapper.deletesyncMyCust2house();
//
//		// 增量同步数据 并处理至中间表 先物业后明源
//		csSyncDataService.syncWy();
//		csSyncDataService.syncMy();
//		csSyncDataService.updateSignDate();
//
//		// 清理无房屋中间表
//		cust2houseService.deleteByHouse();
//
//		// 处理用户数据
//		csSyncCustService.insertFromCenter();
//
//		csCustInfoService.initData();
//		csHouseInfoService.initData();
//		
//		csCustInfoService.updateIsHasMoreHouse();
//	}
//
//}