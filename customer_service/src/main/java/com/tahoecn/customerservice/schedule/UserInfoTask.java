package com.tahoecn.customerservice.schedule;

import com.tahoecn.core.date.DateUtil;
import com.tahoecn.core.util.JsonUtil;
import com.tahoecn.core.util.NetUtil;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.dto.ResponseDto;
import com.tahoecn.customerservice.model.dto.UserInfoDto;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.http.HttpClient;
import com.tahoecn.http.HttpUtil;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by zhanghw on 2018/10/10.
 * 每天凌晨1点拉取更新用户信息
 */
@Component("userInfoTask")
public class UserInfoTask {
    private static final Log log = LogFactory.get();

    @Value("${uc_api_url_userlist}")
    private String userListUrl;
    @Value("${uc_sysId}")
    private String sysId;
    @Value("${uc_priv_key}")
    private String privKey;
    @Value("${bind_host}")
    private String bindHost;
    private String fromTime = "2000-01-01 00:00:00";

    @Autowired
    private CsUcUserService csUcUserService;

    @Scheduled(cron = "0 0 1 * * ?")
    public void pullUserInfo() {
    	if (StringUtils.isNotBlank(bindHost)) {
        	Boolean flg = false;
        	for (String ip : NetUtil.localIpv4s()) {
        		log.info("bind host str={} ,localhost str = {}", bindHost, ip);
        		flg = flg ? true:bindHost.equals(ip);
				
			}
        	if(!flg) {
        		log.info("bind host {} != localhost pullUserInfo scheduled exit! ", bindHost);
        		return;
        	}
        }
        try {
            Long timestamp = System.currentTimeMillis();
            String token = SecureUtil.md5(timestamp + privKey);
            String toTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            Integer pageSize = 50;
            Integer pageNo = 1;
            Integer pageTotal = pageNo;
            fromTime=DateUtil.lastWeek().toDateStr();
            while (pageNo < pageTotal + 1) {
                StringBuilder sb = new StringBuilder(userListUrl);
                HashMap paramMap = new HashMap<>();
                paramMap.put("fromTime", URLEncoder.encode(fromTime, "utf-8"));
                paramMap.put("toTime", URLEncoder.encode(toTime, "utf-8"));
                paramMap.put("sysId", sysId);
                paramMap.put("pageNo", String.valueOf(pageNo));
                paramMap.put("pageSize", String.valueOf(pageSize));
                paramMap.put("timestamp", String.valueOf(timestamp));
                paramMap.put("token", token);
                String url = sb.append("?").append(HttpUtil.mapToUrlParameter(paramMap)).toString();
                String result = HttpClient.httpGet(url);
                log.info("userinfo result = {}", result);
                if (StringUtils.isNotBlank(result)) {
                    ResponseDto<List<UserInfoDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
                    if (responseDto != null && responseDto.getCode() == 0) {
                        pageTotal = responseDto.getTotalPages();
                        if (responseDto.getResult() != null) {
                            for (Object str : responseDto.getResult()) {
                                String info = JsonUtil.convertObjectToJson(str);
                                UserInfoDto userInfoDto = JsonUtil.convertJsonToBean(info, UserInfoDto.class);
                                CsUcUser userInfo = convert(userInfoDto);
                                csUcUserService.synUserInfo(userInfo);
                            }
                        }
                    }
                }
                Thread.sleep(1000);
                pageNo++;
            }
        } catch (Exception e) {
            log.error(e, "pull user info fail! {}", e.getMessage());
        }

    }

    private CsUcUser convert(UserInfoDto userInfoDto) {
        CsUcUser userInfo = new CsUcUser();
        BeanUtils.copyProperties(userInfoDto, userInfo);
        userInfo.setCreateTime(new Date());
        userInfo.setUpdateTime(new Date());
        return userInfo;
    }


}
