package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsFormInst;
import java.util.List;

/**
 * <p>
 * 流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */

public interface BaseProcessService {
    public List getUserByRoleCode(String roleCode,String projectCode);

    public void specialEnd(String formInstId);

    public void upgrade(CsFormInst csForminst, String templateCode, Long upgradeLevel) throws Exception;

    public void sendSms(String templateCode,CsFormInst csForminst,String assignUserName,String mobile);
}
