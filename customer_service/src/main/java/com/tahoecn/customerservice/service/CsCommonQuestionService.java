package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsCommonQuestion;
import com.baomidou.mybatisplus.service.IService;

import java.util.List;

/**
 * <p>
 * 常见问题表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsCommonQuestionService extends IService<CsCommonQuestion> {

    Long addCommonQuestion(CsCommonQuestion cq);

    List<CsCommonQuestion> selectCommQuestionAndPersonal(String userName);

    CsCommonQuestion selectQuestionById(Long id);
}
