package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsCustFamily;
import com.tahoecn.customerservice.model.CsCustInfo;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.service.IService;

/**
 * <p>
 * 客户家庭成员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsCustFamilyService extends IService<CsCustFamily> {

	/**
	 * 查询家庭成员
	 * @param map
	 * @return
	 */
	List<CsCustFamily> certification(Map<String, Object> map);
	
	/**
	 * 增加业主微信
	 * @param CsCustFamily 家庭成员信息
	 * @param weChat 微信
	 */
	String updateWeChat(List<CsCustFamily> csCustFamily, String weChat,String openId);

}
