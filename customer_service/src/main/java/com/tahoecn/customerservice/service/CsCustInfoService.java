package com.tahoecn.customerservice.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.dto.CsCustInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;


/**
 * <p>
 * 客户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsCustInfoService extends IService<CsCustInfo> {

	/**
	 * 获取房人工单联查数据
	 * 
	 * @param dto
	 * @return
	 */
	Page<CsCustInfo> getByHcfInfoDto(HcfInfoDto dto);
	
	void saveOrUpdateCsCustInfo(CsCustInfo csCustInfo);
	
	void insertCsCustInfo(CsCustInfo csCustInfo);
	
	List<CsCustInfo> selectByCustId(String custId);

	List<CsCustInfoDto> selectCustInfoList(Map<String, Object> map);

	Integer getListCount(Map<String, Object> map);

	int insertMyData();

	void updateIsHasMoreHouse();

	void insertTeltempData();

	List<HashMap<String, Object>> selectDifferentTel();

	void updateTelBycustId(HashMap<String, Object> map);

	void deleteTeltempDate();
	
	List<CsHouseInfoCustInfoFrom> selectCsHouseInfoCustInfoFrom(Map<String, Object> map);

	void updateCanNotDelete();

	/**
	 * 增加业主微信
	 * @param csCustInfo 业主信息
	 * @param weChat 微信
	 */
	String updateWeChat(List<CsCustInfo> csCustInfo, String weChat,String openId);

	/**
	 * 查询业主信息
	 * @param map
	 * @return
	 */
	List<CsCustInfo> certification(Map<String,Object> map);

	/**
	 * 根据业主id和房屋num查询业主信息
	 * @return
	 */
	List<CsCustInfo> selectCustByIdNum(Map<String, Object> map);

	/**
	 * APP-查询客户信息
	 * @param map
	 * @return
	 */
	List<CsCustInfo> getCustInfo(Map<String, Object> map);

	/**
	 * APP-更新业主标签
	 * @param map
	 */
	void updateCustLabel(Map<String, Object> map);

	/**
	 * 手机APP-更改客户头像
	 * @param map
	 */
	void updateHeadPortrait(Map<String, Object> map);
	
	/**
	 * 初始化数据
	 */
	void initData();

}
