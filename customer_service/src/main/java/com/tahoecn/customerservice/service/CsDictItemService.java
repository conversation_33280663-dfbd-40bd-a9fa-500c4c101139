package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsDictItem;
import com.baomidou.mybatisplus.service.IService;

import java.util.List;
import java.util.Map;

public interface CsDictItemService extends IService<CsDictItem> {

    List<CsDictItem> selectAllCsDictItemByDictCode(String dictCode);

    CsDictItem selectCsDictItemByItemCode(String itemCode);

    List<CsDictItem> selectAllCsDictItem();

    ResponseMessage selectTreeDictItem();

    ResponseMessage selectOneDictItem(String itemCode);

    ResponseMessage updateDictItemInfo(Map<String, Object> pamam);

	List<Map<String, Object>> selectItTreeDictItem();

	ResponseMessage updateItDictItem(Map<String, Object> pamam);

	ResponseMessage insertItDictItem(Map<String, Object> pamam);

	ResponseMessage deleteItDictItem(String itemCode);
}
