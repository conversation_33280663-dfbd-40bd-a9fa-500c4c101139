package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsFile;
import com.baomidou.mybatisplus.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 附件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsFileService extends IService<CsFile> {

    CsFile selectFileById(Long id);

    Long saveUploadFile(MultipartFile file,String clientPath);

    void deleteCsFile(String formNo,Long id);

    List<CsFile> selectFileByFormNo(String formGenerateId);

    CsFile selectCsFileById(Long id);

    List<CsFile> selectCsFileByIdList(List<Long> list);

    Long relationFormAndFiles(Long id,List<Long> ids);

    /**
     * 微信公众号保存关联图片信息
     * @param id
     * @param pictureUrl
     */
	void saveFile(Long id, List<String> pictureUrl);
	/**
	 * 保存关联图片信息
	 * @param id
	 * @param pictureUrl
	 */
	void saveCommFile(Long id, List<String> pictureUrl);
}
