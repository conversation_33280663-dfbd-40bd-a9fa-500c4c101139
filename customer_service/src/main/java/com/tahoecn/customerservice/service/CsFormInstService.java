package com.tahoecn.customerservice.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.dto.AppFormListDto;
import com.tahoecn.customerservice.model.dto.ComplaintPageDto;
import com.tahoecn.customerservice.model.dto.CsFormInstDto;
import com.tahoecn.customerservice.model.dto.FamilyListDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.dto.OwnerFormDto;
import com.tahoecn.customerservice.model.excelDTO.ExpForm;
import com.tahoecn.customerservice.model.excelDTO.ItExpForm;
import com.tahoecn.customerservice.model.vo.*;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsFormInstService extends IService<CsFormInst> {

    List<CsFormInst> findTemporaryList(Map<String, Object> csFormInstWrapper, Page<CsFormInst> page);

    List<CsFormInstDto> findReturnVisitList(Map<String, Object> map, Page<CsFormInstDto> page);

    /**
     * 保存或修改工单信息
     *
     * @param dto
     * @param username
     * @return
     */
    Long saveOrUpate(FamilyListDto dto);

    /**
     * 报事分派列表查询
     */
    public ResponseMessage selectFormAssignPageList(Map<String, Object> csFormInstWrapper, Integer pageNum, Integer pageSize);

    /**
     * 报事分派列表查询
     */
    public ResponseMessage selectFormHandlingPageList(Map<String, Object> csFormInstWrapper, Integer pageNum, Integer pageSize);

    /**
     * 获取房人工单联查数据
     *
     * @param dto
     * @return
     */
    Page<CsFormInst> getByHcfInfoDto(HcfInfoDto dto);

    CompareVo getCompareField(Long formInstId);

    List<CsFormInst> selectUpgradeList();

    /**
     * 获取工单号
     *
     * @param firstType
     * @return
     */
    String getNo(String firstType);

    /**
     * 查询重大投诉分页集合
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<McomplaintVo> findComplaintPageList(Integer pageNum, Integer pageSize, ComplaintPageDto complaintPageDto);

    McomplaintDetailVo findComplaintDetail(Long id);

    /**
     * 查询/导出
     *
     * @param page
     * @param map
     * @return
     */
    List<ExpForm> expForm(Page<ExpForm> page, Map<String, Object> map);

    CustomerStatisticsReportVo customerStatisticsReportCount(int range);

    CustomerStatisticsReportUpVo customerStatisticsReportUpCount(Integer range, Integer region, Integer type);

    CustomerStatisticsSatisficingVo customerStatisticsSatisficingCount(Integer range);

    CustomerStatisticsReportUpNextVo customerStatisticsReportUpNextCount(Integer range, Integer region, Integer type, String regionName);

    /**
     * 物业报事提交
     */
    ResponseMessage propertyReportSubmission(String wyFormNo, String firstSortCode, String mobile, String customerDemand, String projectCode, String creationDate, String ownerId, String houseInfoId, String createUserName);

    /**
     * 根据报事工单id 查询报事信息
     */
    ResponseMessage updateProcessStateNameByformNo(String formNo, String processStateName);

    /**
     * 公区报事提交
     */
    void sectorNewspaperSubmission(CsFormInst csFormInst) throws Exception;

    /**
     * 户内报事提交
     */
    void indoorNewspaperSubmission(CsFormInst csFormInst) throws Exception;

    /**
     * 工单状态推送 - 已完结
     */
    void workOrderNumberQueryWorkOrderStatus(String CommunityId, String IncidentNum) throws Exception;

    List<CustomerStatisticsReportGroupVo> customerStatisticsReportCountNext(int range, String firstSortCode);

    /**
     * 报事趋势统计(日均)
     *
     * @return
     */
    List<DayOfMonthReportVo> dayOfMonthReportCount();

    CustomerStatisticsReportVo itStatisticsReportCount(Integer range, Integer type);

    CustomerStatisticsReportVo itStatisticsReportGroupNext(Integer range, String projectCode, Integer type);

    List<ItExpForm> getITList(Page<ItExpForm> page, Map<String, Object> map);

    CustomerStatisticsReportAccidentVo itStatisticsAccidentReportCount(Integer range, Integer type);

    List<ItAccidentReportNextVo> itAccidentReportNextCount(Integer range, Integer type, String firstSortCode);

    List<ItAccidentReportNextVo> itAccidentReportNextQuery(Integer range, Integer type, String firstSortCode, String time);

    /**
     *
     * 屏显报事统计
     * @return
     */
    ScreenReportCountVo screenReportCount();
    
    /**
     * 查询业主报事列表
     * @param page
     * @param map
     * @return
     */
    List<OwnerFormDto> getOwnerForm(Page<OwnerFormDto> page, Map<String, Object> map);

    /**
     * 微信报事的取消操作-正常关闭
     * @param formInstId
     * @param cancelReason 取消原因
     */
	void setWeChatFormCancel(String formInstId,String cancelReason);

	/**
	 * 报事进度查询列表
	 * @param map
	 * @return
	 */
	List<CsFormInst> getWeChatFormList(Map<String, String> map);

	/**
	 * 报事进度详情
	 * @param map
	 * @return
	 */
	FamilyListDto setWeChatFormDetails(Map<String, String> map);
	
	/**
	 * 微信公众号生成报事信息
	 * @param map
	 * @return
	 */
	CsFormInst saveFormInst(Map<String, Object> map);
	
	/**
     * 千丁报事提交
     */
    ResponseMessage qdSubmission(Map<String, Object> map);

    /**
     * APP-查询报事处理，分派列表
     * @param map
     * @return
     */
	List<CsFormInst> getAssAndHandleList(Map<String, Object> map);
	
	/**
     * APP-查询报事升级列表
     * @param map
     * @return
     */
	List<CsFormInst> getUpgradeFormList(Map<String, Object> map);
	
	/**
     * APP-查询报事查询列表
     * @param map
     * @return
     */
	List<CsFormInst> getDeptDCFormList(Map<String, Object> map);

	/**
	 * APP-查询报事抢单列表
	 * @param map
	 * @return
	 */
	List<CsFormInst> getGrabFormList(Map<String, Object> map);

    /**
     * 修改当前处理人
     * @param list
     * @return
     */
    String modify(List list);

    /**
     * APP-查询报事升级列表数量
     * @param userName
     * @return
     */
	int selectUpgradeCount(String curAssigneeId);

	/**
     * APP-查询报事抢单列表数量
     * @param userName
     * @return
     */
	int selectGrabCount(String userName);
}
