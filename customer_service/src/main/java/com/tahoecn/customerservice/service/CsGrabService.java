package com.tahoecn.customerservice.service;

import java.util.List;

import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsGrab;
import com.tahoecn.customerservice.model.CsProjectInfo;

/**
 * <p>
 * 抢单配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-21
 */
public interface CsGrabService extends IService<CsGrab> {
	/**
	 * 获取当前code 所有项目
	 * 
	 * @param code
	 * @return
	 */
	List<CsProjectInfo> selectProjectByCode(String code);

	/**
	 * 通过 code 删除数据
	 * 
	 * @param code
	 */
	void deleteByCode(String code);

	/**
	 * 批量添加 code
	 * 
	 * @param code
	 * @param projects
	 */
	void insertGrab(String code, String projects);

}
