package com.tahoecn.customerservice.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.dto.CsHouseCodeDto;
import com.tahoecn.customerservice.model.dto.CsHouseInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;

/**
 * <p>
 * 房屋信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsHouseInfoService extends IService<CsHouseInfo> {

	List<CsHouseInfo> selectHouseInfoRelation(CsHouseInfo ci);

	/**
	 * 获取房人工单联查数据
	 * 
	 * @param dto
	 * @return
	 */
	Page<CsHouseInfo> getByHcfInfoDto(HcfInfoDto dto);

	/**
	 * 通过房号查房
	 * 
	 * @param houseNum
	 * @return
	 */
	CsHouseInfoDto selectPHouse(String houseNum);

	List<CsHouseInfo> selectListPage(Map<String, Object> map, Page<CsHouseInfo> page);

	int insertMyData();

	List<CsHouseInfoCustInfoFrom> selectCsHouseInfoCustInfoFrom(Map<String, Object> map);

	/**
	 * 查询业主房屋信息
	 * 
	 * @param custId
	 *            业主id字符串
	 * @return
	 */
	List<CsHouseInfo> selectCustHouseByCustId(String custId);

	/**
	 * 查询业主房产对应项目列表
	 * 
	 * @param custId
	 *            业主id字符串
	 * @return
	 */
	List<CsHouseCodeDto> selectCustHouseProjectByCustId(String custId);

	/**
	 * 查询业主房产对应区域列表
	 * 
	 * @param custId
	 *            业主id字符串
	 * @return
	 */
	List<CsHouseCodeDto> selectCustHouseRegionByCustId(String custId);

	CsHouseInfo selectAllHoustInfoBy(String houseNum);

	/**
	 * APP-根据项目id查询房屋列表
	 * 
	 * @param projectId
	 * @return
	 */
	List<Map<String, Object>> getHouseListByProjectId(String projectId);

	/**
	 * APP-根据房屋编码查询详情，关联业主信息
	 * 
	 * @param houseNum
	 * @return
	 */
	List<Map<String, Object>> getHouseDetailByNum(String houseNum);

	/**
	 * 初始化数据
	 */
	void initData();
}
