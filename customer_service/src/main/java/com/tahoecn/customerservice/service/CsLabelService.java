package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsLabel;
import com.tahoecn.customerservice.model.dto.CsLabelTreeDto;
import com.tahoecn.customerservice.model.dto.Tree;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;

/**
 * <p>
 * 标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-09
 */
public interface CsLabelService extends IService<CsLabel> {

	/**
	 * 级联删除菜单
	 * @param labelId
	 */
	void delLabels(String labelId);

	List<CsLabelTreeDto> getLabelTree(Map<String, Object> map);

}
