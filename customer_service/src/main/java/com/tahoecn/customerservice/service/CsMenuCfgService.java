package com.tahoecn.customerservice.service;

import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsMenuCfg;
import com.tahoecn.customerservice.model.dto.MenuDO;
import com.tahoecn.customerservice.model.dto.Tree;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 菜单URL配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsMenuCfgService extends IService<CsMenuCfg> {

    String selectUrlByMenuCode(String menuCode);

    List<Map<String, String>> selectUrlByCodeList(List<String> list);

    /**
     * 获取当前用户显示的菜单
     */
    List<Tree<MenuDO>> listMenuTreeByUser();
}
