package com.tahoecn.customerservice.service;

import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;

import java.util.List;

/**
 * <p>
 * 待办表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsProcessWorkitemService extends IService<CsProcessWorkitem> {
    //没有提交时增加待办
    public void addWorkitem(Long formInstId, String comment, String operateType, CsFormInst csFormInst);

    //获取操作记录
    public List getWorkItemList(String formInstId);

    //获取进程记录
    public List getProgressWorkItemList(String formInstId);

    CsProcessWorkitem getLastWorkitemById(Long formInstId);
    
    /**
     * 业主报事提交增加“业主报事”待办
     */
    void setOwnerProcess(Long formInstId, String comment, String operateType, CsFormInst csFormInst);

    /**
     * 通过状态查询
     * @param state
     * @return
     */
    CsProcessWorkitem selectByFormId(String id);
}
