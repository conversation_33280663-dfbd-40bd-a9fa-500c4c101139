package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.dto.MatterStatisticalDto;
import com.tahoecn.customerservice.model.dto.MatterStatisticalParentDto;
import com.tahoecn.customerservice.model.dto.StatisticalDto;
import com.tahoecn.customerservice.model.vo.StatisticalVo;
import com.baomidou.mybatisplus.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 初始化项目信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsProjectInfoService extends IService<CsProjectInfo> {

	List<CsProjectInfo> selectProjectInfoRelation(CsProjectInfo pi);

	List<MatterStatisticalParentDto> recursiveGet(String[] indexName, int i, String condition, String limitTimeStart,String limitTimeEnd);

	List<Integer> getConditionsNum(String limitTimeStart,String limitTimeEnd);

	List<StatisticalVo> satisfaction(String limitTimeStart,String limitTimeEnd);

	List<Object> getData(String region, String city, String condition, String limitTimeStart,String limitTimeEnd);

	Integer getCountNum(String limitTimeStart,String limitTimeEnd, Integer i);

	List<Object> getData(String limitTimeStart,String limitTimeEnd);

	Map<String, Object> getProDetailByCode(String code, String codeType);

	CsProjectInfo selectAllProjectByProjectCode(String projectId);

	/**
	 * APP-查询所有区域城市项目
	 * @return
	 */
	List<Map<String, Object>> getProjectList();

}
