package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsSupplierInfo;
import com.baomidou.mybatisplus.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 供方数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-18
 */
public interface CsSupplierInfoService extends IService<CsSupplierInfo> {

    List<CsSupplierInfo> selectNameByArea(CsSupplierInfo si);

    /**
     * APP-手机端查询主责单位，维修单位
     * @return
     */
	List<CsSupplierInfo> getAppNameByArea(Map<String,Object> map);
}
