package com.tahoecn.customerservice.service;

import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsSyncCust2house;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public interface CsSyncCust2houseService extends IService<CsSyncCust2house> {

	/**
	 * 同步明源数据至中间表
	 */
	void syncMyCust2House();

	/**
	 * 同步物业数据至中间表
	 */
	void syncWyCust2House();
	
	/**
	 * 删除没有房源的关联关系
	 */
	void deleteByHouse();

}
