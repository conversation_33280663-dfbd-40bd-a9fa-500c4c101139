package com.tahoecn.customerservice.service;

import java.util.List;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsSyncCustAbnormal;
import com.tahoecn.customerservice.model.dto.CsSyncCustAbnormalDto;

/**
 * <p>
 * 客户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-08
 */
public interface CsSyncCustAbnormalService extends IService<CsSyncCustAbnormal> {

	/**
	 * 查询当前 证件号对应数据
	 * 
	 * @param id
	 * @param certificateNum
	 * @return
	 */
	List<CsSyncCustAbnormal> abnormal(String id, String certificateNum);

	/**
	 * 通过证件号清理数据
	 * 
	 * @param certificateNum
	 */
	void removeByCode(String certificateNum);

	/**
	 * 查询异常数据
	 * 
	 * @param pageSize
	 * @param pageNum
	 * @param abnormal
	 * @return
	 */
	Page<CsSyncCustAbnormalDto> selectDtoList(Integer pageSize, Integer pageNum, CsSyncCustAbnormal abnormal);

}
