package com.tahoecn.customerservice.service;

import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.CsSyncCust;

/**
 * <p>
 * 客户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public interface CsSyncCustService extends IService<CsSyncCust> {

	/**
	 * 同步明源数据至中间表
	 */
	void syncMyCust();

	/**
	 * 同步物业数据至中间表
	 */
	void syncWyCust();
	/**
	 * 中间表同步
	 */
	void insertFromCenter();

	/**
	 * 保留数据
	 * @param id
	 */
	void retainCustById(String id);
	
	/**
	 * 修改用户信息
	 * @param custInfo
	 */
	void updateCust(CsCustInfo custInfo);
}
