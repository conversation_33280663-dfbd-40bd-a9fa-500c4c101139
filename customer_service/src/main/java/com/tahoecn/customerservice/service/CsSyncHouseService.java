package com.tahoecn.customerservice.service;

import java.util.List;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.CsSyncHouse;
import com.tahoecn.customerservice.model.excelDTO.DeliverDto;

/**
 * <p>
 * 房屋信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
public interface CsSyncHouseService extends IService<CsSyncHouse> {

	/**
	 * 同步明源数据至中间表
	 */
	void syncMyHouse();

	/**
	 * 同步物业数据至中间表
	 */
	void syncWyHouse();

	/**
	 * 查询交付列表
	 * 
	 * @param current
	 * @param size
	 * @param csSyncHouse
	 * @return
	 */
	Page<CsSyncHouse> selectDelivery(Integer current, Integer size, CsSyncHouse csSyncHouse);

	/**
	 * 修改交付状态
	 * 
	 * @param projectId
	 * @param building
	 * @param deliveryStatus
	 */
	void updateDelivery(String projectId, String building, Integer deliveryStatus);
	
	/**
	 * 导出交付数据
	 * 
	 * @param csSyncHouse
	 * @return
	 */
	List<DeliverDto> expDeliver(CsSyncHouse csSyncHouse);

	List<DeliverDto> expDeliver(String projectId, String building);

}
