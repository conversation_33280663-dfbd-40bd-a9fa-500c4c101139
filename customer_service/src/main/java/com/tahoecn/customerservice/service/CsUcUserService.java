package com.tahoecn.customerservice.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.UserPackage;
import com.tahoecn.customerservice.model.dto.CsUserRoleDto;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * UC用户信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsUcUserService extends IService<CsUcUser> {

    /**
     * 同步用户信息
     *
     * @param userInfo
     */
    void synUserInfo(CsUcUser userInfo);

    /**
     * 获取用户信息，实现用户信息查询功能
     *
     * @param userName 用户帐号
     * @return 用户信息
     */
    CsUcUser selectByUsername(String userName);

    /**
     * sso直接获得用户信息
     *
     * @return
     */
    CsUcUser selectByUsername();

    /**
     * 查询用户对应的角色信息
     */
    List<CsUserRoleDto> selectRoleByUserId(String userId);

    /**
     * 获取前端需要的用户信息包
     * @return
     */
    UserPackage getUserPackage();

    ResponseMessage findUserByOrgId(String orgId);
    
    Page<CsUcUser> userForm(Map<String, Object> map, Page<CsUcUser> page);

    ResponseMessage findUsersByNameOrCode(String username);
    
    void userLogin(String account,String password,HttpServletRequest request);
}
