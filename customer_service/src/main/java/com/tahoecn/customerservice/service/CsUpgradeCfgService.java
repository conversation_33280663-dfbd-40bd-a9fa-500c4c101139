package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsUpgradeCfg;
import com.tahoecn.customerservice.model.CsUpgradeCfgBak;
import com.baomidou.mybatisplus.service.IService;

import java.util.Map;

/**
 * <p>
 * 升级配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface CsUpgradeCfgService extends IService<CsUpgradeCfg> {

    ResponseMessage selectTreeUpgradeCfg();

    ResponseMessage saveUpgradeCfg(Map<String, Object> pamam);

    ResponseMessage selectOneUpgradeCfg(String type, String code);

    /**
     * 手机APP-查询工单剩余处理天数
     * @param formId
     * @return
     */
    CsUpgradeCfgBak selDayByFormId(String formId);

}
