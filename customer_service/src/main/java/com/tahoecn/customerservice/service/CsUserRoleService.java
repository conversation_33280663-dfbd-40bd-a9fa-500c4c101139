package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.CsUserRole;
import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.model.dto.ProjectRoleAttrDO;
import com.tahoecn.customerservice.model.dto.ProjectRoleDO;
import com.tahoecn.customerservice.model.dto.Tree;
import com.tahoecn.customerservice.model.dto.TreeExt;

import java.util.List;

import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-16
 */
public interface CsUserRoleService extends IService<CsUserRole> {
    List<TreeExt<ProjectRoleDO>> getTreeList();

    List<ProjectRoleDO> getUserRoleProject();

    /**
     * 根据类型查询对应的负责人集合
     *
     * @param type
     * 区分类型
     * 1.泰禾集团
     * 2.区域
     * 3.城市
     * 4.项目
     * @param typeName
     * @return
     */
    ProjectRoleAttrDO getByType(String type, String typeName,Map<String,String> cacheOrgIdMap);

    /***
     * @param pamam
     * 区分类型
     * 1.泰禾集团
     * 2.区域
     * 3.城市
     * 4.项目
     */
    ResponseMessage updateUserRole(Map<String, Object> pamam);

    /**
     * 获取组织数
     * @return  json 字符串
     */
    ResponseMessage orgTree();

	ResponseMessage selectItUser();

	ResponseMessage saveItUser(Map<String, Object> pamam);
}
