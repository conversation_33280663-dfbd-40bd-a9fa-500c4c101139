package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.CsFormInst;
import java.util.List;

/**
 * <p>
 * 流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
public interface ProcessService extends BaseProcessService {
    public void process(CsFormInst csForminst,String formId, String firstsortCode, String deptCode, String processStateCode,
                        String handleRecord, String comment, String assignUserId, String assignUserName, String mobile,String operateType);

    public List getUserByRole(String operateType,Long formInstId);

    public void updrade(CsFormInst csFormInst) throws Exception;

}
