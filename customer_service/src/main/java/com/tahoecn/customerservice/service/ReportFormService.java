/**
 * 
 */
package com.tahoecn.customerservice.service;

import java.util.List;
import java.util.Map;

import com.tahoecn.customerservice.model.report.ChannelDto;
import com.tahoecn.customerservice.model.report.ClassifyDto;
import com.tahoecn.customerservice.model.report.CurReportDto;
import com.tahoecn.customerservice.model.report.RepairWholeDto;
import com.tahoecn.customerservice.model.report.UpgradeDto;

/**
 * 报表
 * @ClassName ReportFormService
 * <AUTHOR>
 * @date 2019年1月9日
 */
public interface ReportFormService {
	
	/**
	 * 报修整体数据统计
	 * @param map
	 * @return
	 */
	List<RepairWholeDto> repairWhole(Map<String, Object> map);
	
	/**
	 * 报事分类数据统计
	 * @param map
	 * @return
	 */
	List<ClassifyDto> classify(Map<String, Object> map);
	
	/**
	 * 报事升级统计
	 * @param map
	 * @return
	 */
	List<UpgradeDto> upgrade(Map<String, Object> map);

	/**
	 * 报事渠道统计
	 * @param map
	 * @return
	 */
	List<ChannelDto> channel(Map<String, Object> map);
	
	/**
	 * 关闭升级返修报表
	 * @param map
	 * @return
	 */
	List<CurReportDto> curReport(Map<String, Object> map);
}
