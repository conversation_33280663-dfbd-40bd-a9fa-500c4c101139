package com.tahoecn.customerservice.service;

import java.util.List;

import com.tahoecn.customerservice.model.dto.SysprivDto;
import com.tahoecn.customerservice.model.dto.UserDataPrivDto;
import com.tahoecn.customerservice.model.dto.UserStandardRoleDto;

public interface RolePrivInfoService {
    /**
     * 获取用户数据权限
     *
     * @return
     */
    List<UserDataPrivDto> getUserDataPrivInfo(String userName);

    /**
     * 获取登录数据权限
     *
     * @return
     */
    List<UserDataPrivDto> getUserDataPrivInfo();

    /**
     * 根据登录用户查询权限
     *
     * @return
     */
    List<SysprivDto> getsysprivInfo();

    /**
     * 根据用户名获取用户系统权限
     *
     * @param userName
     * @return
     */
    List<SysprivDto> getsysprivInfo(String userName);
    
    /**
     * 查询用户是不是管理员身份
     * return 0:不是管理员  1：是管理员
     */
    Integer isAdmin();
    
    /**
     * 获取用户标准角色列表
     */
    List<UserStandardRoleDto> getUserStandardRoleList(String userName);
}
