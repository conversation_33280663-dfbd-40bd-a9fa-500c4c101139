package com.tahoecn.customerservice.service;

import java.util.List;

import com.baomidou.mybatisplus.service.IService;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.model.UserInfo;

/**
 * <p>
 * 用户 服务类
 * </p>
 *
 * <AUTHOR>  huxin
 * @since 2018-10-10
 * @date 2018-10-12 
 */
public interface UserInfoService extends IService<UserInfo> {
    
    /**
     * 同步用户信息
     * @param userInfo
     */
    void synUserInfo(UserInfo userInfo);
    
//    /**
//     * 获取部门的用户，实现添加管理员选人功能
//     * @param orgId  部门Sid
//     * @return 该部门下的用户信息
//     */
//    ResponseMessage findUserByOrgId(String orgId);
//
//    /**
//     * 获取用户信息，实现用户信息查询功能
//     * @param userName  用户帐号
//     * @return  用户信息
//     */
//    UserInfo selectByUsername(String userName);
//
//    /**
//     * 获取用户权限  实现权限控制功能
//     * @param branchId  分支id
//     * @param flag   1.分支访问    2.菜单访问   
//     * @return  String[0] 状态    String[1] 状态信息
//     */
//    String[] getUserPermission(Integer branchId,String flag);
//    
//    /**
//     * 查询用户信息，实现添加管理员选人功能
//     * @param username  用户名或用户帐号
//     * @return
//     */
//    ResponseMessage findUsersByNameOrCode(String username);
//    
//    /**
//     * 查询用户 实现日志记录功能
//     * @param sid  用户sid
//     * @return
//     */
//    UserInfo findUserBySid(String sid);
//
//    List<UserInfo> findiii(String name, String orgName);

}
