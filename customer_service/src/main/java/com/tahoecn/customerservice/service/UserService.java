package com.tahoecn.customerservice.service;

import com.tahoecn.customerservice.model.User;
import com.baomidou.mybatisplus.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-16
 */
public interface UserService extends IService<User> {

    List<Map<String,Object>> getUsersByXml();

    List<Map<String,Object>> getUsersByAnnotation();

    int filterUser(List<User> users);
}
