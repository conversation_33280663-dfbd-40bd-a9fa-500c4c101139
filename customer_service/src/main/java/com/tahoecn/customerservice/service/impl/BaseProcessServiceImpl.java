package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.utils.OASend;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.utils.VelocityUtils;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.CsSmsTemplate;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.CsUserRole;
import com.tahoecn.customerservice.service.BaseProcessService;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsSendSmsLogService;
import com.tahoecn.customerservice.service.CsSmsTemplateService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.CsUserRoleService;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.velocity.VelocityContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 流程基础类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class BaseProcessServiceImpl implements BaseProcessService {
	
	public Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private CsFormInstService csFormInstService;
    @Autowired
    private CsUserRoleService csUserRoleService;
    @Autowired
    private CsProcessWorkitemService csProcessWorkitemService;
    @Autowired
    private CsUcUserService csUcUserService;
    @Autowired
    private CsSmsTemplateService csSmsTemplateService;
    @Autowired
    private CsSendSmsLogService csSendSmsLogService;
    @Autowired
	private OASend oaSend;
    
    //业务状态
    protected static final Map businessStateMap = new HashMap();

    //节点状态
    protected static final Map processStateMap = new HashMap();

    // 快速处理流程 咨询
    protected static final Map qConsultationNextMap = new HashMap();
    protected static final Map qConsultationRejectMap = new HashMap();

    //快速处理流程 表扬与建议
    protected static final Map qPraiseAndSuggestionNextMap = new HashMap();
    protected static final Map qPraiseAndSuggestionRejectMap = new HashMap();

    //标准流程 投诉
    protected static final Map sComplaintNextMap = new HashMap();
    protected static final Map sComplaintRejectMap = new HashMap();

    //标准流程 报修
    protected static final Map sRepairNextMap = new HashMap();
    protected static final Map sRepairRejectMap = new HashMap();

    //标准流程 咨询
    protected static final Map sConsultationNextMap = new HashMap();
    protected static final Map sConsultationRejectMap = new HashMap();

    //IT流程 快处
    protected static final Map qItNextMap = new HashMap();
    protected static final Map qItRejectMap = new HashMap();

    //IT流程 
    protected static final Map itNextMap = new HashMap();
    protected static final Map itRejectMap = new HashMap();


    static {
        processStateMap.put("draft", "报事录入");
        processStateMap.put("toBeAssigned", "报事分派");
        processStateMap.put("handle", "报事处理");
        processStateMap.put("upgrade", "报事升级");
        processStateMap.put("returnVisit", "报事回访");
        processStateMap.put("nomalEnd", "正常关闭");
        processStateMap.put("specialEnd", "特殊关闭");


        businessStateMap.put("draft", "暂存");
        businessStateMap.put("toBeAssigned", "已受理");
        businessStateMap.put("handle", "已分派");
        businessStateMap.put("upgrade", "报事升级");
        businessStateMap.put("returnVisit", "已处理");
        businessStateMap.put("nomalEnd", "正常关闭");
        businessStateMap.put("specialEnd", "特殊关闭");


        // 快速处理流程 咨询========================================================================
        qConsultationNextMap.put("draft", "nomalEnd");

        // 快速处理流程 表扬与建议==================================================================
        qPraiseAndSuggestionNextMap.put("draft", "nomalEnd");

        //标准流程 投诉=============================================================================

        sComplaintNextMap.put("draft", "toBeAssigned");
        sComplaintNextMap.put("toBeAssigned", "handle");
        sComplaintNextMap.put("handle", "returnVisit");
        sComplaintNextMap.put("returnVisit", "nomalEnd");

        sComplaintRejectMap.put("handle", "toBeAssigned");
        sComplaintRejectMap.put("returnVisit", "toBeAssigned");
        //标准流程 报修=============================================================================

        sRepairNextMap.put("draft", "toBeAssigned");
        sRepairNextMap.put("toBeAssigned", "handle");
        sRepairNextMap.put("handle", "returnVisit");
        sRepairNextMap.put("returnVisit", "nomalEnd");

        sRepairRejectMap.put("handle", "toBeAssigned");
        sRepairRejectMap.put("returnVisit", "toBeAssigned");

        //标准流程 咨询=============================================================================
        sConsultationNextMap.put("draft", "handle");
        sConsultationNextMap.put("handle", "nomalEnd");

        //IT快处
        qItNextMap.put("draft", "nomalEnd");

        //IT
        itNextMap.put("draft", "toBeAssigned");
        itNextMap.put("toBeAssigned", "handle");
        itNextMap.put("handle", "nomalEnd");

        itRejectMap.put("handle", "toBeAssigned");


    }

    public List getUserByRoleCode(String roleCode, String projectCode) {
        Wrapper<CsUserRole> wrapper = new EntityWrapper<CsUserRole>();
        wrapper.where("role_code={0}", roleCode).and("project_code={0}", projectCode);
        List userRoleList = csUserRoleService.selectList(wrapper);
        return userRoleList;
    }

    public void specialEnd(String formInstId) {

        CsUcUser curUser = csUcUserService.selectByUsername();
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}", formInstId);
        CsFormInst form = csFormInstService.selectOne(formWrapper);

        //已办
        Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
        wrapper.where("form_inst_id={0}", form.getId()).and("process_state_code={0}", form.getProcessStateCode()).and("task_status={0}", new Long(20));
        CsProcessWorkitem doneItem = csProcessWorkitemService.selectOne(wrapper);
        doneItem.setTaskStatus(new Long(30));//已办
        doneItem.setComment("特殊关闭");
        doneItem.setLastUpdateDate(new Date());
        doneItem.setTaskEndTime(new Date());
        if("1".equals(form.getIsOwner()) && "draft".equals(form.getProcessStateCode())){
        	doneItem.setAssignId(curUser.getFdUsername());
        	doneItem.setAssignName(curUser.getFdName());
        }

        //新增 app来源
        doneItem.setThPlatform(ThreadLocalUtils.getPlatform());
        
        csProcessWorkitemService.updateById(doneItem);
        oaSend.setTodoDone(String.valueOf(doneItem.getId()));

        try {
            if ("物业".equals(form.getSource())) {
                csFormInstService.workOrderNumberQueryWorkOrderStatus(form.getProjectCode(), form.getWyFormNo());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //结束item
        CsProcessWorkitem item = new CsProcessWorkitem();

        item.setProcessStateCode("specialEnd");
        item.setProcessStateName("特殊关闭");
        item.setFormInstId(new Long(formInstId));
        item.setCreationDate(new Date());
        item.setTaskStartTime(new Date());
        item.setTaskEndTime(addOneSecond(new Date()));
        item.setCreateByUserId(curUser.getFdUsername());
        item.setLastUpdateDate(new Date());
        item.setTaskStartTime(new Date());
        item.setTaskStatus(new Long(30));
        item.setAssignId(curUser.getFdUsername());
        item.setAssignName(curUser.getFdName());
        csProcessWorkitemService.insert(item);

        form.setProcessStateCode("specialEnd");
        form.setProcessStateName("特殊关闭");
        form.setCurAssigneeId(curUser.getFdUsername());
        form.setCurAssigneeName(curUser.getFdName());
        csFormInstService.updateById(form);
    }

    @Override
    public void upgrade(CsFormInst csForminst, String templateCode, Long upgradeLevel) throws Exception {
        List Users = this.getUserByRoleCode(templateCode, csForminst.getProjectCode());

        if (Users != null && Users.size() > 0) {
            CsUserRole userRole = (CsUserRole) Users.get(0);
            VelocityContext context = new VelocityContext();
            context.put("formNo", csForminst.getFormNo());
            context.put("assignUserName", userRole.getUserName());
            context.put("project", csForminst.getProject());
            context.put("ownerName", csForminst.getOwnerId());
            context.put("ownerMobile", csForminst.getMobile());
            Date submitDate = csForminst.getSubmitDate();
            String submitDateStr = " ";
            if (submitDate != null) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                submitDateStr = formatter.format(submitDate);
            }
            context.put("submitDate", submitDateStr);
            context.put("customerDemand", csForminst.getCustomerDemand());
            context.put("buildingNo", csForminst.getBuildingNo() == null ? " " : csForminst.getBuildingNo());
            context.put("buildingUnit", csForminst.getBuildingUnit() == null ? " " : csForminst.getBuildingUnit()+"单元");
            context.put("roomNo", csForminst.getRoomNo() == null ? " " : csForminst.getRoomNo()+"室，");
            context.put("secSortName", csForminst.getSecSortName() == null ? " " : csForminst.getSecSortName());
            context.put("thirdSortName", csForminst.getThirdSortName() == null ? " " : csForminst.getThirdSortName());
            context.put("fourthSortName", csForminst.getFourthSortName() == null ? " " : csForminst.getFourthSortName());
            context.put("curAssigneeName", csForminst.getCurAssigneeName());
            context.put("region", csForminst.getRegion());
            context.put("city", csForminst.getCity());
            Wrapper<CsSmsTemplate> templateWrapper = new EntityWrapper<CsSmsTemplate>();
            templateWrapper.where("template_code={0}", templateCode.substring(0, templateCode.length() - 2));
            CsSmsTemplate template = csSmsTemplateService.selectOne(templateWrapper);
            context.put("remarks", userRole.getRemarks());
            int days = this.differentDays(submitDate, new Date());
            context.put("days", days);
            if (template != null) {
                String smsContent = VelocityUtils.getStrByVelocity(context, template.getTemplateContent());
                csSendSmsLogService.sendSms(userRole.getMobile(), smsContent, userRole.getUserName());
            }
            String levelName = "";
            if (upgradeLevel == 1) {
                levelName = "城市";
            } else if (upgradeLevel == 2) {
                levelName = "区域";
            } else if (upgradeLevel == 3) {
                levelName = "集团";
            }

            String comment = "【升级预警】此工单已升级至" + levelName + "负责人 " + userRole.getUserName();

            CsProcessWorkitem item = new CsProcessWorkitem();

            item.setTaskStartTime(new Date());
            item.setProcessStateCode("upgrade");
            item.setProcessStateName("报事升级");
            item.setTaskEndTime(new Date());
            item.setTaskStatus(new Long(30));//已办
            item.setFormInstId(new Long(csForminst.getId()));
            item.setCreationDate(new Date());
            item.setAssignId(userRole.getUserId());
            item.setAssignName(userRole.getUserName());
            item.setCreateByUserId(userRole.getUserId());
            item.setLastUpdateDate(new Date());
            item.setComment("报事升级");
            csProcessWorkitemService.insert(item);

            csForminst.setUpgradeLevel(upgradeLevel);
            csForminst.setUpgradeFlag(new Long(1));
            csFormInstService.updateById(csForminst);

        } else {
            throw new Exception("没有升级人员");
        }
    }

    @Override
    public void sendSms(String templateCode, CsFormInst csForminst, String assignUserName, String mobile) {
        //發送短信開始======================================================================================
        VelocityContext context = new VelocityContext();
        context.put("formNo", csForminst.getFormNo());
        context.put("assignUserName", assignUserName);
        context.put("project", csForminst.getProject());
        context.put("ownerName", csForminst.getOwnerName());
        context.put("ownerMobile", csForminst.getMobile());
        Date submitDate = csForminst.getSubmitDate();
        String submitDateStr = " ";
        if (submitDate != null) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            submitDateStr = formatter.format(submitDate);
        }
        context.put("submitDate", submitDateStr);
        context.put("customerDemand", csForminst.getCustomerDemand());
        context.put("buildingNo", csForminst.getBuildingNo() == null ? " " : csForminst.getBuildingNo());
        context.put("buildingUnit", csForminst.getBuildingUnit() == null ? " " : csForminst.getBuildingUnit());
        context.put("roomNo", csForminst.getRoomNo() == null ? " " : csForminst.getRoomNo());
        context.put("secSortName", csForminst.getSecSortName() == null ? " " : csForminst.getSecSortName());
        context.put("thirdSortName", csForminst.getThirdSortName() == null ? " " : csForminst.getThirdSortName());
        context.put("fourthSortName", csForminst.getFourthSortName() == null ? " " : csForminst.getFourthSortName());
        context.put("curAssigneeName", csForminst.getCurAssigneeName());
        context.put("region", csForminst.getRegion());
        context.put("city", csForminst.getCity());
        Wrapper<CsSmsTemplate> templateWrapper = new EntityWrapper<CsSmsTemplate>();
        templateWrapper.where("template_code={0}", templateCode);
        CsSmsTemplate template = csSmsTemplateService.selectOne(templateWrapper);
        if (template != null) {
            String smsContent = VelocityUtils.getStrByVelocity(context, template.getTemplateContent());
            csSendSmsLogService.sendSms(mobile, smsContent, assignUserName);
        }
        //發送短信結束======================================================================================
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2)   //同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                } else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        } else    //不同年
        {
            //System.out.println("判断day2 - day1 : " + (day2-day1));
            return day2 - day1;
        }
    }

    public Date addOneSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, 1);
        return calendar.getTime();
    }

}
