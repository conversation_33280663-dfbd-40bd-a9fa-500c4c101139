package com.tahoecn.customerservice.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsCommonQuestionMapper;
import com.tahoecn.customerservice.model.CsCommonQuestion;
import com.tahoecn.customerservice.service.CsCommonQuestionService;

/**
 * <p>
 * 常见问题表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsCommonQuestionServiceImpl extends ServiceImpl<CsCommonQuestionMapper, CsCommonQuestion> implements CsCommonQuestionService {

    @Autowired
    private CsCommonQuestionMapper csCommonQuestionMapper;

    public Long addCommonQuestion(CsCommonQuestion cq){
        csCommonQuestionMapper.insertCommonQuestion(cq);
        return  cq.getId();
    }

    public List<CsCommonQuestion> selectCommQuestionAndPersonal(String userName){
        return csCommonQuestionMapper.selectCommQuestionAndPersonal(userName);
    }

    public CsCommonQuestion selectQuestionById(Long id){
        return csCommonQuestionMapper.selectById(id);
    }
}
