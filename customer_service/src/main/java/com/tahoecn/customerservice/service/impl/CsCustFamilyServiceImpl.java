package com.tahoecn.customerservice.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsCustFamilyMapper;
import com.tahoecn.customerservice.mapper.CsCustInfoMapper;
import com.tahoecn.customerservice.model.CsCustFamily;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.service.CsCustFamilyService;

/**
 * <p>
 * 客户家庭成员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsCustFamilyServiceImpl extends ServiceImpl<CsCustFamilyMapper, CsCustFamily> implements CsCustFamilyService {

	@Autowired
    private CsCustFamilyMapper csCustFamilyMapper;
	
	/**
	 * 查询家庭成员
	 * @param map
	 * @return
	 */
	public List<CsCustFamily> certification(Map<String, Object> map){
		return csCustFamilyMapper.findCustFamilyInfo(map);
	}
	
	/**
	 * 增加业主微信
	 * @param csCustInfo 业主信息
	 * @param weChat 微信
	 */
	public String updateWeChat(List<CsCustFamily> csCustFamily, String weChat,String openId){
		String custIds = "";
//		List<HashMap<String,Object>> resulList = new ArrayList<HashMap<String,Object>>();
//		HashMap<String,Object> map = new HashMap<>();
		for(CsCustFamily cust : csCustFamily){
			cust.setWeChat((weChat == null) ? "" : weChat);
			cust.setOpenId((openId == null) ? "" : openId);
			Integer i = baseMapper.updateById(cust);
			System.out.print(i);
			if(!custIds.contains(cust.getCustId())){
//				map.put("custId", cust.getCustId());
				custIds = custIds + cust.getCustId() + ",";
//				resulList.add(map);
			}
		}
		return custIds;
	}
}
