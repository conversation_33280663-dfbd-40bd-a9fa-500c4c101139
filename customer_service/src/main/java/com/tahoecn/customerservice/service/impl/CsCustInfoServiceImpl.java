package com.tahoecn.customerservice.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.common.utils.DataUtil;
import com.tahoecn.customerservice.mapper.CsCustInfoMapper;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.dto.CsCustInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;
import com.tahoecn.customerservice.service.CsCustInfoService;

/**
 * <p>
 * 客户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsCustInfoServiceImpl extends ServiceImpl<CsCustInfoMapper, CsCustInfo> implements CsCustInfoService {
	@Autowired
	private CsCustInfoMapper csCustInfoMapper;

	@Override
	public void saveOrUpdateCsCustInfo(CsCustInfo csCustInfo) {
		CsCustInfo csCustInfoSelect = new CsCustInfo();
		// 根据人员id和house_num
		csCustInfoSelect.setCustId(csCustInfo.getCustId());
		// csCustInfoSelect.setCertificateNum(csCustInfo.getCertificateNum());
		csCustInfoSelect.setHouseNum(csCustInfo.getHouseNum());
		CsCustInfo csCustInfoOne = csCustInfoMapper.selectOne(csCustInfoSelect);
		// 如果存在，不进行更新
		if (csCustInfoOne != null) {
			csCustInfo.setId(csCustInfoOne.getId());
			// csCustInfo.setCreateDate(csCustInfoOne.getCreateDate());
			// csCustInfoMapper.updateById(csCustInfo);
		} else {
			csCustInfoMapper.insert(csCustInfo);
		}
	}

	@Override
	public List<CsCustInfo> selectByCustId(String custId) {
		return csCustInfoMapper.selectByCustId(custId);
	}

	@Override
	public void insertCsCustInfo(CsCustInfo csCustInfo) {
		csCustInfoMapper.insert(csCustInfo);
	}

	@Override
	public Page<CsCustInfo> getByHcfInfoDto(HcfInfoDto dto) {
		Page<CsCustInfo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
		List<CsCustInfo> info = baseMapper.selectHcf(page, dto);
		page.setRecords(info);
		return page;
	}

	@Override
	public List<CsCustInfoDto> selectCustInfoList(Map<String, Object> map) {
		List<CsCustInfoDto> custInfoList = baseMapper.selectCustInfoList(map);

		// chenyy 20190108
		if (null != custInfoList && custInfoList.size() > 0) {
			for (CsCustInfoDto csCustInfo : custInfoList) {
				if ("0".equals(map.get("isAdmin"))) {// 不是管理员则进行加密
					String telephoneStr = csCustInfo.getTelephone();
					String[] phoneArr = telephoneStr.split(",");
					csCustInfo.setTelephone(DataUtil.phoneDec(phoneArr));// 手机号加密
					csCustInfo.setCertificateNum(DataUtil.idCardDec(csCustInfo.getCertificateNum()));// 身份证号加密
				}
				String custName = csCustInfo.getCustName() + "(" + csCustInfo.getCustHouseCount() + ")";
				csCustInfo.setCustName(custName);
			}
		}
		return custInfoList;
	}

	@Override
	public Integer getListCount(Map<String, Object> map) {
		return baseMapper.getListCount(map);
	}

	@Override
	public int insertMyData() {
		return baseMapper.insertMyData();
	}

	@Override
	public void updateIsHasMoreHouse() {
		baseMapper.updateNoHasMoreHouse();
		baseMapper.updateHasMoreHouse();
	}

	@Override
	public void insertTeltempData() {
		baseMapper.insertTeltempData();
	}

	@Override
	public List<HashMap<String, Object>> selectDifferentTel() {
		return baseMapper.selectDifferentTel();
	}

	@Override
	public void updateTelBycustId(HashMap<String, Object> map) {
		baseMapper.updateTelBycustId(map);
	}

	@Override
	public void deleteTeltempDate() {
		baseMapper.deleteTeltempDate();

	}

	@Override
	public List<CsHouseInfoCustInfoFrom> selectCsHouseInfoCustInfoFrom(Map<String, Object> map) {
		List<CsHouseInfoCustInfoFrom> list = csCustInfoMapper.selectCsHouseInfoCustInfoFrom(map);
		for (CsHouseInfoCustInfoFrom csHouseInfoCustInfoFrom : list) {
			if (StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getDeliveryStatus())) {
				if (csHouseInfoCustInfoFrom.getDeliveryStatus().equals("1")) {
					csHouseInfoCustInfoFrom.setDeliveryStatus("已交付");
				} else if (csHouseInfoCustInfoFrom.getDeliveryStatus().equals("-1")) {
					csHouseInfoCustInfoFrom.setDeliveryStatus("未交付");
				}
			}
			if (StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getFitment())) {
				if (csHouseInfoCustInfoFrom.getFitment().equals("1")) {
					csHouseInfoCustInfoFrom.setFitment("是");
				} else if (csHouseInfoCustInfoFrom.getFitment().equals("0")) {
					csHouseInfoCustInfoFrom.setFitment("否");
				}
			}
			if (StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getBelong())) {
				if (csHouseInfoCustInfoFrom.getBelong().equals("0")) {
					csHouseInfoCustInfoFrom.setBelong("个人");
				} else if (csHouseInfoCustInfoFrom.getBelong().equals("1")) {
					csHouseInfoCustInfoFrom.setBelong("单位");
				}
			}
			if (StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getIs_vip())) {
				if (csHouseInfoCustInfoFrom.getIs_vip().equals("0")) {
					csHouseInfoCustInfoFrom.setIs_vip("否");
				} else if (csHouseInfoCustInfoFrom.getIs_vip().equals("1")) {
					csHouseInfoCustInfoFrom.setIs_vip("是");
				}
			}
			if (StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getIsHasMoreHouse())) {
				if (csHouseInfoCustInfoFrom.getIsHasMoreHouse().equals("0")) {
					csHouseInfoCustInfoFrom.setIsHasMoreHouse("否");
				} else {
					csHouseInfoCustInfoFrom.setIsHasMoreHouse("是");
				}
			}
		}
		return list;
	}

	@Override
	public void updateCanNotDelete() {
		baseMapper.updateCanNotDelete();
	}

	/**
	 * 查询业主信息
	 * 
	 * @param map
	 * @return
	 */
	public List<CsCustInfo> certification(Map<String, Object> map) {
		return csCustInfoMapper.findCustInfo(map);
	}

	/**
	 * 增加业主微信
	 * 
	 * @param csCustInfo
	 *            业主信息
	 * @param weChat
	 *            微信
	 */
	public String updateWeChat(List<CsCustInfo> csCustInfo, String weChat, String openId) {
		String custIds = "";
		// List<HashMap<String,Object>> resulList = new
		// ArrayList<HashMap<String,Object>>();
		// HashMap<String,Object> map = new HashMap<>();
		for (CsCustInfo cust : csCustInfo) {
			cust.setWeChat((weChat == null) ? "" : weChat);
			cust.setOpenId((openId == null) ? "" : openId);
			Integer i = baseMapper.updateById(cust);
			System.out.print(i);
			if (!custIds.contains(cust.getCustId())) {
				// map.put("custId", cust.getCustId());
				// map.put("custName", cust.getCustName());
				custIds = custIds + cust.getCustId() + ",";
				// resulList.add(map);
			}
		}
		return custIds;
	}

	/**
	 * 根据业主id和房屋num查询业主信息
	 */
	@Override
	public List<CsCustInfo> selectCustByIdNum(Map<String, Object> map) {
		return baseMapper.selectCustByIdNum(map);
	}

	/**
	 * APP-查询客户信息
	 */
	@Override
	public List<CsCustInfo> getCustInfo(Map<String, Object> map) {
		return baseMapper.getCustInfo(map);
	}

	/**
	 * APP-更新业主标签
	 */
	@Override
	public void updateCustLabel(Map<String, Object> map) {
		baseMapper.updateCustLabel(map);
	}

	/**
	 * 手机APP-更改客户头像
	 */
	@Override
	public void updateHeadPortrait(Map<String, Object> map) {
		baseMapper.updateHeadPortrait(map);
	}

	@Override
	public void initData() {
		baseMapper.syncDeleteTeltempDate();
		baseMapper.syncInsertTeltemp();
		baseMapper.delete(null);
		baseMapper.initData();
		baseMapper.syncUpdateCanNotDelete();
//		baseMapper.delData();
	}
}
