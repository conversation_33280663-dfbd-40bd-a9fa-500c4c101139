package com.tahoecn.customerservice.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.landray.sso.client.EKPSSOContext;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.mapper.CsCustomerExtensionMapper;
import com.tahoecn.customerservice.model.CsCustomerExtension;
import com.tahoecn.customerservice.service.CsCustomerExtensionService;

/**
 * <p>
 * 客服座机表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsCustomerExtensionServiceImpl extends ServiceImpl<CsCustomerExtensionMapper, CsCustomerExtension> implements CsCustomerExtensionService {

    @Autowired
    private CsCustomerExtensionMapper csCustomerExtensionMapper;
    @Override
    public CsCustomerExtension getLogin() {
        String userName = ThreadLocalUtils.getUserName();
        //String userName = "wanghongxin";
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        CsCustomerExtension csCustomerExtension =new CsCustomerExtension();
        csCustomerExtension.setUserName(userName);
        CsCustomerExtension findCsCustomerExtension =csCustomerExtensionMapper.selectOne(csCustomerExtension);
        if(findCsCustomerExtension==null){
            return null;
        }
        return findCsCustomerExtension;
    }
}
