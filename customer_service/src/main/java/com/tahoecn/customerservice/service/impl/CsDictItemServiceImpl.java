package com.tahoecn.customerservice.service.impl;

import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.mapper.CsFormInstMapper;
import com.tahoecn.customerservice.mapper.CsUpgradeCfgMapper;
import com.tahoecn.customerservice.model.CsDictItem;
import com.tahoecn.customerservice.mapper.CsDictItemMapper;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsUpgradeCfgBak;
import com.tahoecn.customerservice.service.CsDictItemService;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;
import java.util.Map.Entry;

@Transactional
@Service
public class CsDictItemServiceImpl extends ServiceImpl<CsDictItemMapper, CsDictItem> implements CsDictItemService {

    @Autowired
    private CsDictItemMapper csDictItemMapper;
    @Autowired
    private CsFormInstMapper csFormInstMapper;
    @Autowired
    private CsUpgradeCfgMapper csUpgradeCfgMapper;

    @Override
    public List<CsDictItem> selectAllCsDictItemByDictCode(String dictCode) {
        if(dictCode.equals("processBZ")){
            List<String> list = new ArrayList<String>();
            list.add("coZX");
            list.add("coTS");
            list.add("coBX");
            return  csDictItemMapper.selectAllCsDictItemByList(list);
        }
        if(dictCode.equals("processKSCL")){
            List<String> list = new ArrayList<String>();
            list.add("coZX");
            list.add("coJYBY");
            return  csDictItemMapper.selectAllCsDictItemByList(list);
        }
        return csDictItemMapper.selectAllCsDictItemByDictCode(dictCode);
    }

    @Override
    public CsDictItem selectCsDictItemByItemCode(String itemCode) {
        return csDictItemMapper.selectCsDictItemByItemCode(itemCode);
    }

    public List<CsDictItem> selectAllCsDictItem(){
        return csDictItemMapper.selectAllCsDictItem();
    }


    /**
     * <AUTHOR>
     * @Description //TODO
     * @Date 16:07 2018/12/20
     * @Param
     * @return
     **/
    @Override
    public ResponseMessage  selectTreeDictItem() {
        //定义dict_id
        //4是一级分类 5是二级分类 6是三级分类 7是四级分类
        int YI = 4;
        int ER = 5;
        int SAN = 6;
        int SI = 7;

        //定义前台数据返回格式
        //一级格式
        List<Map<String,Object>> info = new ArrayList<>();

        //查询一级分类数据
        List<Map<String, Object>> cos = selectOneDictItem(YI, "", "firstSortCode");

        for (Map<String,Object> co : cos) {
            //获取一级分类名称
            String itemCode = getString(co);
            //设置一级分类级别
            Map<String, Object> o = getMap(co);
            o.put("hc", "一级");
            //查询二级分类数据
            List<Map<String, Object>> twoInfos = selectOneDictItem(ER, itemCode, "");
            //定义前台数据返回格式
            //二级格式
            List<Map<String, Object>> erJiInfo = new ArrayList<>();
            for (Map<String, Object> twoInfo : twoInfos) {
                String twoItemCode = getString(twoInfo);
                Map<String, Object> two = getMap(twoInfo);
                //设置二级分类级别
                two.put("hc", "二级");
                //查询三级分类数据if (twoItemValue.equals(""))
                List<Map<String, Object>> treeInfos = selectOneDictItem(SAN, twoItemCode, "");
                //定义前台数据返回格式
                //三级格式
                List<Map<String, Object>> sanJiInfo = new ArrayList<>();
                for (Map<String, Object> treeInfo : treeInfos) {
                    String treeItemCode = getString(treeInfo);
                    //String treeItemCode = treeInfo.get("itemCode").toString();
                    Map<String, Object> tree = getMap(treeInfo);
                    //设置三级分类级别
                    tree.put("hc", "三级");
                    //查询四级分类数据
                    List<Map<String, Object>> fourInfos = selectOneDictItem(SI, treeItemCode, "");
                    if (fourInfos.size() != 0) {
                        //定义前台数据返回格式
                        //四级格式
                        List<Map<String, Object>> siJiInfo = new ArrayList<>();
                        for (Map<String, Object> fourInfo : fourInfos) {
                            Map<String, Object> four = getMap(fourInfo);
                            //设置四级分类级别
                            four.put("hc", "四级");
                            siJiInfo.add(four);
                        }
                        if (siJiInfo != null && siJiInfo.size() > 0) {
                            tree.put("sc", siJiInfo);
                        }
                    }
                    sanJiInfo.add(tree);
                }
                two.put("sc", sanJiInfo);
                erJiInfo.add(two);
            }
            o.put("sc",erJiInfo);
            info.add(o);
        }
        return ResponseMessage.ok(info);
    }


    /**
     * <AUTHOR>
     * @Description //TODO
     * @Date 16:07 2018/12/20
     * @Param
     * @return
     **/
    @Override
    public ResponseMessage selectOneDictItem(String itemCode) {

        //根据字典项编码查询
        Map<String, Object> itemInfo =  csDictItemMapper.selectOneDictItem(itemCode);
        String status = itemInfo.get("status").toString();
        if (itemCode == null){
            return ResponseMessage.error("未查询到相关数据");
        }

        //对当前字典项进行分析
        String dictId = itemInfo.get("dictId").toString();
        if ("4".equals(dictId)){
            Map<String, Object> co = new HashMap<>(16);
            String itemValue = itemInfo.get("itemValue").toString();
            co.put("itemValue",itemValue);
            co.put("status",status);
            co.put("hc","一级");
            return ResponseMessage.ok(co);
        }
        if ("5".equals(dictId)){
            Map<String, Object> co = new HashMap<>(16);
            String itemValue = "";
            itemValue = gettIemInfo(itemInfo, itemValue,5);
            co.put("itemValue",itemValue);
            co.put("status",status);
            co.put("hc","二级");
            return ResponseMessage.ok(co);
        }
        if ("6".equals(dictId)){
            Map<String, Object> co = new HashMap<>(16);
            String itemValue = "";
            //获取二级名称
            itemValue = gettIemInfo(itemInfo,itemValue,5);

            String dictCode = itemInfo.get("dictCode").toString();
            Map<String, Object> mapInfo = getMapInfo(dictCode);

            //获取一级名称
            itemValue = gettIemInfo(mapInfo,itemValue,6);

            co.put("itemValue",itemValue);
            co.put("status",status);
            co.put("hc","三级");
            return ResponseMessage.ok(co);
        }
        if ("7".equals(dictId)){
            Map<String, Object> co = new HashMap<>(16);
            String itemValue = "";
            //获取三级名称
            itemValue = gettIemInfo(itemInfo,itemValue,5);

            //获取三级对象
            String dictCode = itemInfo.get("dictCode").toString();
            Map<String, Object> mapInfo = getMapInfo(dictCode);

            //获取二级名称
            itemValue = gettIemInfo(mapInfo, itemValue,6);

            //获取二级对象
            String dictCodeER = mapInfo.get("dictCode").toString();
            Map<String, Object> mapInfoER = getMapInfo(dictCodeER);
            //获取一级名称
            itemValue = gettIemInfo(mapInfoER, itemValue,6);
            co.put("itemValue",itemValue);
            co.put("status",status);
            co.put("hc","四级");
            return ResponseMessage.ok(co);

        }
        return null;
    }

    @Override
    public ResponseMessage updateDictItemInfo(Map<String, Object> pamam) {

        //获取对应类型
        String type = pamam.get("type").toString();
        //1是编辑
        if ("1".equals(type)){

            //获取字典编码
            String itemCode = pamam.get("itemCode").toString();
            //获取编辑字典名称
            String itemValue = pamam.get("itemValue").toString();
            //获取启用状态
            String status = pamam.get("status").toString();
            //查询之前字典信息
            CsDictItem csDictItem = csDictItemMapper.selectCsDictItemOne(itemCode);
            String dictId = csDictItem.getDictId().toString();

             if ("7".equals(dictId)){
                //修改字典表
                csDictItem.setItemValue(itemValue);
                csDictItem.setStatus(status);
                csDictItem.setLastUpdateDate(new Date());
                csDictItemMapper.updateById(csDictItem);
                //修改报事表
                List<CsFormInst> csFormInsts = csFormInstMapper.selectFormInstByFourthSortCode(itemCode);
                for (CsFormInst csFormInst : csFormInsts){
                    csFormInst.setFourthSortName(itemValue);
                    csFormInstMapper.updateById(csFormInst);
                }
                //修改升级表
                List<CsUpgradeCfgBak> csUpgradeCfgs = csUpgradeCfgMapper.selectUpgradeCfgByFourthSortCode(itemCode);
                for (CsUpgradeCfgBak csUpgradeCfg : csUpgradeCfgs){
                    csUpgradeCfg.setFourthSortName(itemValue);
                    csUpgradeCfgMapper.updateByIdBak(csUpgradeCfg);
                }
                return ResponseMessage.okm("成功");
            }

            if ("6".equals(dictId)){
                //修改三级分类Value
                csDictItem.setItemValue(itemValue);
                csDictItem.setStatus(status);
                csDictItem.setLastUpdateDate(new Date());
                csDictItemMapper.updateById(csDictItem);
                //修改四级分类名称
                List<CsDictItem> csDictItems = csDictItemMapper.selectLikeDictName(csDictItem.getItemCode());
                for (CsDictItem csDictItem1 : csDictItems){
                    csDictItem1.setDictName("四级分类-"+itemValue);
                    csDictItemMapper.updateById(csDictItem1);
                }
                //修改报事表
                List<CsFormInst> csFormInsts = csFormInstMapper.selectFormInstByFourthSortCode(itemCode);
                for (CsFormInst csFormInst : csFormInsts){
                    csFormInst.setThirdSortName(itemValue);
                    csFormInstMapper.updateById(csFormInst);
                }
                //修改升级表
                List<CsUpgradeCfgBak> csUpgradeCfgs = csUpgradeCfgMapper.selectUpgradeCfgByFourthSortCode(itemCode);
                for (CsUpgradeCfgBak csUpgradeCfg : csUpgradeCfgs) {
                    csUpgradeCfg.setThirdSortName(itemValue);
                    csUpgradeCfgMapper.updateByIdBak(csUpgradeCfg);
                }
                return ResponseMessage.okm("成功");
            }
            if ("5".equals(dictId)){
                //修改二级分类名称
                csDictItem.setItemValue(itemValue);
                csDictItem.setStatus(status);
                csDictItem.setLastUpdateDate(new Date());
                csDictItemMapper.updateById(csDictItem);
                //修改三级分类名称
                List<CsDictItem> csDictItems = csDictItemMapper.selectLikeDictName(csDictItem.getItemCode());
                for (CsDictItem csDictItem1 : csDictItems){
                    csDictItem1.setDictName("三级分类-"+itemValue);
                    csDictItemMapper.updateById(csDictItem1);
                }
                //修改报事表
                List<CsFormInst> csFormInsts = csFormInstMapper.selectFormInstByFourthSortCode(itemCode);
                for (CsFormInst csFormInst : csFormInsts){
                    csFormInst.setSecSortName(itemValue);
                    csFormInstMapper.updateById(csFormInst);
                }
                //修改升级表
                List<CsUpgradeCfgBak> csUpgradeCfgs = csUpgradeCfgMapper.selectUpgradeCfgByFourthSortCode(itemCode);
                for (CsUpgradeCfgBak csUpgradeCfg : csUpgradeCfgs) {
                    csUpgradeCfg.setSecSortName(itemValue);
                    csUpgradeCfgMapper.updateByIdBak(csUpgradeCfg);
                }
                return ResponseMessage.okm("成功");
            }
            if ("4".equals(dictId)){
                //修改一级分类Value
                csDictItem.setItemValue(itemValue);
                csDictItem.setStatus(status);
                csDictItem.setLastUpdateDate(new Date());
                csDictItemMapper.updateById(csDictItem);
                //修改二级分类名称
                List<CsDictItem> csDictItems = csDictItemMapper.selectLikeDictName(csDictItem.getItemCode());
                for (CsDictItem csDictItem1 : csDictItems){
                    csDictItem1.setDictName("二级分类-"+itemValue);
                    csDictItemMapper.updateById(csDictItem1);
                }
                //修改报事表
                List<CsFormInst> csFormInsts = csFormInstMapper.selectFormInstByFourthSortCode(itemCode);
                for (CsFormInst csFormInst : csFormInsts){
                    csFormInst.setFirstSortName(itemValue);
                    csFormInstMapper.updateById(csFormInst);
                }
                //修改升级表
                List<CsUpgradeCfgBak> csUpgradeCfgs = csUpgradeCfgMapper.selectUpgradeCfgByFourthSortCode(itemCode);
                for (CsUpgradeCfgBak csUpgradeCfg : csUpgradeCfgs) {
                    csUpgradeCfg.setFirstSortName(itemValue);
                    csUpgradeCfgMapper.updateByIdBak(csUpgradeCfg);
                }
                return ResponseMessage.okm("成功");
            }
        }

        //2是添加
        if ("2".equals(type)){

            //获取字典编码
            String itemCode = pamam.get("itemCode").toString();
            //获取编辑字典名称
            String itemValue = pamam.get("itemValue").toString();
            //查询之前字典信息
            CsDictItem csDictItem = csDictItemMapper.selectCsDictItemOne(itemCode);
            String dictId = csDictItem.getDictId().toString();
            //添加一级分类下的子节点
            CsDictItem dictItem = new CsDictItem();
            if ("4".equals(dictId)){
                dictItem.setDictId((long) 5);
                dictItem.setDictName("二级分类-"+csDictItem.getItemValue());
            }
            if ("5".equals(dictId)){
                dictItem.setDictId((long) 6);
                dictItem.setDictName("三级分类-"+csDictItem.getItemValue());
            }
            if ("6".equals(dictId)){
                dictItem.setDictId((long) 7);
                dictItem.setDictName("四级分类-"+csDictItem.getItemValue());
            }
            dictItem.setDictCode(itemCode);
            dictItem.setItemCode(itemCode+"-"+codeGeneration(itemCode));
            dictItem.setItemValue(itemValue);
            dictItem.setDisplayOrder((long) getDisplayOrder(itemCode));

            String aStatic = (String) pamam.get("status");
            if (aStatic != null && !aStatic.equals("")){
                dictItem.setStatus(aStatic);
            }else {
                dictItem.setStatus("1");
            }
            dictItem.setCreationDate(new Date());
            dictItem.setLastUpdateDate(new Date());
            csDictItemMapper.insert(dictItem);

            //添加升级信息
            //添加二级升级信息
            if ("4".equals(dictId)){
                //创建新的升级信息
                CsUpgradeCfgBak csUpgradeCfg = new CsUpgradeCfgBak();
                csUpgradeCfg.setFirstSortCode(csDictItem.getItemCode());
                csUpgradeCfg.setFirstSortName(csDictItem.getItemValue());
                //查询二级报事信息
                List<CsDictItem> csDictItemList = csDictItemMapper.selectLikeDictName(csDictItem.getItemCode());
                int size = csDictItemList.size()-1;
                Map<String,Object> panam = new HashMap<>();
                panam.put("itemCode",csDictItem.getItemCode());
                List<CsDictItem> csDictItem4 = csDictItemMapper.selectErDictItem(panam);
                CsDictItem csDictItem1 = csDictItem4.get(size);
                csUpgradeCfg.setSecSortCode(csDictItem1.getItemCode());
                csUpgradeCfg.setSecSortName(csDictItem1.getItemValue());
                csUpgradeCfgMapper.insertByBak(csUpgradeCfg);
            }
            //添加三级升级
            if ("5".equals(dictId)){
                CsUpgradeCfgBak csUpgradeCfg = new CsUpgradeCfgBak();
                //设置二级分类
                csUpgradeCfg.setSecSortCode(csDictItem.getItemCode());
                csUpgradeCfg.setSecSortName(csDictItem.getItemValue());
                //设置三级分类
                //查询二级报事信息
                List<CsDictItem> csDictItemList = csDictItemMapper.selectLikeDictName(csDictItem.getItemCode());
                int size = csDictItemList.size()-1;
                Map<String,Object> panam = new HashMap<>();
                panam.put("itemCode",csDictItem.getItemCode());
                List<CsDictItem> csDictItem4 = csDictItemMapper.selectErDictItem(panam);
                CsDictItem csDictItem1 = csDictItem4.get(size);
                csUpgradeCfg.setThirdSortCode(csDictItem1.getItemCode());
                csUpgradeCfg.setThirdSortName(csDictItem1.getItemValue());
                //查询一级分类
                CsDictItem cd = csDictItemMapper.selectShangJiByDictCode(csDictItem.getDictCode());
                //设置一级分类
                csUpgradeCfg.setFirstSortCode(cd.getItemCode());
                csUpgradeCfg.setFirstSortName(cd.getItemValue());
                csUpgradeCfgMapper.insertByBak(csUpgradeCfg);
            }

            if ("6".equals(dictId)){
                CsUpgradeCfgBak csUpgradeCfg = new CsUpgradeCfgBak();
                //设置三级分类
                csUpgradeCfg.setThirdSortCode(csDictItem.getItemCode());
                csUpgradeCfg.setThirdSortName(csDictItem.getItemValue());
                //设置四级分类
                List<CsDictItem> csDictItemList = csDictItemMapper.selectLikeDictName(csDictItem.getItemCode());
                int size = csDictItemList.size()-1;
                Map<String,Object> panam = new HashMap<>();
                panam.put("itemCode",csDictItem.getItemCode());
                List<CsDictItem> csDictItem4 = csDictItemMapper.selectErDictItem(panam);
                CsDictItem csDictItem1 = csDictItem4.get(size);
                csUpgradeCfg.setFourthSortCode(csDictItem1.getItemCode());
                csUpgradeCfg.setFourthSortName(csDictItem1.getItemValue());
                //设置二级分类
                //查询二级分类
                CsDictItem cd = csDictItemMapper.selectShangJiByDictCode(csDictItem.getDictCode());
                csUpgradeCfg.setSecSortCode(cd.getItemCode());
                csUpgradeCfg.setSecSortName(cd.getItemValue());
                //查询一级分类
                CsDictItem ut = csDictItemMapper.selectShangJiByDictCode(cd.getDictCode());
                csUpgradeCfg.setFirstSortCode(ut.getItemCode());
                csUpgradeCfg.setFirstSortName(ut.getItemValue());
                csUpgradeCfgMapper.insertByBak(csUpgradeCfg);
            }
            return ResponseMessage.okm("成功");
        }
        return ResponseMessage.okm("失败");
    }

    public int getDisplayOrder(String itemCode){
        List<Map<String,Object>> codeInfo = csDictItemMapper.selectCodeGeneration(itemCode);
        if (codeInfo.size()>0){
            int size = codeInfo.size();
            return size;
        }
        if (codeInfo.size()==0){
            int k = csDictItemMapper.selectCounth(itemCode);
            return k;
        }
        return Integer.parseInt(null);
    }

    public String codeGeneration(String itemCode){
        List<Map<String,Object>> codeInfo = csDictItemMapper.selectCodeGeneration(itemCode);
        if (codeInfo.size()>0){
            int size = codeInfo.size();
//            String code = codeInfo.get(size-1).get("itemCode").toString().substring(codeInfo.get(size-1).get("itemCode").toString().length() - 3, codeInfo.get(size-1).get("itemCode").toString().length());
//            int m = Integer.parseInt(code);//把001转换为1
//            m = m+1;
            DecimalFormat mFormat = new DecimalFormat("000");
            String format = mFormat.format(size+1);
            return format;
        }
        if (codeInfo.size()==0){
            return "001";
        }
        return null;
    }

    public String gettIemInfo(Map info,String itemValuea, int i){

        if (i == 5){
            //获取上级字典编码
            String dictCode = info.get("dictCode").toString();
            Map<String, Object> ERitemInfo = getMapInfo(dictCode);
            //获取上级字典名称
            itemValuea = ERitemInfo.get("itemValue").toString();
            //当前字典名称
            itemValuea += "-"+info.get("itemValue").toString();
            return itemValuea;
        }
        if (i == 6){
            //获取上级字典编码
            String dictCode = info.get("dictCode").toString();
            Map<String, Object> ERitemInfo = getMapInfo(dictCode);
            //获取上级字典名称
            itemValuea = ERitemInfo.get("itemValue").toString() +"-"+itemValuea;
            return itemValuea;
        }
        return null;
    }

    public Map<String, Object> getMapInfo(String dictCode){
        Map<String, Object> ERitemInfo =  csDictItemMapper.selectOneDictItem(dictCode);
        return ERitemInfo;
    }
    /***
     *查询数字字典
     */
    public List<Map<String, Object>> selectOneDictItem(int dictId , String dictCode, String itemCode){
        Map<String, Object> panam = new HashMap<>(16);
        panam.put("dictId",dictId);
        panam.put("dictCode",dictCode);
        panam.put("itemCode",itemCode);
        List<Map<String, Object>> listDictItem = csDictItemMapper.selectListDictItem(panam);
        return listDictItem;
    }

    public Map<String,Object> getMap(Map info){
        String itemValue = info.get("itemValue").toString();
        String ItemCode = info.get("itemCode").toString();
        String status = info.get("status").toString();
        Map<String,Object> newInfo = new HashMap<>();
        //设置一级分类名称
        newInfo.put("dictName",itemValue);
        newInfo.put("itemCode",ItemCode);
        newInfo.put("status",status);
        return newInfo;
    }

    public String getString(Map info){
        return info.get("itemCode").toString();
    }

	@Override
	public List<Map<String, Object>> selectItTreeDictItem() {
		//查询一级分类
		Wrapper<CsDictItem> firstWrapper = new EntityWrapper<>();
		firstWrapper.eq("dict_name", "IT项目");
		List<CsDictItem> firstList = baseMapper.selectList(firstWrapper);
		if(null != firstList && firstList.size()>0) {
			//查询二级分类
			Wrapper<CsDictItem> secWrapper = new EntityWrapper<>();
			secWrapper.where("dict_name like {0}", "IT一级%");
			List<CsDictItem> secList = baseMapper.selectList(secWrapper);
			
			List<Map<String,Object>> list = new ArrayList<>();
			for(CsDictItem firstDict : firstList) {
				Map<String, Object> firstDictMap = new HashMap<>();
				firstDictMap = spellDict(firstDict, "一级", "", "");
				String firstItemValue = firstDict.getItemValue();
				//查询一级分类下的二级分类
				List<Object> secMapList = new ArrayList<>();
				if(firstDict.getItemValue().equals("流程")) {
					System.out.println("liucheng");
				}
				if(StringUtils.isNotBlank(firstItemValue)) {
					Map<String, Object> secMap = new HashMap<>();
					for(CsDictItem secDict : secList) {
						String type = "标准";//二级分类默认类别
						String typeCode = "itFscBz";
						String secItemValue = secDict.getItemValue();
						if(StringUtils.isNotBlank(secItemValue)) {
							Map<String, Object> secDictMap = new HashMap<>();
							if(secItemValue.startsWith(firstItemValue)) {
								if(null != secMap.get(secDict.getItemCode() + secDict.getStatus())) {
									secMap.remove(secDict.getItemCode() + secDict.getStatus());
									type = "快速、标准";
									typeCode = "itFscBz,itFscKszx";
								}else if("IT一级快处分类".equals(secDict.getDictName())) {
									type = "快速";
									typeCode = "itFscKszx";
								}
								secDictMap = spellDict(secDict, "二级", type, typeCode);
								secMap.put(secDict.getItemCode() + secDict.getStatus(), secDictMap);
							}
						}
					}
					
					for(Map.Entry<String, Object> entry : secMap.entrySet()) {
						secMapList.add(entry.getValue());
					}
					firstDictMap.put("secDictItem", secMapList);
				}
				
				
				list.add(firstDictMap);
			}
			return list;
		}
		return null;
	}
	
	private Map<String,Object> spellDict(CsDictItem dictItem, String cell, String type, String typeCode) {
		if(null == dictItem) {
			return null;
		}
		Map<String,Object> map = new HashMap<>();
		map.put("id", dictItem.getId());
		map.put("dictId", dictItem.getDictId());
		map.put("dictCode", dictItem.getDictCode());
		map.put("dictName", dictItem.getDictName());
		map.put("itemCode", dictItem.getItemCode());
		map.put("itemValue", dictItem.getItemValue());
		map.put("displayOrder", dictItem.getDisplayOrder());
		map.put("status", dictItem.getStatus());
		map.put("remarks", dictItem.getRemarks());
		map.put("cell", cell);
		map.put("type", type);
		map.put("typeCode", typeCode);
//		map.put("", dictItem.getCreationDate());
//		map.put("", dictItem.getLastUpdateDate());
		return map;
	}

	@Override
	public ResponseMessage updateItDictItem(Map<String, Object> pamam) {
		String dictCodeStr = pamam.get("dictCode").toString();
		if (StringUtils.isEmpty(dictCodeStr)){
			return ResponseMessage.error("请填写参数 : 流程类别");
		}
		String itemValue = pamam.get("itemValue").toString();
		if (StringUtils.isEmpty(itemValue)){
			return ResponseMessage.error("请填写参数 : 流程名称");
		}
		
		String status = pamam.get("status").toString();
		if (StringUtils.isEmpty(status)){
			return ResponseMessage.error("请填写参数 : 是否启用");
		}
		String itemCode = pamam.get("itemCode").toString();
		CsDictItem dictItem = new CsDictItem();
		dictItem.setItemValue(itemValue);
		dictItem.setItemCode(itemCode);
		
		//判断名称是否进行了更改及是否有重复
		String oldItemValue = baseMapper.selectOldItItemValue(dictItem);
		if(!itemValue.equals(oldItemValue)) {//名称有修改
			Wrapper<CsDictItem> countWrapper = new EntityWrapper<>();
			countWrapper.eq("item_value", itemValue).and().eq("remarks", "IT");
			if(baseMapper.selectCount(countWrapper) > 0) {
				return ResponseMessage.error("分类名称已存在！");
			}
		}
		
		if("deptIT".equals(dictCodeStr)) {//一级分类
			Map<String,Object> updateMap = new HashMap<>();
			updateMap.put("oldItemValue", oldItemValue);
			updateMap.put("itemValue", itemValue);
			updateMap.put("status", status);
			baseMapper.updateItDictItemValue(updateMap);
		}else {//二级分类
			Wrapper<CsDictItem> wrapper = new EntityWrapper<>();
			wrapper.eq("item_code", itemCode).and().eq("remarks", "IT");
			baseMapper.delete(wrapper);
			
			dictItem.setDictId(Long.valueOf("644"));
			dictItem.setStatus(pamam.get("status").toString());
			dictItem.setRemarks("IT");
			dictItem.setDisplayOrder(Long.valueOf(1));
			
			String[] dictCodeArr = dictCodeStr.split(",");
			for(String dictCode : dictCodeArr) {
				dictItem.setDictCode(dictCode);
				if("itFscBz".equals(dictCode)) {
					dictItem.setDictName("IT一级标准分类");
				}else {
					dictItem.setDictName("IT一级快处分类");
				}
				baseMapper.insertItDict(dictItem);
			}
		}
		
		return ResponseMessage.okm("success");
	}

	@Override
	public ResponseMessage insertItDictItem(Map<String, Object> pamam) {
		String dictCodeStr = pamam.get("dictCode").toString();
		if (StringUtils.isEmpty(dictCodeStr)){
			return ResponseMessage.error("请填写参数 : 流程类别");
		}
		String itemValue = pamam.get("itemValue").toString();
		if (StringUtils.isEmpty(itemValue)){
			return ResponseMessage.error("请填写参数 : 流程名称");
		}
		String status = pamam.get("status").toString();
		if (StringUtils.isEmpty(status)){
			return ResponseMessage.error("请填写参数 : 是否启用");
		}
		CsDictItem dictItem = new CsDictItem();
		dictItem.setItemValue(itemValue);
		dictItem.setDictId(Long.valueOf("644"));
		dictItem.setStatus(status);
		dictItem.setRemarks("IT");
		dictItem.setDisplayOrder(Long.valueOf(1));
		
		//先删除后添加，防止重复数据
		Wrapper<CsDictItem> wrapper = new EntityWrapper<>();
		wrapper.eq("dict_id", "644").and().eq("item_value", itemValue).and().eq("remarks", "IT");
		baseMapper.delete(wrapper);
		
		String maxCode = baseMapper.selectMaxCode();
		String[] dictCodeArr = dictCodeStr.split(",");
		for(String dictCode : dictCodeArr) {
			dictItem.setDictCode(dictCode);
			if("itFscBz".equals(dictCode)) {
				dictItem.setDictName("IT一级标准分类");
			}else {
				dictItem.setDictName("IT一级快处分类");
			}
			dictItem.setItemCode("itFscBz_" + (Integer.valueOf(maxCode) + 1));
			
			baseMapper.insertItDict(dictItem);
		}
		return ResponseMessage.okm("success");
	}

	@Override
	public ResponseMessage deleteItDictItem(String itemCode) {
		Wrapper<CsDictItem> wrapper = new EntityWrapper<>();
		wrapper.eq("dict_id", "644").and().eq("item_code", itemCode).and().eq("remarks", "IT");
		baseMapper.delete(wrapper);
		return ResponseMessage.okm("success");
	}
}
