package com.tahoecn.customerservice.service.impl;

import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.model.CsFile;
import com.tahoecn.customerservice.mapper.CsFileMapper;
import com.tahoecn.customerservice.model.vo.FormAndFileVo;
import com.tahoecn.customerservice.service.CsFileService;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigInteger;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsFileServiceImpl extends ServiceImpl<CsFileMapper, CsFile> implements CsFileService {

    @Value("${root_path}")
    private String rootPath;

    @Autowired
    private CsFileMapper csFileMapper;

    public void deleteCsFile(String formNo,Long id){
        if(StringUtils.isNotBlank(formNo)){
            csFileMapper.deleteCsFile(formNo);
        }
        if(id!=null){
            csFileMapper.deleteCsFileById(id);
        }
    }

    public CsFile selectFileById(Long id){
        return csFileMapper.selectById(id);
    }

    public List<CsFile> selectFileByFormNo(String formGenerateId){
        return csFileMapper.selectByFormNo(formGenerateId);
    }

    public CsFile selectCsFileById(Long id){
        return csFileMapper.selectCsFileById(id);
    }

    public List<CsFile> selectCsFileByIdList(List<Long> list){
        return csFileMapper.selectCsFileByIdList(list);
    }

    public Long relationFormAndFiles(Long id,List<Long> ids){
    	if(ids == null || ids.size() == 0) {
    		return 0L;
    	}
        FormAndFileVo vo = new FormAndFileVo();
        vo.setId(id);
        vo.setIds(ids);
        return csFileMapper.relationFormAndFiles(vo);
    }

    public Long saveUploadFile(MultipartFile file,String clientPath){
        try {
            File dir;
            String extensionName = "";
            String originalFilename = file.getOriginalFilename();
            if (originalFilename != null && !"".equals(originalFilename)) {
                int index = originalFilename.lastIndexOf(".");
                if (index > 0) {
                    extensionName = originalFilename.substring(index, originalFilename.length());
                }
            }
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            String ypath = rootPath + "/" + year;
            String mpath = rootPath + "/" + year + "/" + month;
            //检查目录是否存在，存在就直接使用，不存在就创建目录
            dir = new File(ypath);
            if (!dir.exists()) {
                dir = new File(mpath);
                dir.mkdirs();
            } else {
                dir = new File(mpath);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
            }
            //获取一个UUID来作为存入服务器中的文件的名字
            String filename = SecureUtil.simpleUUID();
            filename = filename + extensionName;
            String filePath = dir.getPath() + "/" + filename;
            //将文件转存到指定位置
            FileOutputStream out = new FileOutputStream(filePath);
            out.write(file.getBytes());
            out.flush();
            out.close();
            //将文件的服务器地址存到数据库
            String path = "/" + year + "/" + month + "/" + filename;
            CsFile csFile = new CsFile();
            csFile.setFileName(originalFilename);
            if (extensionName != null) {
                csFile.setFileType(extensionName.substring(1));
            }
            csFile.setStatus("-1");
            csFile.setServerPath(path);
            csFile.setClientPath(clientPath);
            csFileMapper.insertFile(csFile);
            Long id = csFileMapper.selectbyUrl(csFile.getServerPath());
            return id;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 微信公众号保存关联图片信息
     */
	@Override
	public void saveFile(Long id, List<String> pictureUrl) {
		for(String url : pictureUrl){
			CsFile file = new CsFile();
			file.setFormGenerateId(id.toString());//报事id
			file.setServerPath(url);//路径
			file.setStatus("-1");//状态，
			String fName = url.trim();  
	        String fileName = fName.substring(fName.lastIndexOf("/")+1);  
			file.setClientPath("content");
			file.setFileName(fileName);
//			String fileType = fName.substring(fName.lastIndexOf(".")+1);  
			file.setFileType("jpg");
			this.insert(file);
		}
	}
	/**
	 * 保存关联图片信息
	 */
	@Override
	public void saveCommFile(Long id, List<String> pictureUrl) {
		for(String url : pictureUrl){
			CsFile file = new CsFile();
			file.setFormGenerateId(id.toString());//报事id
			file.setServerPath(url);//路径
			file.setStatus("-1");//状态，
			String fName = url.trim();  
			String fileName = fName.substring(fName.lastIndexOf("/")+1);  
			file.setClientPath("handle");
			file.setFileName(fileName);
			String fileType = fName.substring(fName.lastIndexOf(".")+1);  
			file.setFileType(fileType);
			this.insert(file);
		}
	}
}
