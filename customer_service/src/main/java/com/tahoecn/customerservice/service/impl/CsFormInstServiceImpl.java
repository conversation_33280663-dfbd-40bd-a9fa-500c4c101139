package com.tahoecn.customerservice.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.tahoecn.core.date.DateUtil;
import com.tahoecn.customerservice.common.utils.MD5Util;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.mapper.*;
import com.tahoecn.customerservice.model.*;
import com.tahoecn.customerservice.model.dto.AppFormListDto;
import com.tahoecn.customerservice.model.dto.ComplaintPageDto;
import com.tahoecn.customerservice.model.dto.CsFormInstDto;
import com.tahoecn.customerservice.model.dto.FamilyListDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.dto.OwnerFormDto;
import com.tahoecn.customerservice.model.excelDTO.ExpForm;
import com.tahoecn.customerservice.model.excelDTO.ItExpForm;
import com.tahoecn.customerservice.model.vo.*;
import com.tahoecn.customerservice.service.CsCustInfoService;
import com.tahoecn.customerservice.service.CsFileService;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsHouseInfoService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsProjectInfoService;
import com.tahoecn.customerservice.service.CsSyncWyService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.ProcessService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.transaction.Transaction;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsFormInstServiceImpl extends ServiceImpl<CsFormInstMapper, CsFormInst> implements CsFormInstService {

    private static final Log log = LogFactory.get();
    @Value("${cfiwy_uri}")
    private String uri;
    @Value("${cfiwy_class}")
    private String Class;
    @Value("${cfiwy_token}")
    private String token;
    @Value("${cfiwy_command1}")
    private String command1;
    @Value("${cfiwy_command2}")
    private String command2;
    @Value("${cfiwy_command3}")
    private String command3;
    @Value("${cfiqd_uri}")
    private String qduri;
    @Value("${qd_organ_id}")
    private String qdOrganId;
    @Value("${qd_key}")
    private String qdKey;
    @Value("${qd_back_key}")
    private String qdBackKey;
    
    @Autowired
    private CsFormInstMapper csFormInstMapper;
    @Autowired
    private CsCustFamilyMapper csCustFamilyMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    CsProcessWorkitemService csProcessWorkitemService;
    @Autowired
    private CsThirdScoreMapper csThirdScoreMapper;
    @Autowired
	private CsProjectInfoService csProjectInfoService;
    @Autowired
	private CsHouseInfoService csHouseInfoService;
    @Autowired
    private CsCustInfoService csCustInfoService;
    @Autowired
	private CsFileService csFileService;
    // ------------报事提交--物业需要------------
    @Autowired
    private CsCustInfoMapper csCustInfoMapper;
    @Autowired
    private CsHouseInfoMapper csHouseInfoMapper;
    @Autowired
    private CsProjectInfoMapper csProjectInfoMapper;
    @Autowired
    private CsDictItemMapper csDictItemMapper;

    @Resource(name = "sComplaintService")
    ProcessService sComplaintService;

    @Resource(name = "sRepairService")
    ProcessService sRepairService;
    @Autowired
    CsUcUserService csUcUserService;
    @Autowired
    CsSyncWyService csSyncWyService;

    @Override
    public List<CsFormInst> findTemporaryList(Map<String, Object> map, Page<CsFormInst> page) {
        return csFormInstMapper.findTemporaryList(map, page);
    }

    @Override
    public List<CsFormInstDto> findReturnVisitList(Map<String, Object> map, Page<CsFormInstDto> page) {
        return csFormInstMapper.findReturnVisitList(map, page);
    }

    @Transactional(readOnly = false)
    @Override
    public Long saveOrUpate(FamilyListDto dto) {
        CsFormInst csFormInst = dto.getCsFormInst();
        csFormInst.setLastUpdateDate(new Date());
        csFormInst.setCurAssigneeId(ThreadLocalUtils.getUserName());
        csFormInst.setCurAssigneeName(ThreadLocalUtils.getRealName());
        // csFormInst.setAssignDate(new Date());
        if (csFormInst.getId() == null) {
            if (StringUtils.isBlank(csFormInst.getFormNo())) {
                csFormInst.setFormNo(getNo(csFormInst.getFirstSortCode()));
            }

            csFormInst.setProcessStateCode("draft");
            csFormInst.setProcessStateName("暂存");
            csFormInst.setCreationDate(new Date());
            csFormInst.setCreateUserId(ThreadLocalUtils.getUserName());
            csFormInst.setCreateUserName(ThreadLocalUtils.getRealName());
            baseMapper.insert(csFormInst);
        } else {
            baseMapper.updateById(csFormInst);
            // 先删除再新增
            Map<String, Object> maps = Maps.newHashMap();
            maps.put("form_inst_id", csFormInst.getId());
            csCustFamilyMapper.deleteByMap(maps);
        }
        List<CsCustFamily> custFamilies = dto.getFormCustFamilies();
        if (custFamilies != null && custFamilies.size() > 0) {
            for (CsCustFamily custFamily : custFamilies) {
                custFamily.setFormInstId(csFormInst.getId());
                custFamily.setCustId(StringUtils.isNotBlank(csFormInst.getOwnerId()) ? csFormInst.getOwnerId() : "-1");
                custFamily.setMemberId(
                        StringUtils.isNotBlank(custFamily.getMemberId()) ? custFamily.getMemberId() : "-1");
                csCustFamilyMapper.insert(custFamily);
            }
        }
        return csFormInst.getId();
    }

    @Override
    public ResponseMessage selectFormAssignPageList(Map<String, Object> csFormInstWrapper, Integer pageNum,
                                                    Integer pageSize) {
        Page<CsFormInst> page = new Page<>(pageNum, pageSize);
        List<CsFormInst> csFormInsts = csFormInstMapper.selectFormAssignList(page, csFormInstWrapper);
        page.setRecords(csFormInsts);
        return ResponseMessage.ok(page);
    }

    @Override
    public ResponseMessage selectFormHandlingPageList(Map<String, Object> csFormInstWrapper, Integer pageNum,
                                                      Integer pageSize) {
        Page<CsFormInst> page = new Page<>(pageNum, pageSize);
        List<CsFormInst> csFormInsts = csFormInstMapper.selectFormHandlingList(page, csFormInstWrapper);
        page.setRecords(csFormInsts);
        return ResponseMessage.ok(page);
    }

    @Override
    public Page<CsFormInst> getByHcfInfoDto(HcfInfoDto dto) {
        Page<CsFormInst> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page.setRecords(baseMapper.selectHcf(page, dto));
        return page;
    }

    @Override
    public CompareVo getCompareField(Long formInstId) {
        return csFormInstMapper.getCompareField(formInstId);
    }

    @Override
    public List<CsFormInst> selectUpgradeList() {
        return csFormInstMapper.selectUpgradeList();
    }

    @SuppressWarnings("unchecked")
	@Override
    public String getNo(String firstType) {
        if (StringUtils.isBlank(firstType)) {
            return "";
        }
        // 获取工单号
        SimpleDateFormat bf = new SimpleDateFormat("yyyyMMdd");
        String code = firstType.substring(firstType.length() - 2) + bf.format(new Date());
        Integer num = (Integer) redisTemplate.opsForValue().get(code);
        if (num == null) {
            num = 1;
            redisTemplate.opsForValue().set(code, num, 1, TimeUnit.DAYS);
        } else {
            num += 1;
            redisTemplate.opsForValue().set(code, num);
        }
        return code + String.format("%04d", num);
    }

    @Override
    public Page<McomplaintVo> findComplaintPageList(Integer pageNum, Integer pageSize,
                                                    ComplaintPageDto complaintPageDto) {
        Page page = new Page(pageNum, pageSize);
        List<CsFormInst> cfiList = csFormInstMapper.findComplaintByCondition(page, complaintPageDto);
        if (cfiList == null) {
            return null;
        }
        List<McomplaintVo> list = new ArrayList<>();

        for (CsFormInst cfi : cfiList) {
            McomplaintVo mcomplaintVo = getMcomplaintVo(cfi);
            list.add(mcomplaintVo);
        }
        page.setRecords(list);
        return page;
    }

    private McomplaintVo getMcomplaintVo(CsFormInst cfi) {
        McomplaintVo mcomplaintVo = new McomplaintVo();
        // 这里取巧使用ownerType存数据
        mcomplaintVo.setDoTime(String.valueOf(cfi.getOwnerType()));
        BeanUtils.copyProperties(cfi, mcomplaintVo);
        return mcomplaintVo;
    }

    @Override
    public McomplaintDetailVo findComplaintDetail(Long id) {
        CsFormInst cfi = selectById(id);
        if (cfi != null) {
            McomplaintDetailVo mcomplaintDetailVo = new McomplaintDetailVo();
            McomplaintVo mcomplaintVo = getMcomplaintVo(cfi);
            List<CsProcessWorkitem> list = csProcessWorkitemService.getWorkItemList(String.valueOf(id));
            mcomplaintDetailVo.setMcomplaintVo(mcomplaintVo);

            if (list != null && list.size() > 0) {
                List<CsProcessWorkitemDateFormatVo> pdfvList = new ArrayList<>();
                for (CsProcessWorkitem cpw : list) {
                    CsProcessWorkitemDateFormatVo csProcessWorkitemDateFormatVo = new CsProcessWorkitemDateFormatVo();
                    BeanUtils.copyProperties(cpw, csProcessWorkitemDateFormatVo);
                    pdfvList.add(csProcessWorkitemDateFormatVo);
                }
                mcomplaintDetailVo.setProcessList(pdfvList);
                CsProcessWorkitem csProcessWorkitem = csProcessWorkitemService.getLastWorkitemById(id);
                mcomplaintVo.setComment(csProcessWorkitem.getComment());
                mcomplaintVo.setLastUpdateDate(csProcessWorkitem.getLastUpdateDate());
            }
            return mcomplaintDetailVo;
        }
        return null;
    }

    @Override
    public List<ExpForm> expForm(Page<ExpForm> page, Map<String, Object> map) {
        if (page == null) {
            return baseMapper.expForm(map);
        }
        return baseMapper.expForm(page, map);
    }

    @Override
    public CustomerStatisticsReportVo customerStatisticsReportCount(int range) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        int allCount = csFormInstMapper.customerStatisticsReportCount2(params);
        Map<String, Object> params2 = new HashMap<>();
        // 不会使用这个值 具体看sql
        params2.put("processStateCode", "nomalEnd");
        params2.putAll(params);
        int finishCount = csFormInstMapper.customerStatisticsReportCount2(params2);
        CustomerStatisticsReportVo customerStatisticsReportVo = new CustomerStatisticsReportVo();
        customerStatisticsReportVo.setAllCount(allCount);
        customerStatisticsReportVo.setFinishCount(finishCount);
        List<CustomerStatisticsReportGroupVo> statisticsGroupList = csFormInstMapper
                .customerStatisticsReportGroup2(params);
        if (statisticsGroupList != null && statisticsGroupList.size() > 0) {
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(0);
            for (CustomerStatisticsReportGroupVo cv : statisticsGroupList) {
                if (cv.getFinishCount() != null && cv.getFinishCount() != 0 && cv.getGroupCount() != null
                        && cv.getGroupCount() != 0) {
                    String result = numberFormat.format((float) cv.getFinishCount() / (float) cv.getGroupCount() * 100);
                    cv.setProportion(result);
                } else {
                    cv.setFinishCount(0);
                    cv.setProportion("0");
                }
            }
            Collections.sort(statisticsGroupList, new Comparator<CustomerStatisticsReportGroupVo>() {
                @Override
                public int compare(CustomerStatisticsReportGroupVo o1, CustomerStatisticsReportGroupVo o2) {
                    if (o1.getGroupCount() > o2.getGroupCount()) {
                        return -1;
                    } else if (o1.getGroupCount() < o2.getGroupCount()) {
                        return 1;
                    }
                    return 0;
                }
            });
        }
        customerStatisticsReportVo.setStatisticsGroupList(statisticsGroupList);
        return customerStatisticsReportVo;
    }

    @Override
    public List<CustomerStatisticsReportGroupVo> customerStatisticsReportCountNext(int range, String firstSortCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        params.put("firstSortCode", firstSortCode);
        List<CustomerStatisticsReportGroupVo> statisticsGroupList = csFormInstMapper
                .customerStatisticsReportGroupNext(params);
        if (statisticsGroupList != null && statisticsGroupList.size() > 0) {
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(0);
            for (CustomerStatisticsReportGroupVo cv : statisticsGroupList) {
                if (cv.getFinishCount() != null && cv.getFinishCount() != 0 && cv.getGroupCount() != null
                        && cv.getGroupCount() != 0) {
                    String result = numberFormat.format((float) cv.getFinishCount() / (float) cv.getGroupCount() * 100);
                    cv.setProportion(result);
                } else {
                    cv.setFinishCount(0);
                    cv.setProportion("0");
                }
            }
            Collections.sort(statisticsGroupList, new Comparator<CustomerStatisticsReportGroupVo>() {
                @Override
                public int compare(CustomerStatisticsReportGroupVo o1, CustomerStatisticsReportGroupVo o2) {
                    if (o1.getGroupCount() > o2.getGroupCount()) {
                        return -1;
                    } else if (o1.getGroupCount() < o2.getGroupCount()) {
                        return 1;
                    }
                    return 0;
                }
            });
        }
        return statisticsGroupList;
    }

    @Override
    public List<DayOfMonthReportVo> dayOfMonthReportCount() {
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(1);
        List<DayOfMonthReportVo> dayOfMonthReportVoList = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            DayOfMonthReportVo dayOfMonthReportVo = new DayOfMonthReportVo();
            if (i == 0) {
                dayOfMonthReportVo = csFormInstMapper.dayOfMonthReportCountCur();
            } else {
                dayOfMonthReportVo = csFormInstMapper.dayOfMonthReportCount(i);
            }
            if (dayOfMonthReportVo != null) {
                if (dayOfMonthReportVo.getMcount() == 0) {
                    dayOfMonthReportVo.setAvgOfMonth("0");
                } else {
                    double rate = (float) dayOfMonthReportVo.getMcount() / (float) dayOfMonthReportVo.getMday();
                    String result = numberFormat.format(rate);
                    if (result == "0") {
                        // 如果保留两位四舍五入还为0,则设置为0.1
                        result = "0.1";
                    }
                    dayOfMonthReportVo.setAvgOfMonth(result);
                }
                dayOfMonthReportVoList.add(dayOfMonthReportVo);
            }
        }
        return dayOfMonthReportVoList;
    }

    @Override
    public CustomerStatisticsReportVo itStatisticsReportCount(Integer range, Integer type) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        if (type != null && 2 == type) {
            params.put("secSortName", "VIP");
        }
        int allCount = csFormInstMapper.itStatisticsReportCount(params);
        Map<String, Object> params2 = new HashMap<>();
        // 不会使用这个值 具体看sql
        params2.put("processStateCode", "nomalEnd");
        params2.putAll(params);
        int finishCount = csFormInstMapper.itStatisticsReportCount(params2);
        CustomerStatisticsReportVo customerStatisticsReportVo = new CustomerStatisticsReportVo();
        customerStatisticsReportVo.setAllCount(allCount);
        customerStatisticsReportVo.setFinishCount(finishCount);
        List<CustomerStatisticsReportGroupVo> statisticsGroupList = csFormInstMapper
                .itStatisticsReportProjectGroup(params);
        if (statisticsGroupList != null && statisticsGroupList.size() > 0) {
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(0);
            for (CustomerStatisticsReportGroupVo cv : statisticsGroupList) {
                if (cv.getFinishCount() != null && cv.getFinishCount() != 0 && cv.getGroupCount() != null
                        && cv.getGroupCount() != 0) {
                    String result = numberFormat.format((float) cv.getFinishCount() / (float) cv.getGroupCount() * 100);
                    cv.setProportion(result);
                } else {
                    cv.setFinishCount(0);
                    cv.setProportion("0");
                }
            }

        }
        customerStatisticsReportVo.setStatisticsGroupList(statisticsGroupList);
        return customerStatisticsReportVo;
    }

    @Override
    public CustomerStatisticsReportVo itStatisticsReportGroupNext(Integer range, String projectCode, Integer type) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        params.put("projectCode", projectCode);
        if (type != null && 2 == type) {
            params.put("secSortName", "VIP");
        }
        int allCount = csFormInstMapper.itStatisticsReportCount(params);
        Map<String, Object> params2 = new HashMap<>();
        // 不会使用这个值 具体看sql
        params2.put("processStateCode", "nomalEnd");
        params2.putAll(params);
        int finishCount = csFormInstMapper.itStatisticsReportCount(params2);
        CustomerStatisticsReportVo customerStatisticsReportVo = new CustomerStatisticsReportVo();
        customerStatisticsReportVo.setAllCount(allCount);
        customerStatisticsReportVo.setFinishCount(finishCount);
        List<CustomerStatisticsReportGroupVo> statisticsGroupList = csFormInstMapper.itStatisticsReportGroup(params);
        if (statisticsGroupList != null && statisticsGroupList.size() > 0) {
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(0);
            for (CustomerStatisticsReportGroupVo cv : statisticsGroupList) {
                if (cv.getFinishCount() != null && cv.getFinishCount() != 0 && cv.getGroupCount() != null
                        && cv.getGroupCount() != 0) {
                    String result = numberFormat.format((float) cv.getFinishCount() / (float) cv.getGroupCount() * 100);
                    cv.setProportion(result);
                } else {
                    cv.setFinishCount(0);
                    cv.setProportion("0");
                }
            }
        }
        customerStatisticsReportVo.setStatisticsGroupList(statisticsGroupList);
        return customerStatisticsReportVo;
    }

    @Override
    public CustomerStatisticsReportUpVo customerStatisticsReportUpCount(Integer range, Integer region, Integer type) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        if (type != null && type == 1) {
            params.put("firstSortCode", "coTS");
        } else if (type != null && type == 2) {
            params.put("firstSortCode", "coBX");
        }
        Integer allCount = csFormInstMapper.customerStatisticsReportCount(params);
        Map<String, Object> params2 = new HashMap<>();
        params.put("upgradeFlag", "1");
        params2.putAll(params);
        Integer upCount = csFormInstMapper.customerStatisticsReportCount(params2);
        CustomerStatisticsReportUpVo customerStatisticsReportUpVo = new CustomerStatisticsReportUpVo();
        customerStatisticsReportUpVo.setAllCount(allCount);
        customerStatisticsReportUpVo.setUpCount(upCount);
        List<CustomerStatisticsReportUpGroupVo> statisticsUpGroupList = null;
        if (region == 2) {
            statisticsUpGroupList = csFormInstMapper.customerStatisticsReportUpRegionGroup(params2);
        } else if (region == 1) {
            statisticsUpGroupList = csFormInstMapper.customerStatisticsReportUpCityGroup(params2);
        }
        if (statisticsUpGroupList != null && statisticsUpGroupList.size() > 0) {
            for (CustomerStatisticsReportUpGroupVo csrugv : statisticsUpGroupList) {
                if (csrugv.getRegionCount() == null) {
                    csrugv.setRegionCount(0);
                }
            }
            customerStatisticsReportUpVo.setStatisticsUpGroupList(statisticsUpGroupList);
        }
        return customerStatisticsReportUpVo;
    }

    @Override
    public CustomerStatisticsSatisficingVo customerStatisticsSatisficingCount(Integer range) {
        CustomerStatisticsSatisficingVo customerStatisticsSatisficingVo = getCustomerStatisticsSatisficingVo(range);

        return customerStatisticsSatisficingVo;
    }

    private CustomerStatisticsSatisficingVo getCustomerStatisticsSatisficingVo(Integer range) {
        CustomerStatisticsSatisficingVo customerStatisticsSatisficingVo = new CustomerStatisticsSatisficingVo();
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(0);
        customerStatisticsSatisficingVo = csFormInstMapper.customerStatisticsSatisficingCount(range);
        if (customerStatisticsSatisficingVo.getSatisfactionSum() != null
                && customerStatisticsSatisficingVo.getSatisfactionAll() != null) {
            double rate = customerStatisticsSatisficingVo.getSatisfactionSum() * 20
                    / (float) customerStatisticsSatisficingVo.getSatisfactionAll();
            String result = String.format("%.1f", rate);
            customerStatisticsSatisficingVo.setSatisfactionProportion(result);
        }
        CsThirdScore csThirdScore = new CsThirdScore();
        // 计算"上/下半年"
        Date now = new Date();
        int month = DateUtil.month(now);
        int year = DateUtil.year(now);
        if (month > 6) {
            csThirdScore.setPhase(1);
            csThirdScore.setAnnum(year);
        } else {
            csThirdScore.setPhase(2);
            csThirdScore.setAnnum(year - 1);
        }
        CsThirdScore findCsThirdScore = csThirdScoreMapper.selectOne(csThirdScore);
        if (findCsThirdScore == null) {
            return null;
        }
        customerStatisticsSatisficingVo.setThirdScore(findCsThirdScore.getScore());
        customerStatisticsSatisficingVo.setScoreName(findCsThirdScore.getName());
        customerStatisticsSatisficingVo.setThirdProportion(findCsThirdScore.getScore());
        List<CustomerStatisticsSatisficingGroupVo> satisficingGroupShowVoList = groupCountByRange(range);
        List<CustomerStatisticsSatisficingGroupVo> satisficingGroupVoList = groupCountByRange(2);
        List<CustomerStatisticsSatisficingGroupVo> satisficingGroupBeforeMonthVoList = groupCountByRange(3);
        satisficingGroupVoList = setTwoChange(satisficingGroupVoList, satisficingGroupBeforeMonthVoList);
        setChange(satisficingGroupShowVoList, satisficingGroupVoList);
        sortByCount(satisficingGroupShowVoList);
        customerStatisticsSatisficingVo.setSatisficingGroupVoList(satisficingGroupShowVoList);
        return customerStatisticsSatisficingVo;
    }

    private void sortByCount(List<CustomerStatisticsSatisficingGroupVo> satisficingGroupShowVoList) {
        Collections.sort(satisficingGroupShowVoList, new Comparator<CustomerStatisticsSatisficingGroupVo>() {
            @Override
            public int compare(CustomerStatisticsSatisficingGroupVo o1, CustomerStatisticsSatisficingGroupVo o2) {
                if (o1.getSatisfactionProportion() > o2.getSatisfactionProportion()) {
                    return -1;
                } else if (o1.getSatisfactionProportion() < o2.getSatisfactionProportion()) {
                    return 1;
                } else if (o1.getSatisfactionGroup() > o2.getSatisfactionGroup()) {
                    return -1;
                } else if (o1.getSatisfactionGroup() < o2.getSatisfactionGroup()) {
                    return 1;
                }
                return 0;
            }
        });
        int i = 0;
        for (CustomerStatisticsSatisficingGroupVo cssgv : satisficingGroupShowVoList) {
            cssgv.setSeq(i);
            ++i;
        }
    }

    private List<CustomerStatisticsSatisficingGroupVo> setTwoChange(
            List<CustomerStatisticsSatisficingGroupVo> satisficingGroupVoList,
            List<CustomerStatisticsSatisficingGroupVo> satisficingGroupBeforeMonthVoList) {
        for (CustomerStatisticsSatisficingGroupVo curr : satisficingGroupVoList) {
            if (satisficingGroupBeforeMonthVoList == null || satisficingGroupBeforeMonthVoList.isEmpty()) {
                curr.setChange("0");
                continue;
            }
            for (CustomerStatisticsSatisficingGroupVo beforeMonth : satisficingGroupBeforeMonthVoList) {
                if (curr.getRegionName().equals(beforeMonth.getRegionName())) {
                    if (curr.getGroupSeq() > beforeMonth.getGroupSeq()) {
                        curr.setChange("-1");
                    } else if (curr.getGroupSeq() < beforeMonth.getGroupSeq()) {
                        curr.setChange("1");
                    } else {
                        curr.setChange("0");
                    }
                    break;
                }
            }
        }
        return satisficingGroupVoList;
    }

    private void setChange(List<CustomerStatisticsSatisficingGroupVo> satisficingGroupShowVoList,
                           List<CustomerStatisticsSatisficingGroupVo> satisficingGroupChangeVoList) {
        for (CustomerStatisticsSatisficingGroupVo curr : satisficingGroupShowVoList) {
            for (CustomerStatisticsSatisficingGroupVo change : satisficingGroupChangeVoList) {
                if (curr.getRegionName().equals(change.getRegionName())) {
                    curr.setChange(change.getChange());
                    break;
                }
            }
            if (curr.getChange() == null) {
                curr.setChange("0");
            }
        }
    }

    private List<CustomerStatisticsSatisficingGroupVo> groupCountByRange(Integer range) {
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(0);
        List<CustomerStatisticsSatisficingGroupVo> satisficingGroupVoList = csFormInstMapper
                .customerStatisticsSatisficingGroup(range);
        if (satisficingGroupVoList != null && satisficingGroupVoList.size() > 0) {
            for (CustomerStatisticsSatisficingGroupVo cssgv : satisficingGroupVoList) {
                if (cssgv.getSatisfactionSum() == 0) {
                    cssgv.setSatisfactionProportion(0);
                } else {
                    String result = numberFormat
                            .format((float) cssgv.getSatisfactionSum() * 20 / (float) cssgv.getSatisfactionGroup());
                    cssgv.setSatisfactionProportion(Integer.parseInt(result));
                }
            }
            Collections.sort(satisficingGroupVoList, new Comparator<CustomerStatisticsSatisficingGroupVo>() {
                @Override
                public int compare(CustomerStatisticsSatisficingGroupVo o1, CustomerStatisticsSatisficingGroupVo o2) {
                    if (o1.getSatisfactionProportion() > o2.getSatisfactionProportion()) {
                        return -1;
                    } else if (o1.getSatisfactionProportion() > o2.getSatisfactionProportion()) {
                        return 1;
                    }
                    return 0;
                }
            });
            int i = 0;
            int j = 0;
            CustomerStatisticsSatisficingGroupVo beforeCssgv = null;
            for (CustomerStatisticsSatisficingGroupVo cssgv : satisficingGroupVoList) {
                if (beforeCssgv != null
                        && beforeCssgv.getSatisfactionProportion() == cssgv.getSatisfactionProportion()) {
                    cssgv.setGroupSeq(beforeCssgv.getGroupSeq());
                } else {
                    cssgv.setGroupSeq(j);
                    j++;
                }
                cssgv.setSeq(i);
                ++i;
                beforeCssgv = cssgv;
            }
        }
        return satisficingGroupVoList;
    }

    @Override
    public CustomerStatisticsReportUpNextVo customerStatisticsReportUpNextCount(Integer range, Integer region,
                                                                                Integer type, String regionName) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        params.put("region", region);
        if (type != null && type == 1) {
            params.put("firstSortCode", "coTS");
        } else if (type != null && type == 2) {
            params.put("firstSortCode", "coBX");
        }
        if (region != null) {
            params.put("upgradeLevel", region);
        }
        params.put("regionName", regionName);
        CustomerStatisticsReportUpNextVo customerStatisticsReportUpNextVo = new CustomerStatisticsReportUpNextVo();
        List<CustomerStatisticsReportUpNextGroupVo> reportUpNextGroupList = csFormInstMapper
                .customerStatisticsReportUpNextGroup(params);
        if (reportUpNextGroupList != null && reportUpNextGroupList.size() > 0) {
            for (CustomerStatisticsReportUpNextGroupVo csrugv : reportUpNextGroupList) {
                if (csrugv.getGroupCount() == null) {
                    csrugv.setGroupCount(0);
                }
            }
            Collections.sort(reportUpNextGroupList, new Comparator<CustomerStatisticsReportUpNextGroupVo>() {
                @Override
                public int compare(CustomerStatisticsReportUpNextGroupVo o1, CustomerStatisticsReportUpNextGroupVo o2) {
                    if (o1.getGroupCount() > o2.getGroupCount()) {
                        return -1;
                    } else if (o1.getGroupCount() < o2.getGroupCount()) {
                        return 1;
                    }
                    return 0;
                }
            });
            customerStatisticsReportUpNextVo.setReportUpNextGroupList(reportUpNextGroupList);
        }
        return customerStatisticsReportUpNextVo;
    }

    /**
     * @return
     * <AUTHOR>
     * @Date 21:25 2018/12/6
     * @Param
     **/
    @Override
    public ResponseMessage propertyReportSubmission(String wyFormNo, String firstSortCode, String mobile,
                                                    String customerDemand, String projectCode, String creationDate, String ownerId, String houseInfoId,
                                                    String createUserName) {
    	
        log.info("---------->>>[物业工单编号] wyFormNo : [" + wyFormNo + "] <<<-----------");
        log.info("---------->>>[一级分类编码] firstSortCode : [" + firstSortCode + "] <<<-----------");
        log.info("---------->>>[移动电话] mobile : [" + mobile + "] <<<-----------");
        log.info("---------->>>[客户诉求] customerDemand : [" + customerDemand + "] <<<-----------");
        log.info("---------->>>[项目编码] projectCode : [" + projectCode + "] <<<-----------");
        log.info("---------->>>[创建日期] creationDate : [" + creationDate + "] <<<-----------");
        log.info("---------->>>[业主ID] ownerId : [" + ownerId + "] <<<-----------");
        log.info("---------->>>[房屋ID] houseInfoId : [" + houseInfoId + "] <<<-----------");
        log.info("---------->>>[创建人名称] createUserName : [" + createUserName + "] <<<-----------");

        // 判断推送物业工单编号是否以推送过
        int i = csFormInstMapper.selectWyFormNoCount(wyFormNo);
        if (i > 1) {
            log.info("---------->>>  根据wyFormNo查询出,库里已有当前推送物业工单编号   <<<-----------");
            return ResponseMessage.error("根据wyFormNo查询出,库里已有当前推送物业工单编号");
        }
        CsUcUser csUcUser = new CsUcUser();// 这个是暂时使用数据
        csUcUser.setFdUsername("liwenye");
        csUcUser.setFdName(createUserName);
        
        ThreadLocalUtils.setUser(csUcUser);
        // 根据业主ID获取业主信息 表cs_cust_info
        // 获取业主信息
        List<CsCustInfo> csCustInfos = csCustInfoMapper.selectWyCust(ownerId);
        if (csCustInfos == null || csCustInfos.isEmpty()) {
            log.info("---------->>>  根据业主id查询出的数据为null   <<<-----------");
            return ResponseMessage.error("根据业主id查询出的数据为null");
        }
        // 获取房屋信息 表cs_house_info
        // String houseInfoId = (String) info.get("houseInfoId");
        List<CsHouseInfo> houstInfos = csHouseInfoMapper.selectWyHouse(houseInfoId);
        if (houstInfos == null || houstInfos.isEmpty()) {
            log.info("---------->>>  根据房屋编码查询出的数据为null   <<<-----------");
            return ResponseMessage.error("根据房屋编码查询出的数据为null");
        }
        CsHouseInfo houstInfo = houstInfos.get(0);
        // 获取项目信息 根据项目编码
        // String projectCode = (String) info.get("projectCode");
        String projectCodeInCsProjectIDImfo = csProjectInfoMapper
                .selectCsProjectIDImfoProjectIdByProjectCode(projectCode);
        if (projectCodeInCsProjectIDImfo != null && !projectCodeInCsProjectIDImfo.equals("")) {
            projectCode = projectCodeInCsProjectIDImfo;
        }
        CsProjectInfo csProjectInfo = csProjectInfoMapper.selectAllProjectByProjectCode(projectCode);

        if (csProjectInfo == null) {
            log.info("---------->>>  根据项目编码查询出的数据为null   <<<-----------");
            return ResponseMessage.error("根据项目编码查询出的数据为null");
        }
        // 获取报事信息 表cs_form_inst
        // 根据一级分类编码进行查询
        // String firstSortCode = (String) info.get("firstSortCode");
        CsDictItem csDictItem = csDictItemMapper.selectFirstSortCode(firstSortCode);
        if (csDictItem == null) {
            log.info("---------->>>  根据一级分类编码查询出的数据为null   <<<-----------");
            return ResponseMessage.error("根据一级分类编码查询出的数据为null");
        }
        // 添加客户诉求
        CsFormInst csFormInst = new CsFormInst();

        // 生成物业工单编号
        String gdbh = "";
        if (firstSortCode.equals("coTS")) {
            gdbh = "deptTS";
            // 投诉标题
            csFormInst.setComplaintHeadlines("物业推送");
        }
        if (firstSortCode.equals("coBX")) {
            gdbh = "deptBX";
        }
        String no = getNo(gdbh);
        // String customerDemand = (String) info.get("customerDemand");
        csFormInst.setCustomerDemand(customerDemand);
        // 添加业主信息
        for (CsCustInfo csCustInfo : csCustInfos) {

            // 业主姓名
            csFormInst.setOwnerName(csCustInfo.getCustName());
            // 业主id
            csFormInst.setOwnerId(csCustInfo.getCustId());

            // 业主类型编码1：个人2：单位
            String belong = csCustInfo.getBelong();
            if (belong != null && belong.equals("")) {
                if (belong.equals("个人")) {
                    csFormInst.setOwnerType(1);
                } else {
                    csFormInst.setOwnerType(2);
                }
            } else {
                csFormInst.setOwnerType(1);
            }

            // 其他版块会员-1：否，1：是
            String otherBoardMember = csCustInfo.getOtherBoardMember();
            if (otherBoardMember != null && otherBoardMember.equals("")) {
                int obm = otherBoardMember.equals("是") ? 1 : -1;
                csFormInst.setOrtherMember(obm);
            } else {
                csFormInst.setOrtherMember(null);
            }

            // 特殊客户-1：否，1：是
            String specialCustomer = csCustInfo.getSpecialCustomer();
            if (specialCustomer != null && specialCustomer.equals("")) {
                int sc = specialCustomer.equals("是") ? 1 : -1;
                csFormInst.setSpecialUser(sc);
            } else {
                csFormInst.setSpecialUser(null);
            }

            // 国籍
            String national = csCustInfo.getNational();
            csFormInst.setNationality(national);

            // 证件类型编码 1 身份证 2 护照
            String certificateName = csCustInfo.getCertificateName();
            if (certificateName != null && certificateName.equals("")) {
                String cfn = certificateName.equals("身份证") ? "1" : "2";
                csFormInst.setIdCode(cfn);
            } else {
                csFormInst.setIdCode("");
            }
            // 性别
            String sex = csCustInfo.getSex();
            if (sex == null) {
                sex = "男";
            }
            csFormInst.setGenderName(sex);
            // 证件类型名称
            csFormInst.setIdName(certificateName);
            // 证件号码
            String certificateNum = csCustInfo.getCertificateNum();
            csFormInst.setIdNo(certificateNum);

            // 出生日期
            String birthday = csCustInfo.getBirthday();
            csFormInst.setBirthDate(getDate(birthday));

            // 工作单位
            csFormInst.setWorkUnit(csCustInfo.getWorkUnit());
            // 职业
            csFormInst.setOccupation(csCustInfo.getProfession());
            // 爱好
            csFormInst.setHobby(csCustInfo.getHobbies());
            // 传真电话
            csFormInst.setFaxPhone(csCustInfo.getFax());
            // 联系地址
            csFormInst.setContactAddress(csCustInfo.getContactAddress());
            // 固定电话
            csFormInst.setFixedTelephone(csCustInfo.getFixedTelephone());
            // 电子邮件
            csFormInst.seteMail(csFormInst.geteMail());
            // 邮政编码
            csFormInst.setPostalCode(csCustInfo.getPostcode());
            
            break;
        }
        // 物业报事编号
        csFormInst.setWyFormNo(wyFormNo);
        // 报事来源
        csFormInst.setSource("物业");
        // 移动电话
        // String mobile = (String) info.get("mobile");
        csFormInst.setMobile(mobile);
        // 添加房屋信息
        
        // 从房屋获取管家信息
        // 管家姓名
        csFormInst.setHousekeeperName(houstInfo.getStewardName());
        // 管家电话
        csFormInst.setHousekeeperTel(houstInfo.getStewardTelephone());
        // 房屋编号
        csFormInst.setHouseNo(houstInfo.getHouseNum());
        // 房屋名称
        csFormInst.setHouseName(houstInfo.getHouseName());
        // 区域编码
        csFormInst.setRegionCode(csProjectInfo.getRegionCode());
        // 区域
        csFormInst.setRegion(csProjectInfo.getRegion());
        // 城市编码
        csFormInst.setCityCode(csProjectInfo.getCityCompanyCode());
        // 城市
        csFormInst.setCity(csProjectInfo.getCityCompany());
        /*
         * if (projectCodeInCsProjectIDImfo != null &&
		 * !projectCodeInCsProjectIDImfo.equals("")){ //项目编码
		 * csFormInst.setProjectCode(projectCodeInCsProjectIDImfo); }else { //项目编码
		 */
        csFormInst.setProjectCode(csProjectInfo.getProjectCode());
        // }
        // 项目
        csFormInst.setProject(csProjectInfo.getProject());
        // 楼栋
        csFormInst.setBuildingNo(houstInfo.getBuilding());
        // 单元号
        csFormInst.setBuildingUnit(houstInfo.getUnit());
        // 房间号
        csFormInst.setRoomNo(houstInfo.getRoomNum());
        //
        csFormInst.setHouseInfoId(houstInfo.getHouseNum());
        // 使用性质
        // 1 车位-无产权车位 2 住宅-叠拼 3 住宅-合院 4 写字楼-LOFT 5 住宅-高层
        String useProperty = houstInfo.getUseProperty();
        String up = "";
        if (useProperty != null && useProperty.equals("")) {
            if (useProperty.equals("车位-无产权车位")) {
                up = "1";
            } else if (useProperty.equals("住宅-叠拼")) {
                up = "2";
            } else if (useProperty.equals("住宅-合院")) {
                up = "3";
            } else if (useProperty.equals("写字楼-LOFT")) {
                up = "4";
            } else if (useProperty.equals("住宅-高层")) {
                up = "5";
            }
        } else {
            csFormInst.setUsePropertyCode("");
        }
        csFormInst.setUsePropertyCode(up);
        // 使用性质名称
        csFormInst.setUsePropertyName(houstInfo.getUseProperty());
        // 合同交房时间
        csFormInst.setContractDeliveryTime(getDate(houstInfo.getDeliveryDate()));
        // 签约时间
        csFormInst.setSigningTime(getDate(houstInfo.getSignDate()));
        // 集中交房时间从
        csFormInst.setFocusDeliveryTimeFrom(houstInfo.getFocusStartDate());
        // 集中交房时间到
        csFormInst.setFocusDeliveryTimeTo(houstInfo.getFocusEndDate());
        // 交付状态1:已交付 -1未交付
        Integer deliveryStatus = houstInfo.getDeliveryStatus();
        if (deliveryStatus != null) {
            csFormInst.setDeliveryState(deliveryStatus.equals(1) ? "已交付" : "未交付");
        } else {
            csFormInst.setDeliveryState(null);
        }
        // 是否精装1:是 -1：否
        String fitment = houstInfo.getFitment();
        if (fitment == null) {
            fitment = "";
        }
        if (fitment.equals("精装修")) {
            csFormInst.setHardcoverState("1");
        } else {
            csFormInst.setHardcoverState("-1");
        }

        // 实际交房时间
        csFormInst.setActualDeliverTime(houstInfo.getActualDeliveryDate());
        // 入住时间
        csFormInst.setCheckInTime(houstInfo.getStayTime());
        // 预计脱保时间
        csFormInst.setEstimatedReleaseTime(houstInfo.getOffAidDate());

        // 添加报事信息
        // 工单编号
        csFormInst.setFormNo(no);
        // 部门编号
        csFormInst.setDeptCode("deptDC");
        // 部门名称
        csFormInst.setDeptName("地产");
        // 报事渠道
        csFormInst.setReportChannelCode("channelBMFK");
        //
        csFormInst.setReportChannelName("其他部门反馈");
        // 受理渠道
        csFormInst.setAcceptChannelCode("物业");
        // 创建日期
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //ParsePosition pos = new ParsePosition(0);
        Date strtodate = new Date();
		try {
			strtodate = formatter.parse(creationDate);
		} catch (ParseException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
        csFormInst.setCreationDate(strtodate);

        // 创建人名称
        csFormInst.setCreateUserId(csUcUser.getFdUsername());
        csFormInst.setCreateUserName(csUcUser.getFdName());
        // 一级分类编码
        csFormInst.setFirstSortCode(firstSortCode);

        String firstSortName = "";
        if (firstSortCode.equals("coTS")) {
            // 投诉
            firstSortName = "投诉";
        }
        if (firstSortCode.equals("coBX")) {
            // 保修
            firstSortName = "报修";
        }
        // 一级分类名称
        csFormInst.setFirstSortName(firstSortName);

        // 业务添加业务状态
        csFormInst.setProcessStateCode("draft");
        csFormInst.setProcessStateName("报事录入");
        csFormInst.setProcessCode("processBZ");
        csFormInst.setProcessName("标准流程");

        try {
            // 报事添加
            baseMapper.insert(csFormInst);
            // 报事暂从
            csProcessWorkitemService.addWorkitem(csFormInst.getId(), "", "draft", csFormInst);
            // 报事提交
            String s = String.valueOf(csFormInst.getId());

            if (firstSortCode.equals("coTS")) {// 投诉
                List<CsUserRole> list = sComplaintService.getUserByRole("submit", csFormInst.getId());
                if (list != null && list.size() > 0) {
                    sComplaintService.process(csFormInst, s, firstSortCode, "deptWY", "draft", "", "物业推送单据",
                            list.get(0).getUserId(), list.get(0).getUserName(), mobile, "submit");
                }
            } else if (firstSortCode.equals("coBX")) {// 报修
                List<CsUserRole> list = sRepairService.getUserByRole("submit", csFormInst.getId());
                if (list != null && list.size() > 0) {
                    sRepairService.process(csFormInst, s, firstSortCode, "deptWY", "draft", "", "物业推送单据",
                            list.get(0).getUserId(), list.get(0).getUserName(), mobile, "submit");
                }
            }

            Long id = csFormInst.getId();
            Integer fid = id.intValue();
            Map<String, Object> formNo = csFormInstMapper.selectFormNo(fid);
            if (formNo != null) {
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setCode(200);
                responseMessage.setMessage("提交成功");
                responseMessage.setData(formNo);
                responseMessage.setSucess(true);
                return responseMessage;
            } else {
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setCode(499);
                responseMessage.setSucess(false);
                responseMessage.setMessage("提交失败");
                responseMessage.setData(csFormInst);
                return responseMessage;
            }
        } catch (Exception e) {
            e.getMessage();
            return ResponseMessage.error(e.getMessage());
        }

    }

    /**
     * @return
     * <AUTHOR> 报事业务步骤修改根据地产工单编号
     * @Date 15:42 2018/12/9
     * @Param
     **/
    @Override
    public ResponseMessage updateProcessStateNameByformNo(String formNo, String processStateName) {

        // 根据地产物业编号进行查询
        CsFormInst csFormInst = csFormInstMapper.selectAllCsFormInsByFormNo(formNo);
        if (csFormInst == null) {
            log.info("---------->>>  根据地产物业编号进行查询出的数据为null   <<<-----------");
            return ResponseMessage.error("根据地产物业编号进行查询出的数据为null");
        }
        csFormInst.setProcessStateName(processStateName);
        //返成功状态给千丁---------start-----------------
        //判断：1千丁的工单   2流程状态的名字 包含关闭  
        if("千丁".equals(csFormInst.getSource()) && processStateName.indexOf("已回访") != -1){
        	Date now = new Date();
        	// 加密orderStatus=1&
        	String str = "qdOrganId="+qdOrganId+"&qdProblemTaskId="+csFormInst.getQdFormNo()
        		+"&timestamp="+now.getTime();
        	String sign = MD5Util.getMD5String(str + qdBackKey).toUpperCase();
        	
            // 路径拼接orderStatus=1&
            String qdurl = "?qdOrganId=" + qdOrganId + "&qdProblemTaskId=" + csFormInst.getQdFormNo()
            	+"&timestamp="+now.getTime()+"&sign="+sign;
            // 调用接口
            log.info("-------Q-D---千丁状态推送--url路径---url明文---->>>>>>>>>>>>>>>  [" + 	qduri + qdurl + "]   " + new Date()
                    + "   <<<<<<<<<<<<<<<<<<---------------------");
            Map<String,Object> paramMap = new HashMap<String,Object>(); 
//            paramMap.put("orderStatus", 1);
//            paramMap.put("orderStatusChangeTime", null);
            paramMap.put("qdOrganId", qdOrganId);
            paramMap.put("qdProblemTaskId", csFormInst.getQdFormNo());
            paramMap.put("timestamp", now.getTime());
            paramMap.put("sign", sign);
            String result = HttpUtil.post(qduri, JSON.toJSONString(paramMap));
            // 判断返回的值
            JSONObject object = JSONUtil.parseObj(result);
            Integer code = (Integer) object.get("code");
            String message = (String) object.get("message");
            if(message.length()>50){
            	message = message.substring(0, 49);
            }
            csFormInst.setQdBackMes("code:"+code+",mes:"+message);
            csFormInstMapper.updateById(csFormInst);
            log.info("-------Q-D---千丁状态推送--返回结果------->>>>>>>>>>>>>>>  [" + result + "]   " + new Date()
            		+ "   <<<<<<<<<<<<<<<<<<---------------------");
        }
        //返成功状态给千丁---------end------------
        Boolean aBoolean = csFormInstMapper.updateProcessStateName(csFormInst);

        if (aBoolean) {
            log.info("-------M-T---地产状态变更------->>>>>>>>>>>>>>>  [推送成功]  <<<<<<<<<<<<<<<<<<---------------------");
            ResponseMessage responseMessage = new ResponseMessage();
            responseMessage.setCode(200);
            responseMessage.setSucess(true);
            responseMessage.setMessage("推送成功");
            responseMessage.setData(null);
            return responseMessage;
        } else {
            log.info("-------M-T---地产状态变更------->>>>>>>>>>>>>>>  [推送失败]  <<<<<<<<<<<<<<<<<<---------------------");
            ResponseMessage responseMessage = new ResponseMessage();
            responseMessage.setCode(499);
            responseMessage.setSucess(false);
            responseMessage.setMessage("推送失败");
            responseMessage.setData(null);
            return responseMessage;
        }

    }

    /**
     * @return
     * <AUTHOR> 公区报事提交
     * @Date 14:44 2018/12/10
     * @Param
     **/
    @Override
    public void sectorNewspaperSubmission(CsFormInst csFormInst) throws Exception {

        if (csFormInst == null) {
            log.info(
                    "-------M-T---公区报事提交--根据报事编号查询工单信息------->>>>>>>>>>>>>>>  未获取到 formNo(工单编号) 相关信息 请检查 formNo 的正确性   <<<<<<<<<<<<<<<<<<---------------------");
            return;
        }
        // 项目ID
        String CommId = csFormInst.getProjectCode();
        String projectId = csProjectInfoMapper.selectCsProjectIDImfoProjectCodeByProjectId(CommId);
        if (projectId != null && !projectId.equals("")) {
            CommId = projectId;
        }
        // 报事内容

        // String Content =
        // "沈女士15800428050，房屋信息：泰和.上海虹桥15-201，业主来电投诉业主反馈因地暖不热，物业在公共区域安装地暖设施，绿化带被破坏安，安装时也没有通知到业主，施工有噪音影响到休息。业主鄂城找到物业，物业态度非常不好，请处理。";
        String Content = csFormInst.getCustomerDemand();
        // 报事类型ID
        // String firstSortCode = csFormInst.getFirstSortCode();
        String secSortCode = csFormInst.getSecSortCode();

        List<Map<String, Object>> TypeIDs = csFormInstMapper.selectTypeId(secSortCode, CommId + "%");
        String TypeID = "";
        if (TypeIDs != null && TypeIDs.size() > 0 && TypeIDs.get(0) != null) {
            TypeID = (TypeIDs.get(0).get("typeId")).toString();
        }
        log.info("---------->>>>>>> 报事类型ID [" + TypeID + "] <<<<<<<<-----------");
        // 类别ID
        String ClassID = "1";
        // 联系人电话
        String Phone = csFormInst.getMobile();
        // 创建时间
        Date creationDate = csFormInst.getCreationDate();
        String Time = "";
        if (creationDate == null) {
            Date datenew = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy/mm/DD HH:MM:SS");
            Time = formatter.format(datenew);
        } else {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy/mm/DD HH:MM:SS");
            Time = formatter.format(creationDate);
        }
        String DCIncidentNum = csFormInst.getFormNo();
        // 设置Mac MD5加密字符串
        // {md5({Attribute}{yyyyMMdd}{Token})}
        // string mac = Attribute.ToString() + DateTime.Now.ToString("yyyyMMdd") +
        // Token.ToString();
        Date date = new Date();
        SimpleDateFormat formatter2 = new SimpleDateFormat("yyyyMMdd");
        String format = formatter2.format(date);
        String MD5t = "<Attributes><CommId>" + CommId + "</CommId><Content>" + Content + "</Content><TypeID>" + TypeID
                + "</TypeID><ClassID>" + ClassID + "</ClassID><Phone>" + Phone + "</Phone><Time>" + Time
                + "</Time><DCIncidentNum>" + DCIncidentNum + "</DCIncidentNum></Attributes>" + format + token;
        String Mac = MD5Util.getMD5String(MD5t);
        log.info("-------M-T---公区报事提交--Md5加密------->>>>>>>>>>>>>>>  密文: [" + Mac + "]  明文: " + MD5t + " " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        // 路径拼接
        String urlbinama = "<Attributes><CommId>" + CommId + "</CommId>" + "<Content>" + Content + "</Content>"
                + "<TypeID>" + TypeID + "</TypeID>" + "<ClassID>" + ClassID + "</ClassID>" + "<Phone>" + Phone
                + "</Phone>" + "<Time>" + Time + "</Time>" + "<DCIncidentNum>" + DCIncidentNum + "</DCIncidentNum>"
                + "</Attributes>";
        // 请求路径
        String encode = URLEncoder.encode(urlbinama, "UTF-8");
        log.info("-------M-T---公区报事提交--Attribute url 编码------->>>>>>>>>>>>>>>  密文: [" + encode + "]  明文: " + urlbinama
                + " " + new Date() + "   <<<<<<<<<<<<<<<<<<---------------------");
        String wyurl = "?Class=" + Class + "&Command=" + command1 + "&Attribute=" + encode + "&Mac=" + Mac;
        String wyurk = "?Class=" + Class + "&Command=" + command1 + "&Attribute=" + urlbinama + "&Mac=" + Mac;
        // 调用接口
        log.info("-------M-T---公区报事提交--请求路径------->>>>>>>>>>>>>>>  [" + uri + wyurl + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        log.info("-------M-T---公区报事提交--请求路径--明文----->>>>>>>>>>>>>>>  [" + uri + wyurk + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        String result = HttpUtil.get(uri + wyurl);
        log.info("-------M-T---公区报事提交--返回结果------->>>>>>>>>>>>>>>  [" + result + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        // 判断返回的值
        JSONObject object = JSONUtil.parseObj(result);
        String IncidentNum = "";
        if (object.get("Result").equals("true")) {
            System.out.println(object.get("data"));
            Map<String, Object> data = (Map<String, Object>) object.get("data");
            // 获取报事编号
            IncidentNum = (String) data.get("IncidentNum");
            log.info("-------M-T---公区报事提交--IncidentNum的值------->>>>>>>>>>>>>>>  [Incidentum : " + IncidentNum + "]   "
                    + new Date() + "   <<<<<<<<<<<<<<<<<<---------------------");
        } else {
            throw new RuntimeException(String.format("物业推送失败：%s", object.get("data")));
        }
        if (!IncidentNum.equals("")) {
            updateCFI(csFormInst, IncidentNum);
        }

    }

    /**
     * @return
     * <AUTHOR> 户内报事提交
     * @Date 15:58 2018/12/10
     * @Param
     **/
    @Override
    public void indoorNewspaperSubmission(CsFormInst csFormInst) throws Exception {
        if (csFormInst == null) {
            log.info(
                    "-------M-T---户内报事提交--根据报事编号查询工单信息------->>>>>>>>>>>>>>>  未获取到 formNo(工单编号) 相关信息 请检查 formNo 的正确性   <<<<<<<<<<<<<<<<<<---------------------");
            return;
        }
        // 项目ID
        String CommID = csFormInst.getProjectCode();
        String projectId = csProjectInfoMapper.selectCsProjectIDImfoProjectCodeByProjectId(CommID);
        if (projectId != null && !projectId.equals("")) {
            CommID = projectId;
        }
        // 报事内容
        String Content = csFormInst.getCustomerDemand();
        // 客户ID
        String CustID = csSyncWyService.convertCustId(csFormInst.getProjectCode(), csFormInst.getOwnerId());
        // 房间ID
        String RoomID = csSyncWyService.convertRoomId(csFormInst.getProjectCode(), csFormInst.getHouseInfoId());
        // 报事类型ID
        String secSortCode = csFormInst.getSecSortCode();
        List<Map<String, Object>> TypeIDs = csFormInstMapper.selectTypeId(secSortCode, CommID + "%");
        String TypeID = "";
        if (TypeIDs != null && TypeIDs.size() > 0 && TypeIDs.get(0) != null) {
            TypeID = (TypeIDs.get(0).get("typeId")).toString();
        }
        log.info("---------->>>>>>> 报事类型ID [" + TypeID + "] <<<<<<<<-----------");
        // 类别ID
        String ClassID = "1";
        // 报事人姓名
        String IncidentMan = csFormInst.getOwnerName();
        // 联系人电话
        String Phone = csFormInst.getMobile();
        // 路径拼接
        String urlbinama = "<Attributes><CommID>" + CommID + "</CommID>" + "<Content>" + Content + "</Content>"
                + "<CustID>" + CustID + "</CustID>" + "<RoomID>" + RoomID + "</RoomID>" + "<TypeID>" + TypeID
                + "</TypeID>" + "<ClassID>" + ClassID + "</ClassID>" + "<IncidentMan>" + IncidentMan + "</IncidentMan>"
                + "<Phone>" + Phone + "</Phone>" + "<DCIncidentNum>" + csFormInst.getFormNo() + "</DCIncidentNum>"
                + "</Attributes>";
        // 请求路径
        String encode = URLEncoder.encode(urlbinama, "UTF-8");
        log.info("-------M-T---户内报事提交--Attribute url 编码------->>>>>>>>>>>>>>>  密文: [" + encode + "]  明文: " + urlbinama
                + " " + new Date() + "   <<<<<<<<<<<<<<<<<<---------------------");
        // 设置Mac MD5加密字符串
        // {md5({Attribute}{yyyyMMdd}{Token})}
        String Attribute = "<Attributes><CommID>" + CommID + "</CommID>" + "<Content>" + Content + "</Content>"
                + "<CustID>" + CustID + "</CustID>" + "<RoomID>" + RoomID + "</RoomID>" + "<TypeID>" + TypeID
                + "</TypeID>" + "<ClassID>" + ClassID + "</ClassID>" + "<IncidentMan>" + IncidentMan + "</IncidentMan>"
                + "<Phone>" + Phone + "</Phone>" + "<DCIncidentNum>" + csFormInst.getFormNo() + "</DCIncidentNum>"
                + "</Attributes>";
        Date date = new Date();
        SimpleDateFormat formatter2 = new SimpleDateFormat("yyyyMMdd");
        String format = formatter2.format(date);
        String Mac = MD5Util.getMD5String(Attribute + format + token);
        log.info("-------M-T---户内报事提交--Md5加密------->>>>>>>>>>>>>>>  密文: [" + Mac + "]  明文: " + Attribute + format
                + token + " " + new Date() + "   <<<<<<<<<<<<<<<<<<---------------------");
        // 路径拼接
        String wyurl = "?Class=" + Class + "&Command=" + command2 + "&Attribute=" + encode + "&Mac=" + Mac;
        String wyurk = "?Class=" + Class + "&Command=" + command2 + "&Attribute=" + urlbinama + "&Mac=" + Mac;
        // 调用接口
        log.info("-------M-T---户内报事提交--请求路径------->>>>>>>>>>>>>>>  [" + uri + wyurl + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        log.info("-------M-T---户内报事提交--请求路径-明文------>>>>>>>>>>>>>>>  [" + uri + wyurk + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        String result = HttpUtil.get(uri + wyurl);
        log.info("-------M-T---户内报事提交--返回结果------->>>>>>>>>>>>>>>  [" + result + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        // 判断返回的值
        JSONObject object = JSONUtil.parseObj(result);
        String IncidentNum = "";
        if (object.get("Result").equals("true")) {
            Map<String, Object> data = (Map<String, Object>) object.get("data");
            // 获取报事编号
            IncidentNum = (String) data.get("IncidentNum");
            log.info("-------M-T---户内报事提交--IncidentNum的值------->>>>>>>>>>>>>>>  [Incidentum : " + IncidentNum + "]   "
                    + new Date() + "   <<<<<<<<<<<<<<<<<<---------------------");
        } else {
            throw new RuntimeException(String.format("物业推送失败：%s", object.get("data")));
        }

        if (!IncidentNum.equals("")) {
            updateCFI(csFormInst, IncidentNum);
        }
    }

    /**
     * @Param CommID 项目ID
     * @Param IncidentNum 工单编号
     */
    @Override
    public void workOrderNumberQueryWorkOrderStatus(String CommID, String IncidentNum) throws Exception {
        // 设置Mac MD5加密字符串
        // {md5({Attribute}{yyyyMMdd}{Token})}
        String projectId = csProjectInfoMapper.selectCsProjectIDImfoProjectIdByProjectCode(CommID);
        if (projectId != null && !projectId.equals("")) {
            CommID = projectId;
        }
        String Attribute = "<Attributes><CommID>" + CommID + "</CommID>" + "<IncidentNum>" + IncidentNum
                + "</IncidentNum>" + "</Attributes>";
        Date date = new Date();
        SimpleDateFormat formatter2 = new SimpleDateFormat("yyyyMMdd");
        String format = formatter2.format(date);
        // 加密url路径
        String encode = URLEncoder.encode(Attribute, "UTF-8");
        // 加密
        String Mac = MD5Util.getMD5String(Attribute + format + token);
        // 路径拼接
        String wyurl = "?Class=" + Class + "&Command=" + command3 + "&Attribute=" + encode + "&Mac=" + Mac;
        String wyurls = "?Class=" + Class + "&Command=" + command3 + "&Attribute=" + Attribute + "&Mac=" + Mac;
        // 调用接口
        log.info("-------M-T---物业状态推送--url路径---url明文---->>>>>>>>>>>>>>>  [" + uri + wyurls + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        log.info("-------M-T---物业状态推送--url路径---url密文---->>>>>>>>>>>>>>>  [" + uri + wyurl + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        String result = HttpUtil.get(uri + wyurl);
        log.info("-------M-T---物业状态推送--返回结果------->>>>>>>>>>>>>>>  [" + result + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
        JSONObject object = JSONUtil.parseObj(result);
        String data = object.get("data").toString();
        // 获取报事编号
        log.info("-------M-T---物业状态推送返回信息--data的值------->>>>>>>>>>>>>>>  [data : " + data + "]   " + new Date()
                + "   <<<<<<<<<<<<<<<<<<---------------------");
    }

    /**
     * 时间转换
     */
    public Date getDate(String date) {
        if (date == null) {
            date = "";
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(date, pos);
        return strtodate;
    }

    /**
     * 报事工单编号修改,根据工单id
     */
    public void updateCFI(CsFormInst csFormInst, String IncidentNum) {
        // 将报事编号保存到表里
        csFormInst.setProcessStateCode("nomalEnd");
        csFormInst.setProcessStateName("提交至物业");
        csFormInst.setWyFormNo(IncidentNum);
        csFormInst.setCurAssigneeId(ThreadLocalUtils.getUserName());
        csFormInst.setCurAssigneeName(ThreadLocalUtils.getRealName());
        csFormInst.setLastUpdateDate(new Date());
        Integer integer = csFormInstMapper.updateById(csFormInst);
        System.out.println(integer);
    }

    @Override
    public List<ItExpForm> getITList(Page<ItExpForm> page, Map<String, Object> map) {
        if (null == page) {
            return baseMapper.getITList(map);
        }
        return baseMapper.getITList(page, map);
    }

    @Override
    public CustomerStatisticsReportAccidentVo itStatisticsAccidentReportCount(Integer range, Integer type) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        Integer allCount = csFormInstMapper.customerStatisticsAccidentReportCount(params);
        Map<String, Object> params2 = new HashMap<>();
        params.put("type", type);
        params2.putAll(params);
        Integer accidentCount = csFormInstMapper.customerStatisticsAccidentReportCount(params2);
        CustomerStatisticsReportAccidentVo customerStatisticsReportAccidentVo = new CustomerStatisticsReportAccidentVo();
        customerStatisticsReportAccidentVo.setAllCount(allCount);
        customerStatisticsReportAccidentVo.setAccidentCount(accidentCount);
        List<CustomerStatisticsReportAccidentGroupVo> accidentGroupVoList = csFormInstMapper
                .customerStatisticsAccidenReportGroup(params2);
        if (accidentGroupVoList != null && accidentGroupVoList.size() > 0) {
            for (CustomerStatisticsReportAccidentGroupVo csrugv : accidentGroupVoList) {
                if (csrugv.getCodeCount() == null) {
                    csrugv.setCodeCount(0);
                }
            }
            customerStatisticsReportAccidentVo.setAccidentGroupVoList(accidentGroupVoList);
        }
        return customerStatisticsReportAccidentVo;
    }

    @Override
    public List<ItAccidentReportNextVo> itAccidentReportNextCount(Integer range, Integer type, String firstSortCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        params.put("type", type);
        if (StringUtils.isNotBlank(firstSortCode)) {
            params.put("projectCode", firstSortCode);
        }
        List<ItAccidentReportNextVo> itAccidentReportNextVoList = csFormInstMapper.itAccidentReportNextList(params);
        return itAccidentReportNextVoList;
    }

    public List<ItAccidentReportNextVo> itAccidentReportNextQuery(Integer range, Integer type, String firstSortCode,
                                                                  String time) {
        Map<String, Object> params = new HashMap<>();
        params.put("range", range);
        params.put("type", type);
        if (StringUtils.isNotBlank(firstSortCode)) {
            params.put("projectCode", firstSortCode);
        }
        params.put("time", time);
        List<ItAccidentReportNextVo> itAccidentReportNextVoList = csFormInstMapper
                .itAccidentReportNextQueryList(params);
        return itAccidentReportNextVoList;
    }

    @Override
    public ScreenReportCountVo screenReportCount() {
        ScreenReportCountVo screenReportCountVo = new ScreenReportCountVo();
        List<ScreenReportCountItemVo> initReportCount = initReportList();
        List<ScreenReportCountItemVo> dayReportCount = csFormInstMapper.screenReportCountItem(3);
        List<ScreenReportCountItemVo> mergeDayReportCount = mergeReport(initReportCount, dayReportCount);
        List<ScreenReportCountItemVo> initReportCount1 = initReportList();
        List<ScreenReportCountItemVo> monthReportCount = csFormInstMapper.screenReportCountItem(2);
        List<ScreenReportCountItemVo> mergeMonthReportCount = mergeReport(initReportCount1, monthReportCount);
        List<ScreenReportCountRegionVo> regionReportCount = csFormInstMapper.screenReportCountRegion();
        Map<String, List<ScreenReportCountRegionVo>> regionReportCountMap = new HashMap<>();
        List<OneListT<List<ScreenReportCountRegionVo>>> list = new ArrayList<>();
        if (regionReportCount != null) {
            for (ScreenReportCountRegionVo srcrv : regionReportCount) {
                if (regionReportCountMap.containsKey(srcrv.getRegionName())) {
                    regionReportCountMap.get(srcrv.getRegionName()).add(srcrv);
                } else {
                    List<ScreenReportCountRegionVo> item = new ArrayList<>();
                    item.add(srcrv);
                    regionReportCountMap.put(srcrv.getRegionName(), item);
                }
            }
            for (Map.Entry<String, List<ScreenReportCountRegionVo>> entry : regionReportCountMap.entrySet()) {
                OneListT<List<ScreenReportCountRegionVo>> oneListT = new OneListT<>();
                oneListT.setName(entry.getKey());
                oneListT.setData(entry.getValue());
                list.add(oneListT);
            }
            screenReportCountVo.setRegionReportCount(list);
        }
        screenReportCountVo.setDayReportCount(mergeDayReportCount);
        screenReportCountVo.setMonthReportCount(mergeMonthReportCount);
        return screenReportCountVo;
    }

    private List<ScreenReportCountItemVo> mergeReport(List<ScreenReportCountItemVo> initReportCount, List<ScreenReportCountItemVo> dayReportCount) {
        if (dayReportCount == null || dayReportCount.size() <= 0) {
            return initReportCount;
        }
        for (ScreenReportCountItemVo s1 : dayReportCount) {
            for (ScreenReportCountItemVo s2 : initReportCount) {
                if (s1.getTypeCode().equals(s2.getTypeCode())) {
                    BeanUtils.copyProperties(s1, s2);
                }
            }
        }
        return initReportCount;
    }

    private List<ScreenReportCountItemVo> initReportList() {
        List<ScreenReportCountItemVo> initReportCount = new ArrayList<>();
        ScreenReportCountItemVo coZXItem = new ScreenReportCountItemVo();
        coZXItem.setTypeName("咨询");
        coZXItem.setTypeCode("coZX");
        initReportCount.add(coZXItem);
        ScreenReportCountItemVo coTSItem = new ScreenReportCountItemVo();
        coTSItem.setTypeName("投诉");
        coTSItem.setTypeCode("coTS");
        initReportCount.add(coTSItem);
        ScreenReportCountItemVo coBXItem = new ScreenReportCountItemVo();
        coBXItem.setTypeName("报修");
        coBXItem.setTypeCode("coBX");
        initReportCount.add(coBXItem);
        ScreenReportCountItemVo coJYBYItem = new ScreenReportCountItemVo();
        coJYBYItem.setTypeName("建议表扬");
        coJYBYItem.setTypeCode("coJYBY");
        initReportCount.add(coJYBYItem);
        return initReportCount;
    }
    
    /**
     * 查询业主报事列表
     * @param page
     * @param map
     * @return
     */
    @Override
    public List<OwnerFormDto> getOwnerForm(Page<OwnerFormDto> page, Map<String, Object> map) {
        if (page == null) {
            return baseMapper.getOwnerForm(map);
        }
        return baseMapper.getOwnerForm(page, map);
    }
    
    /**
     * 微信报事的取消操作-正常关闭
     * @param formInstId
     */
	public void setWeChatFormCancel(String formInstId,String cancelReason){
		 CsUcUser curUser = csUcUserService.selectByUsername();
		//1.修改待办信息，改为已办
		Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
		wrapper.eq("form_inst_id", formInstId).eq("task_status", 20L);
		CsProcessWorkitem doneItem = csProcessWorkitemService.selectOne(wrapper);
		if(doneItem == null) {
    		return;
    	}
		doneItem.setTaskStatus(new Long(30));//已办
		doneItem.setComment("取消报事："+cancelReason);
		doneItem.setLastUpdateDate(new Date());
		doneItem.setTaskEndTime(new Date());
		doneItem.setAssignId(ThreadLocalUtils.getUserName());
		doneItem.setAssignName(ThreadLocalUtils.getRealName());
        //新增 app来源
        doneItem.setThPlatform(ThreadLocalUtils.getPlatform());
        
		csProcessWorkitemService.updateById(doneItem);

		//2.生成新的待办，正常关闭，已办
		CsProcessWorkitem item = new CsProcessWorkitem();

        item.setProcessStateCode("nomalEnd");
        item.setProcessStateName("正常关闭");
        item.setFormInstId(new Long(formInstId));
        item.setCreationDate(new Date());
        item.setTaskStartTime(new Date());
        item.setAssignId(ThreadLocalUtils.getUserName());
        item.setAssignName(ThreadLocalUtils.getRealName());
        item.setTaskEndTime(this.addOneSecond(new Date()));
        item.setTaskStatus(new Long(30));//结束
        item.setCreateByUserId(ThreadLocalUtils.getUserName());
        item.setLastUpdateDate(new Date());
        csProcessWorkitemService.insert(item);
		//3.修改工单流程状态
//        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
//        formWrapper.where("id={0}",formInstId);
        CsFormInst form = baseMapper.selectById(formInstId);
        form.setProcessStateCode("nomalEnd");
        form.setProcessStateName("正常关闭");
        form.setSubmitDate(new Date());
        form.setLastUpdateDate(new Date());
        baseMapper.updateById(form);
        log.info("时间 {} 流程结束执行：formId：{},assignUserId:{}",new Date(),formInstId,ThreadLocalUtils.getRealName());
	}
	private Date addOneSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, 1);
        return calendar.getTime();
    }

	/**
	 * 微信公众号-报事进度查询列表
	 */
	@Override
	public List<CsFormInst> getWeChatFormList(Map<String, String> map) {
		List<CsFormInst> formInstList = baseMapper.getWeChatFormList(map);
		return formInstList;
	}

	/**
	 * 微信公众号-报事进度详情
	 */
	@Override
	public FamilyListDto setWeChatFormDetails(Map<String, String> map) {
		FamilyListDto familyListDto = new FamilyListDto();
		CsFormInst csFormInst = baseMapper.selectById(map.get("formInstId"));
		familyListDto.setCsFormInst(csFormInst);
		familyListDto.setProcessWorkitem(csProcessWorkitemService.getWorkItemList(map.get("formInstId")));
		return familyListDto;
	}

	@Override
	public CsFormInst saveFormInst(Map<String, Object> map) {
		CsFormInst csformInst = new CsFormInst();
		//1.基本信息
		csformInst.setCustomerDemand(map.get("customerDemand").toString());
		csformInst.setHouseName(map.get("houseName").toString());
		csformInst.setHouseNo(map.get("houseNum").toString());
//		csformInst.setMobile(map.get("mobile").toString());
//		csformInst.setOwnerName(map.get("ownerName").toString());
		csformInst.setProject(map.get("project").toString());
		csformInst.setProjectCode(map.get("projectId").toString());
		csformInst.setCustomerDemand(map.get("customerDemand").toString());
		csformInst.setIsOwner("1");//设置微信报事标志
		csformInst.setProcessStateCode("draft");
		csformInst.setProcessStateName("业主报事");
		csformInst.setCreationDate(new Date());
		csformInst.setLastUpdateDate(new Date());
		csformInst.setOpenId(map.get("openId").toString());
		//2.查询业主信息，根据业主id和房屋编码共同查询人员信息，多条取第一条；若业主id为空，则不填；业主id为逗号分隔字符串
		String userId = map.get("userId").toString();
		map.put("custId", userId.split(","));
		csformInst.setOwnerName(map.get("ownerName").toString());//业主姓名和电话按前台传递
		csformInst.setMobile(map.get("mobile").toString());
		//判断是否传递业主id
		if(userId != null && !"".equals(userId)){
			//2.1根据业主、房屋编码查询业主信息
			List<CsCustInfo> listcust = csCustInfoService.selectCustByIdNum(map);
			if(listcust != null && listcust.size()>0){
				CsCustInfo csCustInfo = listcust.get(0);
				// 业主姓名
//				csformInst.setOwnerName(csCustInfo.getCustName());
	            // 业主id
				csformInst.setOwnerId(csCustInfo.getCustId());
	            // 业主类型编码1：个人2：单位
	            String belong = csCustInfo.getBelong();
	            if (belong != null && belong.equals("")) {
	                if (belong.equals("个人")) {
	                	csformInst.setOwnerType(1);
	                } else {
	                	csformInst.setOwnerType(2);
	                }
	            } else {
	            	csformInst.setOwnerType(1);
	            }

	            // 其他版块会员-1：否，1：是
	            String otherBoardMember = csCustInfo.getOtherBoardMember();
	            if (otherBoardMember != null && otherBoardMember.equals("")) {
	                int obm = otherBoardMember.equals("是") ? 1 : -1;
	                csformInst.setOrtherMember(obm);
	            } else {
	            	csformInst.setOrtherMember(null);
	            }

	            // 特殊客户-1：否，1：是
	            String specialCustomer = csCustInfo.getSpecialCustomer();
	            if (specialCustomer != null && specialCustomer.equals("")) {
	                int sc = specialCustomer.equals("是") ? 1 : -1;
	                csformInst.setSpecialUser(sc);
	            } else {
	            	csformInst.setSpecialUser(null);
	            }

	            // 国籍
	            String national = csCustInfo.getNational();
	            csformInst.setNationality(national);

	            // 证件类型编码 1 身份证 2 护照
	            String certificateName = csCustInfo.getCertificateName();
	            if (certificateName != null && certificateName.equals("")) {
	                String cfn = certificateName.equals("身份证") ? "1" : "2";
	                csformInst.setIdCode(cfn);
	            } else {
	            	csformInst.setIdCode("");
	            }
	            // 性别
	            String sex = csCustInfo.getSex();
	            if (sex == null) {
	                sex = "男";
	            }
	            csformInst.setGenderName(sex);
	            // 证件类型名称
	            csformInst.setIdName(certificateName);
	            // 证件号码
	            String certificateNum = csCustInfo.getCertificateNum();
	            csformInst.setIdNo(certificateNum);

	            // 出生日期
	            String birthday = csCustInfo.getBirthday();
	            csformInst.setBirthDate(getDate(birthday));

	            // 工作单位
	            csformInst.setWorkUnit(csCustInfo.getWorkUnit());
	            // 职业
	            csformInst.setOccupation(csCustInfo.getProfession());
	            // 爱好
	            csformInst.setHobby(csCustInfo.getHobbies());
	            // 传真电话
	            csformInst.setFaxPhone(csCustInfo.getFax());
	            // 联系地址
	            csformInst.setContactAddress(csCustInfo.getContactAddress());
	            // 固定电话
	            csformInst.setFixedTelephone(csCustInfo.getFixedTelephone());
	            // 电子邮件
	            csformInst.seteMail(csCustInfo.getEmail());
	            // 邮政编码
	            csformInst.setPostalCode(csCustInfo.getPostcode());
	            // 管家姓名
	            csformInst.setHousekeeperName(csCustInfo.getStewardName());
	            // 管家电话
	            csformInst.setHousekeeperTel(csCustInfo.getStewardTelephone());
			}
		}
		//3.根据项目查询项目信息
		CsProjectInfo csProjectInfo = csProjectInfoService.selectAllProjectByProjectCode(map.get("projectId").toString());
		if(csProjectInfo != null){
			 // 区域编码
			csformInst.setRegionCode(csProjectInfo.getRegionCode());
	        // 区域
			csformInst.setRegion(csProjectInfo.getRegion());
	        // 城市编码
			csformInst.setCityCode(csProjectInfo.getCityCompanyCode());
	        // 城市
			csformInst.setCity(csProjectInfo.getCityCompany());
		}
		//4.根据房屋编码查询房屋信息
		CsHouseInfo houstInfo = csHouseInfoService.selectAllHoustInfoBy(map.get("houseNum").toString());
		if(houstInfo != null ){
			// 房屋编号
	        csformInst.setHouseNo(houstInfo.getHouseNum());
	        // 房屋名称
	        csformInst.setHouseName(houstInfo.getHouseName());
	        // 楼栋
	        csformInst.setBuildingNo(houstInfo.getBuilding());
	        // 单元号
	        csformInst.setBuildingUnit(houstInfo.getUnit());
	        // 房间号
	        csformInst.setRoomNo(houstInfo.getRoomNum());
	        //
	        csformInst.setHouseInfoId(houstInfo.getHouseNum());
	        // 使用性质
	        // 1 车位-无产权车位 2 住宅-叠拼 3 住宅-合院 4 写字楼-LOFT 5 住宅-高层
	        String useProperty = houstInfo.getUseProperty();
	        String up = "";
	        if (useProperty != null && useProperty.equals("")) {
	            if (useProperty.equals("车位-无产权车位")) {
	                up = "1";
	            } else if (useProperty.equals("住宅-叠拼")) {
	                up = "2";
	            } else if (useProperty.equals("住宅-合院")) {
	                up = "3";
	            } else if (useProperty.equals("写字楼-LOFT")) {
	                up = "4";
	            } else if (useProperty.equals("住宅-高层")) {
	                up = "5";
	            }
	        } else {
	            csformInst.setUsePropertyCode("");
	        }
	        csformInst.setUsePropertyCode(up);
	        // 使用性质名称
	        csformInst.setUsePropertyName(houstInfo.getUseProperty());
	        // 合同交房时间
	        csformInst.setContractDeliveryTime(getDate(houstInfo.getDeliveryDate()));
	        // 签约时间
	        csformInst.setSigningTime(getDate(houstInfo.getSignDate()));
	        // 集中交房时间从
	        csformInst.setFocusDeliveryTimeFrom(houstInfo.getFocusStartDate());
	        // 集中交房时间到
	        csformInst.setFocusDeliveryTimeTo(houstInfo.getFocusEndDate());
	        // 交付状态1:已交付 -1未交付
	        Integer deliveryStatus = houstInfo.getDeliveryStatus();
	        if (deliveryStatus != null) {
	            csformInst.setDeliveryState(deliveryStatus.equals(1) ? "已交付" : "未交付");
	        } else {
	            csformInst.setDeliveryState(null);
	        }
	        // 是否精装1:是 -1：否
	        String fitment = houstInfo.getFitment();
	        if (fitment == null) {
	            fitment = "";
	        }
	        if (fitment.equals("精装修")) {
	            csformInst.setHardcoverState("1");
	        } else {
	            csformInst.setHardcoverState("-1");
	        }

	        // 实际交房时间
	        csformInst.setActualDeliverTime(houstInfo.getActualDeliveryDate());
	        // 入住时间
	        csformInst.setCheckInTime(houstInfo.getStayTime());
	        // 预计脱保时间
	        csformInst.setEstimatedReleaseTime(houstInfo.getOffAidDate());
		}
		this.insert(csformInst);
		return csformInst;
	}
	
	/**
     * @return
     * <AUTHOR>
     * @Date 21:25 2018/12/6
     * @Param
     **/
    @Override
    public ResponseMessage qdSubmission(Map<String, Object> pamam) {

    	String thirdSysId = (String) pamam.get("thirdSysId");//第三方系统id（类似appkey的作用）
        String qdProblemTaskId = (String) pamam.get("qdProblemTaskId");//千丁问题工单id
        String qdProblemTypeId = (String) pamam.get("qdProblemTypeId");//千丁原因大类id
        String thirdProblemTypeId = (String) pamam.get("thirdProblemTypeId");//泰禾原因大类id
        String problemTypeName = (String) pamam.get("problemTypeName");//原因大类名称
        Boolean isPublicArea = (Boolean) pamam.get("isPublicArea");//是否公区，固定值true
        String memo = (String) pamam.get("memo");//问题描述
        String pics = (String) pamam.get("pics");//报事图片，最多3张,逗号分隔
        String createUserId = (String) pamam.get("createUserId");//报事人id
        String createUserName = (String) pamam.get("createUserName");//报事人姓名
        String createUserMobile = (String) pamam.get("createUserMobile");//报事人电话
        Long createTime = (Long) pamam.get("createTime");//工单创建时间
        String handler = (String) pamam.get("handler");//处理人，固定空值“”
        String regionId = (String) pamam.get("regionId");//千丁项目id
        String regionName = (String) pamam.get("regionName");//千丁项目名称
        Long timestamp = (Long) pamam.get("timestamp");//时间戳
        String sign = (String) pamam.get("sign");//MD5签名串
        log.info("---------->>>[第三方系统id] thirdSysId : [" + thirdSysId + "] <<<-----------");
        log.info("---------->>>[千丁问题工单id] qdProblemTaskId : [" + qdProblemTaskId + "] <<<-----------");
        log.info("---------->>>[千丁原因大类id] qdProblemTypeId : [" + qdProblemTypeId + "] <<<-----------");
        log.info("---------->>>[泰禾原因大类id] thirdProblemTypeId : [" + thirdProblemTypeId + "] <<<-----------");
        log.info("---------->>>[原因大类名称] problemTypeName : [" + problemTypeName + "] <<<-----------");
        log.info("---------->>>[是否公区，固定值true] isPublicArea : [" + isPublicArea + "] <<<-----------");
        log.info("---------->>>[问题描述] memo : [" + memo + "] <<<-----------");
        log.info("---------->>>[报事图片] pics : [" + pics + "] <<<-----------");
        log.info("---------->>>[报事人id] createUserId : [" + createUserId + "] <<<-----------");
        log.info("---------->>>[报事人姓名] createUserName : [" + createUserName + "] <<<-----------");
        log.info("---------->>>[报事人电话] createUserMobile : [" + createUserMobile + "] <<<-----------");
        log.info("---------->>>[工单创建时间] createTime : [" + createTime + "] <<<-----------");
        log.info("---------->>>[处理人] handler : [" + handler + "] <<<-----------");
        log.info("---------->>>[千丁项目id] regionId : [" + regionId + "] <<<-----------");
        log.info("---------->>>[千丁项目名称] regionName : [" + regionName + "] <<<-----------");
        log.info("---------->>>[时间戳] timestamp : [" + timestamp + "] <<<-----------");
        log.info("---------->>>[MD5签名串] sign : [" + sign + "] <<<-----------");

        //==========================1.判断推送物业工单编号是否以推送过=============================
        int i = csFormInstMapper.selectQdFormNoCount(qdProblemTaskId);
        if (i > 1) {
            log.info("---------->>>  根据qdProblemTaskId查询出,库里已有当前推送千丁工单编号   <<<-----------");
            return ResponseMessage.error("根据qdProblemTaskId查询出,库里已有当前推送物业工单编号");
        }
        //1.设置人员信息
        CsUcUser csUcUser = new CsUcUser();// 这个是暂时使用数据
        csUcUser.setFdUsername("liwenye");
        csUcUser.setFdName(createUserName);
        if (csUcUser == null) {
            log.info("---------->>>  登陆名查询用户失败   <<<-----------");
            return ResponseMessage.error("登陆名查询用户失败");
        }
        ThreadLocalUtils.setUser(csUcUser);
        //=========================2.获取项目信息 根据项目编码===========================
        String projectWyId = csProjectInfoMapper.selectWyIdByQdId(regionId);
        if (projectWyId != null && !projectWyId.equals("")) {
        	regionId = projectWyId;
        }
        String projectCodeInCsProjectIDImfo = csProjectInfoMapper
                .selectCsProjectIDImfoProjectIdByProjectCode(regionId);
        if (projectCodeInCsProjectIDImfo != null && !projectCodeInCsProjectIDImfo.equals("")) {
        	regionId = projectCodeInCsProjectIDImfo;
        }

        CsProjectInfo csProjectInfo = csProjectInfoMapper.selectAllProjectByProjectCode(regionId);

        if (csProjectInfo == null) {
            log.info("---------->>>  根据项目编码查询出的数据为null   <<<-----------");
            return ResponseMessage.error("根据项目编码查询出的数据为null");
        }
        //==================================3.生成报账单================================
        CsFormInst csFormInst = new CsFormInst();
        //============================3.1业主信息=============================
        csFormInst.setMobile(createUserMobile);// 移动电话
        csFormInst.setOwnerName(createUserName);// 业主姓名
//        csFormInst.setOwnerId(csCustInfo.getCustId());// 业主id
        //==================================3.2项目信息=================================
        csFormInst.setRegionCode(csProjectInfo.getRegionCode());// 区域编码
        csFormInst.setRegion(csProjectInfo.getRegion());// 区域
        csFormInst.setCityCode(csProjectInfo.getCityCompanyCode());// 城市编码
        csFormInst.setCity(csProjectInfo.getCityCompany());// 城市
        csFormInst.setProjectCode(csProjectInfo.getProjectCode());
        csFormInst.setProject(csProjectInfo.getProject());// 项目
        //================================5.4基本信息=================================
        // 生成物业工单编号
        String no = getNo("deptWY");
        csFormInst.setFormNo(no);// 工单编号
        csFormInst.setDeptCode("deptWY");// 部门编号
        csFormInst.setDeptName("物业");// 部门名称
        csFormInst.setReportChannelCode("channelWYPZBS");// 报事渠道
        csFormInst.setReportChannelName("物业品质报事");
        csFormInst.setAcceptChannelCode("物业");// 受理渠道
        csFormInst.setPublicArea(isPublicArea == true ? "1" : "-1");
        // 创建日期
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date strtodate = new Date(createTime);
		try {
			strtodate = formatter.parse(formatter.format(strtodate));
		} catch (ParseException e1) {
			e1.printStackTrace();
		}
        csFormInst.setCreationDate(strtodate);
        
        csFormInst.setFirstSortCode(thirdProblemTypeId);// 一级分类编码
        csFormInst.setFirstSortName(problemTypeName);// 一级分类名称
        //二级分类
        List<CsDictItem> sec = csDictItemMapper.selectSecSortCode(thirdProblemTypeId);
        if(sec != null && sec.size() != 0){
        	csFormInst.setSecSortCode(sec.get(0).getItemCode());
        	csFormInst.setSecSortName(sec.get(0).getItemValue());
        }
        // 业务添加业务状态
        csFormInst.setProcessStateCode("draft");
        csFormInst.setProcessStateName("报事录入");
        csFormInst.setProcessCode("processBZ");
        csFormInst.setProcessName("标准流程");
        csFormInst.setCustomerDemand(memo);//客户需求
        csFormInst.setQdFormNo(qdProblemTaskId);//千丁报事编号
        csFormInst.setSource("千丁");// 报事来源
        // 创建人名称
        csFormInst.setCreateUserId(csUcUser.getFdUsername());
        csFormInst.setCreateUserName(csUcUser.getFdName());
        csFormInst.setQdCreateUserId(createUserId);//千丁报事人id
        csFormInst.setQdProblemTypeId(qdProblemTypeId);//千丁原因大类id
        csFormInst.setQdRegionId(regionId);//千丁项目id
        csFormInst.setQdRegionName(regionName);//千丁项目名称
        
        try {
            // 报事添加
            baseMapper.insert(csFormInst);
            //======================6.存储图片信息=====================
            if (pics != null && !"".equals(pics)) {
            	String[] picUrl = pics.split(",");
				List<String> urls = new ArrayList<>();
				for (String url : picUrl) {
					if (StringUtils.isNotBlank(url)) {
						urls.add(url);
					}
				};
				csFileService.saveCommFile(csFormInst.getId(), urls);
			}
            //发送至物业
            this.sectorNewspaperSubmission(csFormInst);
            
            Long id = csFormInst.getId();
            Integer fid = id.intValue();
            Map<String, Object> formNo = csFormInstMapper.selectFormNo(fid);
            if (formNo != null) {
            	Map<String,Object> result = new HashMap<String,Object>();
            	result.put("tahoeTaskId", formNo.get("formNo"));
            	
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setCode(200);
                responseMessage.setMessage("提交成功");
                responseMessage.setData(result);
                responseMessage.setSucess(true);
                return responseMessage;
            } else {
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setCode(499);
                responseMessage.setSucess(false);
                responseMessage.setMessage("提交失败");
                responseMessage.setData(csFormInst);
                return responseMessage;
            }
        } catch (Exception e) {
            e.getMessage();
            return ResponseMessage.error(e.getMessage());
        }
    }

    /**
     * APP-查询报事处理，分派列表
     */
	@Override
	public List<CsFormInst> getAssAndHandleList(Map<String, Object> map) {
		return csFormInstMapper.getAssAndHandleList(map);
	}

	/**
     * APP-查询报事升级列表
     */
	@Override
	public List<CsFormInst> getUpgradeFormList(Map<String, Object> map) {
		return csFormInstMapper.getUpgradeFormList(map);
	}

	/**
     * APP-查询报事查询列表
     */
	@Override
	public List<CsFormInst> getDeptDCFormList(Map<String, Object> map) {
		return csFormInstMapper.getDeptDCFormList(map);
	}

	/**
     * APP-查询报事抢单列表
     */
	@Override
	public List<CsFormInst> getGrabFormList(Map<String, Object> map) {
		return csFormInstMapper.getGrabFormList(map);
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String modify(List list) {
        Boolean aBoolean=false;
       try {
           for (Object o : list) {
               Map<String,String> map= (Map<String, String>) o;
               String id = map.get("id");
               String username = map.get("username");
               //cs_uc_user表 fd_username
               Wrapper<CsUcUser> wrapper=new EntityWrapper<>();
               wrapper.eq("fd_username",username);
               CsUcUser csUcUser = csUcUserService.selectOne(wrapper);

               CsProcessWorkitem csProcessWorkitem=csProcessWorkitemService.selectByFormId(id);
               if (csUcUser==null){
                   return "修改失败,您选择的处理人在UC用户表中不存在!";
               }
               CsFormInst csFormInst=new CsFormInst();
               csFormInst.setId(Long.parseLong(id));
               csFormInst.setCurAssigneeName(csUcUser.getFdName());
               csFormInst.setCurAssigneeId(csUcUser.getFdUsername());
               aBoolean = csFormInstMapper.updateCurAssigneeName(csFormInst);
               csProcessWorkitem.setAssignId(csUcUser.getFdUsername());
               csProcessWorkitem.setAssignName(csUcUser.getFdName());
               csProcessWorkitem.setAssignMobile(csUcUser.getFdTel());
               //新增 app来源
               csProcessWorkitem.setThPlatform(ThreadLocalUtils.getPlatform());
               
               csProcessWorkitemService.updateById(csProcessWorkitem);
//               CsProcessWorkitem csProcessWorkitem=new CsProcessWorkitem();
//               csProcessWorkitem.setAssignName(csUcUser.getFdName());

           }
       }catch (Exception e){
           e.printStackTrace();
           TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
       }
       if (aBoolean){
            return "修改成功!";
       }
            return "修改失败!";
    }

    /**
     * APP-查询报事升级列表数量
     */
	@Override
	public int selectUpgradeCount(String curAssigneeId) {
		return csFormInstMapper.selectUpgradeCount(curAssigneeId);
	}
	
	/**
	 * APP-查询报事抢单列表数量
	 */
	@Override
	public int selectGrabCount(String curAssigneeId) {
		return csFormInstMapper.selectGrabCount(curAssigneeId);
	}

}
