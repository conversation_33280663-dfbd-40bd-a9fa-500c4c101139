package com.tahoecn.customerservice.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsGrabMapper;
import com.tahoecn.customerservice.model.CsGrab;
import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.service.CsGrabService;

/**
 * 抢单配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2019-06-21
 */
@Service
public class CsGrabServiceImpl extends ServiceImpl<CsGrabMapper, CsGrab> implements CsGrabService {

	@Override
	public List<CsProjectInfo> selectProjectByCode(String code) {
		return baseMapper.selectProjectByCode(code);
	}

	@Override
	public void deleteByCode(String code) {
		baseMapper.deleteByCode(code);
	}

	@Override
	public void insertGrab(String code, String projects) {
		baseMapper.deleteByCode(code);
		if (StringUtils.isNotBlank(projects))
			baseMapper.insertGrab(code, projects.split(","));
	}

}
