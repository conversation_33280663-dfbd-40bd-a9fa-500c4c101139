package com.tahoecn.customerservice.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsHouseInfoMapper;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.dto.CsHouseCodeDto;
import com.tahoecn.customerservice.model.dto.CsHouseInfoDto;
import com.tahoecn.customerservice.model.dto.HcfInfoDto;
import com.tahoecn.customerservice.model.excelDTO.CsHouseInfoCustInfoFrom;
import com.tahoecn.customerservice.service.CsHouseInfoService;

/**
 * <p>
 * 房屋信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsHouseInfoServiceImpl extends ServiceImpl<CsHouseInfoMapper, CsHouseInfo> implements CsHouseInfoService {

    @Autowired
    private CsHouseInfoMapper csHouseInfoMapper;

    public List<CsHouseInfo> selectHouseInfoRelation(CsHouseInfo ci){
    	//查询楼栋
    	if(StringUtils.isBlank(ci.getBuilding())){
    		List<CsHouseInfo> list = csHouseInfoMapper.selectBuildings(ci);
    		List<CsHouseInfo> re = new ArrayList<>();
			if(null!=list&&list.size()>0) {
				Boolean flag = false;
				for (CsHouseInfo cs : list) {
					if (cs == null) {
						flag = true;
						break;
					}
				}
				if(flag == true){
					CsHouseInfo csNew = new CsHouseInfo();
					csNew.setBuilding("(无)");
					re.add(csNew);
				}
				for(CsHouseInfo cs : list){
					if(cs!=null){
						re.add(cs);
					}
				}
			}
    		return re;
		}
		//查询单元
		if(StringUtils.isBlank(ci.getUnit())){
    		if(ci.getBuilding().equals("(无)")){
				List<CsHouseInfo> list = csHouseInfoMapper.selectUnitByNullBudding(ci);
				List<CsHouseInfo> re = new ArrayList<>();
				if(null!=list&&list.size()>0){
					Boolean flag = false;
					for (CsHouseInfo cs : list) {
						if (cs == null) {
							flag = true;
							break;
						}
					}
					if(flag == true){
						CsHouseInfo csNew = new CsHouseInfo();
						csNew.setUnit("(无)");
						re.add(csNew);
					}
					for(CsHouseInfo cs : list){
						if(cs!=null){
							re.add(cs);
						}
					}
				}
				return re;
			}
			List<CsHouseInfo> list = csHouseInfoMapper.selectUnits(ci);
			List<CsHouseInfo> re = new ArrayList<>();
			if(null!=list&&list.size()>0){
				Boolean flag = false;
				for (CsHouseInfo cs : list) {
					if (cs == null) {
						flag = true;
						break;
					}
				}
				if(flag == true){
					CsHouseInfo csNew = new CsHouseInfo();
					csNew.setUnit("(无)");
					re.add(csNew);
				}
				for(CsHouseInfo cs : list){
					if(cs!=null){
						re.add(cs);
					}
				}
			}
			return re;
		}
		//查询房间号
		if(StringUtils.isBlank(ci.getRoomNum())){
    		if(ci.getUnit().equals("(无)")){
    			List<CsHouseInfo> list = csHouseInfoMapper.selectRoomNumsByNullUnit(ci);
				List<CsHouseInfo> re = new ArrayList<>();
				if(null!=list&&list.size()>0){
					Boolean flag = false;
					for (CsHouseInfo cs : list) {
						if (cs == null) {
							flag = true;
							break;
						}
					}
					if(flag == true){
						CsHouseInfo csNew = new CsHouseInfo();
						csNew.setRoomNum("(无)");
						re.add(csNew);
					}
					for(CsHouseInfo cs : list){
						if(cs!=null){
							re.add(cs);
						}
					}
				}
				return re;
			}
			List<CsHouseInfo> list = csHouseInfoMapper.selectRoomNums(ci);
			List<CsHouseInfo> re = new ArrayList<>();
			if(null!=list&&list.size()>0){
				Boolean flag = false;
				for (CsHouseInfo cs : list) {
					if (cs == null) {
						flag = true;
						break;
					}
				}
				if(flag == true){
					CsHouseInfo csNew = new CsHouseInfo();
					csNew.setRoomNum("(无)");
					re.add(csNew);
				}
				for(CsHouseInfo cs : list){
					if(cs!=null){
						re.add(cs);
					}
				}
			}
			return re;
		}
        return null;
    }

	@Override
	public Page<CsHouseInfo> getByHcfInfoDto(HcfInfoDto dto) {
		Page<CsHouseInfo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
		List<CsHouseInfo> info =  baseMapper.selectHcf(page,dto);
		page.setRecords(info);
		return page;
	}

	@Override
	public CsHouseInfoDto selectPHouse(String houseNum) {
		return baseMapper.selectPHouse(houseNum);
	}

	@Override
	public List<CsHouseInfo> selectListPage(Map<String, Object> map, Page<CsHouseInfo> page) {
		if(null == page) {
			return baseMapper.selectListPage(map);
		}
		return baseMapper.selectListPage(map, page);
	}

	@Override
	public int insertMyData() {
		return baseMapper.insertMyData();
	}

	@Override
	public List<CsHouseInfoCustInfoFrom> selectCsHouseInfoCustInfoFrom(Map<String, Object> map) {
		List<CsHouseInfoCustInfoFrom> list = csHouseInfoMapper.selectCsHouseInfoCustInfoFrom(map);
		for (CsHouseInfoCustInfoFrom csHouseInfoCustInfoFrom : list) {
			if(StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getDeliveryStatus())){
				if(csHouseInfoCustInfoFrom.getDeliveryStatus().equals("1")){
					csHouseInfoCustInfoFrom.setDeliveryStatus("已交付");
				}else if(csHouseInfoCustInfoFrom.getDeliveryStatus().equals("-1")) {
					csHouseInfoCustInfoFrom.setDeliveryStatus("未交付");
				}
			}
			if(StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getFitment())){
				if(csHouseInfoCustInfoFrom.getFitment().equals("0")){
					csHouseInfoCustInfoFrom.setFitment("精装修");
				}
			}
			if(StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getBelong())){
				if(csHouseInfoCustInfoFrom.getBelong().equals("0")){
					csHouseInfoCustInfoFrom.setBelong("个人");
				}else if (csHouseInfoCustInfoFrom.getBelong().equals("1")) {
					csHouseInfoCustInfoFrom.setBelong("单位");
				}
			}
			if(StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getIs_vip())){
				if(csHouseInfoCustInfoFrom.getIs_vip().equals("0")){
					csHouseInfoCustInfoFrom.setIs_vip("否");
				}else if(csHouseInfoCustInfoFrom.getIs_vip().equals("1")){
					csHouseInfoCustInfoFrom.setIs_vip("是");
				}
			}
			if(StringUtils.isNotEmpty(csHouseInfoCustInfoFrom.getIsHasMoreHouse())){
				if(csHouseInfoCustInfoFrom.getIsHasMoreHouse().equals("0")){
					csHouseInfoCustInfoFrom.setIsHasMoreHouse("否");
				}else {
					csHouseInfoCustInfoFrom.setIsHasMoreHouse("是");
				}
			}
		}
		return list;
		/*return csHouseInfoMapper.selectCsHouseInfoCustInfoFrom(map);*/
	};
	
	/**
	 * 查询业主房屋信息
	 * @param custId 业主id
	 * @return
	 */
	public List<CsHouseInfo> selectCustHouseByCustId(String custId){
		List<CsHouseInfo> list = baseMapper.selectCustHouseByCustId(custId.split(","));
		for(CsHouseInfo house : list){
			List<CsProjectInfo> pro = baseMapper.selectRegionByProId(house.getProjectId());
			if(pro != null && pro.size() != 0){
				house.setArea(pro.get(0).getRegion());
				house.setCity(pro.get(0).getCityCompany());
			}
		}
		return list;
	}
	
	/**
	 * 查询业主房产对应项目列表
	 * @param custId 业主id
	 * @return
	 */
	public List<CsHouseCodeDto> selectCustHouseProjectByCustId(String custId){
		List<CsHouseCodeDto> a = baseMapper.selectCustHouseProjectByCustId(custId.split(","));
		return a;
	}
	
	/**
	 * 查询业主房产对应区域列表
	 * @param custId 业主id
	 * @return
	 */
	public List<CsHouseCodeDto> selectCustHouseRegionByCustId(String custId){
		return baseMapper.selectCustHouseRegionByCustId(custId.split(","));
	}

	@Override
	public CsHouseInfo selectAllHoustInfoBy(String houseNum) {
		return baseMapper.selectAllHoustInfoBy(houseNum);
	}

	/**
	 * APP-根据项目id查询房屋列表
	 */
	@Override
	public List<Map<String, Object>> getHouseListByProjectId(String projectId) {
		return baseMapper.getHouseListByProjectId(projectId);
	}

	/**
	 * APP-根据房屋编码查询详情，关联业主信息
	 */
	@Override
	public List<Map<String, Object>> getHouseDetailByNum(String houseNum) {
		return baseMapper.getHouseDetailByNum(houseNum);
	}

	@Override
	public void initData() {
		baseMapper.initData();	
	}
}
