package com.tahoecn.customerservice.service.impl;

import com.tahoecn.customerservice.model.CsLabel;
import com.tahoecn.customerservice.model.dto.CsLabelTreeDto;
import com.tahoecn.customerservice.mapper.CsLabelMapper;
import com.tahoecn.customerservice.service.CsLabelService;

import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-09
 */
@Service
public class CsLabelServiceImpl extends ServiceImpl<CsLabelMapper, CsLabel> implements CsLabelService {

	@Autowired
	private CsLabelMapper csLabelMapper;

	/**
	 * 级联删除
	 */
	@Override
	public void delLabels(String labelId) {
		csLabelMapper.delLabelsById(labelId);
	}
	
	/**
     * 查询标签列表返回树形结构
     * @param page
     * @param map
     * @return
     */
    @Override
    public List<CsLabelTreeDto> getLabelTree(Map<String, Object> map) {
    	List<CsLabelTreeDto> trees = new ArrayList<CsLabelTreeDto>();
    	List<CsLabel> labelList = baseMapper.getLabelTree(map);
        for (CsLabel label : labelList) {
        	CsLabelTreeDto tree = new CsLabelTreeDto();
        	BeanUtil.copyProperties(label, tree);
            trees.add(tree);
        }

        // 默认顶级菜单为０，根据数据库实际情况调整
        List<CsLabelTreeDto> list = this.buildList(trees, "0");
    	
    	return list;
    }
    
    public static <T> List<CsLabelTreeDto> buildList(List<CsLabelTreeDto> nodes, String idParam) {
		if (nodes == null && StringUtils.isBlank(idParam)) {
			return null;
		}
		List<CsLabelTreeDto> topNodes = new ArrayList<CsLabelTreeDto>();
		for (CsLabelTreeDto children : nodes){
			if(idParam.equals(children.getFatherId())){
				children.setChildren(buildList(nodes,String.valueOf(children.getId())));
				topNodes.add(children);
			}
		}
		return topNodes.size() != 0 ? topNodes : null;
	}
}
