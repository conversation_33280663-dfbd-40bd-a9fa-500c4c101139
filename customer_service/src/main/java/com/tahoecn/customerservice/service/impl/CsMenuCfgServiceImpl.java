package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.tahoecn.customerservice.mapper.CsMenuCfgMapper;
import com.tahoecn.customerservice.model.CsMenuCfg;
import com.tahoecn.customerservice.model.dto.BuildTree;
import com.tahoecn.customerservice.model.dto.MenuDO;
import com.tahoecn.customerservice.model.dto.SysprivDto;
import com.tahoecn.customerservice.model.dto.Tree;
import com.tahoecn.customerservice.service.CsMenuCfgService;
import com.tahoecn.customerservice.service.RolePrivInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 菜单URL配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsMenuCfgServiceImpl extends ServiceImpl<CsMenuCfgMapper, CsMenuCfg> implements CsMenuCfgService {

    @Autowired
    private CsMenuCfgMapper csMenuCfgMapper;

    @Autowired
    private RolePrivInfoService rolePrivInfoService;

    public String selectUrlByMenuCode(String menuCode) {
        return csMenuCfgMapper.selectUrlByMenuCode(menuCode);
    }

    public List<Map<String, String>> selectUrlByCodeList(List<String> list) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> map = Maps.newHashMap();
        for (String menuCode : list) {
            String url = csMenuCfgMapper.selectUrlByMenuCode(menuCode);
            map.put(menuCode, url);
        }
        result.add(map);
        return result;
    }

    @Override
    public List<Tree<MenuDO>> listMenuTreeByUser() {
        List<Tree<MenuDO>> trees = new ArrayList<Tree<MenuDO>>();
        List<MenuDO> menuDOs = listMenuByUser();
        for (MenuDO sysMenuDO : menuDOs) {
            Tree<MenuDO> tree = new Tree<MenuDO>();
            tree.setId(sysMenuDO.getMenuCode());
            tree.setParentId(sysMenuDO.getParentCode());
            tree.setText(sysMenuDO.getMenuName());
            Map<String, Object> attributes = new HashMap<>(16);
            attributes.put("url", sysMenuDO.getUrl());
            attributes.put("orderNum", sysMenuDO.getOrderNum());
            attributes.put("icoCode",sysMenuDO.getIcoCode());
            tree.setAttributes(attributes);
            trees.add(tree);
        }

        // 默认顶级菜单为０，根据数据库实际情况调整
        List<Tree<MenuDO>> list = BuildTree.buildList(trees, "0");
        return list;
    }

    public List<MenuDO> listMenuByUser() {
//        List<SysprivDto> sysprivDtos = rolePrivInfoService.getsysprivInfo();
//        if (sysprivDtos == null) {
//            return null;
//        }
    	List<CsMenuCfg> csMenuCfgList = csMenuCfgMapper.selectList(null);
        List<MenuDO> menuDOs = new ArrayList<>();
        for (CsMenuCfg csMenuCfg2 : csMenuCfgList) {
            MenuDO md = new MenuDO();
            md.setMenuCode(csMenuCfg2.getMenuCode());
            md.setParentCode(csMenuCfg2.getMenuPcode());
//            if (StringUtils.isBlank(sd.getFdPsid())) {
//                md.setParentCode("0");
//            } else {
//                md.setParentCode(sd.getFdPsid());
//            }
            md.setUrl(csMenuCfg2.getMenuUrl());
            md.setOrderNum(csMenuCfg2.getOrderNum());
            md.setMenuName(csMenuCfg2.getName());
            md.setIcoCode(csMenuCfg2.getIcoCode());
            menuDOs.add(md);

        }
        Collections.sort(menuDOs, new Comparator<MenuDO>() {
            @Override
            public int compare(MenuDO o1, MenuDO o2) {
                if(o1.getOrderNum()<o2.getOrderNum()){
                    return 1;
                }
                if(o1.getOrderNum()>o2.getOrderNum()){
                    return -1;
                }
                return 0;
            }
        });
        return menuDOs;
    }

}
