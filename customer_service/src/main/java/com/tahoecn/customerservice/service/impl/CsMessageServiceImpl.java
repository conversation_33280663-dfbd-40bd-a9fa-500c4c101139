package com.tahoecn.customerservice.service.impl;

import com.tahoecn.customerservice.model.CsMessage;
import com.tahoecn.customerservice.mapper.CsMessageMapper;
import com.tahoecn.customerservice.service.CsMessageService;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * APP消息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-21
 */
@Service
public class CsMessageServiceImpl extends ServiceImpl<CsMessageMapper, CsMessage> implements CsMessageService {

	@Autowired
	private CsMessageMapper csMessageMapper;
	
	/**
	 * APP-查询消息列表
	 */
	@Override
	public List<Map<String, Object>> getMessageListMg(Map<String, Object> map){
		return csMessageMapper.getMessageListMg(map);
	}

	/**
	 * 设置消息为已读
	 * @param messId
	 */
	@Override
	public void setMessRead(String messId) {
		CsMessage message = csMessageMapper.selectById(messId);
		message.setState("0");
		message.setUpdate(new Date());
		csMessageMapper.updateById(message);
	}

}
