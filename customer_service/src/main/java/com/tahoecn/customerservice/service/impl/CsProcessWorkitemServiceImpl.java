package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsProcessWorkitemMapper;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsUcUserService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 待办表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsProcessWorkitemServiceImpl extends ServiceImpl<CsProcessWorkitemMapper, CsProcessWorkitem> implements CsProcessWorkitemService {

    @Autowired
    private CsFormInstService csFormInstService;
    @Autowired
    private CsUcUserService csUcUserService;

    @Autowired
    private CsProcessWorkitemMapper csProcessWorkitemMapper;

    @Override
    public void addWorkitem(Long formInstId, String comment, String operateType, CsFormInst csFormInst) {
    	
    	CsUcUser curUser = new CsUcUser();
    	
    	if(!StringUtils.isNotBlank(csFormInst.getWyFormNo()) || !"draft".equals(csFormInst.getProcessStateCode())){
    		curUser = csUcUserService.selectByUsername();
    	}else{
    		curUser.setFdUsername("liwenye");
    		curUser.setFdName(csFormInst.getCreateUserName());
    	}

        //增加记录
        Wrapper<CsFormInst> wrapper = new EntityWrapper<CsFormInst>();
        wrapper.where("id={0}", formInstId);
        CsFormInst form = csFormInstService.selectOne(wrapper);
        CsProcessWorkitem item = new CsProcessWorkitem();

        item.setTaskStartTime(new Date());
        if ("draft".equals(operateType)) {
            item.setProcessStateCode("draft");
            item.setProcessStateName("报事录入");
            item.setTaskStatus(new Long(20));//待办
        }

        if ("upgrade".equals(operateType)) {
            item.setProcessStateCode("upgrade");
            item.setProcessStateName("报事升级");
            item.setTaskEndTime(new Date());
            item.setTaskStatus(new Long(30));//已办
        }

        if ("update".equals(operateType)) {
            item.setProcessStateCode("handle");
            item.setProcessStateName("报事处理");
            item.setTaskStatus(new Long(30));//已办
            item.setTaskEndTime(new Date());
        }
        item.setFormInstId(new Long(formInstId));
        item.setCreationDate(new Date());
        if (curUser != null) {
            item.setAssignId(curUser.getFdUsername());
            item.setAssignName(curUser.getFdName());
            item.setCreateByUserId(curUser.getFdUsername());
        }
        item.setLastUpdateDate(new Date());
        item.setComment(comment);
        this.insert(item);

        if ("update".equals(operateType) && csFormInst != null) {
            csFormInst.setHandleRecord("");
            csFormInst.setChangeRecord("");
            if (!"draft".equals(form.getProcessCode())) {
                csFormInst.setSubmitDate(new Date());
            }
            csFormInst.setLastUpdateDate(new Date());
            csFormInstService.updateById(csFormInst);
        }
    }

    @Override
    public List getWorkItemList(String formInstId) {
        //增加记录
        Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
        wrapper.where("form_inst_id={0}", formInstId);
        wrapper.orderBy("-id");
        wrapper.orderBy("-task_end_time");
        List workItemList = this.selectList(wrapper);
        return workItemList;
    }

    @Override
    public List getProgressWorkItemList(String formInstId) {

        List workItemList = csProcessWorkitemMapper.getProgressWorkItemList(formInstId);
        return workItemList;
    }

    public CsProcessWorkitem getLastWorkitemById(Long formInstId) {
        return csProcessWorkitemMapper.getLastProcessWorkitem(formInstId);
    }
    
    /** 
     * 业主报事提交增加“业主报事”待办
    * @param formInstId formid
    * @param comment 
    * @param csFormInst
    */
    public void setOwnerProcess(Long formInstId, String comment, String operateType, CsFormInst csFormInst){
    	CsUcUser curUser = new CsUcUser();
    	curUser.setFdUsername("");
    	curUser.setFdName("");
        //增加记录
        Wrapper<CsFormInst> wrapper = new EntityWrapper<CsFormInst>();
        wrapper.where("id={0}", formInstId);
        CsProcessWorkitem item = new CsProcessWorkitem();

        item.setTaskStartTime(new Date());

            item.setProcessStateCode("ownerThings");
            item.setProcessStateName("业主报事");
            item.setTaskEndTime(new Date());
            item.setTaskStatus(new Long(30));//已办

        item.setFormInstId(new Long(formInstId));
        item.setCreationDate(new Date());
        if (curUser != null) {
            item.setAssignId(curUser.getFdUsername());
            item.setAssignName("报事人");
            item.setCreateByUserId(curUser.getFdUsername());
        }
        item.setLastUpdateDate(new Date());
        item.setComment(comment);
        this.insert(item);
      //增加记录
        Wrapper<CsFormInst> wrapperdraft = new EntityWrapper<CsFormInst>();
        wrapperdraft.where("id={0}", formInstId);
        CsProcessWorkitem itemdraft = new CsProcessWorkitem();

        itemdraft.setTaskStartTime(new Date());

        itemdraft.setProcessStateCode("draft");
        itemdraft.setProcessStateName("报事录入");
        itemdraft.setTaskEndTime(new Date());
        itemdraft.setTaskStatus(new Long(20));//已办

        itemdraft.setFormInstId(new Long(formInstId));
        itemdraft.setCreationDate(new Date());
        if (curUser != null) {
        	itemdraft.setAssignId(curUser.getFdUsername());
        	itemdraft.setAssignName(curUser.getFdName());
        	itemdraft.setCreateByUserId(curUser.getFdUsername());
        }
        itemdraft.setLastUpdateDate(new Date());
        itemdraft.setComment(comment);
        this.insert(itemdraft);
    }

    @Override
    public CsProcessWorkitem selectByFormId(String id) {

        return  csProcessWorkitemMapper.selectByFormId(id);
    }
}
