package com.tahoecn.customerservice.service.impl;

import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.dto.MatterStatisticalDto;
import com.tahoecn.customerservice.model.dto.MatterStatisticalParentDto;
import com.tahoecn.customerservice.model.dto.StatisticalDto;
import com.tahoecn.customerservice.model.vo.CsProjectInfoNameAndCode;
import com.tahoecn.customerservice.model.vo.StatisticalVo;
import com.tahoecn.customerservice.mapper.CsProjectInfoMapper;
import com.tahoecn.customerservice.service.CsProjectInfoService;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.landray.sso.client.oracle.StringUtil;
import com.landray.sso.client.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 初始化项目信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsProjectInfoServiceImpl extends ServiceImpl<CsProjectInfoMapper, CsProjectInfo>
		implements CsProjectInfoService {

	@Autowired
	private CsProjectInfoMapper csProjectInfoMapper;

	public List<CsProjectInfo> selectProjectInfoRelation(CsProjectInfo pi) {
		return csProjectInfoMapper.selectProjectInfoRelation(pi);
	}

	@Override

	public List<MatterStatisticalParentDto> recursiveGet(String[] indexName, int i, String condition, String limitTimeStart,String limitTimeEnd) {
		// TODO Auto-generated method stub
		List<CsProjectInfoNameAndCode> slaveNames = csProjectInfoMapper.getSlaveNameList(indexName[0], indexName[1]);

		List<MatterStatisticalParentDto> dataListGet = dataListGet(indexName, i, condition, limitTimeStart,limitTimeEnd, slaveNames);

		return dataListGet;
	}

	public List<MatterStatisticalParentDto> dataListGet(String[] indexName, int i, String condition, String limitTimeStart,String limitTimeEnd,
														List<CsProjectInfoNameAndCode> slaveNames) {
		MatterStatisticalDto matterStatisticalDto = null;
		List<MatterStatisticalParentDto> list = new ArrayList<>();
		for (CsProjectInfoNameAndCode masterSlaveName : slaveNames) {
			if (StringUtils.isEmpty(masterSlaveName.getCode()))
				continue;
			indexName[i] = masterSlaveName.getCode();
			matterStatisticalDto = new MatterStatisticalDto();
			matterStatisticalDto.setId(UUID.randomUUID().toString());
			matterStatisticalDto.setMasterSlaveName(masterSlaveName.getName());
			matterStatisticalDto.setMasterCode(masterSlaveName.getCode());
			list.add(setData(indexName, masterSlaveName, matterStatisticalDto, condition, limitTimeStart,limitTimeEnd));
		}
		indexName[i] = null;
		return list;
	}

	@Override
	public List<Integer> getConditionsNum(String limitTimeStart,String limitTimeEnd) {
		// TODO Auto-generated method stub
		List<Integer> list = new ArrayList<>();
		String[] conditions = { "coBX", "coZX", "coJYBY", "coTS" };
		for (String condition : conditions) {
			list.add(csProjectInfoMapper.getCountNum(null, null, null, condition, limitTimeStart,limitTimeEnd, 1));
		}
		return list;
	}

	@Override
	public List<StatisticalVo> satisfaction(String limitTimeStart,String limitTimeEnd) {
		// TODO Auto-generated method stub
		List<StatisticalVo> statisticalDtos = csProjectInfoMapper.satisfaction(limitTimeStart,limitTimeEnd);
		return statisticalDtos;
	}

	@Override
	public List<Object> getData(String region, String city, String condition, String limitTimeStart,String limitTimeEnd) {
		// TODO Auto-generated method stub
		String[] indexName = { region, city, null };
		List<Object> list = new ArrayList<>();
		int i = 2;
		if (StringUtils.isEmpty(region)) {
			i = 0;
		} else if (StringUtils.isEmpty(city)) {
			i = 1;
		}
		MatterStatisticalParentDto matterStatisticalParentDto = null;
		List<CsProjectInfoNameAndCode> slaveNames = csProjectInfoMapper.getSlaveNameList(indexName[0], indexName[1]);
		for (CsProjectInfoNameAndCode string : slaveNames) {
			indexName[i] = string.getCode();
			matterStatisticalParentDto = new MatterStatisticalParentDto();
			matterStatisticalParentDto = setData(indexName, string, matterStatisticalParentDto, condition, limitTimeStart,limitTimeEnd);
			list.add(matterStatisticalParentDto);
		}

		return list;
	}

	@Override
	public Integer getCountNum(String limitTimeStart,String limitTimeEnd, Integer i) {
		// TODO Auto-generated method stub
		Integer countNum = csProjectInfoMapper.getCountNum(null, null, null, null, limitTimeStart,limitTimeEnd, i);
		return countNum;
	}

	public Integer getCountNum(String[] indexName, String condition, String limitTimeStart,String limitTimeEnd, Integer i) {
		Integer countNum = csProjectInfoMapper.getCountNum(indexName[0], indexName[1], indexName[2], condition, limitTimeStart,limitTimeEnd,
				i);
		return countNum;

	}

	public MatterStatisticalParentDto setData(String[] code, CsProjectInfoNameAndCode masterSlaveName,
											  MatterStatisticalParentDto dto, String condition, String limitTimeStart,String limitTimeEnd) {
		Integer i = 1;
		Integer totalNum = getCountNum(code, condition, limitTimeStart,limitTimeEnd, i++);
		dto.setTotalNum(totalNum);
		dto.setMasterSlaveName(masterSlaveName.getName());
		double finshRate = 0, levelUpRate = 0;
		if (totalNum != 0) {
			Integer finishNum = getCountNum(code, condition, limitTimeStart,limitTimeEnd, i++);
			dto.setDoingNum(getCountNum(code, condition, limitTimeStart,limitTimeEnd, i++));

			Integer levelUpNum = getCountNum(code, condition, limitTimeStart,limitTimeEnd, i++);
			double dtotalNum = (double) totalNum;
			double dfinishNum = (double) finishNum;
			double dlevelUpNum = (double) levelUpNum;
			finshRate = dfinishNum / dtotalNum * 100;
			levelUpRate = dlevelUpNum / dtotalNum * 100;

			dto.setFinishNum(finishNum);
			dto.setLevelUpNum(levelUpNum);
			dto.setReworkNum(getCountNum(code, condition, limitTimeStart,limitTimeEnd, i++));

		}
		dto.setFinsihRate(String.format("%.1f", finshRate));
		dto.setLevelUpRate(String.format("%.1f", levelUpRate));
		return dto;
	}

	@Override
	public List<Object> getData(String limitTimeStart,String limitTimeEnd) {
		// TODO Auto-generated method stub
		MatterStatisticalParentDto matterStatisticalParentDto = null;
		List<CsProjectInfoNameAndCode> slaveNames = csProjectInfoMapper.getSlaveNameList(null, null);
		List<Object> list = new ArrayList<>();
		String[] indexName = new String[3];
		for (CsProjectInfoNameAndCode csProjectInfoNameAndCode : slaveNames) {
			indexName[0] = csProjectInfoNameAndCode.getCode();
			matterStatisticalParentDto = new MatterStatisticalParentDto();
			matterStatisticalParentDto = setData(indexName, csProjectInfoNameAndCode, matterStatisticalParentDto, null,
					limitTimeStart,limitTimeEnd);
			list.add(matterStatisticalParentDto);
		}
		return list;
	}

	@Override
	public Map<String, Object> getProDetailByCode(String code, String codeType) {
		return csProjectInfoMapper.getProDetailByCode(code, codeType);
	}

	@Override
	public CsProjectInfo selectAllProjectByProjectCode(String projectId) {
		// TODO Auto-generated method stub
		return csProjectInfoMapper.selectAllProjectByProjectCode(projectId);
	}

	/**
	 * APP-查询所有区域城市项目
	 */
	@Override
	public List<Map<String, Object>> getProjectList() {
		return csProjectInfoMapper.getProjectList();
	}

}
