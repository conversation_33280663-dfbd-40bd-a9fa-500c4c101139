package com.tahoecn.customerservice.service.impl;

import com.tahoecn.customerservice.model.CsSmsTemplate;
import com.tahoecn.customerservice.mapper.CsSmsTemplateMapper;
import com.tahoecn.customerservice.service.CsSmsTemplateService;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-21
 */
@Transactional
@Service
public class CsSmsTemplateServiceImpl extends ServiceImpl<CsSmsTemplateMapper, CsSmsTemplate> implements CsSmsTemplateService {
    @Autowired
    CsSmsTemplateMapper csSmsTemplateMapper;
    public String queryBySql(String value){
        return csSmsTemplateMapper.queryBySql(value);
    }
}
