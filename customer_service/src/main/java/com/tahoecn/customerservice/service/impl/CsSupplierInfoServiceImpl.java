package com.tahoecn.customerservice.service.impl;

import com.tahoecn.customerservice.model.CsSupplierInfo;
import com.tahoecn.customerservice.mapper.CsSupplierInfoMapper;
import com.tahoecn.customerservice.service.CsSupplierInfoService;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 供方数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-18
 */
@Transactional
@Service
public class CsSupplierInfoServiceImpl extends ServiceImpl<CsSupplierInfoMapper, CsSupplierInfo> implements CsSupplierInfoService {

    @Autowired
    private CsSupplierInfoMapper csSupplierInfoMapper;

    public List<CsSupplierInfo> selectNameByArea(CsSupplierInfo si){
        return csSupplierInfoMapper.selectNameByArea(si);
    }

    /**
     * APP-手机端查询主责单位，维修单位
     */
	@Override
	public List<CsSupplierInfo> getAppNameByArea(Map<String,Object> map) {
		return csSupplierInfoMapper.getAppNameByArea(map);
	}
}
