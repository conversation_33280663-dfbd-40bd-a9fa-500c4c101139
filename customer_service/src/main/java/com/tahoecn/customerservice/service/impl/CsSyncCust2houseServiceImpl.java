package com.tahoecn.customerservice.service.impl;

import java.time.LocalDateTime;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsSyncCust2houseMapper;
import com.tahoecn.customerservice.model.CsSyncCust2house;
import com.tahoecn.customerservice.service.CsSyncCust2houseService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
@Service
public class CsSyncCust2houseServiceImpl extends ServiceImpl<CsSyncCust2houseMapper, CsSyncCust2house>
		implements CsSyncCust2houseService {

	/**
	 * 同步明源数据至中间表
	 */
	@Override
	public void syncMyCust2House() {
		baseMapper.insertMySync();
	}

	/**
	 * 同步物业数据至中间表
	 */
	@Override
	public void syncWyCust2House() {
		LocalDateTime syncDate = baseMapper.syncDate(1);
		baseMapper.insertWySync(syncDate);
	}

	@Override
	public void deleteByHouse() {
		baseMapper.deleteByHouse();
	}

}
