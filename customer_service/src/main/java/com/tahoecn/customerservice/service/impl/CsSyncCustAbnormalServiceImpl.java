package com.tahoecn.customerservice.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsCustInfoMapper;
import com.tahoecn.customerservice.mapper.CsSyncCustAbnormalMapper;
import com.tahoecn.customerservice.model.CsSyncCustAbnormal;
import com.tahoecn.customerservice.model.dto.CsSyncCustAbnormalDto;
import com.tahoecn.customerservice.service.CsSyncCustAbnormalService;

/**
 * <p>
 * 客户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-08
 */
@Service
public class CsSyncCustAbnormalServiceImpl extends ServiceImpl<CsSyncCustAbnormalMapper, CsSyncCustAbnormal>
		implements CsSyncCustAbnormalService {
	
	@Autowired
	CsCustInfoMapper csCustInfoMapper;

	@Override
	public List<CsSyncCustAbnormal> abnormal(String id, String certificateNum) {
		Wrapper<CsSyncCustAbnormal> wrapper = new EntityWrapper<CsSyncCustAbnormal>();
		wrapper.eq("certificate_num", certificateNum);
		wrapper.ne(StringUtils.isNotBlank(id),"id", id);
		wrapper.orderBy("abnormal", false);
		wrapper.orderBy("update_date");
		return baseMapper.selectList(wrapper);
	}

	@Override
	public void removeByCode(String certificateNum) {
		if(StringUtils.isBlank(certificateNum))
			return;
		Wrapper<CsSyncCustAbnormal> wrapper = new EntityWrapper<CsSyncCustAbnormal>();
		wrapper.eq("certificate_num", certificateNum);
		baseMapper.delete(wrapper);
		csCustInfoMapper.retainCust(certificateNum);
	}

	@Override
	public Page<CsSyncCustAbnormalDto> selectDtoList(Integer pageSize, Integer pageNum, CsSyncCustAbnormal abnormal) {
		Page<CsSyncCustAbnormalDto> page = new Page<>(pageNum, pageSize);
		page.setRecords(baseMapper.selectDtoList(page, abnormal));
		return page;
	}

}
