package com.tahoecn.customerservice.service.impl;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsSyncCustAbnormalMapper;
import com.tahoecn.customerservice.mapper.CsSyncCustMapper;
import com.tahoecn.customerservice.model.CsCustInfo;
import com.tahoecn.customerservice.model.CsSyncCust;
import com.tahoecn.customerservice.model.CsSyncCustAbnormal;
import com.tahoecn.customerservice.service.CsSyncCustService;

/**
 * <p>
 * 客户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
@Service
public class CsSyncCustServiceImpl extends ServiceImpl<CsSyncCustMapper, CsSyncCust> implements CsSyncCustService {
	
	@Autowired
	private CsSyncCustAbnormalMapper abnormalMapper;

	/**
	 * 同步明源数据至中间表
	 */
	@Override
	public void syncMyCust() {
		LocalDateTime syncDate = baseMapper.syncDate(2);
		baseMapper.insertMySync(syncDate);
	}

	/**
	 * 同步物业数据至中间表
	 */
	@Override
	public void syncWyCust() {
		LocalDateTime syncDate = baseMapper.syncDate(1);
		baseMapper.insertWySync(syncDate);
	}

	@Override
	public void retainCustById(String id) {
		baseMapper.retainCustById(id);
	}

	@Override
	public void insertFromCenter() {
		baseMapper.insertCenterToCust();
		baseMapper.delCust();
		baseMapper.delCenterByCust();
		baseMapper.insertCustToAbn();
		baseMapper.insertCenterToAbn();
		baseMapper.delCenter();
		abnormalMapper.delCustAbnormal();
	}

	@Override
	public void updateCust(CsCustInfo custInfo) {
		CsSyncCust csSyncCust = new CsSyncCust();
		csSyncCust.setCertificateNum(custInfo.getCertificateNum());
		csSyncCust = baseMapper.selectOne(csSyncCust);
		
		// 删除异常数据
		if(csSyncCust.getAbnormal() == 1) {
			Wrapper<CsSyncCustAbnormal> wrapper = new EntityWrapper<CsSyncCustAbnormal>();
			wrapper.eq("certificate_num", custInfo.getCertificateNum());
			abnormalMapper.delete(wrapper);
			csSyncCust.setAbnormal(0);
		}
		
		csSyncCust.setBelong(custInfo.getBelong());
		csSyncCust.setBirthday(custInfo.getBirthday());
		csSyncCust.setContactAddress(custInfo.getContactAddress());
		csSyncCust.setCertificateName(custInfo.getCertificateName());
		csSyncCust.setCustId(custInfo.getCustId());
		csSyncCust.setCustName(custInfo.getCustName());
		csSyncCust.setEmail(custInfo.getEmail());
		csSyncCust.setFax(custInfo.getFax());
		csSyncCust.setFixedTelephone(custInfo.getFixedTelephone());
		csSyncCust.setHobbies(custInfo.getHobbies());
		csSyncCust.setNational(custInfo.getNational());
		csSyncCust.setPostcode(custInfo.getPostcode());
		csSyncCust.setProfession(custInfo.getProfession());
		csSyncCust.setSex(custInfo.getSex());
		csSyncCust.setSource(custInfo.getSource());
		csSyncCust.setTelephone(custInfo.getTelephone());
		csSyncCust.setWorkUnit(custInfo.getWorkUnit());

		baseMapper.updateById(csSyncCust);
	}

}
