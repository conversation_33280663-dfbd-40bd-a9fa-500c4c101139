//package com.tahoecn.customerservice.service.impl;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import com.tahoecn.customerservice.mapper.CsSyncCust2houseMapper;
//import com.tahoecn.customerservice.mapper.CsSyncCustMapper;
//import com.tahoecn.customerservice.mapper.CsSyncHouseMapper;
//import com.tahoecn.customerservice.mapper.CsSyncMyCust2houseMapper;
//import com.tahoecn.customerservice.mapper.CsSyncMyCustMapper;
//import com.tahoecn.customerservice.mapper.CsSyncMyHouseMapper;
//import com.tahoecn.customerservice.mapper.CsSyncWyCust2houseMapper;
//import com.tahoecn.customerservice.mapper.CsSyncWyCustMapper;
//import com.tahoecn.customerservice.mapper.CsSyncWyHouseMapper;
//import com.tahoecn.customerservice.mappermy.SyncMyCust2houseMapper;
//import com.tahoecn.customerservice.mappermy.SyncMyCustMapper;
//import com.tahoecn.customerservice.mappermy.SyncMyHouseMapper;
//import com.tahoecn.customerservice.mapperwy.SyncWyCust2houseMapper;
//import com.tahoecn.customerservice.mapperwy.SyncWyCustMapper;
//import com.tahoecn.customerservice.mapperwy.SyncWyHouseMapper;
//import com.tahoecn.customerservice.model.CsSyncMyCust;
//import com.tahoecn.customerservice.model.CsSyncMyCust2house;
//import com.tahoecn.customerservice.model.CsSyncMyHouse;
//import com.tahoecn.customerservice.model.CsSyncWyCust;
//import com.tahoecn.customerservice.model.CsSyncWyCust2house;
//import com.tahoecn.customerservice.model.CsSyncWyHouse;
//import com.tahoecn.customerservice.service.CsSyncDataService;
//import com.tahoecn.log.Log;
//import com.tahoecn.log.LogFactory;
//
///**
// * <p>
// * 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-07-04
// */
//@Service
//public class CsSyncDataServiceImpl implements CsSyncDataService {
//	private static final Log log = LogFactory.get();
//
//	// 中间表
//	@Autowired
//	private CsSyncCustMapper csSyncCustMapper;
//	@Autowired
//	private CsSyncHouseMapper csSyncHouseMapper;
//	@Autowired
//	private CsSyncCust2houseMapper csSyncCust2houseMapper;
//
//	// 物业同步数据
//	@Autowired
//	private SyncWyCustMapper syncWyCustMapper;
//	@Autowired
//	private CsSyncWyCustMapper wyCustMapper;
//	@Autowired
//	private SyncWyHouseMapper syncWyHouseMapper;
//	@Autowired
//	private CsSyncWyHouseMapper wyHouseMapper;
//	@Autowired
//	private SyncWyCust2houseMapper syncWyCust2houseMapper;
//	@Autowired
//	private CsSyncWyCust2houseMapper wyCust2houseMapper;
//
//	// 明源同步数据
//	@Autowired
//	private SyncMyCustMapper syncMyCustMapper;
//	@Autowired
//	private CsSyncMyCustMapper myCustMapper;
//	@Autowired
//	private SyncMyHouseMapper syncMyHouseMapper;
//	@Autowired
//	private CsSyncMyHouseMapper myHouseMapper;
//	@Autowired
//	private SyncMyCust2houseMapper syncMyCust2houseMapper;
//	@Autowired
//	private CsSyncMyCust2houseMapper myCust2houseMapper;
//
//	@Override
//	public void syncMy() throws Exception {
//		try{
//			long time1 = System.currentTimeMillis();
//			log.info(">>>> 明源数据同步开始：{} ", LocalDateTime.now());
//
//			LocalDateTime syncDate = myCustMapper.syncDate();
//			syncMyCust(syncMyCustMapper.getList(syncDate));
//			csSyncCustMapper.insertMySync(syncDate);
//
//			long time2 = System.currentTimeMillis();
//			log.info(">>>> 明源数据同步业主信息结束：{} ，共计耗时：{}s ", LocalDateTime.now(), (time2 - time1) / 1000);
//
//			LocalDateTime syncDate1 = myHouseMapper.syncDate();
//			syncMyHouse(syncMyHouseMapper.getList(syncDate1));
//			csSyncHouseMapper.insertMySync(syncDate1);
//
//			long time3 = System.currentTimeMillis();
//			log.info(">>>> 明源数据同步房屋信息结束：{} ，共计耗时：{}s ", LocalDateTime.now(), (time3 - time2) / 1000);
//
////		csSyncCust2houseMapper.delMyActive();
//			syncMyCust2house(syncMyCust2houseMapper.getList(null));
//			csSyncCust2houseMapper.insertMySync();
//
//			long time4 = System.currentTimeMillis();
//			log.info(">>>> 明源数据同步关系信息结束：{} ，共计耗时：{}s ", LocalDateTime.now(), (time4 - time3) / 1000);
//			log.info(">>>> 明源数据同步结束：{} ", LocalDateTime.now());
//		} catch (Exception e) {
//			System.out.println(e.getMessage());
//		}
//	}
//
//	/**
//	 * 同步明源人员数据
//	 * 
//	 * @param pageSize
//	 * @throws Exception
//	 */
//	public void syncMyCust(List<CsSyncMyCust> custList) throws Exception {
//		List<CsSyncMyCust> list = new ArrayList<>();
//		for (CsSyncMyCust csSyncMyCust : custList) {
//			if (list.size() % 5000 == 0 && list.size() > 0) {
//				myCustMapper.insertSync(list);
//				list = new ArrayList<>();
//			}
//			list.add(csSyncMyCust);
//		}
//		if (list.size() > 0) {
//			myCustMapper.insertSync(list);
//		}
//	}
//
//	/**
//	 * 同步明源房间数据
//	 * 
//	 * @param pageSize
//	 * @throws Exception
//	 */
//	public void syncMyHouse(List<CsSyncMyHouse> HouseList) throws Exception {
//		List<CsSyncMyHouse> list = new ArrayList<>();
//		for (CsSyncMyHouse csSyncMyHouse : HouseList) {
//			if (list.size() % 5000 == 0 && list.size() > 0) {
//				myHouseMapper.insertSync(list);
//				list = new ArrayList<>();
//			}
//			list.add(csSyncMyHouse);
//		}
//		if (list.size() > 0) {
//			myHouseMapper.insertSync(list);
//		}
//	}
//
//	/**
//	 * 同步明源人房关系数据
//	 * 
//	 * @param pageSize
//	 * @throws Exception
//	 */
//	public void syncMyCust2house(List<CsSyncMyCust2house> Cust2houseList) throws Exception {
//		List<CsSyncMyCust2house> list = new ArrayList<>();
//		for (CsSyncMyCust2house csSyncMyCust2house : Cust2houseList) {
//			if (list.size() % 5000 == 0 && list.size() > 0) {
//				myCust2houseMapper.insertSync(list);
//				list = new ArrayList<>();
//			}
//			list.add(csSyncMyCust2house);
//		}
//		if (list.size() > 0) {
//			myCust2houseMapper.insertSync(list);
//		}
//	}
//
//	/**
//	 * @throws Exception
//	 * 
//	 */
//	@Override
//	public void syncWy() throws Exception {
//		long time1 = System.currentTimeMillis();
//		log.info(">>>> 物业数据同步开始：{} ", LocalDateTime.now());
//
//		LocalDateTime syncDate = wyCustMapper.syncDate();
//		syncWyCust(syncWyCustMapper.getList(syncDate));
//		csSyncCustMapper.insertWySync(syncDate);
//
//		long time2 = System.currentTimeMillis();
//		log.info(">>>> 物业数据同步业主信息结束：{} ，共计耗时：{}s ", LocalDateTime.now(), (time2 - time1) / 1000);
//
//		LocalDateTime syncDate1 = wyHouseMapper.syncDate();
//		syncWyHouse(syncWyHouseMapper.getList(syncDate1));
//		csSyncHouseMapper.insertWySync(syncDate1);
//
//		long time3 = System.currentTimeMillis();
//		log.info(">>>> 物业数据同步房屋信息结束：{} ，共计耗时：{}s ", LocalDateTime.now(), (time3 - time2) / 1000);
//
//		LocalDateTime syncDate2 = wyCust2houseMapper.syncDate();
//		syncWyCust2house(syncWyCust2houseMapper.getList(syncDate2));
//		csSyncCust2houseMapper.insertWySync(null);
////		csSyncCust2houseMapper.delWyActive();
//
//		long time4 = System.currentTimeMillis();
//		log.info(">>>> 物业数据同步关系信息结束：{} ，共计耗时：{}s ", LocalDateTime.now(), (time4 - time3) / 1000);
//		log.info(">>>> 物业数据同步结束：{} ", LocalDateTime.now());
//	}
//
//	/**
//	 * 同步物业人员数据
//	 * 
//	 * @param pageSize
//	 * @throws Exception
//	 */
//	private void syncWyCust(List<CsSyncWyCust> custList) throws Exception {
//		List<CsSyncWyCust> list = new ArrayList<>();
//		for (CsSyncWyCust csSyncWyCust : custList) {
//			if (list.size() % 5000 == 0 && list.size() > 0) {
//				wyCustMapper.insertSync(list);
//				list = new ArrayList<>();
//			}
//			list.add(csSyncWyCust);
//		}
//		if (list.size() > 0) {
//			wyCustMapper.insertSync(list);
//		}
//	}
//
//	/**
//	 * 同步物业房间数据
//	 * 
//	 * @param pageSize
//	 * @throws Exception
//	 */
//	private void syncWyHouse(List<CsSyncWyHouse> houseList) throws Exception {
//		List<CsSyncWyHouse> list = new ArrayList<>();
//		for (CsSyncWyHouse csSyncWyHouse : houseList) {
//			if (list.size() % 5000 == 0 && list.size() > 0) {
//				wyHouseMapper.insertSync(list);
//				list = new ArrayList<>();
//			}
//			list.add(csSyncWyHouse);
//		}
//		if (list.size() > 0) {
//			wyHouseMapper.insertSync(list);
//		}
//	}
//
//	/**
//	 * 同步物业人房关系数据
//	 * 
//	 * @param pageSize
//	 * @throws Exception
//	 */
//	private void syncWyCust2house(List<CsSyncWyCust2house> Cust2houseList) throws Exception {
//		List<CsSyncWyCust2house> list = new ArrayList<>();
//		for (CsSyncWyCust2house csSyncWyCust2house : Cust2houseList) {
//			if (list.size() % 5000 == 0 && list.size() > 0) {
//				wyCust2houseMapper.insertSync(list);
//				list = new ArrayList<>();
//			}
//			list.add(csSyncWyCust2house);
//		}
//		if (list.size() > 0) {
//			wyCust2houseMapper.insertSync(list);
//		}
//	}
//
//	@Override
//	public void updateSignDate() {
//		csSyncHouseMapper.updateSignDate();
//	}
//}
