package com.tahoecn.customerservice.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsHouseInfoMapper;
import com.tahoecn.customerservice.mapper.CsSyncHouseMapper;
import com.tahoecn.customerservice.model.CsHouseInfo;
import com.tahoecn.customerservice.model.CsSyncHouse;
import com.tahoecn.customerservice.model.excelDTO.DeliverDto;
import com.tahoecn.customerservice.service.CsSyncHouseService;

/**
 * <p>
 * 房屋信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-04
 */
@Service
public class CsSyncHouseServiceImpl extends ServiceImpl<CsSyncHouseMapper, CsSyncHouse> implements CsSyncHouseService {

	@Autowired
	CsHouseInfoMapper houseInfoMapper;

	/**
	 * 同步明源数据至中间表
	 */
	@Override
	public void syncMyHouse() {
		LocalDateTime syncDate = baseMapper.syncDate(2);
		baseMapper.insertMySync(syncDate);
	}

	/**
	 * 同步物业数据至中间表
	 */
	@Override
	public void syncWyHouse() {
		LocalDateTime syncDate = baseMapper.syncDate(1);
		baseMapper.insertWySync(syncDate);
	}

	@Override
	public Page<CsSyncHouse> selectDelivery(Integer current, Integer size, CsSyncHouse csSyncHouse) {
		Page<CsSyncHouse> page = new Page<>(current, size);
		page.setRecords(baseMapper.selectDelivery(page, csSyncHouse));
		return page;
	}

	@Override
	public void updateDelivery(String projectId, String building, Integer deliveryStatus) {
		String[] projectIds = projectId.split(",");
		String[] buildings = building.split(",");
		for (int i = 0; i < projectIds.length; i++) {
			if (StringUtils.isBlank(projectIds[i]) || StringUtils.isBlank(buildings[i])) {
				continue;
			}
			CsSyncHouse csSyncHouse = new CsSyncHouse();
			csSyncHouse.setDeliveryStatus(deliveryStatus);
			Wrapper<CsSyncHouse> wrapper = new EntityWrapper<>();
			wrapper.eq("project_id", projectIds[i]);
			wrapper.eq("building", buildings[i]);
			baseMapper.update(csSyncHouse, wrapper);

			// 变更状态 同时变更显示状态
			CsHouseInfo csHouseInfo = new CsHouseInfo();
			csHouseInfo.setDeliveryStatus(deliveryStatus);
			Wrapper<CsHouseInfo> infoWrapper = new EntityWrapper<>();
			infoWrapper.eq("project_id", projectIds[i]);
			infoWrapper.eq("building", buildings[i]);
			houseInfoMapper.update(csHouseInfo, infoWrapper);

		}

	}

	@Override
	public List<DeliverDto> expDeliver(CsSyncHouse csSyncHouse) {
		return baseMapper.expDeliver(csSyncHouse);
	}

	@Override
	public List<DeliverDto> expDeliver(String projectId, String building) {
		List<Map<String, String>> dtos = new ArrayList<>();
		String[] projectIds = projectId.split(",");
		String[] buildings = building.split(",");
		for (int i = 0; i < projectIds.length; i++) {
			if (StringUtils.isBlank(projectIds[i]) || StringUtils.isBlank(buildings[i])) {
				continue;
			}
			Map<String, String> map = new HashMap<String, String>();
			map.put("projectId", projectIds[i]);
			map.put("building", buildings[i]);
			dtos.add(map);
		}

		return baseMapper.expDeliverNew(dtos);
	}

}
