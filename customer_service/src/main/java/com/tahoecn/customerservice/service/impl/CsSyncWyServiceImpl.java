/**
 * 
 */
package com.tahoecn.customerservice.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tahoecn.customerservice.mapper.CsSyncWyCustMapper;
import com.tahoecn.customerservice.mapper.CsSyncWyHouseMapper;
import com.tahoecn.customerservice.service.CsSyncWyService;

/**
 * @ClassName CsSyncWyServiceImpl
 * <AUTHOR>
 * @date 2019年7月23日
 */
@Service
public class CsSyncWyServiceImpl implements CsSyncWyService {

	@Autowired
	private CsSyncWyCustMapper csSyncWyCustMapper;
	@Autowired
	private CsSyncWyHouseMapper csSyncWyHouseMapper;

	@Override
	public Boolean checkCust(String projectId, String custId) {
		return StringUtils.isNotBlank(csSyncWyCustMapper.convertCustId(projectId, custId));
	}

	@Override
	public Boolean checkHouse(String projectId, String houseId) {
		return StringUtils.isNotBlank(csSyncWyHouseMapper.convertRoomId(projectId, houseId));
	}

	@Override
	public String convertCustId(String projectId, String custId) {
		return csSyncWyCustMapper.convertCustId(projectId, custId);
	}

	@Override
	public String convertRoomId(String projectId, String houseId) {
		return csSyncWyHouseMapper.convertRoomId(projectId, houseId);
	}

}
