package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.CsUcOrgMapper;
import com.tahoecn.customerservice.model.CsUcOrg;
import com.tahoecn.customerservice.service.CsUcOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * UC组织机构信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsUcOrgServiceImpl extends ServiceImpl<CsUcOrgMapper, CsUcOrg> implements CsUcOrgService {

    @Autowired
    private CsUcOrgMapper csUcOrgMapper;

    @Override
    public void synOrgInfo(CsUcOrg orgInfo) {
        CsUcOrg orgInfoSelect = new CsUcOrg();
        orgInfoSelect.setFdSid(orgInfo.getFdSid());
        CsUcOrg orgInfoOne = csUcOrgMapper.selectOne(orgInfoSelect);
        if (orgInfoOne != null) {
            orgInfo.setId(orgInfoOne.getId());
            orgInfo.setCreateDate(orgInfoOne.getCreateDate());
            csUcOrgMapper.updateById(orgInfo);
        } else {
            csUcOrgMapper.insert(orgInfo);
        }
    }
}
