package com.tahoecn.customerservice.service.impl;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.core.util.StrUtil;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.mapper.CsUcUserMapper;
import com.tahoecn.customerservice.model.CsCustomerExtension;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.UserPackage;
import com.tahoecn.customerservice.model.dto.CsUserRoleDto;
import com.tahoecn.customerservice.model.dto.MenuDO;
import com.tahoecn.customerservice.model.dto.Tree;
import com.tahoecn.customerservice.model.vo.UserVo;
import com.tahoecn.customerservice.service.CsCustomerExtensionService;
import com.tahoecn.customerservice.service.CsMenuCfgService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.RolePrivInfoService;

/**
 * <p>
 * UC用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsUcUserServiceImpl extends ServiceImpl<CsUcUserMapper, CsUcUser> implements CsUcUserService {
    @Autowired
    private CsUcUserMapper csUcUserMapper;
    @Autowired
    private CsMenuCfgService csMenuCfgService;

    @Autowired
    private CsCustomerExtensionService csCustomerExtensionService;
    
    @Autowired
	RolePrivInfoService rolePrivInfoService;
	@Autowired
	CsUcUserService csUcUserService;
	@Autowired
	RedisTemplate redisTemplate;
	
    @Override
    public void synUserInfo(CsUcUser userInfo) {
        CsUcUser userInfoSelect = new CsUcUser();
        userInfoSelect.setFdSid(userInfo.getFdSid());
        CsUcUser userInfoOne = csUcUserMapper.selectOne(userInfoSelect);
        if (userInfoOne != null) {
            userInfo.setId(userInfoOne.getId());
            userInfo.setCreateTime(userInfoOne.getCreateTime());
            csUcUserMapper.updateById(userInfo);
        } else {
            csUcUserMapper.insert(userInfo);
        }
    }

    @Override
    public CsUcUser selectByUsername(String userName) {
        CsUcUser csUcUser = new CsUcUser();
        csUcUser.setFdUsername(userName);
        return csUcUserMapper.selectOne(csUcUser);
    }

    @Override
    public CsUcUser selectByUsername() {
        String userName =ThreadLocalUtils.getUserName();
//        String userName = "wanghongxin";
        if (StringUtils.isNotBlank(userName)) {
            return selectByUsername(userName);
        }
        return null;
    }

    @Override
    public List<CsUserRoleDto> selectRoleByUserId(String userId) {
        return csUcUserMapper.selectRoleByUserId(userId);
    }

    @Override
    public UserPackage getUserPackage() {
        UserPackage userPackage = new UserPackage();
        CsUcUser csUcUser = selectByUsername();
        if (csUcUser == null) {
            return null;
        }
        userPackage.setSid(csUcUser.getFdSid());
        userPackage.setName(csUcUser.getFdName());
        userPackage.setUsername(csUcUser.getFdUsername());
        userPackage.setOrgId(csUcUser.getFdOrgId());
        userPackage.setOrgName(csUcUser.getFdOrgName());
        userPackage.setOrgIdTree(csUcUser.getFdOrgIdTree());
        userPackage.setOrgNameTree(csUcUser.getFdOrgNameTree());
        List<Tree<MenuDO>> trees = csMenuCfgService.listMenuTreeByUser();
        userPackage.setMenuTree(trees);
        CsCustomerExtension csCustomerExtension = csCustomerExtensionService.getLogin();
        if (csCustomerExtension != null&&csCustomerExtension.getFlag()!=null) {
            if(csCustomerExtension.getFlag()!=2){//临时调整：过滤房修录入员
                userPackage.setSeats(csCustomerExtension);
            }
            userPackage.setFlag(csCustomerExtension.getFlag());
            
            if("1".equals(csCustomerExtension.getFlag()) || 1 == csCustomerExtension.getFlag()) {
            	userPackage.setIsSiBaiSeats(1);
            }
        }
        
        //获取是否是管理员  0:不是  1：是
//        Integer isAdmin = rolePrivInfoService.isAdmin();
        Integer isAdmin = 1;
        userPackage.setIsAdmin(isAdmin);
        return userPackage;
    }

    @Override
    public ResponseMessage findUserByOrgId(String orgId) {
        List<UserVo> list =  csUcUserMapper.findUserByOrgId(orgId) ;
        if(list  != null && list.size() > 0){
            return ResponseMessage.ok(list);
        }
        return ResponseMessage.error("用户为空");
    }

	@Override
	public Page<CsUcUser> userForm(Map<String, Object> map, Page<CsUcUser> page) {
		page.setRecords(baseMapper.userForm(map, page));
		return page;
	}

    /**
     * 查询用户信息
     * @param username  用户名或用户帐号
     * @return
     */
    @Override
    public ResponseMessage findUsersByNameOrCode(String username) {
        String usercode = "";
        if(username != null && !"".equals(username)){
            usercode = username;
            return ResponseMessage.ok(csUcUserMapper.findUsersByUsername(username, usercode));
        }else{
            username = "";
            return ResponseMessage.ok("");
        }
    }

	@Override
	public void userLogin(String account, String password,HttpServletRequest request) {
		if (StrUtil.isBlank(password)||!password.equals("123456")) {
			throw new RuntimeException("密码不正确！");
		}
		String loginName = account;
		if (StringUtils.isNotBlank(loginName)) {
			CsUcUser csUcUser = csUcUserService.selectByUsername(loginName);
			if(csUcUser == null) {
				throw new RuntimeException("账号不存在！");
			}
			redisTemplate.opsForValue().set(loginName, csUcUser, 1, TimeUnit.DAYS);
			//将用户权限对象存储到session
			request.getSession().setAttribute("user", loginName);
			ThreadLocalUtils.setUser(csUcUser);
		}
	}
}
