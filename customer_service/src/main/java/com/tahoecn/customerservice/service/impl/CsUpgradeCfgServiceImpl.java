package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.mapper.CsUpgradeCfgMapper;
import com.tahoecn.customerservice.model.CsUpgradeCfg;
import com.tahoecn.customerservice.model.CsUpgradeCfgBak;
import com.tahoecn.customerservice.model.vo.UpgradeCfgSortVo;
import com.tahoecn.customerservice.service.CsUpgradeCfgService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 升级配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service
public class CsUpgradeCfgServiceImpl extends ServiceImpl<CsUpgradeCfgMapper, CsUpgradeCfg> implements CsUpgradeCfgService {
    private static final Log log = LogFactory.get();

    @Autowired
    private CsUpgradeCfgMapper csUpgradeCfgMapper;

    @Override
    public ResponseMessage selectTreeUpgradeCfg() {

        List<UpgradeCfgSortVo> firstSortList = csUpgradeCfgMapper.selectFirstSort();
        if (firstSortList != null && firstSortList.size() > 0) {
            //一级格式
            List<Map<String, Object>> firstStyle = new ArrayList<>();
            for (UpgradeCfgSortVo ucsv : firstSortList) {
                Map<String, Object> firstSortMap = new HashedMap();
                putMap(ucsv, firstSortMap);
                List<UpgradeCfgSortVo> secSortList = csUpgradeCfgMapper.selectSecSort(ucsv.getSortCode());
                if (secSortList != null && secSortList.size() > 0) {
                    //二级格式
                    List<Map<String, Object>> secStyle = new ArrayList<>();
                    for (UpgradeCfgSortVo secCfg : secSortList) {
                        Map<String, Object> secSortMap = new HashedMap();
                        putMap(secCfg, secSortMap);
                        List<UpgradeCfgSortVo> thirdSortList = csUpgradeCfgMapper.selectThirdSort(ucsv.getSortCode(), secCfg.getSortCode());
                        if (thirdSortList != null && thirdSortList.size() > 0) {
                            //三级格式
                            List<Map<String, Object>> thirdStyle = new ArrayList<>();
                            for (UpgradeCfgSortVo thirdCfg : thirdSortList) {
                                Map<String, Object> thirdSortMap = new HashedMap();
                                putMap(thirdCfg, thirdSortMap);
                                List<UpgradeCfgSortVo> fourthSortList = csUpgradeCfgMapper.selectFourthSort(ucsv.getSortCode(), secCfg.getSortCode(), thirdCfg.getSortCode());
                                if (fourthSortList != null && fourthSortList.size() > 0) {
                                    thirdSortMap.put("sc", fourthSortList);
                                }
                                thirdStyle.add(thirdSortMap);
                            }
                            secSortMap.put("sc", thirdStyle);
                        }
                        secStyle.add(secSortMap);
                    }
                    firstSortMap.put("sc", secStyle);
                }
                firstStyle.add(firstSortMap);
            }
            return ResponseMessage.ok(firstStyle);
        }
        return null;
    }

    private void putMap(UpgradeCfgSortVo ucsv, Map<String, Object> firstSortMap) {
        firstSortMap.put("sortCode",ucsv.getSortCode());
        firstSortMap.put("sortName",ucsv.getSortName());
        firstSortMap.put("levelName",ucsv.getLevelName());
        firstSortMap.put("cityDays",ucsv.getCityDays());
        firstSortMap.put("regionDays",ucsv.getRegionDays());
        firstSortMap.put("groupDasy",ucsv.getGroupDasy());
    }


    /**
     * <AUTHOR>
     * 报事升级编辑提交
     * @Date 16:05 2018/12/24
     * @Param
     * @return
     **/
    @Override
    public ResponseMessage saveUpgradeCfg(Map<String, Object> pamam) {
        //获取报事升级的分类
        String type = pamam.get("type").toString();
        String regionDays = pamam.get("regionDays").toString();
        String groupDasy = pamam.get("groupDasy").toString();
        String code = pamam.get("code").toString();
        List<CsUpgradeCfgBak> csUpgradeCfgBaks = csUpgradeCfgMapper.selectFirstSortByCode(type,code);
        if (csUpgradeCfgBaks == null || csUpgradeCfgBaks.size() == 0){
            return ResponseMessage.error("未查询到数据");
        }
        CsUpgradeCfgBak csUpgradeCfgBak1 = csUpgradeCfgBaks.get(0);
        boolean coBx = csUpgradeCfgBak1.getFirstSortCode().equals("coBX");
        if (coBx){
            String cityDays = pamam.get("cityDays").toString();
            log.info("------------>>>>>>>>>  报事升级修改开始 <<<<<<<<<------------"+new Date());
            for (CsUpgradeCfgBak csUpgradeCfgBak : csUpgradeCfgBaks){
                if (cityDays == null || cityDays.equals("")){
                    csUpgradeCfgBak.setCityDays(null);
                }else {
                    csUpgradeCfgBak.setCityDays(Long.parseLong(cityDays));
                }
                if (regionDays == null || "".equals(regionDays)){
                    csUpgradeCfgBak.setRegionDays(null);
                }else {
                    csUpgradeCfgBak.setRegionDays(Long.parseLong(regionDays));
                }
                if (groupDasy == null || "".equals(groupDasy)){
                    csUpgradeCfgBak.setGroupDasy(null);
                }else {
                    csUpgradeCfgBak.setGroupDasy(Long.parseLong(groupDasy));
                }
                //修改当前级别下所有的时间
                csUpgradeCfgMapper.updateByIdBak(csUpgradeCfgBak);
            }
            log.info("------------>>>>>>>>>  报事升级修改结束 <<<<<<<<<------------"+new Date());
        }else {
            log.info("------------>>>>>>>>>  报事升级修改开始 <<<<<<<<<------------"+new Date());
            for (CsUpgradeCfgBak csUpgradeCfgBak : csUpgradeCfgBaks){
                if (regionDays == null || "".equals(regionDays)){
                    csUpgradeCfgBak.setRegionDays(null);
                }else {
                    csUpgradeCfgBak.setRegionDays(Long.parseLong(regionDays));
                }
                if (groupDasy == null || "".equals(groupDasy)){
                    csUpgradeCfgBak.setGroupDasy(null);
                }else {
                    csUpgradeCfgBak.setGroupDasy(Long.parseLong(groupDasy));
                }
                //修改当前级别下所有的时间
                csUpgradeCfgMapper.updateByIdBak(csUpgradeCfgBak);
            }
            log.info("------------>>>>>>>>>  报事升级修改结束 <<<<<<<<<------------"+new Date());
        }
        return ResponseMessage.okm("编辑成功");
    }


    /**
     * <AUTHOR>
     * 报事升级编辑回显
     * @Date 17:39 2018/12/24
     * @Param
     * @return
     **/
    @Override
    public ResponseMessage selectOneUpgradeCfg(String type, String code) {
        List<CsUpgradeCfgBak> csUpgradeCfgBaks = csUpgradeCfgMapper.selectFirstSortByCode(type,code);
        if (csUpgradeCfgBaks == null || csUpgradeCfgBaks.size() == 0){
            return ResponseMessage.error("类型数据为空,请输入报修和投诉相关的code");
        }
        Map<String, Object> pamam = new HashMap<>(16);
        for (CsUpgradeCfgBak csUpgradeCfgBak : csUpgradeCfgBaks){
            if ("1".equals(type)) {
                if (csUpgradeCfgBak.getFirstSortCode() != null) {
                    pamam.put("codeName",csUpgradeCfgBak.getFirstSortName());
                    pamam.put("level","一级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays",csUpgradeCfgBak.getCityDays());
                    }
                    pamam.put("regionDays",csUpgradeCfgBak.getRegionDays());
                    pamam.put("groupDasy",csUpgradeCfgBak.getGroupDasy());
                }else {
                    pamam.put("codeName",csUpgradeCfgBak.getFirstSortName());
                    pamam.put("level","一级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays","");
                    }
                    pamam.put("regionDays","");
                    pamam.put("groupDasy","");
                }
            }else
            if ("2".equals(type)){
                if (csUpgradeCfgBak.getFirstSortCode() != null && csUpgradeCfgBak.getSecSortCode() != null ) {
                    pamam.put("codeName",csUpgradeCfgBak.getFirstSortName()+"-"+csUpgradeCfgBak.getSecSortName());
                    pamam.put("level","二级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")) {
                        pamam.put("cityDays", csUpgradeCfgBak.getCityDays());
                    }
                    pamam.put("regionDays",csUpgradeCfgBak.getRegionDays());
                    pamam.put("groupDasy",csUpgradeCfgBak.getGroupDasy());
                }else {
                    pamam.put("codeName",csUpgradeCfgBak.getFirstSortName()+"-"+csUpgradeCfgBak.getSecSortName());
                    pamam.put("level","二级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays","");
                    }
                    pamam.put("regionDays","");
                    pamam.put("groupDasy","");
                }
            }else if ("3".equals(type)){
                if (csUpgradeCfgBak.getFirstSortCode() != null && csUpgradeCfgBak.getSecSortCode() != null && csUpgradeCfgBak.getThirdSortCode() != null ) {
                    pamam.put("codeName", csUpgradeCfgBak.getFirstSortName() + "-" + csUpgradeCfgBak.getSecSortName() + "-" + csUpgradeCfgBak.getThirdSortName());
                    pamam.put("level", "三级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays",csUpgradeCfgBak.getCityDays());
                    }
                    pamam.put("regionDays",csUpgradeCfgBak.getRegionDays());
                    pamam.put("groupDasy",csUpgradeCfgBak.getGroupDasy());
                } else {
                    pamam.put("codeName", csUpgradeCfgBak.getFirstSortName() + "-" + csUpgradeCfgBak.getSecSortName() + "-" + csUpgradeCfgBak.getThirdSortName());
                    pamam.put("level", "三级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays","");
                    }
                    pamam.put("regionDays","");
                    pamam.put("groupDasy","");
                }
            }else if ("4".equals(type)){
                if (csUpgradeCfgBak.getFirstSortCode() != null && csUpgradeCfgBak.getSecSortCode() != null && csUpgradeCfgBak.getThirdSortCode() != null && csUpgradeCfgBak.getFourthSortCode() != null) {
                    pamam.put("codeName", csUpgradeCfgBak.getFirstSortName() + "-" + csUpgradeCfgBak.getSecSortName() + "-" + csUpgradeCfgBak.getThirdSortName() + "-" + csUpgradeCfgBak.getFourthSortName());
                    pamam.put("level", "四级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays",csUpgradeCfgBak.getCityDays());
                    }
                    pamam.put("regionDays",csUpgradeCfgBak.getRegionDays());
                    pamam.put("groupDasy",csUpgradeCfgBak.getGroupDasy());
                }else{
                    pamam.put("codeName", csUpgradeCfgBak.getFirstSortName() + "-" + csUpgradeCfgBak.getSecSortName() + "-" + csUpgradeCfgBak.getThirdSortName() + "-" + csUpgradeCfgBak.getFourthSortName());
                    pamam.put("level", "四级");
                    if (csUpgradeCfgBak.getFirstSortCode().equals("coBX")){
                        pamam.put("cityDays","");
                    }
                    pamam.put("regionDays","");
                    pamam.put("groupDasy","");
                }
            }
        }
        return ResponseMessage.ok(pamam);
    }
    
    /**
     * 手机APP-查询工单剩余处理天数
     */
    public CsUpgradeCfgBak selDayByFormId(String formId){
    	return csUpgradeCfgMapper.selDayByFormId(formId);
    }
}
