package com.tahoecn.customerservice.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.common.web.ResponseMessage;
import com.tahoecn.customerservice.mapper.CsProjectInfoMapper;
import com.tahoecn.customerservice.mapper.CsUcOrgMapper;
import com.tahoecn.customerservice.mapper.CsUserRoleMapper;
import com.tahoecn.customerservice.model.CsProjectInfo;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.model.CsUserRole;
import com.tahoecn.customerservice.model.dto.BuildExtTree;
import com.tahoecn.customerservice.model.dto.ChargerDo;
import com.tahoecn.customerservice.model.dto.ChargerExtDo;
import com.tahoecn.customerservice.model.dto.ProjectRoleAttrDO;
import com.tahoecn.customerservice.model.dto.ProjectRoleDO;
import com.tahoecn.customerservice.model.dto.TreeExt;
import com.tahoecn.customerservice.model.dto.UserRoleDictDto;
import com.tahoecn.customerservice.model.vo.OrgInfoVo;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.CsUserRoleService;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-16
 */
@Transactional
@Service
public class CsUserRoleServiceImpl extends ServiceImpl<CsUserRoleMapper, CsUserRole> implements CsUserRoleService {

    private static final Log log = LogFactory.get();

    @Autowired
    private CsUserRoleMapper csUserRoleMapper;
    @Autowired
    private CsProjectInfoMapper csProjectInfoMapper;

    @Autowired
    private CsUcOrgMapper csUcOrgMapper;

    @Autowired
    private CsUcUserService csUcUserService;

    @Override
    public List<TreeExt<ProjectRoleDO>> getTreeList() {
        List<TreeExt<ProjectRoleDO>> treeList = new ArrayList<>();
        List<ProjectRoleDO> projectRoleDOList = getUserRoleProject();
        if (projectRoleDOList != null && projectRoleDOList.size() > 0) {
            for (ProjectRoleDO projectRoleDO : projectRoleDOList) {
                TreeExt<ProjectRoleDO> prTree = new TreeExt<ProjectRoleDO>();
                prTree.setId(projectRoleDO.getMenuCode());
                prTree.setParentId(projectRoleDO.getParentCode());
                prTree.setText(projectRoleDO.getMenuName());
                prTree.setType(projectRoleDO.getType());
                prTree.setTypeName(projectRoleDO.getTypeName());
                prTree.setTreePath(projectRoleDO.getTreePath());
                if (projectRoleDO.getProjectRoleAttrDO() != null) {
                    ProjectRoleAttrDO projectRoleAttrDO = projectRoleDO.getProjectRoleAttrDO();
                    if (projectRoleAttrDO.getServiceLeaders() != null && projectRoleAttrDO.getServiceLeaders().size() > 0) {
                        StringBuffer serviceLeaders = new StringBuffer();
                        for (ChargerDo cd : projectRoleAttrDO.getServiceLeaders()) {
                            serviceLeaders.append(cd.getUserName()).append(",");
                        }
                        if (StringUtils.isNotBlank(serviceLeaders)) {
                            serviceLeaders.deleteCharAt(serviceLeaders.length() - 1);
                        }
                        prTree.setServiceLeaders(serviceLeaders.toString());
                    }
                    if (projectRoleAttrDO.getServicesWorks() != null && projectRoleAttrDO.getServicesWorks().size() > 0) {
                        StringBuffer servicesWorks = new StringBuffer();
                        for (ChargerDo cd : projectRoleAttrDO.getServicesWorks()) {
                            servicesWorks.append(cd.getUserName()).append(",");
                        }
                        if (StringUtils.isNotBlank(servicesWorks)) {
                            servicesWorks.deleteCharAt(servicesWorks.length() - 1);
                        }
                        prTree.setServicesWorks(servicesWorks.toString());
                    }
                    if (projectRoleAttrDO.getRepairsLeaders() != null && projectRoleAttrDO.getRepairsLeaders().size() > 0) {
                        StringBuffer repairsLeaders = new StringBuffer();
                        for (ChargerDo cd : projectRoleAttrDO.getRepairsLeaders()) {
                            repairsLeaders.append(cd.getUserName()).append(",");
                        }
                        if (StringUtils.isNotBlank(repairsLeaders)) {
                            repairsLeaders.deleteCharAt(repairsLeaders.length() - 1);
                        }
                        prTree.setRepairsLeaders(repairsLeaders.toString());
                    }
                    if (projectRoleAttrDO.getRepairsWorks() != null && projectRoleAttrDO.getRepairsWorks().size() > 0) {
                        StringBuffer repairsWorks = new StringBuffer();
                        for (ChargerDo cd : projectRoleAttrDO.getRepairsWorks()) {
                            repairsWorks.append(cd.getUserName()).append(",");
                        }
                        if (StringUtils.isNotBlank(repairsWorks)) {
                            repairsWorks.deleteCharAt(repairsWorks.length() - 1);
                        }
                        prTree.setRepairsWorks(repairsWorks.toString());
                    }
                    if (projectRoleAttrDO.getFixInputUsers() != null && projectRoleAttrDO.getFixInputUsers().size() > 0) {
                        StringBuffer fixInputUsers = new StringBuffer();
                        for (ChargerDo cd : projectRoleAttrDO.getFixInputUsers()) {
                            fixInputUsers.append(cd.getUserName()).append(",");
                        }
                        if (StringUtils.isNotBlank(fixInputUsers)) {
                            fixInputUsers.deleteCharAt(fixInputUsers.length() - 1);
                        }
                        prTree.setFixInputUsers(fixInputUsers.toString());
                    }
                }
                treeList.add(prTree);
            }
        }
        List<TreeExt<ProjectRoleDO>> list = BuildExtTree.buildList(treeList, "-1");
        return list;
    }

    public List<ProjectRoleDO> getUserRoleProject() {
        Map<String, String> cacheOrgIdMap = new HashMap<>();
        List<ProjectRoleDO> projectRoleDOList = new ArrayList<>();
        ProjectRoleDO firstNode = new ProjectRoleDO();
        firstNode.setMenuCode("0");
        firstNode.setMenuName("泰禾集团");
        firstNode.setOrderNum(0);
        firstNode.setParentCode("-1");
        firstNode.setType("1");
        firstNode.setTypeName("泰禾集团");
        firstNode.setTreePath(firstNode.getTypeName());
        firstNode.setProjectRoleAttrDO(getByType(firstNode.getType(), firstNode.getTypeName(),cacheOrgIdMap));
        projectRoleDOList.add(firstNode);
        List<String> regionList = csProjectInfoMapper.groupRegion();
        if (regionList != null && regionList.size() > 0) {
            int i = 1;
            int j = i + 1000;
            int k = j + 100000;
            for (String region : regionList) {
                ProjectRoleDO regionNode = new ProjectRoleDO();
                regionNode.setMenuCode(i + "");
                regionNode.setMenuName(region);
                regionNode.setOrderNum(i);
                regionNode.setParentCode("0");
                regionNode.setType("2");
                regionNode.setTypeName(region);
                regionNode.setTreePath(firstNode.getTreePath() + "-" + regionNode.getTypeName());
                regionNode.setProjectRoleAttrDO(getByType(regionNode.getType(), regionNode.getTypeName(),cacheOrgIdMap));
                List<String> cityList = csProjectInfoMapper.groupCity(region);
                if (cityList != null && cityList.size() > 0) {
                    for (String city : cityList) {
                        ProjectRoleDO cityNode = new ProjectRoleDO();
                        cityNode.setMenuCode(j + "");
                        cityNode.setMenuName(city);
                        cityNode.setOrderNum(j);
                        cityNode.setParentCode(i + "");
                        cityNode.setType("3");
                        cityNode.setTypeName(city);
                        cityNode.setTreePath(regionNode.getTreePath() + "-" + cityNode.getTypeName());
                        cityNode.setProjectRoleAttrDO(getByType(cityNode.getType(), cityNode.getTypeName(),cacheOrgIdMap));
                        CsProjectInfo csProjectInfo = new CsProjectInfo();
                        csProjectInfo.setCityCompany(city);
                        List<CsProjectInfo> csProjectInfoList = csProjectInfoMapper.selectProjectInfoRelation(csProjectInfo);
                        if (csProjectInfoList != null && csProjectInfoList.size() > 0) {
                            for (CsProjectInfo cpi : csProjectInfoList) {
                                ProjectRoleDO projectNode = new ProjectRoleDO();
                                projectNode.setMenuCode(k + "");
                                projectNode.setMenuName(cpi.getProject());
                                projectNode.setOrderNum(k);
                                projectNode.setParentCode(j + "");
                                projectNode.setType("4");
                                projectNode.setTypeName(cpi.getProjectCode());
                                projectNode.setTreePath(cityNode.getTreePath() + "-" + cpi.getProject());
                                projectNode.setProjectRoleAttrDO(getByType(projectNode.getType(), projectNode.getTypeName(),cacheOrgIdMap));
                                projectRoleDOList.add(projectNode);
                                k++;
                            }
                        }
                        projectRoleDOList.add(cityNode);
                        j++;
                    }
                }
                projectRoleDOList.add(regionNode);
                i++;
            }
        }

        return projectRoleDOList;
    }

    @Override
    public ProjectRoleAttrDO getByType(String type, String typeName,Map<String, String> cacheOrgIdMap) {

        ProjectRoleAttrDO projectRoleAttrDO = new ProjectRoleAttrDO();
        List<ChargerDo> serviceLeaders = new ArrayList<>();
        List<ChargerDo> servicesWorks = new ArrayList<>();
        List<ChargerDo> repairsLeaders = new ArrayList<>();
        List<ChargerDo> repairsWorks = new ArrayList<>();
        List<ChargerDo> fixInputUsers = new ArrayList<>();
        projectRoleAttrDO.setServiceLeaders(serviceLeaders);
        projectRoleAttrDO.setServicesWorks(servicesWorks);
        projectRoleAttrDO.setRepairsLeaders(repairsLeaders);
        projectRoleAttrDO.setRepairsWorks(repairsWorks);
        projectRoleAttrDO.setFixInputUsers(fixInputUsers);
        if ("1".equals(type)) {
            List<ChargerExtDo> chargerExtDoList = csUserRoleMapper.selectType1();
            if (chargerExtDoList != null && chargerExtDoList.size() > 0) {
                for (ChargerExtDo exd : chargerExtDoList) {
                    ChargerDo cd = new ChargerDo();
                    BeanUtils.copyProperties(exd, cd);
                    setOrgId(cd, cacheOrgIdMap);
                    if ("sComplaint-upgrade-3".equals(exd.getRoleCode())) {
                        serviceLeaders.add(cd);
                    } else if ("sRepair-upgrade-3".equals(exd.getRoleCode())) {
                        repairsLeaders.add(cd);
                    }
                }
            }
        }
        if ("2".equals(type)) {
            List<ChargerExtDo> chargerExtDoList = csUserRoleMapper.selectType2(typeName);
            if (chargerExtDoList != null && chargerExtDoList.size() > 0) {
                for (ChargerExtDo exd : chargerExtDoList) {
                    ChargerDo cd = new ChargerDo();
                    BeanUtils.copyProperties(exd, cd);
                    setOrgId(cd, cacheOrgIdMap);
                    if ("sComplaint-upgrade-2".equals(exd.getRoleCode())) {
                        serviceLeaders.add(cd);
                    } else if ("sRepair-upgrade-2".equals(exd.getRoleCode())) {
                        repairsLeaders.add(cd);
                    }
                }
            }
        }
        if ("3".equals(type)) {
            List<ChargerExtDo> chargerExtDoList = csUserRoleMapper.selectType3(typeName);
            if (chargerExtDoList != null && chargerExtDoList.size() > 0) {
                for (ChargerExtDo exd : chargerExtDoList) {
                    ChargerDo cd = new ChargerDo();
                    BeanUtils.copyProperties(exd, cd);
                    setOrgId(cd, cacheOrgIdMap);
                    if ("sComplaint-upgrade-1".equals(exd.getRoleCode())) {
                        serviceLeaders.add(cd);
                    } else if ("sRepair-upgrade-1".equals(exd.getRoleCode())) {
                        repairsLeaders.add(cd);
                    }
                }
            }
        }
        if ("4".equals(type)) {
            List<ChargerExtDo> chargerExtDoList = csUserRoleMapper.selectType4(typeName);
            if (chargerExtDoList != null && chargerExtDoList.size() > 0) {
                for (ChargerExtDo exd : chargerExtDoList) {
                    ChargerDo cd = new ChargerDo();
                    BeanUtils.copyProperties(exd, cd);
                    setOrgId(cd, cacheOrgIdMap);
                    if ("sComplaint-toBeAssigned".equals(exd.getRoleCode())) {
                        //投诉待分派
                        serviceLeaders.add(cd);
                    } else if ("sComplaint-handle".equals(exd.getRoleCode())) {
                        //投诉待处理
                        servicesWorks.add(cd);
                    } else if ("sRepair-toBeAssigned".equals(exd.getRoleCode())) {
                        //报修待分派
                        repairsLeaders.add(cd);
                    } else if ("sRepair-handle".equals(exd.getRoleCode())) {
                        //报修待处理
                        repairsWorks.add(cd);
                    } else if ("fangxiu-entry-agent".equals(exd.getRoleCode())) {
                        //房修录入员
                        fixInputUsers.add(cd);
                    }
                }
            }
        }
        return projectRoleAttrDO;
    }

    private void setOrgId(ChargerDo cd, Map<String, String> cacheOrgIdMap) {
        if (cacheOrgIdMap == null) {
            cacheOrgIdMap = new HashMap<>();
        }
        String orgId = cacheOrgIdMap.get(cd.getUserId());
        if (StringUtils.isNotBlank(orgId)) {
            cd.setOrgId(orgId);
            return;
        }
        CsUcUser csUcUser = csUcUserService.selectByUsername(cd.getUserId());
        cd.setOrgId(csUcUser.getFdSid());
        cacheOrgIdMap.put(cd.getUserId(), csUcUser.getFdSid());
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 客服人员编辑修改
     * @Date 22:24 2018/12/17
     * @Param
     **/
    @Override
    public ResponseMessage updateUserRole(Map<String, Object> pamam) {
        /***
         *  参数说明 :
         *   type  : 区分类型
         *      1.泰禾集团
         *      2.区域
         *      3.城市
         *      4.项目
         *    jtCustomerUserId  : 集团客服负责人
         *    jtFangxiuUserId  :  集团房修负责人
         *    regionCode  : 区域编码
         *    qyCustomerUserId : 区域客服负责人
         *    qyFangxiuUserId :  区域房修负责人
         *    cityCode : 获取城市编码
         *    csCustomerUserId : 城市客服负责人
         *    csFangxiuUserId : 城市房修负责人
         *    projectCode : 项目编码
         *    xmCustomerUserId  : 项目客服负责人
         *    xmProcessingCustomerUserId : 项目客服处理人
         *    xmProcessingFangxiuLRYUserId : 项目房修录入员
         *    xmFangxiuUserId : 项目房修负责人
         *    xmProcessingFangxiuUserId : 项目房修处理人
         */
        //获取前台数据
        //获取类型 1: 集团, 2: 区域, 3: 城市, 4: 项目
        String type = (String) pamam.get("type");
        if (type == null || type.equals("")) {
            ResponseMessage.error("类型为空,请传递参数");
        }
        //集团
        if ("1".equals(type)) {
            //集团
            //集团客服负责人
            String jtCustomerUserIdf = pamam.get("jtCustomerUserId").toString();
            //集团房修负责人
            String jtFangxiuUserIdf = pamam.get("jtFangxiuUserId").toString();
            //查询库里已有数据集团客服负责人的信息
            List<CsUserRole> csUserRoles = csUserRoleMapper.selcetJiTuan();
            if (csUserRoles == null || csUserRoles.size() == 0) {
                List<CsUserRole> csUserRolesS = csUserRoleMapper.selectJiTuanProjectCode();
                for (CsUserRole csUserRolesSs : csUserRolesS) {
                    //添加集团客服负责人
                    if (jtCustomerUserIdf == null || jtCustomerUserIdf.equals("")){
                        //return ResponseMessage.error("请填写参数 : jtCustomerUserId");
                    }else {
                        String[] jtCustomerUserIds = pamam.get("jtCustomerUserId").toString().split(",");
                        for (String jtCustomerUserId : jtCustomerUserIds) {
                            //查询当前集团客服负责人的相关详细
                            CsUcUser csUcUserKeFu = csUserRoleMapper.selectCUU(jtCustomerUserId);
                            //客服负责人
                            CsUserRole csUserRolem = new CsUserRole();
                            csUserRolem.setRemarks("集团");
                            csUserRolem.setRoleCode("sComplaint-upgrade-3");
                            csUserRolem.setRoleName("投诉-升级-集团");
                            csUserRolem.setUserId(csUcUserKeFu.getFdUsername());
                            csUserRolem.setUserName(csUcUserKeFu.getFdName());
                            csUserRolem.setProjectCode(csUserRolesSs.getProjectCode());
                            csUserRolem.setMobile(csUcUserKeFu.getFdTel());
                            csUserRoleMapper.insert(csUserRolem);
                        }
                    }
                    //添加集团房修负责人
                    if (jtFangxiuUserIdf == null || jtFangxiuUserIdf.equals("")){
                        //return ResponseMessage.error("请填写参数 : jtFangxiuUserId");
                    }else {
                        String[] jtFangxiuUserIds = pamam.get("jtFangxiuUserId").toString().split(",");
                        for (String jtFangxiuUserId : jtFangxiuUserIds) {
                            //查询当前集团房修负责人的相关详细
                            CsUcUser csUcUserFangXiu = csUserRoleMapper.selectCUU(jtFangxiuUserId);
                            //客服负责人
                            CsUserRole csUserRole = new CsUserRole();
                            csUserRole.setRemarks("集团");
                            csUserRole.setRoleName("报修-升级-集团");
                            csUserRole.setUserId(csUcUserFangXiu.getFdUsername());
                            csUserRole.setUserName(csUcUserFangXiu.getFdName());
                            csUserRole.setProjectCode(csUserRolesSs.getProjectCode());
                            csUserRole.setMobile(csUcUserFangXiu.getFdTel());
                            csUserRole.setRoleCode("sRepair-upgrade-3");
                            csUserRoleMapper.insert(csUserRole);
                        }
                    }
                }
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setSucess(true);
                responseMessage.setMessage("编辑成功");
                responseMessage.setData(null);
                responseMessage.setCode(200);
                return responseMessage;
            }

            //如果有值
            //进行删除
            csUserRoleMapper.deleteUserRole();
            //然后进行添加
            List<CsUserRole> csUserRolesS = csUserRoleMapper.selectJiTuanProjectCode();
            for (CsUserRole csUserRolesSs : csUserRolesS) {
                //添加集团客服负责人
                if (jtCustomerUserIdf == null || jtCustomerUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : jtCustomerUserId");
                }else {
                    String[] jtCustomerUserIds = pamam.get("jtCustomerUserId").toString().split(",");
                    for (String jtCustomerUserId : jtCustomerUserIds) {
                        //查询当前集团客服负责人的相关详细
                        CsUcUser csUcUserKeFu = csUserRoleMapper.selectCUU(jtCustomerUserId);
                        //客服负责人
                        CsUserRole csUserRole = new CsUserRole();
                        csUserRole.setRemarks("集团");
                        csUserRole.setRoleName("投诉-升级-集团");
                        csUserRole.setUserId(csUcUserKeFu.getFdUsername());
                        csUserRole.setUserName(csUcUserKeFu.getFdName());
                        csUserRole.setProjectCode(csUserRolesSs.getProjectCode());
                        csUserRole.setMobile(csUcUserKeFu.getFdTel());
                        csUserRole.setRoleCode("sComplaint-upgrade-3");
                        csUserRoleMapper.insert(csUserRole);
                    }
                }
                //添加集团房修负责人
                if (jtFangxiuUserIdf == null || jtFangxiuUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : jtFangxiuUserId");
                }else {
                    String[] jtFangxiuUserIds = pamam.get("jtFangxiuUserId").toString().split(",");
                    for (String jtFangxiuUserId : jtFangxiuUserIds) {
                        //查询当前集团房修负责人的相关详细
                        CsUcUser csUcUserFangXiu = csUserRoleMapper.selectCUU(jtFangxiuUserId);
                        //客服负责人
                        CsUserRole csUserRole = new CsUserRole();
                        csUserRole.setRemarks("集团");
                        csUserRole.setRoleName("报修-升级-集团");
                        csUserRole.setUserId(csUcUserFangXiu.getFdUsername());
                        csUserRole.setUserName(csUcUserFangXiu.getFdName());
                        csUserRole.setProjectCode(csUserRolesSs.getProjectCode());
                        csUserRole.setMobile(csUcUserFangXiu.getFdTel());
                        csUserRole.setRoleCode("sRepair-upgrade-3");
                        csUserRoleMapper.insert(csUserRole);
                    }
                }
            }
            ResponseMessage responseMessage = new ResponseMessage();
            responseMessage.setSucess(true);
            responseMessage.setMessage("编辑成功");
            responseMessage.setData(null);
            responseMessage.setCode(200);
            return responseMessage;
        }
        //区域
        if ("2".equals(type)) {
            //获取区域编码
            String regionCode = pamam.get("regionCode").toString();
            if (regionCode == null || regionCode.equals("")){
                return ResponseMessage.error("请填写参数 : regionCode");
            }
            //区域客服负责人
            String qyCustomerUserIdf = pamam.get("qyCustomerUserId").toString();
            //区域房修负责人
            String qyFangxiuUserIdf = pamam.get("qyFangxiuUserId").toString();
            //根据区域编码获取相关项目
            List<CsUserRole> csUserRole = csUserRoleMapper.selectProjectCode(regionCode);
            if (csUserRole == null || csUserRole.size() == 0) {
                //如果为空在此查询区域相关项目
                List<CsProjectInfo> csProjectInfoss = csProjectInfoMapper.selectProjectCodeNew(regionCode);
                for (CsProjectInfo csProjectInfos : csProjectInfoss) {
                    if (qyCustomerUserIdf == null || qyCustomerUserIdf.equals("")){
                        //return ResponseMessage.error("请填写参数 : qyCustomerUserId");
                    }else {
                        String[] qyCustomerUserIds = pamam.get("qyCustomerUserId").toString().split(",");
                        for (String qyCustomerUserId : qyCustomerUserIds) {
                            //查询当前区域客服负责人的相关详细
                            CsUcUser qyUcUserKeFu = csUserRoleMapper.selectCUU(qyCustomerUserId);
                            //创建区域客服负责人
                            CsUserRole csUserRoles = new CsUserRole();
                            csUserRoles.setUserId(qyUcUserKeFu.getFdUsername());
                            csUserRoles.setUserName(qyUcUserKeFu.getFdName());
                            csUserRoles.setMobile(qyUcUserKeFu.getFdTel());
                            csUserRoles.setProjectCode(csProjectInfos.getProjectCode());
                            csUserRoles.setRoleCode("sComplaint-upgrade-2");
                            csUserRoles.setRoleName("投诉-升级-区域");
                            csUserRoles.setRemarks("区域");
                            csUserRoleMapper.insert(csUserRoles);
                        }
                    }
                    if (qyFangxiuUserIdf == null || qyFangxiuUserIdf.equals("")){
                        //return ResponseMessage.error("请填写参数 : qyFangxiuUserId");
                    }else {
                        String[] qyFangxiuUserIds = pamam.get("qyFangxiuUserId").toString().split(",");
                        for (String qyFangxiuUserId : qyFangxiuUserIds) {
                            //查询当前区域房修负责人的相关详细
                            CsUcUser qyUcUserFangXiu = csUserRoleMapper.selectCUU(qyFangxiuUserId);
                            //创建区域房修负责人
                            CsUserRole csUserRolea = new CsUserRole();
                            csUserRolea.setUserId(qyUcUserFangXiu.getFdUsername());
                            csUserRolea.setUserName(qyUcUserFangXiu.getFdName());
                            csUserRolea.setMobile(qyUcUserFangXiu.getFdTel());
                            csUserRolea.setProjectCode(csProjectInfos.getProjectCode());
                            csUserRolea.setRoleCode("sRepair-upgrade-2");
                            csUserRolea.setRoleName("报修-升级-区域");
                            csUserRolea.setRemarks("区域");
                            csUserRoleMapper.insert(csUserRolea);
                        }
                    }
                }
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setSucess(true);
                responseMessage.setMessage("编辑成功");
                responseMessage.setData(null);
                responseMessage.setCode(200);
                return responseMessage;
            }
            //删除当前所属区域的值
            List<CsProjectInfo> csProjectInfoss = csProjectInfoMapper.selectProjectCodeNew(regionCode);
            for (CsProjectInfo csProjectInfos : csProjectInfoss) {
                csUserRoleMapper.deleteUserRoleQY(csProjectInfos.getProjectCode());
            }
            //如果为空在此查询区域相关项目
            List<CsProjectInfo> csProjectInfokk = csProjectInfoMapper.selectProjectCodeNew(regionCode);
            for (CsProjectInfo csProjectInfos : csProjectInfokk) {
                if (qyCustomerUserIdf == null || qyCustomerUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : qyCustomerUserId");
                }else {
                    String[] qyCustomerUserIds = pamam.get("qyCustomerUserId").toString().split(",");
                    for (String qyCustomerUserId : qyCustomerUserIds) {
                        //查询当前区域客服负责人的相关详细
                        CsUcUser qyUcUserKeFu = csUserRoleMapper.selectCUU(qyCustomerUserId);
                        //创建区域客服负责人
                        CsUserRole csUserRoles = new CsUserRole();
                        csUserRoles.setUserId(qyUcUserKeFu.getFdUsername());
                        csUserRoles.setUserName(qyUcUserKeFu.getFdName());
                        csUserRoles.setMobile(qyUcUserKeFu.getFdTel());
                        csUserRoles.setProjectCode(csProjectInfos.getProjectCode());
                        csUserRoles.setRoleCode("sComplaint-upgrade-2");
                        csUserRoles.setRoleName("投诉-升级-区域");
                        csUserRoles.setRemarks("区域");
                        csUserRoleMapper.insert(csUserRoles);
                    }
                }
                if (qyFangxiuUserIdf == null || qyFangxiuUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : qyFangxiuUserId");
                }else {
                    String[] qyFangxiuUserIds = pamam.get("qyFangxiuUserId").toString().split(",");
                    for (String qyFangxiuUserId : qyFangxiuUserIds) {
                        //查询当前区域房修负责人的相关详细
                        CsUcUser qyUcUserFangXiu = csUserRoleMapper.selectCUU(qyFangxiuUserId);
                        //创建区域房修负责人
                        CsUserRole csUserRolea = new CsUserRole();
                        csUserRolea.setUserId(qyUcUserFangXiu.getFdUsername());
                        csUserRolea.setUserName(qyUcUserFangXiu.getFdName());
                        csUserRolea.setMobile(qyUcUserFangXiu.getFdTel());
                        csUserRolea.setProjectCode(csProjectInfos.getProjectCode());
                        csUserRolea.setRoleCode("sRepair-upgrade-2");
                        csUserRolea.setRoleName("报修-升级-区域");
                        csUserRolea.setRemarks("区域");
                        csUserRoleMapper.insert(csUserRolea);
                    }
                }
            }
            ResponseMessage responseMessage = new ResponseMessage();
            responseMessage.setSucess(true);
            responseMessage.setMessage("编辑成功");
            responseMessage.setData(null);
            responseMessage.setCode(200);
            return responseMessage;
        }
        //城市
        if ("3".equals(type)) {
            //获取区域编码
            String regionCode = (String) pamam.get("regionCode");
            if (regionCode == null || regionCode.equals("")) {
                ResponseMessage.error("区域编码为空,请传递参数");
            }
            //获取城市编码
            String city = (String) pamam.get("cityCode");
            if (regionCode == null || regionCode.equals("")) {
                ResponseMessage.error("城市编码为空,请传递参数");
            }
            //城市客服负责人
            String csCustomerUserIdf = pamam.get("csCustomerUserId").toString();
            //城市房修负责人
            String csFangxiuUserIdf = pamam.get("csFangxiuUserId").toString();
            //查询库里已有数据集团客服负责人的信息
            List<CsUserRole> csUserRoles = csUserRoleMapper.selcetcCity(regionCode, city);
            if (csUserRoles == null || csUserRoles.size() == 0) {
                //如果为空在此查询区域相关项目
                List<CsProjectInfo> csProjectInfoss = csProjectInfoMapper.selectProjectCodeNum(regionCode, city);
                for (CsProjectInfo csProjectInfos : csProjectInfoss) {
                    if (csCustomerUserIdf == null || csCustomerUserIdf.equals("")){
                        //return ResponseMessage.error("请填写参数 : csCustomerUserId");
                    }else {
                        String[] csCustomerUserIds = pamam.get("csCustomerUserId").toString().split(",");
                        for (String csCustomerUserId : csCustomerUserIds) {
                            //查询当前城市客服负责人的相关详细
                            CsUcUser csUcUserKeFu = csUserRoleMapper.selectCUU(csCustomerUserId);
                            //创建城市客服负责人
                            CsUserRole csUserRoless = new CsUserRole();
                            csUserRoless.setUserId(csUcUserKeFu.getFdUsername());
                            csUserRoless.setUserName(csUcUserKeFu.getFdName());
                            csUserRoless.setMobile(csUcUserKeFu.getFdTel());
                            csUserRoless.setProjectCode(csProjectInfos.getProjectCode());
                            csUserRoless.setRoleCode("sComplaint-upgrade-1");
                            csUserRoless.setRoleName("投诉-升级-城市");
                            csUserRoless.setRemarks("城市");
                            csUserRoleMapper.insert(csUserRoless);
                        }
                    }
                    if (csFangxiuUserIdf == null || csFangxiuUserIdf.equals("")){
                        //return ResponseMessage.error("请填写参数 : csFangxiuUserId");
                    }else {
                        String[] csFangxiuUserIds = pamam.get("csFangxiuUserId").toString().split(",");
                        for (String csFangxiuUserId : csFangxiuUserIds) {
                            //查询当前集团房修负责人的相关详细
                            CsUcUser csUcUserFangXiu = csUserRoleMapper.selectCUU(csFangxiuUserId);
                            //创建区域房修负责人
                            CsUserRole csUserRolea = new CsUserRole();
                            csUserRolea.setUserId(csUcUserFangXiu.getFdUsername());
                            csUserRolea.setUserName(csUcUserFangXiu.getFdName());
                            csUserRolea.setMobile(csUcUserFangXiu.getFdTel());
                            csUserRolea.setProjectCode(csProjectInfos.getProjectCode());
                            csUserRolea.setRoleCode("sRepair-upgrade-1");
                            csUserRolea.setRoleName("报修-升级-城市");
                            csUserRolea.setRemarks("城市");
                            csUserRoleMapper.insert(csUserRolea);
                        }
                    }
                }
            }
            //删除之前数据
            List<CsUserRole> csUserRolessss = csUserRoleMapper.selcetcCity(regionCode, city);
            for (CsUserRole csUserRoleskkk : csUserRolessss) {
                csUserRoleMapper.deleteUserRoleCS(csUserRoleskkk.getId());
            }
            List<CsProjectInfo> csProjectInfoss = csProjectInfoMapper.selectProjectCodeNum(regionCode, city);
            for (CsProjectInfo csProjectInfos : csProjectInfoss) {
                if (csCustomerUserIdf == null || csCustomerUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : csCustomerUserId");
                }else {
                    String[] csCustomerUserIds = pamam.get("csCustomerUserId").toString().split(",");
                    for (String csCustomerUserId : csCustomerUserIds) {
                        //查询当前城市客服负责人的相关详细
                        CsUcUser csUcUserKeFu = csUserRoleMapper.selectCUU(csCustomerUserId);
                        //创建城市客服负责人
                        CsUserRole csUserRoless = new CsUserRole();
                        csUserRoless.setUserId(csUcUserKeFu.getFdUsername());
                        csUserRoless.setUserName(csUcUserKeFu.getFdName());
                        csUserRoless.setMobile(csUcUserKeFu.getFdTel());
                        csUserRoless.setProjectCode(csProjectInfos.getProjectCode());
                        csUserRoless.setRoleCode("sComplaint-upgrade-1");
                        csUserRoless.setRoleName("投诉-升级-城市");
                        csUserRoless.setRemarks("城市");
                        csUserRoleMapper.insert(csUserRoless);
                    }
                }
                if (csFangxiuUserIdf == null || csFangxiuUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : csFangxiuUserId");
                }else {
                    String[] csFangxiuUserIds = pamam.get("csFangxiuUserId").toString().split(",");
                    for (String csFangxiuUserId : csFangxiuUserIds) {
                        //查询当前集团房修负责人的相关详细
                        CsUcUser csUcUserFangXiu = csUserRoleMapper.selectCUU(csFangxiuUserId);
                        //创建区域房修负责人
                        CsUserRole csUserRolea = new CsUserRole();
                        csUserRolea.setUserId(csUcUserFangXiu.getFdUsername());
                        csUserRolea.setUserName(csUcUserFangXiu.getFdName());
                        csUserRolea.setMobile(csUcUserFangXiu.getFdTel());
                        csUserRolea.setProjectCode(csProjectInfos.getProjectCode());
                        csUserRolea.setRoleCode("sRepair-upgrade-1");
                        csUserRolea.setRoleName("报修-升级-城市");
                        csUserRolea.setRemarks("城市");
                        csUserRoleMapper.insert(csUserRolea);
                    }
                }
            }
            ResponseMessage responseMessage = new ResponseMessage();
            responseMessage.setSucess(true);
            responseMessage.setMessage("编辑成功");
            responseMessage.setData(null);
            responseMessage.setCode(200);
            return responseMessage;
        }
        //项目
        if ("4".equals(type)) {
            //获取项目编码
            String projectCode = (String) pamam.get("projectCode");
            if (projectCode == null || projectCode.equals("")) {
                return ResponseMessage.error("请填写参数 : 项目编码 [projectCode]");
            }
            //项目客服负责人
            String xmCustomerUserIdf = pamam.get("xmCustomerUserId").toString();
            //项目客服处理
            String xmProcessingCustomerUserIdf = pamam.get("xmProcessingCustomerUserId").toString();
            //项目录入员
            String xmProcessingFangxiuLRYUserIdf = pamam.get("xmProcessingFangxiuLRYUserId").toString();
            //项目房修负责人
            String xmFangxiuUserIdf = pamam.get("xmFangxiuUserId").toString();
            //项目房修处理人
            String xmProcessingFangxiuUserId = pamam.get("xmProcessingFangxiuUserId").toString();
            //根据项目编码查询相关详细
            List<CsUserRole> csUserRoles = csUserRoleMapper.selectAllCsUserRole(projectCode);
            if (csUserRoles == null || csUserRoles.size() == 0) {
                if (xmCustomerUserIdf == null || xmCustomerUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : 客服负责人 [xmCustomerUserId]");
                }else {
                    String[] xmCustomerUserIds = xmCustomerUserIdf.split(",");
                    for (String customerUserId : xmCustomerUserIds) {
                        //查询当前项目客服负责人的相关详细
                        CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(customerUserId);
                        //客服负责人
                        CsUserRole csUserRole = new CsUserRole();
                        csUserRole.setRemarks("投诉-待分配");
                        csUserRole.setRoleName("投诉-待分配");
                        csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                        csUserRole.setUserName(xmUcUserKeFu.getFdName());
                        csUserRole.setProjectCode(projectCode);
                        csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                        csUserRole.setRoleCode("sComplaint-toBeAssigned");
                        csUserRoleMapper.insert(csUserRole);
                        //查询当前项目客服负责人的相关详细
                        CsUcUser xmUcUserKeFu2 = csUserRoleMapper.selectCUU(customerUserId);
                        //咨询待处理
                        CsUserRole csUserRole2 = new CsUserRole();
                        csUserRole2.setRemarks("咨询-待处理");
                        csUserRole2.setRoleName("咨询-待处理");
                        csUserRole2.setUserId(xmUcUserKeFu.getFdUsername());
                        csUserRole2.setUserName(xmUcUserKeFu.getFdName());
                        csUserRole2.setProjectCode(projectCode);
                        csUserRole2.setMobile(xmUcUserKeFu.getFdTel());
                        csUserRole2.setRoleCode("sConsultation-handle");
                        csUserRoleMapper.insert(csUserRole2);
                    }
                }
                if (xmProcessingCustomerUserIdf == null || xmProcessingCustomerUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : 客服处理 [xmProcessingCustomerUserId]");
                }else {
                    String[] xmProcessingCustomerUserIds = xmProcessingCustomerUserIdf.split(",");
                    for (String processingCustomerUserId : xmProcessingCustomerUserIds) {
                        //查询当前项目客服处理人的相关详细
                        CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(processingCustomerUserId);
                        //客服处理人
                        CsUserRole csUserRolecl = new CsUserRole();
                        csUserRolecl.setRemarks("投诉-待处理");
                        csUserRolecl.setRoleName("投诉-待处理");
                        csUserRolecl.setUserId(xmUcUserKeFu.getFdUsername());
                        csUserRolecl.setUserName(xmUcUserKeFu.getFdName());
                        csUserRolecl.setProjectCode(projectCode);
                        csUserRolecl.setMobile(xmUcUserKeFu.getFdTel());
                        csUserRolecl.setRoleCode("sComplaint-handle");
                        csUserRoleMapper.insert(csUserRolecl);
                    }
                }
                if (xmFangxiuUserIdf == null || xmFangxiuUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : 房修负责人 [xmFangxiuUserId]");
                }else {
                    String[] fangxiuUserIds = pamam.get("xmFangxiuUserId").toString().split(",");
                    for (String fangxiuUserId : fangxiuUserIds) {
                        //查询当前项目房修负责人的相关详细
                        CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(fangxiuUserId);
                        //房修负责人
                        CsUserRole csUserRole = new CsUserRole();
                        csUserRole.setRemarks("报修-待分派");
                        csUserRole.setRoleName("报修-待分派");
                        csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                        csUserRole.setUserName(xmUcUserKeFu.getFdName());
                        csUserRole.setProjectCode(projectCode);
                        csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                        csUserRole.setRoleCode("sRepair-toBeAssigned");
                        csUserRoleMapper.insert(csUserRole);
                    }
                }
                if (xmProcessingFangxiuUserId == null || xmProcessingFangxiuUserId.equals("")){
                    //return ResponseMessage.error("请填写参数 : 房修处理人 [xmProcessingFangxiuUserId]");
                }else {
                    String[] processingFangxiuUserIds = pamam.get("xmProcessingFangxiuUserId").toString().split(",");
                    for (String processingFangxiuUserId : processingFangxiuUserIds) {
                        //查询当前项目房修负责人的相关详细
                        CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(processingFangxiuUserId);
                        //房修处理人
                        CsUserRole csUserRole = new CsUserRole();
                        csUserRole.setRemarks("报修-待处理");
                        csUserRole.setRoleName("报修-待处理");
                        csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                        csUserRole.setUserName(xmUcUserKeFu.getFdName());
                        csUserRole.setProjectCode(projectCode);
                        csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                        csUserRole.setRoleCode("sRepair-handle");
                        csUserRoleMapper.insert(csUserRole);
                    }
                }
                if (xmProcessingFangxiuLRYUserIdf == null || xmProcessingFangxiuLRYUserIdf.equals("")){
                    //return ResponseMessage.error("请填写参数 : 房修录入员 [xmProcessingFangxiuLRYUserId]");
                }else {
                    String[] xmProcessingFangxiuLRYUserIds = xmProcessingFangxiuLRYUserIdf.split(",");
                    for (String xmProcessingFangxiuLRYUserId : xmProcessingFangxiuLRYUserIds) {
                        //查询当前项目房修录入员的相关详细
                        CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(xmProcessingFangxiuLRYUserId);
                        //房修处理人
                        CsUserRole csUserRole = new CsUserRole();
                        csUserRole.setRemarks("房修录入员");
                        csUserRole.setRoleName("房修录入员");
                        csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                        csUserRole.setUserName(xmUcUserKeFu.getFdName());
                        csUserRole.setProjectCode(projectCode);
                        csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                        csUserRole.setRoleCode("fangxiu-entry-agent");
                        csUserRoleMapper.insert(csUserRole);
                    }
                }
                ResponseMessage responseMessage = new ResponseMessage();
                responseMessage.setSucess(true);
                responseMessage.setMessage("编辑成功");
                responseMessage.setData(null);
                responseMessage.setCode(200);
                return responseMessage;
            }
            List<CsUserRole> csUserRoleooo = csUserRoleMapper.selectAllCsUserRole(projectCode);
            //删除当前项目下所有的数据
            for (CsUserRole csUserRoless : csUserRoleooo) {
                csUserRoleMapper.deleteUserRoleCS(csUserRoless.getId());
            }
            if (xmCustomerUserIdf == null || xmCustomerUserIdf.equals("")){
                //return ResponseMessage.error("请填写参数 : 客服负责人 [xmCustomerUserId]");
            }else {
                String[] xmCustomerUserIds = xmCustomerUserIdf.split(",");
                for (String customerUserId : xmCustomerUserIds) {
                    //查询当前项目客服负责人的相关详细
                    CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(customerUserId);
                    //客服负责人
                    CsUserRole csUserRole = new CsUserRole();
                    csUserRole.setRemarks("投诉-待分配");
                    csUserRole.setRoleName("投诉-待分配");
                    csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                    csUserRole.setUserName(xmUcUserKeFu.getFdName());
                    csUserRole.setProjectCode(projectCode);
                    csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                    csUserRole.setRoleCode("sComplaint-toBeAssigned");
                    csUserRoleMapper.insert(csUserRole);
                    //查询当前项目客服负责人的相关详细
                    CsUcUser xmUcUserKeFu2 = csUserRoleMapper.selectCUU(customerUserId);
                    //咨询待处理
                    CsUserRole csUserRole2 = new CsUserRole();
                    csUserRole2.setRemarks("咨询-待处理");
                    csUserRole2.setRoleName("咨询-待处理");
                    csUserRole2.setUserId(xmUcUserKeFu.getFdUsername());
                    csUserRole2.setUserName(xmUcUserKeFu.getFdName());
                    csUserRole2.setProjectCode(projectCode);
                    csUserRole2.setMobile(xmUcUserKeFu.getFdTel());
                    csUserRole2.setRoleCode("sConsultation-handle");
                    csUserRoleMapper.insert(csUserRole2);
                }
            }
            if (xmProcessingCustomerUserIdf == null || xmProcessingCustomerUserIdf.equals("")){
                //return ResponseMessage.error("请填写参数 : 客服处理 [xmProcessingCustomerUserId]");
            }else {
                String[] xmProcessingCustomerUserIds = xmProcessingCustomerUserIdf.split(",");
                for (String processingCustomerUserId : xmProcessingCustomerUserIds) {
                    //查询当前项目客服处理人的相关详细
                    CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(processingCustomerUserId);
                    //客服处理人
                    CsUserRole csUserRolecl = new CsUserRole();
                    csUserRolecl.setRemarks("投诉-待处理");
                    csUserRolecl.setRoleName("投诉-待处理");
                    csUserRolecl.setUserId(xmUcUserKeFu.getFdUsername());
                    csUserRolecl.setUserName(xmUcUserKeFu.getFdName());
                    csUserRolecl.setProjectCode(projectCode);
                    csUserRolecl.setMobile(xmUcUserKeFu.getFdTel());
                    csUserRolecl.setRoleCode("sComplaint-handle");
                    csUserRoleMapper.insert(csUserRolecl);
                }
            }
            if (xmFangxiuUserIdf == null || xmFangxiuUserIdf.equals("")){
                //return ResponseMessage.error("请填写参数 : 房修负责人 [xmFangxiuUserId]");
            }else {
                String[] fangxiuUserIds = pamam.get("xmFangxiuUserId").toString().split(",");
                for (String fangxiuUserId : fangxiuUserIds) {
                    //查询当前项目房修负责人的相关详细
                    CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(fangxiuUserId);
                    //房修负责人
                    CsUserRole csUserRole = new CsUserRole();
                    csUserRole.setRemarks("报修-待分派");
                    csUserRole.setRoleName("报修-待分派");
                    csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                    csUserRole.setUserName(xmUcUserKeFu.getFdName());
                    csUserRole.setProjectCode(projectCode);
                    csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                    csUserRole.setRoleCode("sRepair-toBeAssigned");
                    csUserRoleMapper.insert(csUserRole);
                }
            }
            if (xmProcessingFangxiuUserId == null || xmProcessingFangxiuUserId.equals("")){
                //return ResponseMessage.error("请填写参数 : 房修处理人 [xmProcessingFangxiuUserId]");
            }else {
                String[] processingFangxiuUserIds = pamam.get("xmProcessingFangxiuUserId").toString().split(",");
                for (String processingFangxiuUserId : processingFangxiuUserIds) {
                    //查询当前项目房修负责人的相关详细
                    CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(processingFangxiuUserId);
                    //房修处理人
                    CsUserRole csUserRole = new CsUserRole();
                    csUserRole.setRemarks("报修-待处理");
                    csUserRole.setRoleName("报修-待处理");
                    csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                    csUserRole.setUserName(xmUcUserKeFu.getFdName());
                    csUserRole.setProjectCode(projectCode);
                    csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                    csUserRole.setRoleCode("sRepair-handle");
                    csUserRoleMapper.insert(csUserRole);
                }
            }
            if (xmProcessingFangxiuLRYUserIdf == null || xmProcessingFangxiuLRYUserIdf.equals("")){
                //return ResponseMessage.error("请填写参数 : 房修录入员 [xmProcessingFangxiuLRYUserId]");
            }else {
                String[] xmProcessingFangxiuLRYUserIds = xmProcessingFangxiuLRYUserIdf.split(",");
                for (String xmProcessingFangxiuLRYUserId : xmProcessingFangxiuLRYUserIds) {
                    //查询当前项目房修录入员的相关详细
                    CsUcUser xmUcUserKeFu = csUserRoleMapper.selectCUU(xmProcessingFangxiuLRYUserId);
                    //房修处理人
                    CsUserRole csUserRole = new CsUserRole();
                    csUserRole.setRemarks("房修录入员");
                    csUserRole.setRoleName("房修录入员");
                    csUserRole.setUserId(xmUcUserKeFu.getFdUsername());
                    csUserRole.setUserName(xmUcUserKeFu.getFdName());
                    csUserRole.setProjectCode(projectCode);
                    csUserRole.setMobile(xmUcUserKeFu.getFdTel());
                    csUserRole.setRoleCode("fangxiu-entry-agent");
                    csUserRoleMapper.insert(csUserRole);
                }
            }
            ResponseMessage responseMessage = new ResponseMessage();
            responseMessage.setSucess(true);
            responseMessage.setMessage("编辑成功");
            responseMessage.setData(null);
            responseMessage.setCode(200);
            return responseMessage;
        }
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.setSucess(false);
        responseMessage.setMessage("编辑失败");
        responseMessage.setData(null);
        responseMessage.setCode(499);
        return responseMessage;
    }

    @Override
    public ResponseMessage orgTree() {
        String json;
        // redis 没数据，重新查一遍，在放入redis，30分钟过期
        // 1. 查询全部组织
        List<OrgInfoVo> orgs = csUcOrgMapper.findAllOrgInfo();
        Map<String, List<OrgInfoVo>> maps = new HashMap<String, List<OrgInfoVo>>((int) (orgs.size() / 0.75 + 1));
        OrgInfoVo root = null;
        // 2. 递归组织
        if (orgs != null && orgs.size() > 0) {
            // 2.1 给每个节点分配一个 子节点集合
            for (OrgInfoVo o : orgs) {
                maps.put(o.getSid(), new ArrayList<OrgInfoVo>());
            }
            // 2.2 填充每个节点的子节点
            for (OrgInfoVo o : orgs) {
                if ("".equals(o.getParentSid())) {
                    root = o;
                }
                if (maps.get(o.getParentSid()) != null) {
                    maps.get(o.getParentSid()).add(o);
                }
            }
            // 2.2 递归全部节点，组装成完成的树
            mapToList(root, maps);
        }
        List<OrgInfoVo> roots = new ArrayList<OrgInfoVo>();
        roots.add(root);
        json = JSON.toJSONString(roots);
        if (roots != null && roots.size() > 0) {
            return ResponseMessage.ok(roots);
        } else {
            return ResponseMessage.error("组织树为空");
        }
    }

    /**
     * 递归组织数  第一种实现方式
     *
     * @param org  当前节点
     * @param maps 所有的节点，已挂了子节点
     */
    private void mapToList(OrgInfoVo org, Map<String, List<OrgInfoVo>> maps) {
        if (org != null) {
            List<OrgInfoVo> childs = maps.get(org.getSid());
            for (OrgInfoVo o : childs) {
                mapToList(o, maps);
            }
            org.setChilds(childs);
        }
    }

	@Override
	public ResponseMessage selectItUser() {
		List<UserRoleDictDto> curList = baseMapper.selectItUser();
		if(null == curList || curList.size() == 0) {
			return null;
		}
		
		Map<String, Object> map = new HashMap<>();
		
		for(UserRoleDictDto cur : curList) {
			Map<String, Object> roleMap = new HashMap<>();
			if(null != map.get(cur.getProjectCode())) {
				roleMap = (Map<String, Object>) map.get(cur.getProjectCode());
			}
			roleMap.put(cur.getRoleCode(), cur);
			roleMap.put("projectCode", cur.getProjectCode());
			roleMap.put("projectName", cur.getProjectName());
			map.put(cur.getProjectCode(), roleMap);
		}
		return ResponseMessage.ok(map);
	}
	
	@Override
	public ResponseMessage saveItUser(Map<String, Object> pamam) {
		
		List<CsUserRole> insertList = new ArrayList<>();
		if(null != pamam.get("assignUserId")) {
			String[] assignUserIds = pamam.get("assignUserId").toString().split(",");
			for (String assignUserId : assignUserIds) {
				//查询当前区域客服负责人的相关详细
				CsUcUser assignUser = csUserRoleMapper.selectCUU(assignUserId);
				//IT项目分配人
				CsUserRole csUserRoles = new CsUserRole();
				try {
					csUserRoles.setUserId(assignUser.getFdUsername());
					csUserRoles.setUserName(assignUser.getFdName());
					csUserRoles.setMobile(assignUser.getFdTel());
					csUserRoles.setProjectCode(pamam.get("projectCode").toString());
					csUserRoles.setRoleCode("itRepair-toBeAssigned");
					csUserRoles.setRoleName("IT-待分配");
					csUserRoles.setRemarks("IT-待分配");
					insertList.add(csUserRoles);
				} catch (Exception e) {
					log.info("IT项目管理添加分派人_无法找到相关人员：" + assignUserId);
					return ResponseMessage.error("IT项目管理添加分派人_无法找到相关人员：" + assignUserId);
				}
			}
		}
		if(null != pamam.get("handleUserId")) {
			String[] handleUserIds = pamam.get("handleUserId").toString().split(",");
			for (String handleUserId : handleUserIds) {
				//查询当前区域客服负责人的相关详细
				CsUcUser handleUser = csUserRoleMapper.selectCUU(handleUserId);
				//IT项目分配人
				CsUserRole csUserRoles = new CsUserRole();
				try {
					csUserRoles.setUserId(handleUser.getFdUsername());
					csUserRoles.setUserName(handleUser.getFdName());
					csUserRoles.setMobile(handleUser.getFdTel());
					csUserRoles.setProjectCode(pamam.get("projectCode").toString());
					csUserRoles.setRoleCode("itRepair-handle");
					csUserRoles.setRoleName("IT-待处理");
					csUserRoles.setRemarks("IT-待处理");
					insertList.add(csUserRoles);
				} catch (Exception e) {
					log.info("IT项目管理添加处理人_无法找到相关人员：" + handleUserId);
					return ResponseMessage.error("IT项目管理添加处理人_无法找到相关人员：" + handleUserId);
				}
			}
		}
		
		Wrapper<CsUserRole> wrapper = new EntityWrapper<>();
		wrapper.where("role_code like {0} AND project_code={1}", "itRepair%",pamam.get("projectCode").toString());
		csUserRoleMapper.delete(wrapper);
		
		csUserRoleMapper.insertUserList(insertList);
        return ResponseMessage.okm("success");
	}
}
