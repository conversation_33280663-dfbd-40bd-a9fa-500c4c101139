/**
 * 
 */
package com.tahoecn.customerservice.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.utils.OASend;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.ProcessService;

/**
 * It类工单报事
 * 
 * @ClassName ItProcessServiceImpl
 * <AUTHOR>
 * @date 2018年12月22日
 */
@Transactional
@Service("itProcessService")
public class ItProcessServiceImpl extends BaseProcessServiceImpl implements ProcessService {

	private static final String rolePre = "itRepair-";

	@Autowired
	private CsFormInstService csFormInstService;
	@Autowired
	private CsProcessWorkitemService csProcessWorkitemService;
	@Autowired
	private OASend oaSend;

	@Override
	public void process(CsFormInst csForminst, String formId, String firstsortCode, String deptCode,
			String processStateCode, String handleRecord, String comment, String assignUserId, String assignUserName,
			String mobile, String operateType) {
		if ("processKSCL".equals(csForminst.getProcessCode())) { //It快处流程
			String nextprocessStateCode = (String) this.qItNextMap.get(processStateCode);
			String nextprocessStateName = (String) this.processStateMap.get(nextprocessStateCode);
			Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
			formWrapper.where("id={0}", formId);
			CsFormInst form = csFormInstService.selectOne(formWrapper);
			form.setProcessStateCode(nextprocessStateCode);
			form.setProcessStateName(nextprocessStateName);
			form.setCurAssigneeId(ThreadLocalUtils.getUserName());
			form.setCurAssigneeName(ThreadLocalUtils.getRealName());
			form.setLastUpdateDate(new Date());
			csFormInstService.updateById(form);
		} else {
			logger.info("时间 {} 流程开始执行：formId：{},processStateCode:{},assignUserId:{},operateType:{}",new Date(),formId,processStateCode,assignUserId,operateType);
			//已办
	        Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
	        wrapper.where("form_inst_id={0}",formId).and("process_state_code={0}",processStateCode).and("task_status={0}",new Long(20));
	        CsProcessWorkitem doneItem = csProcessWorkitemService.selectOne(wrapper);
	        //处理流程跑了。单子没跑的逻辑
	        if(doneItem == null ) {
	        	logger.error("流程跑了单子没跑 单号：{}",formId);
	        	wrapper = new EntityWrapper<CsProcessWorkitem>();
	        	wrapper.eq("form_inst_id", formId).eq("task_status", 20L);
	        	doneItem = csProcessWorkitemService.selectOne(wrapper);
	        	if(doneItem == null) {
	        		return;
	        	}
	        	CsFormInst formInst = new CsFormInst();
	        	formInst.setId(Long.valueOf(formId));
	        	formInst.setProcessStateCode(doneItem.getProcessStateCode());
	        	formInst.setProblemPositionName(String.valueOf(this.businessStateMap.get(doneItem.getProcessStateCode())));
	        	formInst.setAssignId(doneItem.getAssignId());
	        	formInst.setAssignName(doneItem.getAssignName());
	        	csFormInstService.updateById(formInst);
	        	return ;
	        }
	        
	        doneItem.setTaskStatus(new Long(30));//已办
	        if(handleRecord!=null && !"".equals(handleRecord)){
	            doneItem.setComment(handleRecord);
	        }
	        String nextprocessStateCode = "";
	        if(operateType.equals("submit")) {
	            nextprocessStateCode = (String) this.itNextMap.get(processStateCode);
	            if("draft".equals(processStateCode))
	            {
	                doneItem.setComment("提交");
	            }else if(!"".equals(processStateCode) && processStateCode!=null && processStateCode.equals("toBeAssigned")){
	                doneItem.setComment("分派【" + assignUserName +"】进行处理");
	            }else if(!"".equals(processStateCode) && processStateCode!=null && processStateCode.equals("returnVisit")){
	                doneItem.setComment("回访满意度："+ csForminst.getSatisfactionName());
	            }
	        }
	        if(operateType.equals("reject")){
	            nextprocessStateCode = (String) this.itRejectMap.get(processStateCode);
	            doneItem.setComment(comment);
	        }

	        doneItem.setLastUpdateDate(new Date());
	        doneItem.setTaskEndTime(new Date());
	        if(nextprocessStateCode.indexOf("End")>1 || processStateCode.equals("returnVisit")){
	            doneItem.setAssignId(ThreadLocalUtils.getUserName());
	            doneItem.setAssignName(ThreadLocalUtils.getRealName());
	        }
	        if("1".equals(csForminst.getIsOwner()) && "draft".equals(csForminst.getProcessStateCode())){
	        	doneItem.setAssignId(ThreadLocalUtils.getUserName());
	        	doneItem.setAssignName(ThreadLocalUtils.getRealName());
	        }
            //新增 app来源
	        doneItem.setThPlatform(ThreadLocalUtils.getPlatform());
            
	        csProcessWorkitemService.updateById(doneItem);
	        oaSend.setTodoDone(String.valueOf(doneItem.getId()));

	        //待办生成
	        String nextprocessStateName = (String)this.processStateMap.get(nextprocessStateCode);
	        String nextBusinessStateName = (String)this.businessStateMap.get(nextprocessStateCode);
	        CsProcessWorkitem item = new CsProcessWorkitem();

	        item.setProcessStateCode(nextprocessStateCode);
	        item.setProcessStateName(nextprocessStateName);
	        item.setFormInstId(new Long(formId));
	        item.setCreationDate(new Date());
	        item.setTaskStartTime(new Date());
	        if(nextprocessStateCode.indexOf("End")<0){
	            //前台传过来
	            item.setAssignId(assignUserId);
	            item.setAssignName(assignUserName);
	            item.setTaskStatus(new Long(20));//待办
	            item.setAssignMobile(mobile);
	        }else{
	            item.setAssignId(ThreadLocalUtils.getUserName());
	            item.setAssignName(ThreadLocalUtils.getRealName());
	            item.setTaskEndTime(this.addOneSecond(new Date()));
	            item.setTaskStatus(new Long(30));//结束
	        }
	        item.setCreateByUserId(ThreadLocalUtils.getUserName());
	        item.setLastUpdateDate(new Date());
	        csProcessWorkitemService.insert(item);
	        
	        // 处理类型
	        String type = "toBeAssigned".equals(processStateCode)?"分派":
	        	"toBeAssigned".equals(nextprocessStateCode)?
	        	"handle".equals(processStateCode)?"退回":"提交":null;
	        
	        if(type != null) {
	        	// "请处理[****项目]（上一处理人）（提交/分派/退回）的工单：（工单编号）-（工单一级分类）-（工单二级分类）";
	        	String title = String.format("请处理[%s项目]%s%s的工单：%s-%s-%s", 
	        			csForminst.getProject(),
	        			doneItem.getAssignName(),
	        			type,
	        			csForminst.getFormNo(),
	        			csForminst.getFirstSortName(),
	        			csForminst.getSecSortName());
	            oaSend.sendTodo(Long.valueOf(formId), String.valueOf(item.getId()), title, "{\"LoginName\":\""+item.getAssignId()+"\"}");
	        }

	        //修改主表状态
	        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
	        formWrapper.where("id={0}",formId);
	        CsFormInst form = csFormInstService.selectOne(formWrapper);
	        form.setProcessStateCode(nextprocessStateCode);
	        form.setProcessStateName(nextBusinessStateName);
	        form.setSubmitDate(new Date());
	        form.setLastUpdateDate(new Date());
	        if(nextprocessStateCode.equals("toBeAssigned")){//回写分派人
	            form.setAssignId(assignUserId);
	            form.setAssignName(assignUserName);
	        }
	        if(processStateCode.equals("toBeAssigned")){//回写分派人时间
	            form.setAssignDate(new Date());
	        }
	        if(nextprocessStateCode.indexOf("End")<0) {
	            form.setCurAssigneeId(assignUserId);
	            form.setCurAssigneeName(assignUserName);
	        }
	        csFormInstService.updateById(form);
	        logger.info("时间 {} 流程结束执行：formId：{},processStateCode:{},assignUserId:{},operateType:{}",new Date(),formId,form.getProcessStateCode(),assignUserId,operateType);
		}

	}

	@Override
	public List getUserByRole(String operateType, Long formInstId) {
		Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formInstId);
        CsFormInst form = csFormInstService.selectOne(formWrapper);

        if("processKSCL".equals(form.getProcessCode())) {
        	return new ArrayList<>();
        }
        
        String nextprocessStateCode = "";
        if(operateType.equals("submit")) {
            nextprocessStateCode = (String) this.itNextMap.get(form.getProcessStateCode());
        }else{
            nextprocessStateCode = (String) this.itRejectMap.get(form.getProcessStateCode());
        }
        String roleCode = rolePre+nextprocessStateCode;
        List Users = this.getUserByRoleCode(roleCode,form.getProjectCode());
        return Users;
	}

	@Override
	public void updrade(CsFormInst csFormInst) throws Exception {
		if ("processBZ".equals(csFormInst.getProcessCode())) {
			String templateCode = "";
			Long upgradeLevel = csFormInst.getUpgradeLevel() == null ? new Long(0) : csFormInst.getUpgradeLevel();
			upgradeLevel = upgradeLevel + new Long(1);
			// 3集团就不升级
			if (upgradeLevel > 3) {
				return;
			}
			templateCode = rolePre + "upgrade-" + upgradeLevel;
			super.upgrade(csFormInst, templateCode, upgradeLevel);
		}
	}

}
