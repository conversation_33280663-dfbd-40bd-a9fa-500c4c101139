package com.tahoecn.customerservice.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.service.ProcessService;

/**
 * <p>
 * 流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service("processService")
public class ProcessServiceImpl extends BaseProcessServiceImpl implements ProcessService {

    @Override
    public void process(CsFormInst csForminst, String formId, String firstsortCode, String deptCode, String processStateCode, String comment, String handleRecord, String assignUserId, String assignUserName,String mobile, String operateType) {
        
    }

    @Override
    public List getUserByRole(String operateType, Long formInstId) {
        return null;
    }

    @Override
    public void updrade(CsFormInst csFormInst) {

    }
}
