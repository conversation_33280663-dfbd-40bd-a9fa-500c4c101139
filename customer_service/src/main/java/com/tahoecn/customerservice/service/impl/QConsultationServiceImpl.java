package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.ProcessService;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 快速处理流程 咨询
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service("qConsultationService")
public class QConsultationServiceImpl extends BaseProcessServiceImpl implements ProcessService {

    @Autowired
    private CsFormInstService csFormInstService;

    @Autowired
    private CsUcUserService csUcUserService;

    @Override
    public void process(CsFormInst csForminst,String formId,String firstsortCode,String deptCode,String processStateCode,String handleRecord,String comment,String assignUserId,String assignUserName,String mobile,String operateType) {

    	CsUcUser curUser = new CsUcUser();
    	
    	if(!StringUtils.isNotBlank(csForminst.getWyFormNo()) || !"draft".equals(csForminst.getProcessStateCode())){
    		curUser = csUcUserService.selectByUsername();
    	}else{
    		curUser.setFdUsername("liwenye");
    		curUser.setFdName(csForminst.getCreateUserName());
    	}

        if(deptCode.equals("wuye")){
            //提交物业
        }else{
            //修改主表状态
            String nextprocessStateCode = (String)this.qPraiseAndSuggestionNextMap.get(processStateCode);
            String nextprocessStateName = (String)this.processStateMap.get(nextprocessStateCode);
            Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
            formWrapper.where("id={0}",formId);
            CsFormInst form = csFormInstService.selectOne(formWrapper);
            form.setProcessStateCode(nextprocessStateCode);
            form.setProcessStateName(nextprocessStateName);
            form.setCurAssigneeId(curUser.getFdUsername());
            form.setCurAssigneeName(curUser.getFdName());
            form.setLastUpdateDate(new Date());
            csFormInstService.updateById(form);
        }
    }

    @Override
    public List getUserByRole(String operateType, Long formInstId) {
        return null;
    }

    @Override
    public void updrade(CsFormInst csFormInst) {

    }
}
