/**
 * 
 */
package com.tahoecn.customerservice.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.ls.LSInput;

import com.tahoecn.customerservice.mapper.ReportFormMapper;
import com.tahoecn.customerservice.model.report.ChannelDto;
import com.tahoecn.customerservice.model.report.ClassifyDto;
import com.tahoecn.customerservice.model.report.CurReportDto;
import com.tahoecn.customerservice.model.report.RepairWholeDto;
import com.tahoecn.customerservice.model.report.SortTreeDto;
import com.tahoecn.customerservice.model.report.UpgradeDto;
import com.tahoecn.customerservice.service.ReportFormService;

/**
 * @ClassName ReportFormServiceImpl
 * <AUTHOR>
 * @date 2019年1月9日
 */
@Transactional
@Service
public class ReportFormServiceImpl implements ReportFormService {

	@Autowired
	ReportFormMapper reportFormMapper;

	@Override
	public List<RepairWholeDto> repairWhole(Map<String, Object> map) {
		List<RepairWholeDto> list = reportFormMapper.repairWhole(map);
		list.sort(new Comparator<RepairWholeDto>() {

			@Override
			public int compare(RepairWholeDto o1, RepairWholeDto o2) {
				return -o1.getTimelyClosureRate().compareTo(o2.getTimelyClosureRate());
			}
		});
		for (int i = 0; i < list.size(); i++) {
			list.get(i).setClosureSort(i + 1);
		}

		list.sort(new Comparator<RepairWholeDto>() {

			@Override
			public int compare(RepairWholeDto o1, RepairWholeDto o2) {
				return -o1.getSatisfaction().compareTo(o2.getSatisfaction());
			}
		});
		RepairWholeDto dto = new RepairWholeDto();
		dto.setRegion("集团");
		dto.setFzzNum(0);
		dto.setTotal(0);
		dto.setTimelyClosureNum(0);
		dto.setCloseNum(0);
		dto.setSpecialClosureNum(0);
		dto.setSatisfactionTotalNum(0);
		dto.setSatisfactionNum(0);

		for (int i = 0; i < list.size(); i++) {
			dto.setFzzNum(dto.getFzzNum() + list.get(i).getFzzNum());
			dto.setTotal(dto.getTotal() + list.get(i).getTotal());
			dto.setTimelyClosureNum(dto.getTimelyClosureNum() + list.get(i).getTimelyClosureNum());
			dto.setCloseNum(dto.getCloseNum() + list.get(i).getCloseNum());
			dto.setSpecialClosureNum(dto.getSpecialClosureNum() + list.get(i).getSpecialClosureNum());
			dto.setSatisfactionTotalNum(dto.getSatisfactionTotalNum() + list.get(i).getSatisfactionTotalNum());
			dto.setSatisfactionNum(dto.getSatisfactionNum() + list.get(i).getSatisfactionNum());
			list.get(i).setSatisfactionSort(i + 1);
		}
		list.add(0, dto);

		return list;
	}

	@Override
	public List<ClassifyDto> classify(Map<String, Object> map) {
		List<SortTreeDto> list = reportFormMapper.sortTree(map);
		List<SortTreeDto> f = list.stream().filter((SortTreeDto d) -> d.getSecSortCode() == null)
				.collect(Collectors.toList());
		List<ClassifyDto> cList = new ArrayList<>();
		// 循环嵌套查询
		for (SortTreeDto fd : f) {
			List<SortTreeDto> s = list.stream()
					.filter((SortTreeDto d) -> fd.getProjectCode().equals(d.getProjectCode())
							&& fd.getFirstSortCode().equals(d.getFirstSortCode()) && d.getSecSortCode() != null
							&& d.getThirdSortCode() == null)
					.collect(Collectors.toList());
			for (SortTreeDto sd : s) {
				List<SortTreeDto> t = list.stream()
						.filter((SortTreeDto d) -> sd.getProjectCode().equals(d.getProjectCode())
								&& sd.getFirstSortCode().equals(d.getFirstSortCode())
								&& sd.getSecSortCode().equals(d.getSecSortCode()) && d.getThirdSortCode() != null
								&& d.getFourthSortCode() == null)
						.collect(Collectors.toList());
				if (t.size() == 0) {
					cList.add(new ClassifyDto(fd, sd, null, null));
				}
				for (SortTreeDto td : t) {
					List<SortTreeDto> fo = list.stream()
							.filter((SortTreeDto d) -> td.getProjectCode().equals(d.getProjectCode())
									&& td.getFirstSortCode().equals(d.getFirstSortCode())
									&& td.getSecSortCode().equals(d.getSecSortCode())
									&& td.getThirdSortCode().equals(d.getThirdSortCode()) && d.getFourthSortCode() != null)
							.collect(Collectors.toList());
					if (fo.size() == 0) {
						cList.add(new ClassifyDto(fd, sd, td, null));
					}
					for (SortTreeDto fod : fo) {
						cList.add(new ClassifyDto(fd, sd, td, fod));
					}
				}
			}
		}

		return cList;
	}

	@Override
	public List<UpgradeDto> upgrade(Map<String, Object> map) {
		List<SortTreeDto> list = reportFormMapper.sortTree(map);
		list = list.stream().filter((SortTreeDto d) -> d.getUpgradeNum() > 0)
				.collect(Collectors.toList());
		List<SortTreeDto> f = list.stream().filter((SortTreeDto d) -> d.getSecSortCode() == null)
				.collect(Collectors.toList());
		List<UpgradeDto> cList = new ArrayList<>();
		// 循环嵌套查询
		for (SortTreeDto fd : f) {
			List<SortTreeDto> s = list.stream()
					.filter((SortTreeDto d) -> fd.getProjectCode().equals(d.getProjectCode())
							&& fd.getFirstSortCode().equals(d.getFirstSortCode()) && d.getSecSortCode() != null
							&& d.getThirdSortCode() == null)
					.collect(Collectors.toList());
			for (SortTreeDto sd : s) {
				List<SortTreeDto> t = list.stream()
						.filter((SortTreeDto d) -> sd.getProjectCode().equals(d.getProjectCode())
								&& sd.getFirstSortCode().equals(d.getFirstSortCode())
								&& sd.getSecSortCode().equals(d.getSecSortCode()) && d.getThirdSortCode() != null
								&& d.getFourthSortCode() == null)
						.collect(Collectors.toList());
				if (t.size() == 0) {
					cList.add(new UpgradeDto(fd, sd, null, null));
				}
				for (SortTreeDto td : t) {
					List<SortTreeDto> fo = list.stream()
							.filter((SortTreeDto d) -> td.getProjectCode().equals(d.getProjectCode())
									&& td.getFirstSortCode().equals(d.getFirstSortCode())
									&& td.getSecSortCode().equals(d.getSecSortCode())
									&& td.getThirdSortCode().equals(d.getThirdSortCode()) && d.getFourthSortCode() != null)
							.collect(Collectors.toList());
					if (fo.size() == 0) {
						cList.add(new UpgradeDto(fd, sd, td, null));
					}
					for (SortTreeDto fod : fo) {
						cList.add(new UpgradeDto(fd, sd, td, fod));
					}
				}
			}
		}

		return cList;
	}

	@Override
	public List<ChannelDto> channel(Map<String, Object> map) {
		return reportFormMapper.channel(map);
	}

	@Override
	public List<CurReportDto> curReport(Map<String, Object> map) {
		List<SortTreeDto> list = reportFormMapper.sortTree(map);
		List<SortTreeDto> f = list.stream().filter((SortTreeDto d) -> d.getSecSortCode() == null)
				.collect(Collectors.toList());
		List<CurReportDto> cList = new ArrayList<>();
		// 循环嵌套查询
		for (SortTreeDto fd : f) {
			List<SortTreeDto> s = list.stream()
					.filter((SortTreeDto d) -> fd.getProjectCode().equals(d.getProjectCode())
							&& fd.getFirstSortCode().equals(d.getFirstSortCode()) && d.getSecSortCode() != null
							&& d.getThirdSortCode() == null)
					.collect(Collectors.toList());
			for (SortTreeDto sd : s) {
				List<SortTreeDto> t = list.stream()
						.filter((SortTreeDto d) -> sd.getProjectCode().equals(d.getProjectCode())
								&& sd.getFirstSortCode().equals(d.getFirstSortCode())
								&& sd.getSecSortCode().equals(d.getSecSortCode()) && d.getThirdSortCode() != null
								&& d.getFourthSortCode() == null)
						.collect(Collectors.toList());
				if (t.size() == 0) {
					cList.add(new CurReportDto(fd, sd, null, null));
				}
				for (SortTreeDto td : t) {
					List<SortTreeDto> fo = list.stream()
							.filter((SortTreeDto d) -> td.getProjectCode().equals(d.getProjectCode())
									&& td.getFirstSortCode().equals(d.getFirstSortCode())
									&& td.getSecSortCode().equals(d.getSecSortCode())
									&& td.getThirdSortCode().equals(d.getThirdSortCode()) && d.getFourthSortCode() != null)
							.collect(Collectors.toList());
					if (fo.size() == 0) {
						cList.add(new CurReportDto(fd, sd, td, null));
					}
					for (SortTreeDto fod : fo) {
						cList.add(new CurReportDto(fd, sd, td, fod));
					}
				}
			}
		}

		return cList;
	}

}
