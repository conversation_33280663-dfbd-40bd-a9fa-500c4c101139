package com.tahoecn.customerservice.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.landray.sso.client.EKPSSOContext;
import com.tahoecn.core.util.JsonUtil;
import com.tahoecn.crypto.SecureUtil;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.model.dto.ResponseDto;
import com.tahoecn.customerservice.model.dto.SysprivDto;
import com.tahoecn.customerservice.model.dto.UserDataPrivDto;
import com.tahoecn.customerservice.model.dto.UserStandardRoleDto;
import com.tahoecn.customerservice.service.RolePrivInfoService;
import com.tahoecn.http.HttpClient;
import com.tahoecn.http.HttpUtil;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;

@Transactional
@Service
public class RolePrivInfoServiceImpl implements RolePrivInfoService {

    @Value("${uc_api_url}")
    private String apiUrl;
    @Value("${uc_sysId}")
    private String sysId;
    @Value("${uc_priv_key}")
    private String privKey;
    
    @Value("${uc_admin_fd_name}")
    private String adminFdName;
    private static final Log log = LogFactory.get();

    @Override
    public List<UserDataPrivDto> getUserDataPrivInfo(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String token = SecureUtil.md5(timestamp + privKey);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("userName", userName);
        paramMap.put("sysId", sysId);
        paramMap.put("token", token);
        paramMap.put("timestamp", timestamp);
        StringBuilder sb = new StringBuilder(apiUrl);
        String url = sb.append("/v1/userDataPriv/list?").append(HttpUtil.mapToUrlParameter(paramMap)).toString();
        log.info("userDataPriv url=" + url);
        String result = HttpClient.httpGet(url);
        log.info("userDataPriv result=" + result);
        if (StringUtils.isNotBlank(result)) {
            ResponseDto<List<UserDataPrivDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
            if (responseDto != null && responseDto.getCode() == 0) {
                List<UserDataPrivDto> resultList = new ArrayList<>();
                for (Object str : responseDto.getResult()) {
                    String info = JsonUtil.convertObjectToJson(str);
                    UserDataPrivDto userDataPrivDto = JsonUtil.convertJsonToBean(info, UserDataPrivDto.class);
                    resultList.add(userDataPrivDto);
                }
                return resultList;
            }
        }
        return null;
    }

    @Override
    public List<UserDataPrivDto> getUserDataPrivInfo() {
        String userName = ThreadLocalUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        return getUserDataPrivInfo(userName);
    }

    @Override
    public List<SysprivDto> getsysprivInfo() {
        String userName = ThreadLocalUtils.getUserName();
        //String userName = "wanghongxin";
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        return getsysprivInfo(userName);
    }

    @Override
    public List<SysprivDto> getsysprivInfo(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String token = SecureUtil.md5(timestamp + privKey);
        StringBuilder sb = new StringBuilder(apiUrl);
        List<SysprivDto> list = new ArrayList<>();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("userName", userName);
        paramMap.put("sysId", sysId);
        paramMap.put("token", token);
        paramMap.put("timestamp", new Long(timestamp).intValue() + "");
        String url = sb.append("/v1/syspriv/list?").append(HttpUtil.mapToUrlParameter(paramMap)).toString();
        log.info("syspriv url=" + url);
        String result = HttpClient.httpGet(url);
        log.info("syspriv result=" + result);
        if (StringUtils.isNotBlank(result)) {
            ResponseDto<List<SysprivDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
            if (responseDto != null && responseDto.getCode() == 0) {
                List<SysprivDto> resultList = new ArrayList<>();
                for (Object str : responseDto.getResult()) {
                    String info = JsonUtil.convertObjectToJson(str);
                    SysprivDto sysprivDto = JsonUtil.convertJsonToBean(info, SysprivDto.class);
                    resultList.add(sysprivDto);
                }
                return resultList;
            }
        }
        return null;
    }

	@Override
	public Integer isAdmin() {
		String userName = ThreadLocalUtils.getUserName();
//        String userName = "wanghongxin";
        if (StringUtils.isBlank(userName)) {
            return 0;
        }
//        List<UserStandardRoleDto> userStandardRoleList = getUserStandardRoleList(userName);
//        if(null != userStandardRoleList && userStandardRoleList.size() > 0) {
//        	for(UserStandardRoleDto usrDto : userStandardRoleList) {
//        		if(adminFdName.equals(usrDto.getFdName())) {
//        			return 1;
//        		}
//        	}
//        }
//        return 0;
        return 1;
	}
	
	@Override
	public List<UserStandardRoleDto> getUserStandardRoleList(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String token = SecureUtil.md5(timestamp + privKey);
        StringBuilder sb = new StringBuilder(apiUrl);
        List<SysprivDto> list = new ArrayList<>();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("userName", userName);
        paramMap.put("sysId", sysId);
        paramMap.put("userId", "");
        paramMap.put("token", token);
        paramMap.put("timestamp", new Long(timestamp).intValue() + "");
        String url = sb.append("/v1/userStandardRole/list?").append(HttpUtil.mapToUrlParameter(paramMap)).toString();
        log.info("syspriv url=" + url);
        String result = HttpClient.httpGet(url);
        log.info("syspriv result=" + result);
        if (StringUtils.isNotBlank(result)) {
            ResponseDto<List<UserStandardRoleDto>> responseDto = JsonUtil.convertJsonToBean(result, ResponseDto.class);
            if (responseDto != null && responseDto.getCode() == 0) {
                List<UserStandardRoleDto> resultList = new ArrayList<>();
                for (Object str : responseDto.getResult()) {
                    String info = JsonUtil.convertObjectToJson(str);
                    UserStandardRoleDto usrDto = JsonUtil.convertJsonToBean(info, UserStandardRoleDto.class);
                    resultList.add(usrDto);
                }
                return resultList;
            }
        }
        return null;
    }
}
