package com.tahoecn.customerservice.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.utils.APPSend;
import com.tahoecn.customerservice.common.utils.OASend;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.common.utils.WeixinApi;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.ProcessService;

/**
 * <p>
 * 标准流程 投诉
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional(readOnly = false)
@Service("sComplaintService")
public class SComplaintServiceImpl extends BaseProcessServiceImpl implements ProcessService {

    @Autowired
    private CsProcessWorkitemService csProcessWorkitemService;

    @Autowired
    private CsFormInstService csFormInstService;

    @Autowired
    private CsUcUserService csUcUserService;
    
    @Autowired
    private OASend oaSend;
    
    @Autowired
    private WeixinApi weixinApi;

    @Autowired
    private APPSend appSend;
    
    private static final String rolePre = "sComplaint-"; //角色前缀

    //operateType : submit,reject
    @Override
    public void process(CsFormInst csForminst,String formId,String firstsortCode,String deptCode,String processStateCode,String handleRecord,String comment,String assignUserId,String assignUserName,String mobile,String operateType) {
    	logger.info("时间 {} 流程开始执行：formId：{},processStateCode:{},assignUserId:{},operateType:{}",new Date(),formId,processStateCode,assignUserId,operateType);
    	CsUcUser curUser = new CsUcUser();
    	
    	if(!StringUtils.isNotBlank(csForminst.getWyFormNo()) || !"draft".equals(csForminst.getProcessStateCode())){
    		curUser = csUcUserService.selectByUsername();
    	}else{
    		curUser.setFdUsername("liwenye");
    		curUser.setFdName(csForminst.getCreateUserName());
    	}
    	if(curUser == null) {
    		curUser = new CsUcUser();
    		curUser.setFdUsername(assignUserId);
    		curUser.setFdName(assignUserName);
    	}

        //已办
        Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
        wrapper.where("form_inst_id={0}",formId).and("process_state_code={0}",processStateCode).and("task_status={0}",new Long(20));
        CsProcessWorkitem doneItem = csProcessWorkitemService.selectOne(wrapper);
        //处理流程跑了。单子没跑的逻辑
        if(doneItem == null ) {
        	logger.error("流程跑了单子没跑 单号：{}",formId);
        	wrapper = new EntityWrapper<CsProcessWorkitem>();
        	wrapper.eq("form_inst_id", formId).eq("task_status", 20L);
        	doneItem = csProcessWorkitemService.selectOne(wrapper);
        	if(doneItem == null) {
        		return;
        	}
        	CsFormInst formInst = new CsFormInst();
        	formInst.setId(Long.valueOf(formId));
        	formInst.setProcessStateCode(doneItem.getProcessStateCode());
        	formInst.setProblemPositionName(String.valueOf(this.businessStateMap.get(doneItem.getProcessStateCode())));
        	formInst.setAssignId(doneItem.getAssignId());
        	formInst.setAssignName(doneItem.getAssignName());
        	csFormInstService.updateById(formInst);
        	return ;
        }
        doneItem.setTaskStatus(new Long(30));//已办
        if(handleRecord!=null && !"".equals(handleRecord)){
            String changeRecord = csForminst.getChangeRecord()==null?"":csForminst.getChangeRecord();
            doneItem.setComment(handleRecord + changeRecord);
        }
        String nextprocessStateCode = "";
        if(operateType.equals("submit")) {
            nextprocessStateCode = (String) this.sComplaintNextMap.get(processStateCode);
            if("draft".equals(processStateCode))
            {
                doneItem.setComment("提交");
            }else if(!"".equals(processStateCode) && processStateCode!=null && processStateCode.equals("toBeAssigned")){
                String changeRecord = csForminst.getChangeRecord()==null?"":csForminst.getChangeRecord();
                doneItem.setComment("分派【" + assignUserName +"】进行处理" + changeRecord);
            }else if(!"".equals(processStateCode) && processStateCode!=null && processStateCode.equals("returnVisit")){
                doneItem.setComment("回访满意度："+ csForminst.getSatisfactionName());
            }
        }
        if(operateType.equals("reject")){
            nextprocessStateCode = (String) this.sComplaintRejectMap.get(processStateCode);
            doneItem.setComment(comment);
        }

        doneItem.setLastUpdateDate(new Date());
        doneItem.setTaskEndTime(new Date());
        if("1".equals(csForminst.getIsOwner()) && "draft".equals(csForminst.getProcessStateCode())){
        	doneItem.setAssignId(curUser.getFdUsername());
        	doneItem.setAssignName(curUser.getFdName());
        }
        if(nextprocessStateCode.indexOf("End")>1 || processStateCode.equals("returnVisit")){
            doneItem.setAssignId(curUser.getFdUsername());
            doneItem.setAssignName(curUser.getFdName());
        }

        //新增 app来源
        doneItem.setThPlatform(ThreadLocalUtils.getPlatform());
        
        csProcessWorkitemService.updateById(doneItem);
        oaSend.setTodoDone(String.valueOf(doneItem.getId()));

        //待办生成
        String nextProcessStateName = (String)this.processStateMap.get(nextprocessStateCode);
        String nextBusinessStateName = (String)this.businessStateMap.get(nextprocessStateCode);
        CsProcessWorkitem item = new CsProcessWorkitem();

        item.setProcessStateCode(nextprocessStateCode);
        item.setProcessStateName(nextProcessStateName);
        item.setFormInstId(new Long(formId));
        item.setCreationDate(new Date());
        item.setTaskStartTime(new Date());
        if(nextprocessStateCode.indexOf("End")<0){
            //前台传过来
            item.setAssignId(assignUserId);
            item.setAssignName(assignUserName);
            item.setTaskStatus(new Long(20));//待办
            item.setAssignMobile(mobile);
        }else{
            item.setAssignId(curUser.getFdUsername());
            item.setAssignName(curUser.getFdName());
            item.setTaskEndTime(addOneSecond(new Date()));
            item.setTaskStatus(new Long(30));//结束
            
            try {
            	if("物业".equals(csForminst.getSource())) {
            		csFormInstService.workOrderNumberQueryWorkOrderStatus(csForminst.getProjectCode(), csForminst.getWyFormNo());
            	}
			} catch (Exception e) {
				e.printStackTrace();
			}
        }
        item.setCreateByUserId(curUser.getFdUsername());
        item.setLastUpdateDate(new Date());
        csProcessWorkitemService.insert(item);
        
        // 处理类型
        String type = "toBeAssigned".equals(processStateCode)?"分派":
        	"toBeAssigned".equals(nextprocessStateCode)?
        	"handle".equals(processStateCode)?"退回":"提交":null;
        
        if(type != null) {
        	// "请处理[****项目]（上一处理人）（提交/分派/退回）的工单：（工单编号）-（工单一级分类）-（工单二级分类）";
        	String title = String.format("请处理[%s项目]%s%s的工单：%s-%s-%s", 
        			csForminst.getProject(),
        			doneItem.getAssignName(),
        			type,
        			csForminst.getFormNo(),
        			csForminst.getFirstSortName(),
        			csForminst.getSecSortName());
            oaSend.sendTodo(Long.valueOf(formId), String.valueOf(item.getId()), title, "{\"LoginName\":\""+item.getAssignId()+"\"}");
            //APP推送消息
            appSend.sendTodo(formId, String.valueOf(item.getId()), title, item.getAssignId());
            
        }
        
        //修改主表状态
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formId);
        CsFormInst form = csFormInstService.selectOne(formWrapper);
        form.setProcessStateCode(nextprocessStateCode);
        form.setProcessStateName(nextBusinessStateName);
        form.setSubmitDate(new Date());
        form.setLastUpdateDate(new Date());
        form.setChangeRecord("");
//        form.setHandleRecord("");
        form.setHandleRecord(handleRecord);//update by chenyy  处理记录存入数据库
        if(operateType.equals("reject") && processStateCode.equals("handle") && nextprocessStateCode.equals("toBeAssigned") ){
            form.setRejectFlag(new Long(1));
        }
        if(operateType.equals("reject") && processStateCode.equals("returnVisit") && nextprocessStateCode.equals("toBeAssigned") ){
            form.setReworkFlag(new Long(1));
        }

        if(nextprocessStateCode.equals("toBeAssigned")){//回写分派人
            form.setAssignId(assignUserId);
            form.setAssignName(assignUserName);
        }
        if(processStateCode.equals("toBeAssigned")){//回写分派时间
            form.setAssignDate(new Date());
        }

        if(nextprocessStateCode.indexOf("End")<0) {
            form.setCurAssigneeId(assignUserId);
            form.setCurAssigneeName(assignUserName);
        }
        csFormInstService.updateById(form);
        String templateCode = "";
        templateCode = rolePre+nextprocessStateCode;
        //super.sendSms(templateCode,form,assignUserName,mobile);
        if("returnVisit".equals(processStateCode) && operateType.equals("reject")) {
            try {
                this.updrade(form);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        if("returnVisit".equals(nextprocessStateCode) && StringUtils.isNotBlank(form.getOpenId()) && "1".equals(form.getIsOwner())) {
        	String key1 = form.getCustomerDemand().length()>100?form.getCustomerDemand().substring(0, 100)+"...":form.getCustomerDemand();
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        	weixinApi.sendTemplate(form.getOpenId(), formId, key1, sdf.format(form.getLastUpdateDate()));
        }
        logger.info("时间 {} 流程结束执行：formId：{},processStateCode:{},assignUserId:{},operateType:{}",new Date(),formId,form.getProcessStateCode(),assignUserId,operateType);
    }

    @Transactional(readOnly = true)
    @Override
    public List getUserByRole(String operateType,Long formInstId) {
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formInstId);
        CsFormInst form = csFormInstService.selectOne(formWrapper);

        String nextprocessStateCode = "";
        if(operateType.equals("submit")) {
            nextprocessStateCode = (String) this.sComplaintNextMap.get(form.getProcessStateCode());
        }else{
            nextprocessStateCode = (String) this.sComplaintRejectMap.get(form.getProcessStateCode());
        }
        String roleCode = rolePre+nextprocessStateCode;
        List Users = this.getUserByRoleCode(roleCode,form.getProjectCode());
        return Users;
    }

    @Override
    public void updrade(CsFormInst csForminst) throws Exception {
        //投诉直接升到区域
        String templateCode = "";
        Long upgradeLevel = csForminst.getUpgradeLevel()==null?new Long(1):csForminst.getUpgradeLevel();
        upgradeLevel = upgradeLevel + new Long(1);
        //3集团就不升级
        if(upgradeLevel>3){
            return;
        }
        templateCode = rolePre+"upgrade-"+upgradeLevel;
        super.upgrade(csForminst,templateCode,upgradeLevel);
    }
}
