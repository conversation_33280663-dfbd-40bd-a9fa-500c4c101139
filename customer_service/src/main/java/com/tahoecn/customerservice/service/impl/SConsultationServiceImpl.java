package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.tahoecn.customerservice.common.utils.APPSend;
import com.tahoecn.customerservice.common.utils.OASend;
import com.tahoecn.customerservice.common.utils.ThreadLocalUtils;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsProcessWorkitem;
import com.tahoecn.customerservice.model.CsUcUser;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.CsProcessWorkitemService;
import com.tahoecn.customerservice.service.CsUcUserService;
import com.tahoecn.customerservice.service.ProcessService;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 标准流程 咨询
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional(readOnly = false)
@Service("sConsultationService")
public class SConsultationServiceImpl extends BaseProcessServiceImpl implements ProcessService {

    @Autowired
    private CsProcessWorkitemService csProcessWorkitemService;

    @Autowired
    private CsFormInstService csFormInstService;

    @Autowired
    private CsUcUserService csUcUserService;
    
    @Autowired
    private OASend oaSend;

    @Autowired
    private APPSend appSend;
    
    private static final String rolePre = "sConsultation-";

    //operateType : submit,reject
    @Override
    public void process(CsFormInst csForminst,String formId,String firstsortCode,String deptCode,String processStateCode,String handleRecord,String comment,String assignUserId,String assignUserName,String mobile,String operateType) {
    	logger.info("时间 {} 流程开始执行：formId：{},processStateCode:{},assignUserId:{},operateType:{}",new Date(),formId,processStateCode,assignUserId,operateType);
    	CsUcUser curUser = new CsUcUser();
    	
    	if(!StringUtils.isNotBlank(csForminst.getWyFormNo()) || !"draft".equals(csForminst.getProcessStateCode())){
    		curUser = csUcUserService.selectByUsername();
    	}else{
    		curUser.setFdUsername("liwenye");
    		curUser.setFdName(csForminst.getCreateUserName());
    	}

        //已办
        Wrapper<CsProcessWorkitem> wrapper = new EntityWrapper<CsProcessWorkitem>();
        wrapper.where("form_inst_id={0}",formId).and("process_state_code={0}",processStateCode).and("task_status={0}",new Long(20));
        CsProcessWorkitem doneItem = csProcessWorkitemService.selectOne(wrapper);
        //处理流程跑了。单子没跑的逻辑
        if(doneItem == null ) {
        	logger.error("流程跑了单子没跑 单号：{}",formId);
        	wrapper = new EntityWrapper<CsProcessWorkitem>();
        	wrapper.eq("form_inst_id", formId).eq("task_status", 20L);
        	doneItem = csProcessWorkitemService.selectOne(wrapper);
        	if(doneItem == null) {
        		return;
        	}
        	CsFormInst formInst = new CsFormInst();
        	formInst.setId(Long.valueOf(formId));
        	formInst.setProcessStateCode(doneItem.getProcessStateCode());
        	formInst.setProblemPositionName(String.valueOf(this.businessStateMap.get(doneItem.getProcessStateCode())));
        	formInst.setAssignId(doneItem.getAssignId());
        	formInst.setAssignName(doneItem.getAssignName());
        	csFormInstService.updateById(formInst);
        	return ;
        }
        
        doneItem.setTaskStatus(new Long(30));//已办
        if(handleRecord!=null && !"".equals(handleRecord)){
            doneItem.setComment(handleRecord);
        }
        String nextprocessStateCode = "";
        if(operateType.equals("submit")) {
            nextprocessStateCode = (String) this.sConsultationNextMap.get(processStateCode);
            if("draft".equals(processStateCode))
            {
                doneItem.setComment("提交");
            }else if(!"".equals(processStateCode) && processStateCode!=null && processStateCode.equals("toBeAssigned")){
                doneItem.setComment("分派【" + assignUserName +"】进行处理");
            }else if(!"".equals(processStateCode) && processStateCode!=null && processStateCode.equals("returnVisit")){
                doneItem.setComment("回访满意度："+ csForminst.getSatisfactionName());
            }
        }
        if(operateType.equals("reject")){
            nextprocessStateCode = (String) this.sConsultationRejectMap.get(processStateCode);
            doneItem.setComment(comment);
        }

        doneItem.setLastUpdateDate(new Date());
        doneItem.setTaskEndTime(new Date());
        if("1".equals(csForminst.getIsOwner()) && "draft".equals(csForminst.getProcessStateCode())){
        	doneItem.setAssignId(ThreadLocalUtils.getUserName());
        	doneItem.setAssignName(ThreadLocalUtils.getRealName());
        }
        if(nextprocessStateCode.indexOf("End")>1 || processStateCode.equals("returnVisit")){
            doneItem.setAssignId(curUser.getFdUsername());
            doneItem.setAssignName(curUser.getFdName());
        }
        //新增 app来源
        doneItem.setThPlatform(ThreadLocalUtils.getPlatform());
        
        csProcessWorkitemService.updateById(doneItem);
//        oaSend.setTodoDone(String.valueOf(doneItem.getId()));

        //待办生成
        String nextprocessStateName = (String)this.processStateMap.get(nextprocessStateCode);
        String nextBusinessStateName = (String)this.businessStateMap.get(nextprocessStateCode);
        CsProcessWorkitem item = new CsProcessWorkitem();

        item.setProcessStateCode(nextprocessStateCode);
        item.setProcessStateName(nextprocessStateName);
        item.setFormInstId(new Long(formId));
        item.setCreationDate(new Date());
        item.setTaskStartTime(new Date());
        if(nextprocessStateCode.indexOf("End")<0){
            //前台传过来
            item.setAssignId(assignUserId);
            item.setAssignName(assignUserName);
            item.setTaskStatus(new Long(20));//待办
            item.setAssignMobile(mobile);
        }else{
            item.setAssignId(curUser.getFdUsername());
            item.setAssignName(curUser.getFdName());
            item.setTaskEndTime(this.addOneSecond(new Date()));
            item.setTaskStatus(new Long(30));//结束
        }
        item.setCreateByUserId(curUser.getFdUsername());
        item.setLastUpdateDate(new Date());
        csProcessWorkitemService.insert(item);
        
        // 处理类型
        String type = "handle".equals(nextprocessStateCode)?"提交":null;
        
//        if(type != null) {
//        	// "请处理[****项目]（上一处理人）（提交/分派/退回）的工单：（工单编号）-（工单一级分类）-（工单二级分类）";
//        	String title = String.format("请处理[%s项目]%s%s的工单：%s-%s-%s", 
//        			csForminst.getProject(),
//        			doneItem.getAssignName(),
//        			type,
//        			csForminst.getFormNo(),
//        			csForminst.getFirstSortName(),
//        			csForminst.getSecSortName());
//            oaSend.sendTodo(Long.valueOf(formId), String.valueOf(item.getId()), title, "{\"LoginName\":\""+item.getAssignId()+"\"}");
//            //APP推送消息
//            appSend.sendTodo(formId, String.valueOf(item.getId()), title, item.getAssignId());
//            //
//        }

        //修改主表状态
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formId);
        CsFormInst form = csFormInstService.selectOne(formWrapper);
        form.setProcessStateCode(nextprocessStateCode);
        form.setProcessStateName(nextBusinessStateName);
        form.setSubmitDate(new Date());
        form.setLastUpdateDate(new Date());
        if(nextprocessStateCode.equals("toBeAssigned")){//回写分派人
            form.setAssignId(assignUserId);
            form.setAssignName(assignUserName);
        }
        if(processStateCode.equals("toBeAssigned")){//回写分派人时间
            form.setAssignDate(new Date());
        }
        if(nextprocessStateCode.indexOf("End")<0) {
            form.setCurAssigneeId(assignUserId);
            form.setCurAssigneeName(assignUserName);
        }
        csFormInstService.updateById(form);
        logger.info("时间 {} 流程结束执行：formId：{},processStateCode:{},assignUserId:{},operateType:{}",new Date(),formId,form.getProcessStateCode(),assignUserId,operateType);
    }

    @Transactional(readOnly = true)
    @Override
    public List getUserByRole(String operateType, Long formInstId) {
        Wrapper<CsFormInst> formWrapper = new EntityWrapper<CsFormInst>();
        formWrapper.where("id={0}",formInstId);
        CsFormInst form = csFormInstService.selectOne(formWrapper);

        String nextprocessStateCode = "";
        if(operateType.equals("submit")) {
            nextprocessStateCode = (String) this.sConsultationNextMap.get(form.getProcessStateCode());
        }else{
            nextprocessStateCode = (String) this.sConsultationRejectMap.get(form.getProcessStateCode());
        }
        String roleCode = rolePre+nextprocessStateCode;
        List Users = this.getUserByRoleCode(roleCode,form.getProjectCode());
        return Users;
    }

    @Override
    public void updrade(CsFormInst csFormInst) {

    }
}
