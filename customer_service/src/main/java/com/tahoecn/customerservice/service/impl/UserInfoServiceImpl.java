package com.tahoecn.customerservice.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.mapper.UserInfoMapper;
import com.tahoecn.customerservice.model.UserInfo;
import com.tahoecn.customerservice.service.UserInfoService;

/**
 * <p>
 * 用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-10-10
 */
@Transactional
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Override
    public void synUserInfo(UserInfo userInfo) {
        UserInfo userInfoSelect = new UserInfo();
        userInfoSelect.setSid(userInfo.getSid());
        UserInfo userInfoOne = userInfoMapper.selectOne(userInfoSelect);
        if (userInfoOne != null) {
            userInfo.setId(userInfoOne.getId());
            userInfo.setGmtCreate(userInfoOne.getGmtCreate());
            userInfoMapper.updateById(userInfo);
        } else {
            userInfoMapper.insert(userInfo);
        }
    }
}

