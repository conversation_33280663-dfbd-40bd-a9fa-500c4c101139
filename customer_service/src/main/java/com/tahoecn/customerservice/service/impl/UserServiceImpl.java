package com.tahoecn.customerservice.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.tahoecn.customerservice.common.utils.ModelValidatorUtil;
import com.tahoecn.customerservice.mapper.UserMapper;
import com.tahoecn.customerservice.model.User;
import com.tahoecn.customerservice.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 * <AUTHOR>
 * @since 2018-08-16
 */
@Transactional
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Override
    public List<Map<String, Object>> getUsersByXml() {
        return this.baseMapper.selectListByXml();
    }

    @Override
    public List<Map<String, Object>> getUsersByAnnotation() {
        return this.baseMapper.selectListByAnnotation();
    }

    /**
     * 编程式验证方式
     * @param users
     * @return
     */
    @Override
    public int filterUser(List<User> users) {
        System.out.println("=============================");

        int iCount = 0;
        for(User user : users){
            List<String> list = ModelValidatorUtil.modelValidator(user);
            if(list.size()!=0){
                System.out.println(user);
            }else{
                iCount++;
            }
        }

        System.out.println(iCount);
        return users.size();
    }
}
