package com.tahoecn.customerservice.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.tahoecn.customerservice.model.CsFormInst;
import com.tahoecn.customerservice.model.CsUserRole;
import com.tahoecn.customerservice.service.CsFormInstService;
import com.tahoecn.customerservice.service.ProcessService;

/**
 * <p>
 * 流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-15
 */
@Transactional
@Service("wyProcessService")
public class WyProcessServiceImpl extends BaseProcessServiceImpl implements ProcessService {

	@Autowired
	private CsFormInstService csFormInstService;

	@Override
	public void process(CsFormInst csForminst, String formId, String firstsortCode, String deptCode,
			String processStateCode, String comment, String handleRecord, String assignUserId, String assignUserName,
			String mobile, String operateType) {
		try {			
			if ("1".equals(csForminst.getPublicArea())) {
				csFormInstService.sectorNewspaperSubmission(csForminst);
			} else {
				csFormInstService.indoorNewspaperSubmission(csForminst);
			}
		} catch (Exception e) {
			throw new RuntimeException(e.getMessage());
		}

	}

	@Override
	public List getUserByRole(String operateType, Long formInstId) {
		List<CsUserRole> list = Lists.newArrayList();
		list.add(new CsUserRole());
		return list;
	}

	@Override
	public void updrade(CsFormInst csFormInst) {

	}
}
