/**
 * 
 */
package com.tahoecn.customerservice.utils;

import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.tahoecn.customerservice.model.CsSendSmsLog;
import com.tahoecn.customerservice.service.CsSendSmsLogService;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
/**
 * <AUTHOR>
 * @version
 * @since 2018年6月26日 下午4:45:38
 */
@Component("smsSender")
public class SmsSender {
	/*private static final String CONTENT_PREFIX = "【泰禾集团】";
	private  Log _log = LogFactory.get();
	@Value("${sms_uri}")
	private  String uri;
	@Value("${sms_account}")
	private  String account;
	@Value("${sms_pwd}")
	private  String pwd;
	@Value("${sms_needstatus}")
	private  String needstatus;
	@Value("${sms_product}")
	private  String product;
	@Value("${sms_extno}")
	private  String extno;
	@Autowired
	private  CsSendSmsLogService csSendSmsLogService;
	*//**
	 * <AUTHOR>
	 * @param mobiles 139XXXXXXXX,150XXXXXXXX
	 * @param content 短信信息
	 * @param userName 人员姓名
	 *//*
	
	public void sendSms(String mobiles, String content , String userName) {

        if (!StringUtils.startsWith(content, CONTENT_PREFIX)) {
            content = CONTENT_PREFIX + content;
        }
        if (StringUtils.isBlank(mobiles)) {
            _log.error("mobiles is null");
            return;
        }
        final String contentnew = content;
        ThreadUtil.execute(new Runnable() {
            @Override
            public void run() {
                //csSendSmsLogService
                try {
                    String rt = "";
                    _log.info(uri+account+pwd+
                            mobiles+ contentnew+ needstatus+ product+
                            extno);
                    if (StringUtils.contains(",", mobiles)) {
                        rt = batchSend(uri, account, pwd,
                                mobiles, contentnew, needstatus, product,
                                extno,userName);
                    } else {
                        rt = send(uri, account, pwd,
                                mobiles, contentnew, needstatus, product,
                                extno,userName);
                    }
                    System.out.println(rt);
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    _log.error("mobiles:"+mobiles+",内容contentnew:"+contentnew);
//                    csSendSmsLog.setSendResult("发送异常！");
//                    csSendSmsLogService.saveCsSendSmsLog(csSendSmsLog);
                }

            }
        });
    }
    public String send(String uri, String account, String pswd, String mobiles, String content,
    		String needstatus, String product, String extno, String userName) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("account", account);
        params.put("pswd", pswd);
        params.put("mobile", mobiles);
        params.put("needstatus", String.valueOf(needstatus));
        params.put("msg", URLEncoder.encode(content, "UTF-8"));
        params.put("product", product);
        params.put("extno", extno);
        //String result = HttpClient.httpPost(uri+"HttpSendSM", params);
        String result = HttpUtil.post(uri+"HttpSendSM", params);
        CsSendSmsLog csSendSmsLog = new CsSendSmsLog();
    	csSendSmsLog.setUserName(userName);
    	csSendSmsLog.setCreateDate(new Date());
    	csSendSmsLog.setSendMobiles(mobiles);
    	csSendSmsLog.setContentDesc(content);
    	csSendSmsLog.setSendResult(result);
        csSendSmsLogService.saveCsSendSmsLog(csSendSmsLog);
        return result;
    }
    
    public String batchSend(String uri, String account, String pswd, String mobiles, String content,
    		String needstatus, String product, String extno, String userName) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("account", account);
        params.put("pswd", pswd);
        params.put("mobile", mobiles);
        params.put("needstatus", String.valueOf(needstatus));
        params.put("msg", URLEncoder.encode(content, "UTF-8"));
        params.put("product", product);
        params.put("extno", extno);
        //String result = HttpClient.httpPost(uri+"HttpSendSM", params);
        String result = HttpUtil.post(uri+"HttpBatchSendSM", params);
        CsSendSmsLog csSendSmsLog = new CsSendSmsLog();
    	csSendSmsLog.setUserName(userName);
    	csSendSmsLog.setCreateDate(new Date());
    	csSendSmsLog.setSendMobiles(mobiles);
    	csSendSmsLog.setContentDesc(content);
    	csSendSmsLog.setSendResult(result);
        csSendSmsLogService.saveCsSendSmsLog(csSendSmsLog);
        return result;
    }*/
}
