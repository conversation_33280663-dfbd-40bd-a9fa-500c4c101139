######################################
##  客服系统 develop
######################################
spring.application.name=customerservice
server.port=8286
spring.jackson.time-zone=GMT+8

#Redis
spring.redis.host=************
spring.redis.port=6379
spring.redis.database=2
spring.redis.password=Tyzq@123!

# MULTIPART (MultipartProperties)
file.relativePath=/uploadfiles/**
file.physicalPath=/mnt/kefu/uploadfiles_nas/kefutest/
spring.servlet.multipart.max-file-size=10240MB
spring.servlet.multipart.max-request-size=10240MB

#fileUpload 文件上传路径
root_path=/opt/attachments

#Durid
spring.datasource.core.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.core.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.core.url = ************************************************************************************************************************************************************
spring.datasource.core.username = root
spring.datasource.core.password = fk123456

##Durid2
#spring.datasource.dm.driver-class-name=net.sourceforge.jtds.jdbc.Driver
#spring.datasource.dm.url=***************************************************************************************
#spring.datasource.dm.username=query_kf
#spring.datasource.dm.password=123456

##Durid2
#spring.datasource.my.driver-class-name=net.sourceforge.jtds.jdbc.Driver
#spring.datasource.my.url=****************************************************************************************
#spring.datasource.my.username=query_kf
#spring.datasource.my.password=123456
#
##Durid2
#spring.datasource.wy.driver-class-name=net.sourceforge.jtds.jdbc.Driver
#spring.datasource.wy.url=***************************************************************************************
#spring.datasource.wy.username=Kfdl
#spring.datasource.wy.password=kfdl1970

spring.datasource.druid.initial-size=10
spring.datasource.druid.max-active=100
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-wait=10000
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.stat-view-servlet.enabled=true

bind_host=
uc_api_url=http://ucapitest.tahoecndemo.com:8080
uc_sysId=KEFU
uc_priv_key=luanqibazaod_zhend_buzhid

qx_uc_api_url=http://ucapi.tahoecn.com:8080
qx_uc_sysId=KEFU
qx_uc_priv_key=11anqibakeod_zhend_buzhfu


uc_api_url_userlist=http://ucapitest.tahoecndemo.com:8080/v3/user/list
uc_api_url_orglist=http://ucapitest.tahoecndemo.com:8080/v3/org/list

#ekp
ekp_url=http://oa.tahoecndemo.com:8080/ekp/sys/webservice/sysNotifyTodoWebService?wsdl
ekp_link_url=http://csm.tahoecndemo.com:8080/#/customerService/addReport

# erp wuye
wuye_api_url=http://***********:9999/
wuye_token=20181114THJCSJCX
#sms
sms_uri=http://hy.qixinhl.com/msg/
sms_account=taihe99
sms_pwd=taihe@123
sms_needstatus=true
sms_product=459367
sms_extno=001
sms_switch=off
max_give_score=5
sso_url=http://oa.tahoecndemo.com:8080/ekp/login.jsp?THRedirectUrl=http://cs-local.tahoecndemo.com:8181

#cfiwy
cfiwy_uri=http://***********:9999/Twinterface/Service/Service.ashx
cfiwy_class=IncidentAcceptManager_th
cfiwy_token=20171024IncidentAcceptManager
cfiwy_command1=IncidentAccept_Property_20181211
cfiwy_command2=IncidentAccept_20181211
cfiwy_command3=EditIncidentState

#uc uc_admin_fd_name
uc_admin_fd_name=客服系统管理员(授权)

#qd
# 千丁地址
cfiqd_uri=https://qagw.qdingnet.com/tahoe/task/complete
# 千丁租户id 
qd_organ_id=20190322150933268ca
# 千丁秘钥
qd_key=luanqibazaod_zhend_buzhid
#回调千丁秘钥
qd_back_key=172891D331093BE1

#app
#生产
#APP_KEY=3fbc05f3770921f54f9839be
#MASTER_SECRET=68b19b65b977e62fbf319abe
#测试1
APP_KEY=705bb434a9f454b8907ab383
MASTER_SECRET=fb42c471dbf8d2f0264aeed5

sso.loginUrl=http://8.130.39.58:9988/login?sysId=KEFU
sso.logoutUrl=http://8.130.39.58:9988/logout
sso.ucwebUrl=http://8.130.39.58:8080/
sso.cookieDomain=8.130.39.58
sso.ucssowebUrl=http://8.130.39.58:9988

WX_APP_ID=wx15a59b1748bbb1fc
WX_SECRET=f507540469181b6d0afd88b1e0e6f809
WX_TEMPLATE_ID=XKaUk73nmTNv1P09YXi9zHyduHqwHOkuqxhiWumzd58
