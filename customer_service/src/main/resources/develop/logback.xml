<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <jmxConfigurator />

    <appender name="STD" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss}] %-5level %logger[%line] - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/catelina.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover 保存历史记录到这个文件夹一日起为后缀 -->
            <fileNamePattern>../logs/catelina.log.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- keep 10 days' worth of history -->
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss}] %-5level %logger[%line] - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <root level="INFO">
        <appender-ref ref="STD" />
        <appender-ref ref="FILE" />
    </root>
    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache" level="INFO" />
    <logger name="com.alibaba" level="INFO" />
    <logger name="com.tahoecn" level="DEBUG" />
    <logger name="org.mybatis" level="INFO" />
    
</configuration>