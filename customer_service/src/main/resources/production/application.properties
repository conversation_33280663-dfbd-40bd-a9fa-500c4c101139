######################################
##  客服系统 properties
######################################
spring.application.name=customerservice
server.port=8181
spring.jackson.time-zone=GMT+8

#Redis
spring.redis.database=150
spring.redis.sentinel.master=redis
spring.redis.sentinel.nodes=************:26379,************:26379
spring.redis.password=!~*$P#%m!@0%#)4&i!^De;r

# MULTIPART (MultipartProperties)
file.relativePath=/opt/rh/apache-tomcat-8.5.35/webapps/uploadfiles/**
file.physicalPath=/opt/rh/apache-tomcat-8.5.35/webapps/uploadfiles
spring.servlet.multipart.max-file-size=10240MB
spring.servlet.multipart.max-request-size=10240MB

#fileUpload 文件上传路径
root_path=/opt/data

#Durid
spring.datasource.core.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.core.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.core.url = ***************************************************************************************************
spring.datasource.core.username = thkf
spring.datasource.core.password = THKF_2018

#Durid2
#spring.datasource.dm.driver-class-name=net.sourceforge.jtds.jdbc.Driver
#spring.datasource.dm.url=*********************************************************************************
#spring.datasource.dm.username=query_kf
#spring.datasource.dm.password=thkf12345

#Durid2
spring.datasource.my.driver-class-name=net.sourceforge.jtds.jdbc.Driver
spring.datasource.my.url=*********************************************************************************
spring.datasource.my.username=query_kf
spring.datasource.my.password=thkf12345

#Durid2
spring.datasource.wy.driver-class-name=net.sourceforge.jtds.jdbc.Driver
spring.datasource.wy.url=***************************************************************************************
spring.datasource.wy.username=Kfdl
spring.datasource.wy.password=kfdl1970

spring.datasource.druid.initial-size=10
spring.datasource.druid.max-active=100
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-wait=10000
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.stat-view-servlet.enabled=true

bind_host=************
uc_api_url=http://ucapi.tahoecn.com:8080
uc_sysId=KEFU
uc_priv_key=11anqibakeod_zhend_buzhfu

qx_uc_api_url=http://ucapi.tahoecn.com:8080
qx_uc_sysId=KEFU
qx_uc_priv_key=11anqibakeod_zhend_buzhfu

uc_api_url_userlist=http://ucapi.tahoecn.com:8080/v3/user/list
uc_api_url_orglist=http://ucapi.tahoecn.com:8080/v3/org/list

#ekp
ekp_url=http://oa.tahoecn.com/ekp/sys/webservice/sysNotifyTodoWebService?wsdl
ekp_link_url=http://csm.tahoecn.com:8080/#/customerService/addReport

# erp wuye
wuye_api_url=http://wyerp.tahoecn.com
wuye_token=20181114THJCSJCX
#sms
sms_uri=http://hy.qixinhl.com/msg/
sms_account=taihe99
sms_pwd=taihe@123
sms_needstatus=true
sms_product=459367
sms_extno=001
sms_switch=on
max_give_score=5
sso_url=http://oa.tahoecn.com/ekp/login.jsp?THRedirectUrl=http://csm.tahoecn.com:8080

#cfiwy
cfiwy_uri=https://wyerp.tahoecn.com:9999/Twinterface/service/service.ashx
cfiwy_class=IncidentAcceptManager_th
cfiwy_token=20171024IncidentAcceptManager
cfiwy_command1=IncidentAccept_Property_20181211
cfiwy_command2=IncidentAccept_20181211
cfiwy_command3=EditIncidentState

#uc uc_admin_fd_name
uc_admin_fd_name=客服系统管理员(授权)

#qd
# 千丁地址
cfiqd_uri=https://gw.qdingnet.com/qdp-rosetta-app/tahoe/task/complete
# 千丁租户id 
qd_organ_id=20190322150933268ca
# 千丁秘钥
qd_key=dwanqibazaod_zhend_buzhiq
#回调千丁秘钥
qd_back_key=172891D331093BE1

#app
APP_KEY=3fbc05f3770921f54f9839be
MASTER_SECRET=68b19b65b977e62fbf319abe

sso.loginUrl=http://8.130.39.58:9988/login?sysId=KEFU
sso.logoutUrl=http://8.130.39.58:9988/logout
sso.ucwebUrl=http://8.130.39.58:8080/
sso.cookieDomain=8.130.39.58
sso.ucssowebUrl=http://8.130.39.58:9988

WX_APP_ID=wxe74d07c39a45b592
WX_SECRET=c44a3d8ae434e60cd74587ca976d8381
WX_TEMPLATE_ID=MlPrdDPFSeZEDvOg4K1CgT9czjFQeqAeJ_LBynIB_j0
