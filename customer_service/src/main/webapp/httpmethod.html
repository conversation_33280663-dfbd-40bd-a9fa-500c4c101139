<html lang="zh-cn">
<head>
	<meta charset="utf-8"/>
	<title>http1.1 method</title>
	<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
	<script type="application/javascript">
        $(document).ready(function(){

            //使用put方法请求
            $("#put").click(function(){
                var text = $.ajax({
								type: "put",
								url: "/http/method/put",
								data: "name=put_tahoe",
								async: false
							}).responseText;
                alert(text);
            });

            //使用post方法请求
            $("#post").click(function(){
                var text = $.ajax({
                    type: "post",
                    url: "/http/method/post",
                    data: "name=post_tahoe",
                    async: false
                }).responseText;
                alert(text);
            });

            //使用get方法请求
            $("#get").click(function(){
                var text = $.ajax({
                    type: "get",
                    url: "/http/method/get",
                    data: "name=get_tahoe",
                    async: false
                }).responseText;
                alert(text);
            });

            //使用delete方法请求
            $("#delete").click(function(){
                var text = $.ajax({
                    type: "delete",
                    url: "/http/method/delete",
                    async: false
                }).responseText;
                alert(text);
            });

            //使用head方法请求
            $("#head").click(function(){
                $.ajax({
                    type: "head",
                    url: "/http/method/head",
                    async: true,
                    success: function(message, text, response) {
                        alert(response.getResponseHeader("Content-Type"));
                    }
                });
            });


        });
	</script>
</head>
<body>

<h1>http1.1 method</h1>

POST方法用来传输实体的主体，PUT方法用来传输文件，自身不带验证机制。<br/>
这两个方法看起来都是讲一个资源附加到服务器端的请求，但其实是不一样的。一些狭窄的意见认为，POST方法用来创建资源，而PUT方法则用来更新资源。<br/>
这个说法本身没有问题，但是并没有从根本上解释了二者的区别。事实上，它们最根本的区别就是：POST方法不是幂等的，而PUT方法则有幂等性。<br/><br/>
DELETE方法是没有参数的，根据rest风格约定URL就是唯一资源，使用DELETE按照约定服务器端收到请求后执行删除资源操作
<br/><br/>
HEAD和GET本质是一样的，区别在于HEAD不含有呈现数据，而仅仅是HTTP头信息。有的人可能觉得这个方法没什么用，其实不是这样的。想象一个业务情景：欲判断某个资源是否存在，我们通常使用GET，但这里用HEAD则意义更加明确。

<br/><br/>
<input type="button" name="put" id="put" value="put">
<input type="button" name="post" id="post" value="post">
<input type="button" name="get" id="get" value="get">
<input type="button" name="delete" id="delete" value="delete">
<input type="button" name="head" id="head" value="head">

<hr/>


</body>
</html>