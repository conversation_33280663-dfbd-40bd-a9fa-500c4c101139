<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>springboot mvc</title>
</head>
<body>
<h1>spring boot mvc</h1>
<hr />
<p><a href="/info">在controller层的validation演示，使用BindingResult实现</a></p>
<p><a href="/filteruser">非Controller层的validation演示,使用ValidatorFactory实现</a></p>
<p><a href="/excel">Excel导入导出测试</a></p>
<p><a href="httpmethod.html">http1.1方法测试</a></p>
</body>
</html>