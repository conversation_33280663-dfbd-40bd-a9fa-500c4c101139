package com.tahoecn.customerservice;

import cn.afterturn.easypoi.entity.ImageEntity;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.inter.IExcelDataHandler;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.core.util.StrUtil;
import com.tahoecn.customerservice.common.utils.ExcelUtil;
import com.tahoecn.customerservice.model.User;
import com.tahoecn.customerservice.model.excelDTO.OrderGoods;
import com.tahoecn.customerservice.model.excelDTO.OrderInfo;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class EasyPioSample {

    private final String export_maps = "d:/export_maps.xls";
    private final String export_users = "d:/export_users.xls";
    private final String export_orders = "d:/export_orders.xls";
    private final String local_image = "d:/tahoe-logo.png";
    private final String export_template = "d:/export_template.xls";
    private final String export_excel = "d:/export_excel.xls";

    /**
     * 简单的EXCEL导出示例
     */
    @Test
    public void exportExcel_01(){
        List<User> users = new ArrayList();
        for(int i=0;i<10;i++){
            User user = new User();
            user.setId(i);
            user.setAge(100+i);
            user.setCreatedTime(new Date());
            user.setStatus(1);
            user.setType(1);
            user.setName("USER_ID_"+i);
            users.add(user);
        }

        try {
            ExcelUtil.listToExcel(users,"泰禾流程信息部",FileUtils.openOutputStream(new File(this.export_users)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 属性中含有集合的实体对象，导出EXCEL示例
     * 实体对象中有图片属性，目前只能本地图片能存到EXCEL中
     */
    @Test
    public void exportExcel_02(){
        DecimalFormat df=new DecimalFormat("0.00");
        List<OrderInfo> orders = new ArrayList();
        Random random = new Random();
        for(int i=0;i<10;i++){
            String orderId = "ORDER_ID_"+i;

            OrderInfo info = new OrderInfo();

            info.setCreateTime(new Date());
            info.setOrderId(orderId);
            info.setUserId(random.nextInt(100));

            double amount = 0d;
            List<OrderGoods> list = new ArrayList();
            for(int j=0;j<random.nextInt(10);j++){
                OrderGoods goods = new OrderGoods();
                goods.setGoodsId("GOODS_ID_"+random.nextInt(200));
                goods.setGoodsNum(random.nextInt(10));

                double price = random.nextDouble();
                amount += price;
                goods.setPrice(Double.valueOf(df.format(price)));
                goods.setGoodsPic(this.local_image);//读取本地图片OK
                list.add(goods);
            }

            info.setAmount(Double.valueOf(df.format(amount)));
            info.setGoods(list);
            orders.add(info);
        }

        try {
            ExcelUtil.listToExcel(orders,"泰禾流程信息部",FileUtils.openOutputStream(new File(this.export_orders)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 基于Map的的EXCEL导出示例(使用ExcelExportEntity建立EXCEL中的Column与MAP中的Key关系)
     * 实体对象中有图片属性，目前只能本地图片能存到EXCEL中
     * 可以不用创建对应的VO对象
     */
    @Test
    public void exportExcel_03(){
        List<ExcelExportEntity> entity = new ArrayList();
        ExcelExportEntity attribute;

        attribute = new ExcelExportEntity();
        attribute.setWidth(20);
        attribute.setName("手机型号");
        attribute.setKey("PhoneId");
        entity.add(attribute);

        attribute = new ExcelExportEntity();
        attribute.setWidth(20);
        attribute.setName("手机标记");
        attribute.setKey("PhoneTags");
        entity.add(attribute);

        attribute = new ExcelExportEntity();
        attribute.setWidth(25);
        attribute.setName("手机照片");
        attribute.setKey("PhoneImage");
        attribute.setExportImageType(1);
        attribute.setType(2);
        entity.add(attribute);

        attribute = new ExcelExportEntity();
        attribute.setWidth(20);
        attribute.setName("销售价格");
        attribute.setKey("PhonePrice");
        attribute.setType(10);
        attribute.setNumFormat("#.##");
        entity.add(attribute);

        attribute = new ExcelExportEntity();
        attribute.setWidth(30);
        attribute.setName("销售日期");
        attribute.setKey("PhoneDate");
        attribute.setFormat("yyyy-MM-dd HH:mm:ss");
        attribute.setDatabaseFormat("yyyyMMddHHmmss");
        entity.add(attribute);

        List<Map<String,Object>> datas = new ArrayList();
        Random random = new Random();
        for(int i=0;i<10;i++) {
            Map<String, Object> map = new HashMap();
            map.put("PhoneId","PHONE_ID_"+i);
            map.put("PhoneTags","Tags_"+i);
            map.put("PhoneImage",this.local_image);
            map.put("PhonePrice",random.nextDouble()*100d+random.nextInt(10000));
            map.put("PhoneDate",new Date());
            datas.add(map);
        }

        try {
            ExcelUtil.listToExcel(entity,datas,"泰禾流程信息部",FileUtils.openOutputStream(new File(this.export_maps)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }










    /**
     * 从EXCEL文件到LIST简单的导入功能（一一对应的）
     */
    @Test
    public void importExcel_01(){
        try {
            JSONResult jsonResult = ExcelUtil.excelToList(FileUtils.openInputStream(new File(this.export_users)),User.class,1,1);
            if(jsonResult.getCode()==0){
                for(Object obj : (List<Object>)jsonResult.getData()){
                    System.out.println(obj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 从EXCEL文件到LIST简单的导入功能（一一对应的）
     */
    @Test
    public void importExcel_02() {
        try {
            JSONResult jsonResult = ExcelUtil.excelToList(FileUtils.openInputStream(new File(this.export_maps)),Map.class,1,1);
            if(jsonResult.getCode()==0){
                for(Object obj : (List<Object>)jsonResult.getData()){
                    System.out.println(obj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 从EXCEL文件到LIST<MAP>对象导入功能（选择性的导入字段，自由空间最大）
     */
    @Test
    public void importExcel_03(){
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(2);
        params.setNeedSave(true);
        params.setDataHandler(new IExcelDataHandler(){

            @Override
            public Object exportHandler(Object o, String s, Object o2) {
                return null;
            }

            @Override
            public String[] getNeedHandlerFields() {
                return new String[0];
            }

            @Override
            public Object importHandler(Object o, String s, Object o2) {
                return null;
            }

            @Override
            public void setNeedHandlerFields(String[] strings) {

            }

            @Override
            public Hyperlink getHyperlink(CreationHelper creationHelper, Object o, String s, Object o2) {
                return null;
            }

            @Override
            public void setMapValue(Map map, String columnTitle, Object columnValue) {
                if(columnValue!=null){
                    System.out.println(columnValue.getClass());
                    map.put(toMapKey(columnTitle), columnValue);
                }
            }

            private String toMapKey(String columnTitle) {
                String key;
                switch (columnTitle){
                    case "订单编号":
                        key="ORDER_ID";
                        break;
                    default:
                        key = columnTitle;
                        break;
                }
                return key;
            }
        });

        try {
            JSONResult jsonResult = ExcelUtil.excelToList(FileUtils.openInputStream(new File(this.export_orders)),Map.class,1,2);
            for(Object obj : (List<Object>)jsonResult.getData()){
                System.out.println(obj);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }






    /**
     * 通过EXCEL模板进行数据导出，类似JSTL在JSP上的操作
     * 使用EXCEL模板进行数据导出需要注意以下几点：
     * 【1】循环的列不能为空，使用null来进行填充
     * 【2】循环的列如果需要设定固定值，需要把固定值存在变量中；否则生成的EXCEL这个列将是空值
     * 【3】循环开始{{$fe:records
     * 【4】循环结束}}
     * @throws Exception
     */
    @Test
    public void dataToExcelByTemplate() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        TemplateExportParams params = new TemplateExportParams(this.export_template);
        Map<String,Object> map = new HashMap();
        //字符串类型值
        map.put("customerservice","北京院子");
        map.put("stages","四期");
        map.put("get_project_date",simpleDateFormat.format(new Date()));
        map.put("version","1.0.1");
        //数值类型值
        map.put("input_year",2018);
        map.put("input_month",06);
        map.put("input_day",20);
        //图片类型值
        ImageEntity image = new ImageEntity();
        image.setUrl(this.local_image);
        map.put("logo",image);

        Random random = new Random();
        List<Map<String,Object>> list = new ArrayList();
        for(int i=0;i<20;i++) {
            Map<String, Object> entity = new HashMap();
            entity.put("code", StrUtil.reverse((i+"000000000").substring(0,10)));
            entity.put("id", i+1);
            entity.put("type",new String[]{"人力资源","计算机科学","社会关系","中国历史","现代兽医"}[random.nextInt(5)]);
            list.add(entity);
        }
        map.put("records",list);

        Workbook workbook = ExcelExportUtil.exportExcel(params, map);
        FileOutputStream fos = new FileOutputStream(this.export_excel);
        workbook.write(fos);
        fos.close();
    }
}
