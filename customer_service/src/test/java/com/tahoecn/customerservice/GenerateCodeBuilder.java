
package com.tahoecn.customerservice;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/***
 * 代码生成器
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class GenerateCodeBuilder {

    @Value("${spring.datasource.core.url}")
    private String url;

    @Value("${spring.datasource.core.username}")
    private String username;

    @Value("${spring.datasource.core.password}")
    private String password;

    @Value("${spring.datasource.core.driver-class-name}")
    private String driver;

    //作者
    private final String author = "zghw";

    //输出路径(设定为项目文件目录，二次生成会覆盖目标文件)不建议直接将文件生成到项目目录中
    private final String outputdir = "d:/";

    //包名称
    private final String packageName = "com.tahoecn.customerservice";

    @Test
    public void generateCodeBuilder() {
        boolean serviceNameStartWithI = false;
        generateByTables(serviceNameStartWithI, packageName, "cs_sync_my_cust", "cs_sync_my_cust2house", "cs_sync_my_house");
    }











    /**
     * 代码生成器
     * @param serviceNameStartWithI 接口是否以“I”开头
     * @param packageName 包的名称
     * @param tableNames 需要生成的表
     */
    private void generateByTables(boolean serviceNameStartWithI, String packageName, String... tableNames) {

        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setUrl(this.url)
                .setUsername(this.username)
                .setPassword(this.password)
                .setDriverName(this.driver);

        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setCapitalMode(true)
                .setEntityLombokModel(false)
                .setDbColumnUnderline(true)
                .setNaming(NamingStrategy.underline_to_camel)
                .setInclude(tableNames);

        GlobalConfig config = new GlobalConfig();
        config.setActiveRecord(false)
                .setBaseResultMap(false)
                .setBaseColumnList(false)
                .setEnableCache(false)
                .setAuthor(author)
                .setOutputDir(outputdir)
                .setFileOverride(true);

        if (!serviceNameStartWithI) {
            config.setServiceName("%sService");
        }

        PackageConfig packageConfig = new PackageConfig();
        packageConfig.setParent(packageName)
                .setController("controller")
                .setEntity("model");

        new AutoGenerator().setGlobalConfig(config)
                .setDataSource(dataSourceConfig)
                .setStrategy(strategyConfig)
                .setPackageInfo(packageConfig)
                .execute();
    }
}
