
package com.tahoecn.customerservice;

import com.tahoecn.customerservice.service.UserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MybatisPlusSample {

    @Autowired
    private UserService userServiceimpl;

    @Test
    public void customSqlTest() {
        //XML方式
        List<Map<String,Object>> list_1 = this.userServiceimpl.getUsersByXml();
        for(Map<String,Object> map : list_1){
            System.out.println(map.get("name"));
        }

        //Annotation方式
        System.out.println("==================================");
        List<Map<String,Object>> list_2 = this.userServiceimpl.getUsersByAnnotation();
        for(Map<String,Object> map : list_2){
            System.out.println(map.get("name"));
        }
    }
}