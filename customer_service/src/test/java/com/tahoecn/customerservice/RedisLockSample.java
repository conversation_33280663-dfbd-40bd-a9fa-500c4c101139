//package com.tahoecn.customerservice;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import com.tahoecn.customerservice.schedule.SyncData;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class RedisLockSample {
//	@Autowired
//	private SyncData syncData;
//
//	@Test
//	public void testSetMYCustomerData() {
//		long time1 = System.currentTimeMillis();
//		System.err.println("开始" + time1);
//
//		try {
//			syncData.syncData();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		long time2 = System.currentTimeMillis();
//		System.err.println("结束" + time1);
//		System.err.println("用时" + (time2 - time1) / 1000);
//	}
//
//}
