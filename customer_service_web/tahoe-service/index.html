<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta name="Generator" content="EditPlus®">
    <meta name="Author" content="">
    <meta name="Keywords" content="">
    <meta name="Description" content="">
    <title>客服管理系统</title>
    <link rel="icon" href="./static/favicon.ico">
    <link rel="stylesheet" href="static/softphone/assets/css/ukefu.css">
    <script src="static/softphone/assets/js/jquery-1.10.2.min.js"></script>
    <script src="static/softphone/assets/js/layer/layer.js"></script>
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script>
      var winHash = window.location.hash;
      var winHref = window.location.href;
      var strcookie = document.cookie;//获取cookie字符串
      var arrcookie = strcookie.split("; ");//分割
      if(winHash.indexOf('#/screen') == -1){
          if(!getCookie('LtpaTokens')) {
            document.cookie = "LtpaTokens="+guid();
            sessionStorage.clear();
            var href = "http://***********:8287";
            // if (location.hostname.indexOf("demo") > 0)
            //   href = 'http://ucsso.tahoecndemo.com:9988/login?sysId=WXKEFU&ReturnURL=' + location.href;
            // else
            //   href = 'hrefhttp://ucsso.tahoecn.com:9988/login?sysId=WXKEFU&ReturnURL=' + location.href;
            window.location.href= href;
          }
      }else{
        window.location.href = winHref
      }
      function guid() {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
              var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
              return v.toString(16);
          });
      }
      function getCookie(c_name) {
					if(document.cookie.length > 0) {
						c_start = document.cookie.indexOf(c_name + "=");//获取字符串的起点
						if(c_start != -1) {
							c_start = c_start + c_name.length + 1;//获取值的起点
							c_end = document.cookie.indexOf(";", c_start);//获取结尾处
							if(c_end == -1) c_end = document.cookie.length;//如果是最后一个，结尾就是cookie字符串的结尾
							return decodeURI(document.cookie.substring(c_start, c_end));//截取字符串返回
						}
					}
					return "";
			}

    </script>
  </body>
</html>
