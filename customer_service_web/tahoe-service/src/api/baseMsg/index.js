import axios from "@/axios/api.js"

export function getSelect(params) {
  return axios.service({
    url: axios.api + `/csHouseInfo/getSelect`,
    method: 'get',
    params
  })
}

/* 获取房屋列表 */
export function hList(params) {
  return axios.service({
    url: axios.api + `/csHouseInfo/list`,
    method: 'get',
    params
  })
}

/* 获取业主列表 */
export function uList(params) { 
  return axios.service({
    url: axios.api + `/csCustInfo/list`,
    method: 'get',
    params
  })
}

/* 新增保存业主信息 */ 
export function saveCusInfo(data) {
  return axios.service({
    url: axios.api + '/csCustInfo/save',
    method: 'post',
    data
  })
}

/* 新增保存业主信息家庭成员 */ 
export function saveCusFamily(data) {
  return axios.service({
    url: axios.api + '/csCustFamily/save',
    method: 'post',
    data
  })
}

/* 获取异常用户列表 */
export function aList(params) {
  return axios.service({
    url: axios.api + `/csSyncCustAbnormal/list`,
    method: 'get',
    params
  })
}

/* 获取异常用户 */
export function aGet(params) {
  return axios.service({
    url: axios.api + `/csSyncCustAbnormal/get`,
    method: 'get',
    params
  })
}

/* 获取相关联业主信息 */
export function otherCode(params) { 
  return axios.service({
    url: axios.api + `/csSyncCustAbnormal/otherCode`,
    method: 'get',
    params
  })
}

/* 保留业主信息 */ 
export function retain(params) {
  return axios.service({
    url: axios.api + '/csSyncCustAbnormal/retain',
    method: 'post',
    params
  })
}

/* 获取交付列表 */
export function selectDelivery(params) { 
  return axios.service({
    url: axios.api + `/csHouseInfo/selectDelivery`,
    method: 'get',
    params
  })
}

/* 修改交付状态 */ 
export function updateDelivery(params) {
  return axios.service({
    url: axios.api + '/csHouseInfo/updateDelivery',
    method: 'post',
    params
  })
}
