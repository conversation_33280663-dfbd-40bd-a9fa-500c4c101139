export function getServerDate() {//获取服务器时间
    var xhr = null;
    if (window.XMLHttpRequest) {
        xhr = new window.XMLHttpRequest();
    } else { // ie
        xhr = new ActiveObject("Microsoft");
    }

    xhr.open("GET", "/", false)//false不可变
    xhr.send(null);
    var date = xhr.getResponseHeader("Date");
    if (date == null) { 
        return new Date().valueOf();
    }
    return new Date(date).valueOf();
}