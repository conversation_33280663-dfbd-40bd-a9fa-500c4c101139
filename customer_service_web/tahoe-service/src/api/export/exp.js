import axios from "@/axios/api.js"

export function rfList(params) {
  return axios.service({
    url: axios.api + `/repForm/list`,
    method: "get",
    params
  })
}

export function rfExport(params) {
  let url = axios.api + `/repForm/export?`;
  for(var i in params){
      if (params.hasOwnProperty(i)) {
        if (params[i]) { 
          url +=i+"="+params[i]+"&"
        }
      }
  }
  return url;
}

/*
公用导出文件方法
@params  params 传入对象参数
@params  url 传入接口路径
*/
export function exportFiles(params,url) {
    let eUrl  = axios.api + url +'?';
    for(var i in params){
      if(params.hasOwnProperty(i)){
        if(params[i]) {
          eUrl += i+"="+params[i]+"&"
        }
      }
    } 
    location.href=eUrl;
}

// 报事整体数据统计
export function repairWhole(params) {
  return axios.service({
    url: axios.api + `/reportForm/repairWhole`,
    method: "get",
    params
  })
}

export function repairWholeExport(params) {
  let url = axios.api + `/reportForm/repairWhole?isExport=1&`;
  for(var i in params){
      if (params.hasOwnProperty(i)) {
        if (params[i]) { 
          url +=i+"="+params[i]+"&"
        }
      }
  }
  return url;
}

// 报事分类数据统计
export function classify(params) {
  return axios.service({
    url: axios.api + `/reportForm/classify`,
    method: "get",
    params
  })
}

export function classifyExport(params) {
  let url = axios.api + `/reportForm/classify?isExport=1&`;
  for(var i in params){
      if (params.hasOwnProperty(i)) {
        if (params[i]) { 
          url +=i+"="+params[i]+"&"
        }
      }
  }
  return url;
}

// 报事升级统计
export function upgrade(params) {
  return axios.service({
    url: axios.api + `/reportForm/upgrade`,
    method: "get",
    params
  })
}

export function upgradeExport(params) {
  let url = axios.api + `/reportForm/upgrade?isExport=1&`;
  for(var i in params){
      if (params.hasOwnProperty(i)) {
        if (params[i]) { 
          url +=i+"="+params[i]+"&"
        }
      }
  }
  return url;
}

// 报事渠道统计
export function channel(params) {
  return axios.service({
    url: axios.api + `/reportForm/channel`,
    method: "get",
    params
  })
}

export function channelExport(params) {
  let url = axios.api + `/reportForm/channel?isExport=1&`;
  for(var i in params){
      if (params.hasOwnProperty(i)) {
        if (params[i]) { 
          url +=i+"="+params[i]+"&"
        }
      }
  }
  return url;
}

// 关闭升级返修报表
export function curReport(params) {
  return axios.service({
    url: axios.api + `/reportForm/curReport`,
    method: "get",
    params
  })
}

export function curReportExport(params) {
  let url = axios.api + `/reportForm/curReport?isExport=1&`;
  for(var i in params){
      if (params.hasOwnProperty(i)) {
        if (params[i]) { 
          url +=i+"="+params[i]+"&"
        }
      }
  }
  return url;
}

//更改当前处理人
export function editRecentHandle(data) {
  return axios.service({
    url: axios.api + '/repForm/modify',
    method: 'post',
    data
  })
}