
export function isvalidPhone(str) {
  const reg = /^(1[0-9]{10},)*(1[0-9]{10})$/
  return reg.test(str)
}

export function isTel(str) {
  const reg = /0\d{2}-\d{7,8}/
  return reg.test(str)
}

export function isSingleTelNum(str) {
  const reg = /^(1[0-9]{10})$/
  return reg.test(str)
}

export function isAllChinese(str) {
  const reg = /^[\u4E00-\u9FA5A-Za-z]+$/
  return reg.test(str);
}

export function isEmail(str) {
  const reg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/
  return reg.test(str)
}

export function stripscript(s) {//过滤特殊字符
  let pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？%]")
  let rs = "";
  for (let i = 0; i < s.length; i++) {
    rs = rs + s.substr(i, 1).replace(pattern, '');
  }
  return rs;
}