import axios from "@/axios/api.js"


/* 报事录入 */
//两表联查
export function floorMsg(params) {
    return axios.service({
        url: axios.api + `/csHouseInfo/hcfInfo`,
        method: 'get',
        params
    })
}


export function personMsg(params) {
    return axios.service({
        url: axios.api + `/csCustInfo/hcfInfo`,
        method: 'get',
        params
    })
}
export function formMsg(params) {
    return axios.service({
        url: axios.api + `/csFormInst/hcfInfo`,
        method: 'get',
        params
    })
}
// 房屋信息
export function hiSelectHouseInfoById(params) {
    return axios.service({
        url: axios.api + `/csHouseInfo/selectHouseInfoById`,
        method: 'get',
        params
    })
}
//业主信息
export function ciSelecthcfInfoByNum(params) {
    return axios.service({
        url: axios.api + `/csCustInfo/selecthcfInfoByNum`,
        method: 'get',
        params
    })
}

//保存
export function fiSave(data) {
    return axios.service({
        url: axios.api + `/csFormInst/save`,
        method: 'post',
        data
    })
}

export function regionCode(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectAllCsDictItemByDictCode`,
        method: 'get',
        params
    })
}

export function processType(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectCommDict`,
        method: 'get',
        params
    })
}

//区域、城市、项目联动
export function area(params) {
    return axios.service({
        url: axios.api + `/csProjectInfo/selectProjectInfoRelation`,
        method: 'get',
        params
    })
}

export function searchInterimReport(params) {
    return axios.service({
        url: axios.api + `/csProjectInfo/selectProjectInfoRelation`,
        method: 'get',
        params
    })
}

export function searchInterimReports(params) {
    return axios.service({
        url: axios.api + `/csProjectInfo/selectProjectInfoRelation`,
        method: 'get',
        params
    })
}

export function searchInterimReportsList(params) {
    return axios.service({
        url: axios.api + `/csFormInst/temporaryList`,
        method: 'post',
        params
    })
}


//楼栋、单元号、房间号联动
export function BuildingSearch(params) {
    return axios.service({
        url: axios.api + `/csHouseInfo/selectHouseInfoRelation`,
        method: 'get',
        params
    })
}

export function ReportStep(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectCommDict`,
        method: 'get',
        params
    })
}


//报事处理
export function searchClose(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectCommDict`,
        method: 'get',
        params
    })
}

export function onesearchClose(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectAllCsDictItemByDictCode`,
        method: 'get',
        params
    })
}

export function searchCloseList(params) {
    return axios.service({
        url: axios.api + `/csFormInst/getFormHandlingList`,
        method: 'get',
        params
    })
}

//报事分派

export function Assignment(params) {
    return axios.service({
        url: axios.api + `/csFormInst/getFormAssignList`,
        method: 'get',
        params
    })
}

//报事回访
export function searchReport(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectCommDict`,
        method: 'get',
        params
    })
}

export function searchC(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectAllCsDictItemByDictCode`,
        method: 'get',
        params
    })
}

export function searchReturnList(params) {
    return axios.service({
        url: axios.api + `/csFormInst/returnVisitList`,
        method: 'post',
        params
    })
}

//字典
export function ReportReturn(params) {
    return axios.service({
        url: axios.api + `/csFormInst/returnVisitList`,
        method: 'post',
        params
    })
}


//区域、城市、项目
export function piSelectProjectInfoRelation(params) {
    return axios.service({
        url: axios.api + `/csProjectInfo/selectProjectInfoRelation`,
        method: 'get',
        params
    })
}
// 楼栋、单元号、房间号
export function piSelectHouseInfoRelation(params) {
    return axios.service({
        url: axios.api + `/csHouseInfo/selectHouseInfoRelation`,
        method: 'get',
        params
    })
}
//多级分类字典值
export function selectOptions(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectAllCsDictItemByDictCode`,
        method: 'get',
        params
    })
}

//初始化下拉字典
export function initSelect(params) {
    return axios.service({
        url: axios.api + `/csDictItem/selectCommDict`,
        method: 'get',
        params
    })
}

//初始化常用问题
export function initTroubleSelect(params) {
    return axios.service({
        url: axios.api + `/csCommonQuestion/selectQuestionByUserId`,
        method: 'get',
        params
    })
}

//增加常用问题
export function troubleSelectADD(params) {
    return axios.service({
        url: axios.api + `/csCommonQuestion/addCommonQuestion`,
        method: 'post',
        params
    })
}

//删除常用问题
export function deleteTrouble(params) {
    return axios.service({
        url: axios.api + '/csCommonQuestion/deleteById',
        method: 'get',
        params
    })
}
//获取常用问题描述
export function selectQuestionById(params) {
    return axios.service({
        url: axios.api + `/csCommonQuestion/selectQuestionById`,
        method: 'get',
        params
    })
}
//页面加载
export function Loading(params) {
    return axios.service({
        url: axios.api + `/csMenuCfg/selectUrlByCodeList`,
        method: 'get',
        params
    })
}



//获取登陆信息 
export function userCookie(params) {
    return axios.service({
        url: axios.api + `/perm/userPackage`,
        method: 'get',
        params
    })
}

//  快速分派
export function QuickDispatch(params) {
    return axios.service({
        url: axios.api + `/process/getTaskUser`,
        method: 'get',
        params
    })
}
// 快速分派提交
export function userSubmit(params) {
    return axios.service({
        url: axios.api + `/process/batchSubmit`,
        method: 'get',
        params
    })
}

//主责任单位选项
export function siSelectNameByArea(params) {
    return axios.service({
        url: axios.api + `/csSupplierInfo/selectNameByArea`,
        method: 'get',
        params
    })
}

//process node
export function getProcessNodeByFormId(params) {
    return axios.service({
        url: axios.api + `/csProcessWorkitem/getProgressList`,
        method: 'get',
        params
    })
}

//get regionCode
export function getRegionInfoByCode(params) {
    return axios.service({
        url: axios.api + '/csProjectInfo/getProDetailByCode',
        method: 'get',
        params
    })
}

export function getItExportList(params){
    return axios.service({
        url: axios.api + '/repForm/getITList',
        method: 'get',
        params
    })
}
