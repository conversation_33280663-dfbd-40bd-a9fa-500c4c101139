import axios from "@/axios/api.js"

export function getReportSettingList(params) {//获取报事设置列表
    return axios.service({
        url: axios.api + `/csUserRole/tree/list`,
        method: 'get',
        params
    })
}
export function getReportSettingEdit(params) {//报事设置编辑
    return axios.service({
        url: axios.api + `/csUserRole/tree/edit`,
        method: 'get',
        params
    })
}

//组织 部门
export function orgTree(params) {
    return axios.service({
        url: axios.api + `/csUserRole/orgTree`,
        method: 'get',
        params
    })
}
//通过部门选择添加管理员列表
export function userList(params) {
    return axios.service({
        url: axios.api + `/csUserRole/findUserByOrgId`,
        method: 'get',
        params
    })
}

export function submitList(data) {
    return axios.service({
        url: axios.api + `/csUserRole/projectEditing`,
        method: 'post',
        data,
    })
}

export function userListBySearch(params) {
    return axios.service({
        url: axios.api + `/csUserRole/findUsersByNameOrCode`,
        method: 'get',
        params
    })
}

export function getItReportTypeList(params){//获取IT报事类型列表
    return axios.service({
        url: axios.api + '/csDictItem/itTreeListQuery',
        method: 'get',
        params
    })
}

export function saveEditItReportType(data){//保存编辑IT报事类型
    return axios.service({
        url: axios.api + '/csDictItem/updateItDictItem',
        method: 'post',
        data
    })
}

export function addItReportTypeChild(data){//添加IT报事类型子集
    return axios.service({
        url: axios.api + '/csDictItem/insertItDictItem',
        method: 'post',
        data
    })
}

export function deleteItReportType(params){//删除IT报事类型
    return axios.service({
        url: axios.api + '/csDictItem/deleteItDictItem',
        method: 'post',
        params
    })
}

export function csSelectItUser(params){//IT项目人员查询
    return axios.service({
        url: axios.api + '/csUserRole/selectItUser',
        method: 'get',
        params
    })
}

export function csSaveItUser(data){//IT项目人员编辑
    return axios.service({
        url: axios.api + '/csUserRole/saveItUser',
        method: 'post',
        data
    })
}

export function getAppNoticeInfo(data){//获取App公告信息
    return axios.service({
        url: axios.api + '/csAnnouncement/announcement',
        method: 'post',
        data
    })
}

export function saveAppNoticeInfo(data) {//保存App公告
    return axios.service({
        url: axios.api + '/csAnnouncement/saveAnnouncement',
        method: 'post',
        data
    })
}