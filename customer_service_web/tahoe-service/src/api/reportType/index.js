import axios from "@/axios/api.js"

export function getReportTypeList(params) {//获取报事类型列表
    return axios.service({
        url: axios.api + `/csDictItem/treeListQuery`,
        method: 'get',
        params
    })
}
// 报事类型编辑
export function getReportTypeEdit(params) {
    return axios.service({
        url: axios.api + `/csDictItem/updateEchoInfo`,
        method: 'get',
        params
    })
}
// 提交
export function reportTypeEditSubmit(data) {
    return axios.service({
        url: axios.api + `/csDictItem/dictItemInfoSave`,
        method: 'post',
        data
    })
}
