import axios from "@/axios/api.js"

export function getReportUpgradeList(params) {//获取报事升级列表
    return axios.service({
        url: axios.api + `/csUpgradeCfg/tree/list`,
        method: 'get',
        params
    })
}
export function reportUpgradeEdit(params) {//获取报事升级编辑
    return axios.service({
        url: axios.api + `/csUpgradeCfg/upgradeCfgEcho`,
        method: 'get',
        params
    })
}
export function reportUpgradeEditSubmit(data) {//获取报事升级编辑提交
    return axios.service({
        url: axios.api + `/csUpgradeCfg/updateSaveUpgradeCfg`,
        method: 'post',
        data
    })
}
