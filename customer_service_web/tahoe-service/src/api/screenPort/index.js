import axios from "@/axios/api.js"
//大屏显示接口
export function getScreenReportData() {//报事管理
    return axios.service({
        url: '/web/screenDisplay/reportCount',
        method: 'get',
    })
}

export function getCurrentSysCall(date) {//实时监听
    return axios.service({
        url: '/screen/v1/GetCurrentSysCall/?query=VCurrentTime:'+date,
        method: 'get',
    })
}

export function getCurrentSumSysCall(date) {//小时查询
    return axios.service({
        url: '/screen/v1/GetCurrentSumSysCall/?query=VCurrentTime:'+date,
        method: 'get',
    })
}