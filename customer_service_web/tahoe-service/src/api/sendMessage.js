import axios from "@/axios/api.js"

// 保存发送短信信息
export function saveMessageInfo(params) {
    return axios.service({
        url: axios.api + `/csSmsTark/save`,
        method: 'post',
        params
    })
}
// 获取定时短信列表
export function getMessageInfoList(params){
    return axios.service({
        url: axios.api + '/csSmsTark/list',
        method: 'get',
        params
    })
}

export function cancleSendTask(params) {
    return axios.service({
        url: axios.api + '/csSmsTark/delete',
        method: 'get',
        params
    })
}