import axios from "@/axios/api.js"


/* 获取家庭成员信息 */
export function cfSelectByCustId(params) {
  return axios.service({
    url: axios.api + `/csCustFamily/selectByCustId`,
    method: 'get',
    params
  })
}
//获取工单信息
export function fiSelectCsformInst(params) {
  return axios.service({
    url: axios.api + `/csFormInst/selectCsformInst`,
    method: 'get',
    params
  })
}

export function pGetTaskUser(params) {
  return axios.service({
    url: axios.api + `/process/getTaskUser`,
    method: 'get',
    params
  })
}

// 提交
export function pSubmit(params) {
  return axios.service({
    url: axios.api + `/process/submit`,
    method: 'post',
    params
  })
}

// 特殊关闭
export function pSpecialEnd(params) {
  return axios.service({
    url: axios.api + `/process/specialEnd`,
    method: 'get',
    params
  })
}
//工单附件关联
export function cfRelationFormAndFiles(data) {
  return axios.service({
    url: axios.api + `/csFile/relationFormAndFiles`,
    method: 'post',
    data
  })
}
//回显附件信息
export function cfSelectFileByFormNo(params) {
  return axios.service({
    url: axios.api + `/csFile/selectFileByFormNo`,
    method: 'get',
    params
  })
}
//下载附件
export function cfFileDownload(params) {
  return axios.service({
    url: axios.api + `/csFile/fileDownload`,
    method: 'get',
    params
  })
}
export function deleteFile(params) {
  return axios.service({
    url: axios.api + `/csFile/fileDelete`,
    method: 'get',
    params
  })
}

//升级
export function pLevelUp(params) {
  return axios.service({
    url: axios.api + `/process/upgrade`,
    method: 'get',
    params
  })
}

export function project() {
  return axios.service({
    url: axios.api + `/csProjectInfo/selectAll`,
    method: "get"
  })
}

export function dict() {
  return axios.service({
    url: axios.api + `/csDictItem/selectAll`,
    method: "get"
  })
}

//通过电话查用户
export function cuSelectUserTel(params) {
  return axios.service({
    url: axios.api + `/csUcUser/selectUserTel`,
    method: "get",
    params
  })
}

// 报事回访加锁
export function cfReturnVisitLockQuery(params) { 
  return axios.service({
    url: axios.api + `/csFormInst/returnVisitLockQuery`,
    method: "post",
    params
  })
}

// 报事回访加锁
export function cfReturnVisitLock(params) { 
  return axios.service({
    url: axios.api + `/csFormInst/returnVisitLock`,
    method: "post",
    params
  })
}

// 获取工单号
export function getFormNo(params) { 
  return axios.service({
    url: axios.api + `/csFormInst/getFormNo`,
    method: "get",
    params
  })
}

// 查询用户列表
export function userList(params) { 
  return axios.service({
    url: axios.api + `/csUcUser/list`,
    method: "get",
    params
  })
}

export function updateReportState(data){
  return axios.service({
    url: axios.api + '/csFormInst/updateFormProcessState',
    method: 'post',
    data
  })
}

// 无电话，则添加电话
export function setCustPhone(data){
  return axios.service({
    url: axios.api + '/csCustInfo/updatePhone',
    method: 'post',
    data
  })
}
