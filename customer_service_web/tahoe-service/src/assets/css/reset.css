html,
body {
  height: 100%;
}

#app,
#mainPage {
  height: 100%;
  overflow: hidden;
}

body {
  background-color: #f5f5f5;
  font-size: 12px;
}

* {
  padding: 0;
  margin: 0;
}

ul,
li {
  list-style: none;
}

.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-30 {
  margin-right: 30px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-30 {
  margin-left: 30px;
}

.pt-15 {
  padding-top: 15px;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.clear {
  clear: both;
}

.ft-14 {
  font-size: 14px;
}

.ft-16 {
  font-size: 16px;
}

.ft-18 {
  font-size: 18px;
}

.fw-bd {
  font-weight: bold
}

.ta-rt {
  text-align: right;
}

.ta-ct {
  text-align: center;
}

.ta-lt {
  text-align: left;
}

/* 背景基本样式 */

.bg-style {
  /* margin-top: 10px; */
  border-radius: 4px;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  /* overflow: hidden; */
}

/*面包屑*/

.breadAndBtn {
  height: 30px;
  line-height: 30px;
  position: relative;
}

/*input text*/

.input-text {
  padding-left: 10px;
  height: 24px;
  line-height: 24px;
  background: #ffffff;
  border: 1px solid #d2d2d2;
  border-radius: 4px;
  font-size: 12px;
  /* color: #b4b4b4; */
  outline: none;
}

/* placeholder 样式 */

input::-webkit-input-placeholder {
  color: #b4b4b4;
  font-size: 12px;
}

/* 左侧红色竖线 */

.redSide {
  width: 2px;
  height: 15px;
  background-color: red;
  display: inline-block;
  /* vertical-align: middle; */
  position: relative;
  top: 3px;
}

/*面包屑颜色*/

#app .bread {
  color: #b4b4b4;
}

#app .btnSet {
  float: right;
  /* margin-right: 20px; */
}

#app .el-menu {
  border-right: none;
}

/* 按钮样式 新增、编辑等*/

.el-button {
  width: 100px;
  height: 28px;
  background: #E63F3C;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: none;
  font-size: 14px;
  color: #fff;
  letter-spacing: 0;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  margin-left: 20px;
  outline: none;
  padding: 0;
}

#app .el-scrollbar .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item {
  font-size: 12px;
}

#app .el-input__inner,
#app .el-form-item__content,
#app .el-radio__label {
  font-size: 12px;
}

#app .el-input__inner {
  height: 24px;
  line-height: 24px;
}

#app .it-report .el-input__inner {
  height: 24px !important;
  line-height: 24px;
}

#app .add-report .el-input__inner {
  height: 24px !important;
  line-height: 24px;
}

.el-button:focus,
.el-button:hover {
  /* color: #E63F3C;
    border-color:none;
    background-color:#fff; */
}

.el-button--primary:focus,
.el-button--primary:hover {
  color: #E63F3C;
  border-color: rgb(248, 197, 197);
  background-color: rgb(253, 236, 236);
}

/* 按钮样式 全选、添加、删除、下载*/

.btnDown {
  width: 70px;
  height: 22px;
  cursor: pointer;
  display: inline-block;
  background: #FFFFFF;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.20);
  border-radius: 100px;
  text-align: center;
  line-height: 22px;
  color: #E63F3C;
  border: none;
  outline: none;
  font-size: 12px;
}

.btnDown:hover {
  box-shadow: 0 1px 4px 0 rgba(230, 63, 60, 0.60);
}

/*选项框下拉栏字体*/

#app .el-select-dropdown__item {
  font-size: 12px;
}

/* #app .el-date-editor{
  width: 128px;
} */

/*复选框样式*/

#app .el-checkbox__inner:hover {
  border-color: #E63F3C;
}

#app .el-checkbox__input.is-checked .el-checkbox__inner,
#app .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border-color: #E63F3C;
  background-color: #E63F3C;
}

#app .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #E63F3C;
}

/*分页配置*/

#app .pagination {
  margin: 10px 0 10px 25%;
  padding-bottom: 10px;
}

#app .el-pager li.active {
  color: #E63F3C;
}

#app .el-pager li:hover {
  color: #E63F3C;
}

#app .el-pagination button:hover {
  color: #E63F3C;
}

/* 富文本框 */

#app .ck-content {
  min-height: 200px;
  max-height: 400px;
}

/* 表单样式 */

#app .el-form-item__error {
  position: relative;
}

#app .el-form-item {
  margin-bottom: 0;
}

#app .el-form-item__label {
  font-size: 12px;
}

#app .el-tree {
  max-height: 300px;
}

#app .el-select {
  width: 100%;
}

/* 确定按钮颜色 */

.color {
  background-color: #e63f3c !important;
  color: #fff !important;
  box-shadow: none !important;
}

/* 重置按钮颜色 */

.resetColor {
  background: #B4B4B4 !important;
  color: #fff !important;
  box-shadow: none !important;
}

#app .el-table td,
#app .el-table th {
  padding: 6px 0;
}

#app .el-table__empty-block {
  line-height: 60px;
}

/* 列表 */

table {
  width: 100%;
  border-spacing: 0px;
  text-align: center;
  border-collapse: collapse;
}

table tr {
  min-height: 30px;
  line-height: 30px;
}

table tr td {
  text-align: center;
  cursor: pointer;
}

.odd {
  background-color: #F1F4F8;
}

.even {
  background-color: #fff;
}

.select-tab .el-radio__input {
  display: none;
}

.select-tab .el-radio__label {
  padding-left: 5px;
}

.select-tab .el-radio.el-radio--small.is-bordered {
  height: 22px;
  box-shadow: 0 1px 4px 0;
  border-radius: 11px;
  line-height: 6px;
  border: solid thin rgba(0, 0, 0, 0.2);
}

.select-tab .el-radio.el-radio--small.is-bordered.is-checked {
  border: solid thin #E63F3C;
}

#homepage .table-body .el-icon-arrow-right {
  display: none;
}

#app textarea {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  font-size: 12px;
}

.el-message {
  z-index: 999999999 !important;
}

.leftMenu span {
  vertical-align: middle;
}

.confirm-reset-style {
  padding: 6px 35px;
}

.el-dialog__wrapper,
.el-message-box__wrapper {
  z-index: 9999999 !important;
}

.v-modal {
  z-index: 999999 !important;
}

.el-picker-panel,
.el-select-dropdown {
  z-index: 10000000 !important;
}

#diytextarea .el-textarea .el-textarea__inner {
  height: 100%;
}

body .el-table th.gutter {
  display: table-cell !important;
}

.print-dialog .el-dialog {
  width: 635px;
}

.mainScrollConta {
  background-color: #efefef;
}

#homepage {
  background-color: #efefef;
}

#homepage #firstLevelTable .el-table__row:nth-child(odd) {
  background-color: #ffffff;
}

#homepage #firstLevelTable .el-table__row:nth-child(even) {
  background-color: #f1f5f8;
}

#homepage #secondLevelTable .el-table__row:nth-child(odd) {
  background-color: #f1f5f8;
}

#homepage #secondLevelTable .el-table__row:nth-child(even) {
  background-color: #ffffff;
}

#homepage #thirdLevelTable .el-table__row:nth-child(odd) {
  background-color: #ffffff;
}

#homepage #thirdLevelTable .el-table__row:nth-child(even) {
  background-color: #f1f5f8;
}

.customer-service {
  background-color: #efefef;
}

#mainScrollContainer {
  background-color: #efefef;
}

#main-page {
  background-color: #efefef;
}

.el-input.is-disabled .el-input__inner {
  color: #606266
}

.el-textarea.is-disabled .el-textarea__inner {
  color: #606266
}

#homepage .table-body tbody tr td:first-child {
  width: 0;
}

#homepage .table-body .el-table__header thead tr th:first-child {
  width: 0;
}

#homepage .reset-select-lineheight .el-input__icon {
  line-height: 16px;
}

#homepage .reset-select-lineheight .el-range-separator {
  line-height: 19px;
}

#itReportType .table-body tbody tr td:first-child {
  width: 0;
}

#itReportType .table-body .el-table__header thead tr th:first-child {
  width: 0;
}

#itReportType .table-body .el-icon-arrow-right {
  display: none;
}

#reportsetting .table-body .el-icon-arrow-right {
  display: none;
}

#reportsetting .table-body tbody tr td:first-child {
  width: 0;
}

#reportsetting .table-body .el-table__header thead tr th:first-child {
  width: 0;
}

.el-tooltip__popper {
  max-width: 500px !important;
  line-height: 24px !important;
  font-size: 12px;
}

.el-loading-mask.is-fullscreen {
  z-index: 99999 !important;
  background-color: rgba(255, 255, 255, .5);
}

.cust-info .el-date-editor.el-input,
.cust-info.el-date-editor.el-input__inner {
  width: 100% !important;
}

.cust-info .el-tag.el-tag--info.el-tag--small {
  background-color: rgba(0, 0, 0, 0);
}

.el-select-dropdown__item {
  font-size: 12px;
}

.screen-container {
  width: 100vw;
  height: 100vh;
}

.screen-container .el-carousel {
  width: 100%;
  height: 100%;
}

.screen-container .el-carousel .el-carousel__container {
  width: 100%;
  height: 100%;
}

.screen-container .el-dialog__footer {
  text-align: center;
}

.break-word textarea {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-all;
}

.bordernone-input-table input {
  border: none;
}

.bordernone-input-table .el-textarea {
  border: none;
}

.bordernone-input-table .el-textarea__inner {
  border: none;
}

.bordernone-input-table .cell {
  padding: 5px !important;
}

.itreporttype-setting .el-input__inner {
  height: 24px !important;
}

.itreporttype-setting .el-select .el-tag--small {
  height: 22px !important;
}
