import axios from 'axios'
import qs from 'qs'
import {Message, MessageBox} from 'element-ui';
import Router from '@/router/index'

axios.defaults.withCredentials = true;
// axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
const service = axios.create({
  // baseURL: process.env.BASE_API, // api的base_url
  timeout: 100000 // 请求超时时间
});
service.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
// 请求拦截器
service.interceptors.request.use(function(config) {
    // if(config.method!='get'){
    //   config.data = qs.stringify(config.data);
    // }
    if(config.params){
      config.params.time = Date.parse(new Date())/1000;
    }
    return config;
   
  }, function(error) {
    return Promise.reject(error);
  });
  // 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    if(response.status ===200){
      return res;
    }
  },
  error => {
    console.log(error)
    if (error.response && error.response.status === 401)
      Router.push({path:'/login'}) 
    
    return Promise.reject(error);
  }
);


// let api = 'http://standard-test.tahoecndemo.com:8080';
let api = '/api';

export default {
  service,
  api
}
