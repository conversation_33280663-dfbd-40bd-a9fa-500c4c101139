<template>
    <div class="error500">
        <span class="text1">{{title}}，服务器错误</span>
        <div class="text2">如有需要，请联系管理员</div>
        <span class="text3" @click="back">返回</span>
        <img class="pic2" src="../../assets/images/500.png" alt="">
    </div>
</template>

<script>
    export default {
        data() {
            return {
                title:this.$route.query.title || '500',
            }
        },
        methods: {
            back() {
                this.$router.push({path:'/'})
            }
        },
        // mounted(){
        //     this.title = this.$route.query.title;
        // }
    }
</script>

<style scoped>
.error500{
    margin-left: 33%;
    margin-top: 10%;
    position: relative;
}
.pic2{
    width: 285px;
    height: 251px;
    position: absolute;
    top: 90px;
    left: 106px;
}
.text1{
    /* border: 1px solid #979797; */
    display: inline-block;
    font-family: MicrosoftYaHei-Bold;
    font-size: 18px;
    font-weight: bold;
    color: #2A4074;
    letter-spacing: -0.87px;
    border-bottom: 3px solid #000;
    padding: 10px 0 10px 0;
}
.text2{
    font-size: 14px;
    color: #434040;
    letter-spacing: -0.12px;
    margin-top: 10px;
}
.text3{
    display: inline-block;
    width: 95px;
    height: 22px;
    border-radius: 100px;
    background-color: #E63F3C;
    color: #fff;
    text-align: center;
    line-height: 22px;
    margin-top: 10px;
    cursor: pointer;
}
</style>