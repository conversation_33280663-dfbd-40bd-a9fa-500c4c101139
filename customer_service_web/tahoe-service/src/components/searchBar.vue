<template>
  <div class="search-bar">
    <div class="bg-style">
      <el-form
        ref="form"
        label-position="right"
        :model="searchData"
        label-width="80px"
        :disabled="disabled"
      >
        <div style="height:10px;"></div>
        <el-row>
          <el-col :span="5" :offset="0">
            <el-form-item label="区域">
              <el-select
                v-model="searchData.area"
                filterable
                placeholder="请选择"
                @change="getCityList"
              >
                <el-option
                  v-for="item in areaOptions"
                  :key="item.id"
                  :label="item.region"
                  :value="item.regionCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="城市">
              <el-select
                v-model="searchData.city"
                :disabled="cityprojectDisabled"
                filterable
                placeholder="请选择"
                @change="getProjectList"
              >
                <el-option
                  v-for="item in cityOptions"
                  :key="item.cityCompanyCode"
                  :label="item.cityCompany"
                  :value="item.cityCompanyCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="项目">
              <el-select
                v-model="searchData.project"
                :disabled="projectDisabled"
                filterable
                placeholder="请选择"
                @change="getBuildList"
              >
                <el-option
                  v-for="item in projectOptions"
                  :key="item.projectCode"
                  :label="item.project"
                  :value="item.projectCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="楼号">
              <el-select
                v-model="searchData.floorNum"
                :disabled="buildprojectDisabled"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in buildOptions"
                  :key="item.building"
                  :label="item.building"
                  :value="item.building"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="mt-10" v-show="showIf && stepFlg">
          <el-col :span="5" :offset="0">
            <el-form-item label="单元号">
              <el-input v-model="searchData.unit" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="房号">
              <el-input v-model="searchData.roomNum" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="5" :offset="1">
            <el-form-item label="房屋编号">
              <el-input v-model="searchData.houseNum" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="5" :offset="1">
            <el-form-item label="房屋名称">
              <el-input v-model="searchData.houseName" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="业务步骤" v-if="stepFlg">
              <el-select v-model="searchData.step" filterable placeholder="请选择">
                <el-option
                  v-for="item in step"
                  :key="item.itemCode"
                  :label="item.itemValue"
                  :value="item.itemCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="mt-10" v-show="showIf">
          <el-col :span="5" :offset="0">
            <el-form-item label="电话">
              <el-input v-model="searchData.phone" placeholder="请输入" @blur="removeBlank"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="创建时间">
              <el-date-picker
                prefix-icon="el-icon-date"
                v-model="searchData.createDate"
                :picker-options="createDate"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="至">
              <el-date-picker
                prefix-icon="el-icon-date"
                v-model="searchData.endDate"
                :picker-options="endDate"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="23:59:59"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="mt-10">
          <el-col :span="24" style="text-align:right;padding-right:4%;">
            <button
              class="btnDown"
              style="margin-top:9px;"
              @click.prevent="searchItem()"
              :disabled="disabled"
            >查询</button>
            <button
              class="btnDown"
              style="margin-top:9px;margin-left:15px;"
              @click.prevent="reset"
            >重置</button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="ta-ct mt-10" style="position:relative;height:30px;">
              <hr style="background-color:#e6e6e6;height:1px;border:none;">
              <span class="txt" @click.prevent="showIf =!showIf">
                <i
                  style="font-size:18px;color:#E63F3C;"
                  :class="showIf?'el-icon-caret-top':'el-icon-caret-bottom'"
                ></i>
              </span>
            </div>
          </el-col>
        </el-row>
        <!--<el-row>
          <el-col :span='8' :offset="7">
            <div class="mb-10 mt-10">
              &lt;!&ndash;<el-button class="color" @click="searchItem()" :disabled="disabled">查询</el-button>
              <el-button class="resetColor" @click="reset">重置</el-button>&ndash;&gt;
            </div>
          </el-col>
        </el-row>-->
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  searchInterimReport,
  areaReport,
  searchInterimReports,
  BuildingSearch,
  ReportStep,
  area
} from "@/api/port";
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    stepFlg: {
      type: Boolean,
      default: false
    },
    entryReport: {
      type: String,
      default: ""
    }
  },
  watch: {
    "searchData.area"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.cityDisabled = false;
          this.floorDisabled = true;
          this.projectOptions = [];
          this.buildOptions = [];
          this.searchData.city = this.searchData.floorNum = this.searchData.project =
            "";
          if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type=='entryReport' && JSON.parse(sessionStorage.getItem('params')).searchData.area){
            this.getCityList(newval);
            if(newval == JSON.parse(sessionStorage.getItem('params')).searchData.area){
              this.$set(this.searchData,'city',JSON.parse(sessionStorage.getItem('params')).searchData.city)
            }else{
              this.$set(this.searchData,'city','')
            }
          }
        }
      } else {
        this.cityprojectDisabled = true;
        this.cityDisabled = true;
        this.floorDisabled = true;
        this.projectDisabled = true;
      }
    },
    "searchData.city"(newval, oldval) {
      if (newval != oldval) {
        this.searchData.project = "";
        this.searchData.floorNum = "";
        if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type=='entryReport' && JSON.parse(sessionStorage.getItem('params')).searchData.city){
           this.getProjectList(newval);
           if(newval == JSON.parse(sessionStorage.getItem('params')).searchData.city){
             this.$set(this.searchData,'project',JSON.parse(sessionStorage.getItem('params')).searchData.project);
           }else{
             this.$set(this.searchData,'project','');
           }
        }
      }
      if (newval == "") {
        this.projectDisabled = true;
      } else {
        this.projectDisabled = false;
      }
    },
    "searchData.project"(newval, oldval) {
      if (newval == "") {
        this.buildprojectDisabled = true;
      } else {
        this.buildprojectDisabled = false;
      }
    }
  },
  data() {
    return {
      searchData: {
        createDate: "",
        endDate: "",
        area: "",
        phone: "",
        step: "",
        city: "",
        project: "",
        floorNum: "",
        houseNum: "",
      },
      step: [],
      createDate: {
        disabledDate: time => {
          if (this.endDate) {
            return time.getTime() > new Date(this.endDate).getTime();
          } else {
            return time.getTime();
          }
        }
      },
      endDate: {
        disabledDate: time => {
          if (this.searchData.createDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < new Date(this.searchData.createDate).getTime()
            );
          } else {
            return time.getTime() > Date.now();
          }
        }
      },

      buildprojectDisabled: true,
      cityprojectDisabled: true,
      stepVisible: true,
      cityDisabled: true,
      floorDisabled: true,
      projectDisabled: true,
      areaOptions: [],
      cityOptions: [],
      projectOptions: [],
      buildOptions: [],
      searchDataReset: {
        createDate: "",
        endDate: "",
        area: "",
        phone: "",
        step: "",
        city: "",
        project: "",
        floorNum: "",
        houseNum: ""
      },
      showIf: false
    };
  },
  methods: {
    removeBlank() {
      this.searchData.phone = this.searchData.phone.replace(/^\s*|\s*$/g, "");
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },

    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.area
      }).then(res => {
        console.log(res.data);
        this.projectOptions = res.data;
      });
    },

    getBuildList(val) {
      BuildingSearch({ projectId: val }).then(res => {
        if (res.code == 200) {
          this.buildOptions = res.data;
          console.log(this.buildOptions);
        } else {
          // this.$message({
          //   type: "error",
          //   message: res.message
          // });
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    reset() {
      sessionStorage.removeItem('params');
      this.searchData = Object.assign({}, this.searchDataReset);
    },

    ReportStepMonted(val) {
      ReportStep({ itemCode: val }).then(res => {
        this.step = res.data.processStateCode;
        this.step.map((item, index) => {
          if (item.itemValue == "报事升级") {
            this.step.splice(index, 1);
          }
        });
      });
    },
    searchItem() {
      this.$emit("searchItem", this.searchData);
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    }
  },
  created() {
    this.areaList();
  },
  mounted() {
    this.ReportStepMonted();
    if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type=='entryReport'){
      this.searchData = JSON.parse(sessionStorage.getItem('params')).searchData;
      this.$set(this.searchData,'pageNum',JSON.parse(sessionStorage.getItem('params')).pageNum); 
      this.searchData.total = JSON.parse(sessionStorage.getItem('params')).total; 
      this.searchItem();
    }
  }
};
</script>

<style scoped>
/* .search-bar >>> .el-input {
  width: 128px;
} */
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.search-bar >>> .el-input__inner {
  height: 24px !important;
  line-height: 24px;
}
.search-bar >>> .el-select-dropdown__item {
  font-size: 12px !important;
  padding: 0 15px;
  height: 30px;
  line-height: 30px;
}
.el-date-editor,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
