<template>
  <div class="wcg_page">
    <span v-if="showSpan" class="demonstration" style="fontSize:14px;color:#4A4A4A">显示第 {{pageStart}} 到第 {{pageStop}} 条记录，总共 {{total}} 条记录

      <span v-if="recordControl" style="margin: 0 20px;">
        每页显示
        <select v-model="size" :disabled="disabled" :class="{'disabled-button':disabled}" style="width: 42px;margin: 0 5px;border: 1px solid #DCDCDC;height:29px">
          <option :value="10">10</option>
          <option :value="20">20</option>
          <option :value="30">30</option>
          <option :value="50">50</option>
        </select>条记录
      </span>
      <a v-if="isRefresh" href="#" :disabled="disabled" :class="{'disabled-button':disabled}" style="color:#4D83FF;fontSize:14px" @click="handlerRefresh"> 刷新</a>
    </span>
    <el-pagination :disabled="disabled" style="float:right" :class="paginationSize" @current-change="handlerCurrentChange" :pager-count="pagerCount" :page-size="size" :current-page="currentPage" layout="prev, pager, next" :total="total">

    </el-pagination>
  </div>
</template>
<script>
export default {
  name: "wcgPage",
  props: {
    pageSize: {
      type: Number,
      default: 20
    },
    pagerCount: {
      type: Number,
      default: 7
    },
    currentPage: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 0
    },
    floorList: {
      type: Array,
      default: []
    },
    showSpan: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    "pagination-size": {
      type: String,
      default: ""
    },
    isRefresh: {
      type:Boolean,
      default: true
    },
    recordControl: {
      type:Boolean,
      default: true
    }
  },
  data() {
    return {
      size: 20,
      pageStart: 0,
      pageStop: 0
    };
  },
  watch: {
    size(v) {
      this.$emit("size-change", v);
    },
    floorList: {
      handler(v) {
        if (v.length > 0) {
          let s = (this.currentPage - 1) * this.pageSize;
          this.pageStart = s + 1;
          this.pageStop = s + v.length;
        }else{
          this.pageStart = 0;
          this.pageStop = 0;
        }
      }
    }
  },
  methods: {
    handlerCurrentChange(val) {
      this.$emit("current-change", val);
    },
    handlerRefresh() {
      this.$emit("on-change");
    }
  },
  mounted() {
    this.size = this.pageSize;
  }
};
</script>
<style>
.wcg_page {
  margin-top: 10px;
  padding: 0 30px;
}
/*不可用的确定按钮*/
.disabled-button,
.el-icon-more.disabled {
  cursor: not-allowed !important;
}
.min .el-pager li{
  min-width: 24px  !important;
}
</style>

