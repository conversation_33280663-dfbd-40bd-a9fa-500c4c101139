import Vue from "vue"

// 日期格式
Vue.filter('dateMoment', function(time) {
    if(time){
        let a = '';
        if(typeof(time) == 'string'){
            a = time.split('T')[0];
            a = a.replace(new RegExp(/-/gm) ,"/");
        }else{
            a = time;
        }
        let getTime = (a, type = 'all') => {
        let myDate = new Date(a);
        let year = myDate.getFullYear();
        let month = myDate.getMonth() + 1 >= 10 ? myDate.getMonth() + 1 : '0' + (myDate.getMonth() + 1);
        let day = myDate.getDate() >= 10 ? myDate.getDate() : '0' + myDate.getDate();
        let hours = myDate.getHours() >= 10 ? myDate.getHours() : '0' + myDate.getHours();
        let minutes = myDate.getMinutes() >= 10 ? myDate.getMinutes() : '0' + myDate.getMinutes();
        let seconds = myDate.getSeconds() >= 10 ? myDate.getSeconds() : '0' + myDate.getSeconds();
        if (type === 'year') {
            return year;
        } else if (type === 'month') {
            return year + '-' + month;
        } else if (type === 'day') {
            return year + '-' + month + '-' + day;
        } else if (type === 'hours') {
            return hours;
        } else if (type === 'minutes') {
            return hours + '-' + minutes;
        } else if (type === 'seconds') {
            return hours + '-' + minutes + '-' + seconds;
        } else if (type === 'all') {
            return year + '-' + month + '-' + day ;
        }
        };
        return getTime(a);
    }else{
        return '';
    }
    
  })

Vue.filter('fileSize',function(size){
    let a=(size/1024).toFixed(2);
    if(a<1){
        return size+'B';
    }else if(a>=1 &&a<1024){
        return a+'KB';
    }else if(a>=1024){
        return (a/1024).toFixed(2)+'MB';
    }
})
Vue.filter('TimeMoment', function(time,t) {
    if(time){
        let a = time.replace('T',' ').split('.')[0];
        a = a.replace(new RegExp(/-/gm) ,"/");
        let getTime = (a, type = t?t:'all') => {
        let myDate = new Date(a);
        let year = myDate.getFullYear();
        let month = myDate.getMonth() + 1 >= 10 ? myDate.getMonth() + 1 : '0' + (myDate.getMonth() + 1);
        let day = myDate.getDate() >= 10 ? myDate.getDate() : '0' + myDate.getDate();
        let hours = myDate.getHours() >= 10 ? myDate.getHours() : '0' + myDate.getHours();
        let minutes = myDate.getMinutes() >= 10 ? myDate.getMinutes() : '0' + myDate.getMinutes();
        let seconds = myDate.getSeconds() >= 10 ? myDate.getSeconds() : '0' + myDate.getSeconds();
        if (type === 'year') {
            return year;
        } else if (type === 'month') {
            return year + '-' + month;
        } else if (type === 'day') {
            return year + '-' + month + '-' + day;
        } else if (type === 'hours') {
            return hours;
        } else if (type === 'minutes') {
            return hours + '-' + minutes;
        } else if (type === 'seconds') {
            return hours + '-' + minutes + '-' + seconds;
        } else if (type === 'all') {
            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        }
        };
        if(time == null || time == ''){
        return ''
        }
        return getTime(a);
    }else{
        return '';
    }
    
})

Vue.filter('titleSize',function(title){
    let arr = [];
    arr = title.split('/');
    let str = '';
    if(title!='所有文档'){
        if(title.length>40){
            return arr[1]+'.../'+arr[arr.length-1];
        }else{
            arr.splice(0,1);
            str = arr.join('/');
            return str;
        }
    }else{
        return title;
    }

})
Vue.filter('orgSplit',function(title){
    let arr = [];
    arr = title.split('/');
    return arr[arr.length-1]
})
