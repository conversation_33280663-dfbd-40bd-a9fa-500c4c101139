// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import 'babel-polyfill'
import { Message } from 'element-ui';
import Vue from 'vue'
import App from './App'
import ElementUI from 'element-ui'
import Vuex from 'vuex'
import router from './router'
//import 'element-ui/lib/theme-chalk/index.css'
import printPage from '@/api/plugin/Print.js'
import '@/assets/element-reset/index.css'//整体重置element-ui主题
import $ from 'jquery'
import './assets/css/reset.css'
import store from './store'
import echarts from 'echarts'
import './filter'
import utilJs from "./assets/util.js";
import jsxlsx from "js-xlsx"
import xlsx from "xlsx"
Vue.use(jsxlsx)
Vue.prototype.$xlsx = xlsx;
Vue.prototype.$echarts = echarts
Vue.use(ElementUI)
Vue.use(Vuex)
Vue.use(printPage)
//Vue.use(Message)
Vue.config.productionTip = false
Vue.prototype.$utilJs = utilJs;

Vue.prototype.timeFilter = function(time) {
  if(time==''||time==null){return ''}
  let myDate = new Date(time);
  let year = myDate.getFullYear();
  let month = myDate.getMonth() + 1 >= 10 ? myDate.getMonth() + 1 : '0' + (myDate.getMonth() + 1);
  let day = myDate.getDate() >= 10 ? myDate.getDate() : '0' + myDate.getDate();
  let HH = myDate.getHours() >= 10 ? myDate.getHours() : '0' + myDate.getHours();
  let mm = myDate.getMinutes() >= 10 ? myDate.getMinutes() : '0' + myDate.getMinutes();
  let ss = myDate.getSeconds() >= 10 ? myDate.getSeconds() : '0' + myDate.getSeconds();
  return year + '-' + month + '-' + day +' '+HH+':'+mm+':'+ss;
}

Vue.prototype.checkTextLength = function(val,length){
  let l = val.length;
  if(l > length){
    Vue.prototype.$message({
      type:'warning',
      message:'超出最大字数限制('+length+'字)，当前输入字数'+l+'字'
    });
    val = val.substring(0,length);
    return val;
  }
}

/* eslint-disable no-new */
// window.onscroll= function(){
//   console.log(123333)
// }
export default new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
