<template>
    <div class="login">
        <div class="form">
            <div class="company-name">天宇正清客服管理</div>
            <el-form ref="form" :model="form" :rules="rules">
                <el-form-item prop="account">
                    <el-input v-model="form.account" placeholder="请输入用户名"/>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input 
                        v-model="form.password" 
                        placeholder="请输入密码" 
                        type="password" 
                        show-password
                    />
                </el-form-item>
            </el-form>
            <div class="btns">
                <el-button @click="submitForm('form')">登录</el-button>
                <el-button @click="resetForm('form')">重置</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import { login } from '@/api/login'
export default {
    data(){
        return{
            form: {
                account: '',
                password: '',
            },
            rules: {
                account: [
                    { required: true, message: '用户名不能为空', trigger: ['blur','change','submit'] }
                ],
                password: [
                    { required: true, message: '密码不能为空', trigger: ['blur','change','submit'] }
                ],
            }
        }
    },
    methods:{
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.login();
                }
            });
        },
        login () {
            let { account, password } = this.form;
            login({
                account,
                password
            }).then(({code,msg})=>{
                if(code === 200){
                    // 登录成功跳转首页
                    this.$router.push({ name: "homePage" });
                }
            })
        }
    },
    created(){
        
    }
}
</script>
<style lang="scss" scoped="scoped">
.login{
    position: relative;
    width: 100%;
    height: 100%;
    background: url('./../../assets/images/login-bg.png') no-repeat;
    background-size: cover;
    .form{
        position: absolute;
        right: 20%;
        top: 20%;
        padding: 10px 30px;
        width: 310px;
        background: #fff;
        border-radius: 20px;
        .el-form-item{
            margin-bottom: 18px !important;
        }
        >>> .el-form-item__error{
            position: absolute !important;
        }
        >>> .el-input__inner{
            height: 34px !important;
            line-height: 34px !important;
        }
    }
    .btns{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 50px 18px;
        .el-button{
            margin: 0;
            padding: 0 20px;
            width: 80px;
            height: 30px !important;
            font-size: 12px;
            font-weight: normal;
            color: #1660a3;
            background: #e1f1ff;
            border:  1px solid #1660a3;
            border-radius: 8px;
            box-shadow: none;
        }
    }
    .company-name{
        padding: 26px 0;
        color: rgb(70, 70, 70);
        font-size: 32px;
        font-family: 'Microsoft YaHei';
        font-weight: bold;
        text-align: center;
    }
}
</style>
