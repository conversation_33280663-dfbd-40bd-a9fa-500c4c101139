<template>
    <div class="app-notice-container">
        <el-form ref="form" :rules="rules" :model="noticeForm" label-width="80px">
            <el-form-item label="公告内容" prop="content">
                <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6}" placeholder="请输入公告内容" v-model="noticeForm.content"/>
            </el-form-item>
            <el-form-item label="是否展现">
                <el-switch v-model="noticeForm.isShow"></el-switch>
            </el-form-item>
            <el-form-item style="text-align:center;">
                <el-button @click="onSubmit" class="btnDown">立即发布</el-button>
            </el-form-item>
        </el-form>
    </div>  
</template>
<script>
import { getAppNoticeInfo, saveAppNoticeInfo } from '@/api/reportSetting'
export default {
    data(){
        return{
            noticeForm:{
                content:'',
                isShow:true,
            },
            objData:{},
            rules: {
                content: [
                    { required: true, message: '请输入公告内容', trigger: 'blur' },
                ]
            }
        }
    },
    methods:{
        onSubmit(){
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.editInfo();
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        editInfo(){
            let params = {
                    announceMent: this.noticeForm.content,
                    id: this.objData.id || '',
                    state: this.noticeForm.isShow?'1':'0'
            }
            saveAppNoticeInfo(params).then(res => {
                if(res.code == 200 && res.sucess){
                    this.$message({
                        type:'success',
                        message:'公告发布成功!'
                    })
                }else{
                    this.$message({
                        type:"error",
                        message:res.message
                    })
                }
            })
        },
        getInfo(){
            getAppNoticeInfo().then(res => {
                this.objData = res.data;
                this.$set(this.noticeForm,'content',res.data.announceMent);
                if(res.data.state == '1'){
                    this.$set(this.noticeForm,'isShow',true);
                }else{
                    this.$set(this.noticeForm,'isShow',false);
                }
            })
        }
    },
    created(){
        this.getInfo();
    }
}
</script>
<style lang="scss" scoped="scoped">
.app-notice-container{
    background-color:#ffffff;
    padding:20px 20px 20px 10px;
    border-radius:5px;
}
</style>
