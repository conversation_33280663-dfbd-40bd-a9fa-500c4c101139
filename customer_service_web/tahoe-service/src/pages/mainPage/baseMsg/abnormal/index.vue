<template>
  <div class="search-bar">
    <div class="bg-style" style="margin-right:10px;padding:10px 0;">
      <el-form ref="form" label-position="right" :model="searchData" label-width="90px" size="mini">
        <el-row>
          <el-col :span='5' :offset="0">
            <el-form-item label="客户姓名">
              <el-input v-model="searchData.custName" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='5' :offset="1">
            <el-form-item label="客户电话">
              <el-input v-model="searchData.telephone" placeholder="请输入" @blur="removeBlank()"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='5' :offset="1">
            <el-form-item label="证件号">
              <el-input v-model="searchData.certificateNum" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <button class="btnDown" @click.prevent="searchList()" style="margin-left:20px;margin-top:3px;">查询</button>
            <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
            <button class="btnDown" v-show="row.id" @click.prevent="retainRow" style="margin-left:10px">保留</button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="bg-style mt-10 mb-20" style="margin-right:10px">
      <div style="width:100%;overflow:hidden;">
        <el-table v-loading="loading" stripe element-loading-text="拼命加载中" :row-class-name="reportRowStyle" @row-click="getRow" tooltip-effect="dark" :data="list" style="width: 100%">
          <el-table-column label="客户姓名" show-overflow-tooltip>
            <template slot-scope="scope">
              <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.custName}}</span>
            </template>
          </el-table-column>
          <el-table-column label="客户身份" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{scope.row.source == 1?'业主':'准业主'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="certificateName" label="证件类型" show-overflow-tooltip/>
          <el-table-column prop="certificateNum" label="证件号码" show-overflow-tooltip/>
          <el-table-column prop="telephone" label="客户电话" show-overflow-tooltip/>
          <el-table-column prop="houseNum" label="关联房屋" show-overflow-tooltip/>
        </el-table>
        <!-- 分页 -->
        <wcg-page :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchList"></wcg-page>

      </div>
    </div>

  </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import { aList, otherCode, retain } from "@/api/baseMsg";
export default {
  props: [],
  components: { wcgPage },
  data() {
    return {
      loading: false,
      row: {},
      searchData: {
        pageSize: 20
      },
      list: [],
      total: 0
    };
  },
  methods: {
    getRow(item) {
      this.row = this.row && this.row.id == item.id ? {} : item;
    },
    reportRowStyle({ row, rowIndex }) {
      return this.row.id == row.id ? "active" : "";
    },
    retainRow() {
      if (this.row.id) {
        this.$confirm(`确定保留当前 ${this.row.custName} 客户的数据么？`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          center: true,
          showClose: false
        })
          .then(() => {
            retain({ id: this.row.id, certificateNum: this.row.certificateNum })
              .then(res => {
                this.$message("保留处理完成");
                this.searchList();
              })
              .catch(err => {});
          })
          .catch(() => {});
      }
    },
    removeBlank() {
      this.searchData.telephone = (this.searchData.telephone || "").replace(
        /^\s*|\s*$/g,
        ""
      );
    },
    reset() {
      this.searchData = {};
    },
    searchList() {
      this.loading = true;
      this.list = [];
      aList(this.searchData)
        .then(res => {
          this.loading = false;
          if (res.sucess && res.data) {
            this.list = res.data.records;
            this.total = res.data.total;
          }
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.searchData.pageSize = val;
      this.searchData.pageNum = 1;
      this.searchList();
    },
    handleCurrentChange(val) {
      this.searchData.pageNum = val;
      this.searchList();
    },
    goDetail(item) {
      //跳转详情
      this.$router.push({
        path: "abnormalInfo",
        query: {
          certificateNum: item.certificateNum
        }
      });
    }
  },
  mounted() {},
  created() {
    this.searchList();
  }
};
</script>

<style>
.el-table .active td {
  background-color: #e63f3c !important;
  color: #fff !important;
}
.el-table tr.active:hover > td {
  background-color: #e63f3c !important;
  color: #fff !important;
}
</style>

