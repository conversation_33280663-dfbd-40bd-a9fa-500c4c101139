<template>
    <div class="cust-info">
        <div class="fr" id="flex-top" :style="'top:'+fixTop" style="margin-right:15px;">
            <button class="btnDown" @click="goback">返回</button>
        </div>
        <el-form v-for="cusForm in custInfos" :key="cusForm.id" class="card-form" :model="cusForm" ref="cusForm" label-position="right" label-width="120px">
            <el-row style="margin-bottom:10px;border-bottom:solid thin rgba(0,0,0,0.1);padding:10px;">
                <el-col :span="7" style="height:10px;text-align:left;">
                    <span class="table-title">客户信息{{cusForm.abnormal == 1?'（正式数据）':'（异常ID：' + cusForm.id + '）'}}</span>
                </el-col>
                <el-col :span="7" :offset="1" style="height:10px;"></el-col>
                <el-col :span="7" :offset="2" style="text-align:right;">
                    <span class="btnDown" @click="retainRow(cusForm)">保留</span>
                    <!-- <span class="btnDown" @click="goback" v-if="cusForm.abnormal == 1">返回</span> -->
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="5">
                    <el-form-item label="客户姓名:">
                        <el-input type="text" v-model="cusForm.custName" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="客户身份:">
                        <el-input type="text" :value="cusForm.source == 1?'业主':'准业主'" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label=" ">
                        <el-radio-group v-model="cusForm.belong" v-if="cusForm.source == 1" :disabled="true">
                            <el-radio label="0">个人</el-radio>
                            <el-radio label="1">单位</el-radio>
                        </el-radio-group>
                        <el-radio-group v-model="cusForm.belong" v-else :disabled="true">
                            <el-radio label="个人">个人</el-radio>
                            <el-radio label="单位">单位</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="性别:">
                        <el-input type="text" v-model="cusForm.sex" :disabled="true" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="5">
                    <el-form-item label="移动电话:">
                        <el-input type="text" v-model="cusForm.telephone" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="传真电话:">
                        <el-input type="text" v-model="cusForm.fax" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="固定电话:">
                        <el-input type="text" v-model="cusForm.fixedTelephone" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="电子邮件:">
                        <el-input type="text" v-model="cusForm.email" :disabled="true" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="11">
                    <el-form-item label="联系地址:">
                        <el-input type="text" v-model="cusForm.contactAddress" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="邮政编码:">
                        <el-input type="text" v-model="cusForm.postcode" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="国籍:">
                        <el-input type="text" v-model="cusForm.national" :disabled="true" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="5">
                    <el-form-item label="证件类型:">
                        <el-input type="text" v-model="cusForm.certificateName" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="证件号码:">
                        <el-input type="text" v-model="cusForm.certificateNum" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="出生日期:">
                        <el-date-picker v-model="cusForm.birthday" type="date" value-format="yyyy-MM-dd" :disabled="true" placeholder="选择日期" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="兴趣爱好:">
                        <el-input type="text" v-model="cusForm.hobbies" :disabled="true" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="11">
                    <el-form-item label="工作单位:">
                        <el-input type="text" v-model="cusForm.workUnit" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-form-item label="职业:">
                        <el-input type="text" v-model="cusForm.profession" :disabled="true" />
                    </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1" v-if="cusForm.abnormal == 0">
                    <el-form-item label="同步时间:">
                        <el-date-picker v-model="cusForm.updateDate" type="date" format="yyyy-MM-dd HH:mm:ss" :disabled="true" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
import { otherCode, retain } from "@/api/baseMsg";
export default {
  data() {
    return {
      fixTop: 0,
      loading: false,
      cusForm: {},
      custInfos: []
    };
  },
  methods: {
    getOtherCode() {
      otherCode(this.$route.query).then(res => {
        if (res.sucess) this.custInfos = res.data;
      });
    },
    retainRow(custInfo) {
      if (custInfo.id) {
        this.$confirm(`确定保留当前 ${custInfo.custName} 客户的数据么？`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          center: true,
          showClose: false
        }).then(() => {
          retain({
            id: custInfo.id,
            certificateNum: custInfo.certificateNum
          }).then(res => {
            this.$message("保留处理完成");
            this.goback();
          });
        });
      }
    },
    goback() {
      window.history.go(-1);
    }
  },
  created() {
    this.getOtherCode();
  },
  mounted() {
    let headerHeight = document.getElementById("homepage-header").offsetHeight;
    let iframeHeight =
      document.getElementById("if-rame") && this.userInfo.seats
        ? document.getElementById("if-rame").offsetHeight
        : 0;
    this.fixTop = Number(headerHeight) + Number(iframeHeight) + 8 + "px";
  }
};
</script>


<style lang="scss" scoped>
.btnDown {
  margin-right: 15px;
}
.card-form {
  background-color: #ffffff;
  padding: 15px 0;
  padding-top: 5px;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}
.table-title {
  display: inline-block;
  height: 13px;
  line-height: 14px;
  font-size: 14px;
  padding: 3px;
  border-left: solid 2px #e63f3c;
  margin-left: 10px;
}
#flex-top {
  position: fixed;
  right: 10px;
  z-index: 1000;
}
#flex-top .btnDown {
  margin: 0 5px;
  margin-top: 2px;
}
</style>