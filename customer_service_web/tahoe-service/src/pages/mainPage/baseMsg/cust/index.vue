<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="90px" size="mini">
                <div style="height:10px;"></div>
                <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="区域">
                            <el-select v-model="searchData.regionCode" filterable placeholder="请选择" @change="getCityList">
                                <el-option v-for="item in areaOptions" :key="item.id" :label="item.region" :value="item.regionCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="城市">
                            <el-select v-model="searchData.cityCompanyCode" :disabled="cityprojectDisabled" filterable placeholder="请选择" @change="getProjectList">
                                <el-option v-for="item in cityOptions" :key="item.cityCompanyCode" :label="item.cityCompany" :value="item.cityCompanyCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="项目">
                            <el-select v-model="searchData.projectCode" :disabled="projectDisabled" filterable placeholder="请选择">
                                <el-option v-for="item in projectOptions" :key="item.projectCode" :label="item.project" :value="item.projectCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="客户姓名">
                            <el-input v-model="searchData.custName" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="mt-10" v-if="flag">
                    <el-col :span='5' :offset="0">
                        <el-form-item label="客户电话">
                            <el-input v-model="searchData.telephone" placeholder="请输入" @blur="removeBlank()"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="身份证号">
                            <el-input v-model="searchData.certificateNum" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span='5' :offset="1">
                        <el-form-item label="房屋编号">
                            <el-input v-model="searchData.sHouseNum" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span='5' :offset="1">
                        <el-form-item label="房屋名称">
                            <el-input v-model="searchData.sHouseName" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="个人/单位">
                            <el-select v-model="searchData.belong" filterable placeholder="请选择">
                                <el-option value="个人" />
                                <el-option value="单位" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="box" v-if="flag">
                    <el-row class="mt-10">
                        <el-col :span="5" :offset="0">
                            <el-form-item label="是否有多套房">
                                <el-select v-model="searchData.isHasMoreHouse" filterable placeholder="请选择">
                                    <el-option value="1" label="是" />
                                    <el-option value="0" label="否" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="出生日期">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.bStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.bEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="客户身份">
                                <el-select v-model="searchData.cusIdentity" filterable placeholder="请选择">
                                    <el-option value="业主" />
                                    <el-option value="准业主" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row class="mt-10">
                        <el-col :span="5" :offset="0">
                            <el-form-item label="电子邮件">
                                <el-input v-model="searchData.email" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="国籍">
                                <el-input v-model="searchData.national" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="管家姓名">
                                <el-input v-model="searchData.stewardName" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="性别">
                                <el-select v-model="searchData.sex" filterable placeholder="请选择">
                                    <el-option value="男" />
                                    <el-option value="女" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <el-row class="mt-10">
                    <el-col :span="24" style="text-align:right;padding-right:4%;">
                        <button class="btnDown" @click.prevent="searchReturn()" style="margin-left:20px;margin-top:3px;">查询</button>
                        <button class="btnDown" @click.prevent="download" style="margin-left:20px;margin-top:3px;">导出</button>
                        <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
                    </el-col>
                </el-row>
                <el-row class="mt-10">
                    <el-col :span="24">
                        <div class="ta-ct mt-10" style="position:relative;height:30px;">
                            <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                            <span class="txt" @click.prevent="change()">
                                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
                            </span>
                        </div>
                    </el-col>
                </el-row>

            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">
            <div style="width:100%;overflow:hidden;">
                <el-table v-loading="loading" stripe element-loading-text="拼命加载中" tooltip-effect="dark" :data="list" style="width: 100%">
                    <el-table-column label="客户姓名" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.custName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="cusIdentity" label="客户身份" show-overflow-tooltip/>
                    <el-table-column prop="telephone" label="客户电话" show-overflow-tooltip/>
                    <el-table-column label="出生日期">
                        <template slot-scope="scope">
                            <span>{{scope.row.birthday,'day' | TimeMoment}}</span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="profession" label="职业" />
                    <el-table-column prop="isVip" label="VIP" :formatter="isFormatter" />
                    <el-table-column prop="sugLeader" label="意见领袖" :formatter="isFormatter" />
                    <!-- <el-table-column prop="houseNum" label="关联房屋" width="350" show-overflow-tooltip/> -->
                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="searchData.pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturnListOK"></wcg-page>

            </div>
        </div>

    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import { uList } from "@/api/baseMsg";
import { exportFiles } from "@/api/export/exp";
import {
  area,
  searchInterimReports,
  searchInterimReport,
  ReportStep,
  getRegionInfoByCode
} from "@/api/port";
export default {
  props: [],
  components: { wcgPage },
  watch: {
    "searchData.regionCode"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.floorDisabled = true;
          this.projectOptions = [];
          this.buildOptions = [];
          this.$set(this.searchData, "cityCompanyCode", "");
          this.$set(this.searchData, "projectCode", "");
        }
      } else {
        this.cityprojectDisabled = true;
        if ((this.cityprojectDisabled = true)) {
          this.projectDisabled = true;
        }
        this.floorDisabled = true;
        this.projectDisabled = true;
      }
    },
    "searchData.cityCompanyCode"(newval, oldval) {
      if (newval != oldval) {
        this.$set(this.searchData, "projectCode", "");
      }
      if (newval == "") {
        this.projectDisabled = true;
      } else {
        this.projectDisabled = false;
      }
    }
  },
  data() {
    return {
      flag: false,
      loading: false,
      currentPage: 1,
      total: 0,
      cityDisabled: "",
      lcDisabled: false,
      oneDisabled: true,
      projectDisabled: true,
      searchData: {
        pageSize: 20
      },
      step: [],
      areaOptions: [],
      projectOptions: [],
      cityOptions: [],
      buildprojectDisabled: true,
      cityprojectDisabled: true,
      list: []
    };
  },
  methods: {
    download() {
      let params = JSON.parse(JSON.stringify(this.searchData));
      delete params.pageNum;
      delete params.pageSize;
      exportFiles(params, "/csCustInfo/exportCustInfo");
      this.$message({ type: "success", message: "正在导出数据，请等待" });
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.regionCode
      }).then(res => {
        console.log(res.data);
        this.projectOptions = res.data;
      });
    },
    change() {
      this.flag = !this.flag;
    },
    removeBlank() {
      this.searchData.telephone = (this.searchData.telephone || "").replace(
        /^\s*|\s*$/g,
        ""
      );
    },
    goDetail(item) {
      //跳转详情
      this.$router.push({
        path: "custInfo",
        query: {
          id: item.custId,
          path: this.$route.path,
          disabled: true
        }
      });
    },
    searchReturn() {
      this.currentPage = 1;
      this.searchData.pageSize = 20;
      this.searchReturnListOK();
    },
    searchReturnListOK(n) {
      if (n) {
        this.searchData.pageNum = n;
      } else {
        this.searchData.pageNum = 1;
      }
      this.loading = true;
      this.list = [];
      uList(this.searchData).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          this.currentPage = res.data.current;
          this.total = res.data.total;
        }
      });
    },
    reset() {
      this.searchData = {};
    },
    handleSizeChange(val) {
      this.searchData.pageSize = val;
      this.searchReturnListOK();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchReturnListOK(val);
    },
    isFormatter(v, c) {
      if (v[c.property] == "1") return "是";
      if (v[c.property] == "0") return "否";
    }
  },
  mounted() {},
  created() {
    this.areaList();
    this.searchReturnListOK();
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.search-bar{
  min-width: 1100px;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.mb-20 {
  /* height: 400px; */
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,
.levelUpTag img {
  vertical-align: middle;
  width: 32px;
  height: 16px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
