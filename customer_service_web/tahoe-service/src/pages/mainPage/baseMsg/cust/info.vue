<template>
    <div class="cust-info">
        <el-form class="card-form" :model="cusForm" ref="cusform" :rules="cusFormRules" label-position="right" label-width="120px">
            <el-row style="margin-bottom:10px;border-bottom:solid thin rgba(0,0,0,0.1);padding:10px;">
                <el-col :span="7" style="height:10px;text-align:left;">
                    <span class="table-title">客户信息</span>
                </el-col>
                <el-col :span="7" :offset="1" style="height:10px;"></el-col>
                <el-col :span="7" :offset="2" style="text-align:right;">
                    <span class="btnDown" @click="saveAndSubmit">提交</span>
                    <span class="btnDown" @click="goback">返回</span>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="isBigScreen?5:7">
                    <el-form-item label="客户姓名:">
                        <el-input type="text" v-model="cusForm.custName" :disabled="true"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="客户身份:">
                        <el-select v-model="cusForm.cusIdentity" :disabled="true" placeholder="请选择">
                            <el-option
                                v-for="item in cusIdentityOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label=" ">
                        <el-radio-group v-model="cusForm.belong" v-if="cusForm.source == 1" :disabled="isReport">
                            <el-radio label="0">个人</el-radio>
                            <el-radio label="1">单位</el-radio>
                        </el-radio-group>
                        <el-radio-group v-model="cusForm.belong" v-else :disabled="isReport">
                            <el-radio label="个人">个人</el-radio>
                            <el-radio label="单位">单位</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :class="isBigScreen?'':'clear-both-item'" :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="性别:">
                        <el-select v-model="cusForm.sex" placeholder="请选择" :disabled="isReport">
                            <el-option
                                v-for="item in sexOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :class="isBigScreen?'':'clear-both-item'" >
                    <el-form-item label="客户标签:">
                        <el-tag type="info" style="cursor: pointer;" @click="addTagVisible = true">+ 添加标签</el-tag>
                        <el-tag :key="tag" style="margin: 0 5px;color:#000;" v-for="(tag,index) in cusForm.labelName?cusForm.labelName.split(','):[]" closable :disable-transitions="false" @close="handleClose(index)">
                            {{tag}}
                        </el-tag>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="isBigScreen?5:7" :offset="isBigScreen?0:1">
                    <el-form-item label="VIP:">
                        <el-select v-model="cusForm.isVip" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="意见领袖:">
                        <el-select v-model="cusForm.sugLeader" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col> -->
            </el-row>
            <el-row>
                <el-col v-for="(item,index) in telephoneArr" :key="index" :span="isBigScreen?5:7" :offset="telOffset(index)">
                    <el-form-item :label="index==0?'移动电话:':''" :prop="item.tel!=null || item.tel!=''?'telephone'+index:''">
                        <el-input class="addTelInput" v-model="item.tel" type="text" :disabled="isReport"/>
                        <span v-if="!isReport && telephoneArr.length!=1" class="el-icon-remove-outline" @click="removeInput(index)"></span>
                        <span v-if="!isReport && ((index==telephoneArr.length-1 && item.tel!=null && item.tel!='') || (index==0 && telephoneArr.length==1))" class="el-icon-circle-plus-outline" @click="addTel"></span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="isBigScreen?5:7">
                    <el-form-item label="联系电话:">
                        <el-input v-model="cusForm.contactTel" :disabled="isReport"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="省(直辖市、自治区):">
                        <el-select v-model="cusForm.province" placeholder="请选择" @change="selectProvince" :disabled="isReport">
                            <el-option
                                v-for="item in provinceOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="市:">
                        <el-select v-model="cusForm.city" placeholder="请选择" @change="selectCity" :disabled="isReport">
                            <el-option
                                v-for="item in cityOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="区(县):">
                        <el-select v-model="cusForm.area" placeholder="请选择"  @change="selectArea" :disabled="isReport">
                            <el-option
                                v-for="item in areaOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="isBigScreen?11:15">
                    <el-form-item label="联系地址:">
                        <el-input type="text" v-model="cusForm.contactAddress" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="固定电话:">
                        <el-input type="text" v-model="cusForm.fixedTelephone" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="电子邮件:">
                        <el-input type="text" v-model="cusForm.email" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?0:1">
                    <el-form-item label="传真电话:">
                        <el-input type="text" v-model="cusForm.fax" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="邮政编码:">
                        <el-input type="text" v-model="cusForm.postcode" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="管家姓名:">
                        <el-input type="text" v-model="cusForm.stewardName" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="管家电话:">
                        <el-input type="text" v-model="cusForm.stewardTelephone" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?0:1">
                    <el-form-item label="国籍:">
                        <el-input type="text" v-model="cusForm.national" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="证件类型:">
                        <el-select v-model="cusForm.certificateName" placeholder="请选择" :disabled="true">
                            <el-option
                                v-for="item in cardTypeOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="证件号码:">
                        <el-input type="text" v-model="cusForm.certificateNum" :disabled="true"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="出生日期:">
                        <el-date-picker
                            v-model="cusForm.birthday"
                            type="date"
                            value-format="yyyy-MM-dd"
                            :disabled="true"
                            placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?11:15">
                    <el-form-item label="工作单位:">
                        <el-input type="text" v-model="cusForm.workUnit" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="职业:">
                        <el-input type="text" v-model="cusForm.profession" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="年收入:">
                        <el-select v-model="cusForm.yearsReceive" placeholder="请选择" :disabled="isReport">
                            <el-option
                                v-for="item in yearsReceiveOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="0">
                    <el-form-item label="客户积分:">
                        <el-input type="text" v-model="cusForm.integral" :disabled="isReport"/>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="isBigScreen?5:7" :offset="isBigScreen?0:1">
                    <el-form-item label="福州金卡用户:">
                        <el-select v-model="cusForm.isFZgoldCard" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="天宇拥有多套房屋:">
                        <el-select v-model="cusForm.isHasMoreHouse" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="医疗板块客户:">
                        <el-select v-model="cusForm.isMedicalCareUser" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="金融板块客户:">
                        <el-select v-model="cusForm.isFinanceUser" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?0:1">
                    <el-form-item label="地产板块客户:">
                        <el-select v-model="cusForm.isRealEstateUser" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="isBigScreen?1:0">
                    <el-form-item label="教育板块客户:">
                        <el-select v-model="cusForm.isEducationUser" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="影院教育板块客户:">
                        <el-select v-model="cusForm.isCinemaUser" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="isBigScreen?5:7" :offset="1">
                    <el-form-item label="拥有车辆:">
                        <el-select v-model="cusForm.hasCar" placeholder="请选择">
                            <el-option
                                v-for="item in baseOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col> -->
            </el-row>
            <el-row>
                <el-col :span="isBigScreen?11:15">
                    <el-form-item label="兴趣爱好:">
                        <!-- <el-input type="text" v-model="cusForm.hobbies"/> -->
                        <el-select  :disabled="isReport"
                            class="autoSelectHeight"
                            v-model="value9"
                            multiple
                            filterable
                            allow-create
                            default-first-option
                            placeholder="请输入关键词"
                            :loading="loading">
                            <el-option
                                v-for="item in options4"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <!-- cusForm.hobbies -->
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="table-container" style="margin-top:10px;padding-bottom:15px;">
            <div style="padding:10px 0px;padding-top:10px;padding-left:10px;display:flex;justify-content:space-between;border-bottom:solid 1px rgba(0,0,0,0.1)"><span class="table-title">家庭成员信息</span><span v-if="!isReport" class="btnDown" @click="addPersonVisible = true">添加成员</span></div>
             <el-table :data="tableData1" border style="width: 100%;margin-top:10px;" :stripe="true">
                <el-table-column prop="memberName" label="家庭成员"></el-table-column>
                <el-table-column prop="sex" label="性别"></el-table-column>
                <el-table-column prop="householdRelation" label="与户主关系"></el-table-column>
                <el-table-column prop="nationality" label="国籍"></el-table-column>
                <el-table-column prop="birthday" label="出生日期"></el-table-column>
                <el-table-column prop="certificateName" label="证件名称"></el-table-column>
                <el-table-column prop="idNumber" label=" 证件号码"></el-table-column>
                <el-table-column prop="mobile" label="移动电话"></el-table-column>
             </el-table>
        </div>
        <div class="table-container" style="margin-top:10px;padding-bottom:15px;">
            <div style="padding:10px 15px;padding-left:10px;margin-bottom:10px;font-size:18px;border-bottom:solid thin rgba(0,0,0,0.1)"><span class="table-title">关联房屋信息</span></div>
             <el-table :data="tableData2" border style="width: 100%" :stripe="true">
                <el-table-column prop="houseNum" label="房屋编号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="houseName" label="房屋名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="area" label="区域"></el-table-column>
                <el-table-column prop="city" label="城市"></el-table-column>
                <el-table-column prop="project" label="项目"></el-table-column>
                <el-table-column prop="signDate" label="签约时间"></el-table-column>
                <el-table-column prop="deliveryStatus" label="交付状态"></el-table-column>
             </el-table>
        </div>
        <el-dialog id="dialogPane" title="" :visible.sync="addPersonVisible" width="40%" center>
            <el-form :inline-message="true" :show-message="false" ref="cusFamily" label-position="right" :rules="cusFamilyRules" :model="cusFamily" label-width="100px">
                <el-form-item label="成员姓名" prop="memberName">
                    <el-input v-model="cusFamily.memberName"></el-input>
                </el-form-item>
                <el-form-item label="性别" prop="sex">
                    <el-select v-model="cusFamily.sex" filterable placeholder="请选择">
                        <el-option label="男" value="男">
                        </el-option>
                        <el-option label="女" value="女">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="与户主关系" prop="householdRelation">
                    <el-select v-model="cusFamily.householdRelation" placeholder="请选择" @change="changeSelect('householdRelation','householdRelation','reWithHouseholder')">
                        <el-option v-for="i in dict.filter(i=>i.dictCode =='reWithHouseholder')" :key="i.itemCode" :label="i.itemValue" :value="i.itemValue">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="国籍">
                    <el-input v-model="cusFamily.nationality"></el-input>
                </el-form-item>
                <el-form-item label="出生日期">
                    <el-date-picker style="width:100%!important;" v-model="cusFamily.birthday" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="证件名称">
                    <el-select v-model="cusFamily.certificateName" placeholder="请选择" @change="changeSelect('certificateName','certificateCode','certificateType')">
                        <el-option v-for="i in dict.filter(i=>i.dictCode =='certificateType')" :key="i.itemCode" :label="i.itemValue" :value="i.itemValue">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码">
                    <el-input v-model="cusFamily.idNumber"></el-input>
                </el-form-item>
                <el-form-item label="移动电话" prop="mobile">
                    <el-input v-model="cusFamily.mobile"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button class="btnDown" @click="sureAdd">确 定</el-button>
                <el-button class="btnDown" @click="cancelAdd">取 消</el-button>
            </span>
        </el-dialog>

        <el-dialog id="addTagPage" title="添加标签" :visible.sync="addTagVisible" width="40%">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText">
            </el-input>

            <el-tree class="filter-tree" v-if="addTagVisible" :data="tagList" show-checkbox
                node-key="id"
                :default-expanded-keys="cusForm.labelId?cusForm.labelId.split(','):[]"
                :default-checked-keys="cusForm.labelId?cusForm.labelId.split(','):[]"
                default-expand-all :filter-node-method="filterNode" ref="tagTree">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span>{{ data.itemName }}</span>
                    <span>{{ data.score }}</span>
                </span>
            </el-tree>
            <span slot="footer" class="dialog-footer">
                <el-button class="btnDown" @click="tagAdd">确 定</el-button>
                <el-button class="btnDown" @click="addTagVisible = false">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import { ciSelecthcfInfoByNum, floorMsg } from '@/api/port'
    import { dict, cfSelectByCustId } from "@/api/wsd";
    import {isSingleTelNum} from "@/api/form";
    import { saveCusInfo, saveCusFamily } from '@/api/baseMsg'
    import { selCustLabel } from "@/api/csmTag";
    export default {
        watch: {
            filterText(val) {
                this.$refs.tagTree.filter(val);
            }
        },
        data(){
            let that = this;
            var validPhone=(rule, value,callback)=>{
                if (!value){
                    callback(new Error('请输入电话号码'))
                }else  if (!isSingleTelNum(value)){
                    callback(new Error('请输入正确的11位手机号码'))
                }else {
                    callback()
                }
            }
            return{
                addPersonVisible:false,
                addTagVisible:false,
                cusFamily:{},
                loading:false,
                isBigScreen:true,
                isReport:false,
                cusFamilyRules:{
                    memberName:[
                        { required: true, message: '请填写姓名', trigger: 'blur' }
                    ],
                    sex:[
                        { required: true, message: '请选择性别', trigger: 'change' }
                    ],
                    householdRelation:[
                        { required: true, message: '请选择与户主关系', trigger: 'change' }
                    ],
                    mobile:[
                        { required: true, message: '请填写移动电话', trigger: 'blur' }
                    ],
                },
                cusFormRules:{
                    telphone0: [
                        { required: true, trigger: 'blur', validator: validPhone }
                    ],
                },
                dict:[],
                telephoneArr:[],
                cusForm:{
                    province:null,
                    city:null,
                    area:null
                },
                sexOption:[
                    {label:'男',value:'男'},
                    {label:'女',value:'女'}
                ],
                cardTypeOption:[
                    {label:'身份证',value:'身份证'},
                    {label:'居住证',value:'居住证'},
                    {label:'签证',value:'签证'},
                    {label:'护照',value:'护照'},
                    {label:'户口本',value:'户口本'},
                    {label:'军人证',value:'军人证'},
                    {label:'团员证',value:'团员证'},
                    {label:'党员证',value:'党员证'},
                    {label:'港澳通行证',value:'港澳通行证'},
                ],
                baseOption:[
                    {label:'是',value:'1'},
                    {label:'否',value:'0'}
                ],
                cusIdentityOption:[
                    {label:'业主',value:'yz'},
                    {label:'租户',value:'zh'},
                    {label:'家庭成员',value:'jtcy'},
                ],
                yearsReceiveOption:[
                    {label:'10万-20万',value:'10-20'},
                    {label:'20万-50万',value:'20-50'},
                    {label:'50万-100万',value:'50-100'},
                    {label:'100万-200万',value:'100-200'},
                    {label:'200万以上',value:'200+'},
                ],
                options4:require('@/assets/staticData/hobbies.json'),
                value9:[],
                provinceOption:[],
                cityOption:[],
                areaOption:[],
                tableData1:[],
                tableData2:[],
                areaData:require('@/assets/staticData/area.json'),
                filterText:"",
                tagList:[],
                tagProps: {
                    children: 'children',
                    label: 'itemName'
                }
            }
        },
        methods:{
            saveAndSubmit(){
                if(!this.submitForm('cusform')){
                    return;
                }
                this.$confirm('确认信息无误并保存？', {
                    confirmButtonText: '确定',
                    center: true,
                    showClose:false,
                    showCancelButton:true,
                    confirmButtonClass:'confirm-reset-style',
                }).then(() => {
                    let arr = new Array();
                    this.telephoneArr.map((item) => {
                        arr.push(item.tel)
                    })
                    this.cusForm.hobbies = this.value9.toString();
                    this.cusForm.telephone = arr.toString();
                    saveCusInfo(this.cusForm).then((res) => {
                        if(res.code == 200){
                            this.$confirm('提交成功！', {
                                confirmButtonText: '确定',
                                center: true,
                                showClose:false,
                                showCancelButton:false,
                                confirmButtonClass:'confirm-reset-style',
                            }).then(() => {
                                if(this.$route.query.isReport)window.close();
                                window.history.go(-1);
                            })
                        }else{
                            this.$confirm(res.message, {
                                confirmButtonText: '确定',
                                center: true,
                                showClose:false,
                                showCancelButton:false,
                                confirmButtonClass:'confirm-reset-style',
                            }).then(()=>{

                            })
                        }
                    })
                }).catch(() => {

                })
            },
            telOffset(index){
                if(this.isBigScreen){
                    if(index%4==0){
                        return 0;
                    }else{
                        return 1;
                    }
                }else if(!this.isBigScreen){
                    if(index%3==0){
                        return 0;
                    }else{
                        return 1;
                    }
                }
            },
            goback(){
                if(this.$route.query.isReport)window.close();
                window.history.go(-1);
            },
            selectProvince(val){
                this.cityOption = [];
                this.areaOption = [];
                this.$set(this.cusForm,'city',null);
                this.$set(this.cusForm,'area',null);
                this.provinceOption.map((item) => {
                    if(item.value == this.cusForm.province){
                        item.city.map((citem) => {
                            let obj = new Object();
                            obj.label = citem.name;
                            obj.value = citem.name;
                            obj.area = citem.area;
                            this.cityOption.push(obj);
                        })
                    }
                })
            },
            selectCity(val){
                this.areaOption = [];
                this.$set(this.cusForm,'area',null);
                this.cityOption.map((item) => {
                    if(item.value == this.cusForm.city){
                        item.area.map((aitem) => {
                            let obj = new Object();
                            obj.label = aitem;
                            obj.value = aitem;
                            this.areaOption.push(obj);
                        })
                    }
                })
            },
            selectArea(val){

            },
            changeSelect(k, v, d) {
                if (this.dict && Array.isArray(this.dict)) {
                    let s = this.dict.filter(
                    i => i.itemCode == this.cusFamily[v] && i.dictCode == d
                    );
                    if (s && s.length == 1) this.$set(this.cusFamily, k, s[0].itemValue);
                }
            },
            addTel(){
                let obj = new Object();
                obj.tel = null;
                this.telephoneArr.push(obj);
            },
            removeInput(index){
                this.telephoneArr.splice(index,1);
            },
            sureAdd(){
                if(this.submitForm('cusFamily')){
                    this.cusFamily.custId = this.$route.query.id;
                    saveCusFamily(this.cusFamily).then((res) => {
                        if(res.code == 200){
                            this.tableData1.push(this.cusFamily);
                            this.cusFamily = {};
                            this.addPersonVisible = false;
                        }else{
                            this.$confirm(res.message, {
                                confirmButtonText: '确定',
                                center: true,
                                showClose:false,
                                showCancelButton:false,
                                confirmButtonClass:'confirm-reset-style',
                            }).then(() => {})
                        }
                    })
                }else{
                    console.log('信息不完整！')
                }
            },
            cancelAdd(){
                this.cusFamily = {};
                this.addPersonVisible = false;
            },
            getPageData(){
                let params = {
                    id:this.$route.query.id,
                    custId: this.$route.query.id
                }
                ciSelecthcfInfoByNum(params).then((res) => {
                    if(res.code == 200){
                        this.cusForm = res.data;
                        if(this.cusForm.hobbies){
                            this.value9 = this.cusForm.hobbies.split(',');
                        }
                        if(res.data.telephone == null){
                            let obj = {tel:null};
                            this.telephoneArr.push(obj);
                        }else{
                            this.cusForm.telephone = res.data.telephone.split(',');
                            this.cusForm.telephone.map((item,index) => {
                                let obj = new Object()
                                obj.tel = item;
                                this.telephoneArr.push(obj);
                                this.$set(this.cusForm,'telephone'+index,item);
                            })
                        }
                        if(this.cusForm.province){
                            let addOption = new Promise((resolve,reject) => {
                                this.areaData.map((item) => {
                                    if(item.name == this.cusForm.province){
                                        this.cityOption = [];
                                        item.city.map((citem,index) => {
                                            let obj = new Object()
                                            obj.label = citem.name;
                                            obj.value = citem.name;
                                            this.cityOption.push(obj);
                                            if(index == item.city.length-1){
                                                resolve(item.city);
                                            }
                                        })
                                    }
                                })
                            })
                            addOption.then((citys) => {
                                citys.map((item) => {
                                    if(item.name == this.cusForm.city){
                                        this.areaOption = [];
                                        item.area.map((aitem) => {
                                            let obj = new Object();
                                            obj.label = aitem;
                                            obj.value = aitem;
                                            this.areaOption.push(obj);
                                        })
                                    }
                                })
                            })
                        }
                    }
                })
                floorMsg(params).then((res) => {
                    if(res.code == 200){
                        this.tableData2 = res.data.records
                    }
                })
                cfSelectByCustId(params).then((res) => {
                    if(res.code == 200){
                        this.tableData1 = res.data;
                    }
                })
            },
            getAllDict(){
                dict().then((res) => {
                    if (res.sucess && res.data) {
                        this.dict = res.data;
                    }
                })
            },
            submitForm(formName){//验证必填项
                let flag = true;
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                    flag = true;
                    } else {
                    flag = false;
                    }
                });
                return flag;
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.itemName.indexOf(value) !== -1;
            },
            selCustLabel(){
                selCustLabel({flag:'cust'}).then(res => {
                    if (res.code == 200) {
                        this.loading = false;
                        this.tagList = res.data[0].children;
                    }
                });
            },
            tagAdd(){
                var tag = "";
                var tagId = "";
                if(this.$refs.tagTree.getCheckedNodes(true)){
                    this.$refs.tagTree.getCheckedNodes(true).forEach(e => {
                       tag += e.itemName + ',';
                       tagId += e.id + ',';
                    });
                }
                this.$set(this.cusForm,'labelName',tag.length > 0 ? tag.substring(0,tag.length - 1): "");
                this.$set(this.cusForm,'labelId',tagId.length > 0 ? tagId.substring(0,tagId.length - 1): "");
                this.addTagVisible = false;
            },
            handleClose(index){
                let ids = this.cusForm.labelId.split(',');
                let names = this.cusForm.labelName.split(',');
                ids.splice(index,1);
                names.splice(index,1);
                this.$set(this.cusForm,'labelId',ids.length>0?ids.join(","):"");
                this.$set(this.cusForm,'labelName',names.length>0?names.join(","):"");
            }
        },
        created(){
            this.isReport = this.$route.query.isReport || false;
            this.areaData.map((item) => {
                let obj = new Object();
                obj.label = item.name;
                obj.value = item.name;
                obj.city = item.city;
                this.provinceOption.push(obj)
            })
            this.getPageData();
            this.getAllDict();
            this.selCustLabel();
        },
        mounted() {
            let screenWidth = document.body.offsetWidth;
            if(screenWidth >= 1600){
                this.isBigScreen = true;
            }else{
                this.isBigScreen = false;
            }
            window.onload = () => {
                window.onresize = () => {
                if(document.body.offsetWidth >= 1600){
                    this.isBigScreen = true;
                }else{
                    this.isBigScreen = false;
                }
            }
            }
        }
    }
</script>

<style lang="scss" scoped>
.btnDown{
    margin-right:15px;
}
.card-form,.table-container{
    background-color:#ffffff;
    padding:15px 0;
    padding-top:5px;
    border-radius:5px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1)
}
.table-title{
    display: inline-block;
    height: 13px;
    line-height: 14px;
    font-size: 14px;
    padding: 3px;
    border-left: solid 2px #E63F3C;
    margin-left: 10px;
}
.el-icon-circle-plus-outline,.el-icon-remove-outline{
    font-size:16px;
    font-weight:bold;
    color:#E63F3C;
}
.addTelInput{
    width:70%;
}
.clear-both-item{
    clear:both;
}
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}
</style>
