<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="90px" size="mini">
                <div style="height:10px;"></div>
                <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="区域">
                            <el-select filterable v-model="searchData.regionCode" placeholder="请选择">
                                <el-option v-for="item in areaOptions" :key="item.regionCode" :label="item.region" :value="item.regionCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="城市">
                            <el-select filterable v-model="searchData.cityCompanyCode" :disabled="searchData.regionCode?false:true" placeholder="请选择">
                                <el-option v-for="item in cityOptions.filter(i => i.regionCode == this.searchData.regionCode)" :key="item.cityCompanyCode" :label="item.cityCompany" :value="item.cityCompanyCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="项目">
                            <el-select filterable v-model="searchData.projectId" :disabled="searchData.cityCompanyCode?false:true" placeholder="请选择">
                                <el-option v-for="item in projectList.filter(i => i.cityCompanyCode == this.searchData.cityCompanyCode)" :key="item.projectCode" :label="item.project" :value="item.projectCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="5" :offset="1">
                        <el-form-item label="交付状态">
                            <el-select v-model="searchData.deliveryStatus" filterable placeholder="请选择">
                                <el-option label="全部" value="" />
                                <el-option label="已交付" value="1" />
                                <el-option label="未交付" value="0" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <!-- <el-col :span='5' :offset="1">
                        <el-form-item label="楼栋">
                            <el-input v-model="searchData.building" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col> -->
                </el-row>

                <!-- <div class="box" v-if="flag">
                    <el-row class="mt-10" style="margin-top:10px;">
                        <el-col :span="5" :offset="0">
                            <el-form-item label="交付状态">
                                <el-select v-model="searchData.deliveryStatus" filterable placeholder="请选择">
                                    <el-option label="已交付" value="1" />
                                    <el-option label="未交付" value="0" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div> -->
                <el-row class="mt-10">
                    <el-col :span="24" style="text-align:right;padding-right:4%;margin-bottom:5px;">
                        <button class="btnDown" @click.prevent="searchReturn()" style="margin-left:20px;margin-top:3px;">查询</button>
                        <!-- <button class='btnDown' @click="download()" style="margin-left:10px;margin-top:3px;">导出</button> -->
                        <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
                    </el-col>
                </el-row>
                <!-- <el-row class="mt-10">
                    <el-col :span="24">
                        <div class="ta-ct mt-10" style="position:relative;height:30px;">
                            <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                            <span class="txt" @click.prevent="change()">
                                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
                            </span>
                        </div>
                    </el-col>
                </el-row> -->

                <div style="height:10px;"></div>
            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">
            <div style="height:44px;line-height:44px;">
                <span class="redSide ml-10"> </span>
                <span class="ft-14 fw-bd">交付数据列表</span>
                <button class='btnDown fr mt-10 mr-10' @click="updateDelivery(0)">未交付</button>
                <button class='btnDown fr mt-10 mr-10' @click="updateDelivery(1)">已交付</button>
                <button class='btnDown fr mt-10 mr-10' @click="download()" >导出</button>
            </div>
            <div style="width:100%;overflow:hidden;">
                <el-table ref="deliverRef" v-loading="loading" stripe element-loading-text="拼命加载中" tooltip-effect="dark" :data="list" style="width: 100%">
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="area" label="区域" show-overflow-tooltip/>
                    <el-table-column prop="city" label="城市" show-overflow-tooltip/>
                    <el-table-column prop="project" label="项目" />
                    <el-table-column prop="building" label="楼栋" />
                    <el-table-column label="交付状态">
                        <template slot-scope="scope">
                            {{scope.row.deliveryStatus == '1'?"已交付":scope.row.deliveryStatus == 0?"未交付":""}}
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="searchData.pageSize" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturnListOK"></wcg-page>

            </div>
        </div>

    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import { project } from "@/api/wsd";
import { updateDelivery, selectDelivery } from "@/api/baseMsg";
import { exportFiles } from "@/api/export/exp";
export default {
  props: [],
  components: { wcgPage },
  watch: {
    "searchData.regionCode"() {
      this.$set(this.searchData, "cityCompanyCode", "");
    },
    "searchData.cityCompanyCode"() {
      this.$set(this.searchData, "projectId", "");
    }
  },
  data() {
    return {
      flag: false,
      loading: false,
      total: 0,
      searchData: {
        pageSize: 20
      },
      areaOptions: [],
      cityOptions: [],
      projectList: [],
      list: []
    };
  },
  methods: {
    download() {
      let sel = this.$refs.deliverRef.selection;
      if (sel != null && sel.length > 0) {
        let p = "",
          b = "";
        sel.forEach(e => {
          p += "," + e.projectId;
          b += "," + e.building;
        });
        let params = JSON.parse(JSON.stringify({
        projectIds: p.substring(1),
        buildings: b.substring(1)
        }))
    //   let params = JSON.parse(JSON.stringify(this.searchData));
    //   delete params.pageNum;
    //   delete params.pageSize;
        exportFiles(params, "/csHouseInfo/expDeliverNew");
        this.$message({ type: "success", message: "正在导出数据，请等待" });
      } else {
        this.$message({ type: "error", message: "请选择需要导出数据" });
      }
    },
    change() {
      this.flag = !this.flag;
    },
    searchReturn() {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = 20;
      this.searchReturnListOK();
    },
    searchReturnListOK() {
      this.loading = true;
      this.list = [];
      selectDelivery(this.searchData).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          this.total = res.data.total;
        }
      });
    },
    reset() {
      this.searchData = { pageNum: 1, pageSize: 20 };
    },
    handleSizeChange(val) {
      this.searchData.pageSize = val;
      this.searchReturnListOK();
    },
    handleCurrentChange(val) {
      this.searchData.pageNum = val;
      this.searchReturnListOK();
    },
    updateDelivery(sta) {
      let sel = this.$refs.deliverRef.selection;
      if (sel != null && sel.length > 0) {
        let p = "",
          b = "";
        sel.forEach(e => {
          p += "," + e.projectId;
          b += "," + e.building;
        });
        updateDelivery({
          projectId: p.substring(1),
          building: b.substring(1),
          deliveryStatus: sta
        }).then(res => {
          if (res.sucess) {
            this.searchReturnListOK();
            this.$message({
              type: "success",
              message: "处理成功"
            });
          } else
            this.$message({
              type: "error",
              message: res.message
            });
        });
      }
    }
  },
  mounted() {
    project().then(res => {
      this.projectList = res.data;
      let rList = [];
      let cList = [];
      this.projectList.forEach(v => {
        rList.push(v.regionCode);
        cList.push(v.cityCompanyCode);
      });
      rList = rList.filter((v, i, s) => s.indexOf(v) == i);
      cList = cList.filter((v, i, s) => s.indexOf(v) == i);
      rList.forEach(v => {
        let r = this.projectList.filter(i => i.regionCode == v)[0];
        this.areaOptions.push({ region: r.region, regionCode: r.regionCode });
      });
      cList.forEach(v => {
        let c = this.projectList.filter(i => i.cityCompanyCode == v)[0];
        this.cityOptions.push({
          region: c.region,
          regionCode: c.regionCode,
          cityCompany: c.cityCompany,
          cityCompanyCode: c.cityCompanyCode
        });
      });
    });
  },
  created() {
    this.searchReturnListOK();
  }
};
</script>

