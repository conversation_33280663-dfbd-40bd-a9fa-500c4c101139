<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="90px" size="mini">
                <div style="height:10px;"></div>
                <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="区域">
                            <el-select filterable v-model="searchData.regionCode" placeholder="请选择">
                                <el-option v-for="item in areaOptions" :key="item.regionCode" :label="item.region" :value="item.regionCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="城市">
                            <el-select filterable v-model="searchData.cityCompanyCode" :disabled="searchData.regionCode?false:true" placeholder="请选择">
                                <el-option v-for="item in cityOptions.filter(i => i.regionCode == this.searchData.regionCode)" :key="item.cityCompanyCode" :label="item.cityCompany" :value="item.cityCompanyCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="项目">
                            <el-select filterable v-model="searchData.projectId" :disabled="searchData.cityCompanyCode?false:true" placeholder="请选择">
                                <el-option v-for="item in projectList.filter(i => i.cityCompanyCode == this.searchData.cityCompanyCode)" :key="item.projectCode" :label="item.project" :value="item.projectCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="楼栋">
                            <el-input v-model="searchData.building" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="box" v-if="flag">
                    <el-row class="mt-10" style="margin-top:10px;">
                        <el-col :span="5" :offset="0">
                            <el-form-item label="客户姓名">
                                <el-input v-model="searchData.custName" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span='5' :offset="1">
                            <el-form-item label="房屋编号">
                                <el-input v-model="searchData.houseNum" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col> -->
                        <el-col :span='5' :offset="1">
                            <el-form-item label="房屋名称">
                                <el-input v-model="searchData.houseName" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="产品类型">
                                <el-select v-model="searchData.useProperty" filterable placeholder="请选择">
                                    <el-option v-for="item in useProperty" v-if="item" :key="item.useProperty" :label="item.useProperty" :value="item.useProperty"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="房屋装修类型">
                                <el-select v-model="searchData.fitment" filterable placeholder="请选择">
                                    <el-option v-for="item in fitment" v-if="item" :key="item.fitment" :label="item.fitment == '0'?'精装':item.fitment" :value="item.fitment"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mt-10">
                        <el-col :span='5' :offset="0">
                            <el-form-item label="取证时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.obtainStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.obtainEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="签约时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.sStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.sEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mt-10">
                        <el-col :span='5' :offset="0">
                            <el-form-item label="合同交房时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.dStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.dEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="集中交房时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.focusStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.focusEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mt-10">
                        <el-col :span='5' :offset="0">
                            <el-form-item label="实际交房时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.aStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.aEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="预计脱保时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.oStartDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.oEndDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row class="mt-10">
                        <el-col :span="5" :offset="0">
                            <el-form-item label="交付状态">
                                <el-select v-model="searchData.deliveryStatus" filterable placeholder="请选择">
                                    <el-option label="已交付" value="1" />
                                    <el-option label="未交付" value="0" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <el-row class="mt-10">
                    <el-col :span="24" style="text-align:right;padding-right:4%;">
                        <button class="btnDown" @click.prevent="searchReturn()" style="margin-left:20px;margin-top:3px;">查询</button>
                        <button class="btnDown" @click.prevent="download" style="margin-left:20px;margin-top:3px;">导出</button>
                        <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
                    </el-col>
                </el-row>
                <el-row class="mt-10">
                    <el-col :span="24">
                        <div class="ta-ct mt-10" style="position:relative;height:30px;">
                            <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                            <span class="txt" @click.prevent="change()">
                                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
                            </span>
                        </div>
                    </el-col>
                </el-row>

            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">
            <div style="width:100%;overflow:hidden;">
                <el-table v-loading="loading" stripe element-loading-text="拼命加载中" tooltip-effect="dark" :data="list" style="width: 100%">
                    <!-- <el-table-column label="房屋编号" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.houseNum}}</span>
                        </template>
                    </el-table-column> -->
                    <el-table-column label="房屋名称" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.houseName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="area" label="区域" width="100" show-overflow-tooltip/>
                    <el-table-column prop="city" label="城市" width="100" show-overflow-tooltip/>
                    <el-table-column prop="project" label="项目" />
                    <el-table-column label="签约时间" width="90">
                        <template slot-scope="scope">
                            <span>{{scope.row.signDate,'day' | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="交付状态">
                        <template slot-scope="scope">
                            {{scope.row.deliveryStatus == '1'?"已交付":scope.row.deliveryStatus == 0?"未交付":""}}
                        </template>
                    </el-table-column>
                    <el-table-column label="合同交房时间" width="100">
                        <template slot-scope="scope">
                            <span>{{scope.row.deliveryDate,'day' | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="预计脱保时间" width="100">
                        <template slot-scope="scope">
                            <span>{{scope.row.offAidDate,'day' | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="searchData.pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturnListOK"></wcg-page>

            </div>
        </div>

    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import { dict, project } from "@/api/wsd";
import { hList, getSelect } from "@/api/baseMsg";
import { exportFiles } from "@/api/export/exp";
export default {
  props: [],
  components: { wcgPage },
  watch: {
    "searchData.regionCode"() {
      this.$set(this.searchData, "cityCompanyCode", "");
    },
    "searchData.cityCompanyCode"() {
      this.$set(this.searchData, "projectId", "");
    }
  },
  data() {
    return {
      flag: false,
      loading: false,
      currentPage: 1,
      total: 0,
      cityDisabled: "",
      lcDisabled: false,
      oneDisabled: true,
      projectDisabled: true,
      searchData: {
        pageSize: 20
      },
      step: [],
      areaOptions: [],
      cityOptions: [],
      projectList: [],
      buildprojectDisabled: true,
      cityprojectDisabled: true,
      list: [],
      dict: [],
      fitment: [],
      useProperty: []
    };
  },
  methods: {
    download() {
      let params = JSON.parse(JSON.stringify(this.searchData));
      delete params.pageNum;
      delete params.pageSize;
      exportFiles(params, "/csHouseInfo/exportHouseInfo");
      this.$message({ type: "success", message: "正在导出数据，请等待" });
    },
    change() {
      this.flag = !this.flag;
    },
    goDetail(item) {
      //跳转详情
      this.$router.push({
        path: "houseInfo",
        query: {
          id: item.houseNum,
          title: "报事查询",
          path: this.$route.path,
          disabled: true
        }
      });
    },
    searchReturn() {
      this.currentPage = 1;
      this.searchData.pageSize = 20;
      this.searchReturnListOK();
    },
    searchReturnListOK(n) {
      if (n) {
        this.searchData.pageNum = n;
      } else {
        this.searchData.pageNum = 1;
      }
      this.loading = true;
      this.list = [];
      hList(this.searchData).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          this.currentPage = res.data.current;
          this.total = res.data.total;
        }
      });
    },
    reset() {
      this.searchData = {};
    },
    handleSizeChange(val) {
      this.searchData.pageSize = val;
      this.searchReturnListOK();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchReturnListOK(val);
    }
  },
  mounted() {
    project().then(res => {
      this.projectList = res.data;
      let rList = [];
      let cList = [];
      this.projectList.forEach(v => {
        rList.push(v.regionCode);
        cList.push(v.cityCompanyCode);
      });
      rList = rList.filter((v, i, s) => s.indexOf(v) == i);
      cList = cList.filter((v, i, s) => s.indexOf(v) == i);
      rList.forEach(v => {
        let r = this.projectList.filter(i => i.regionCode == v)[0];
        this.areaOptions.push({ region: r.region, regionCode: r.regionCode });
      });
      cList.forEach(v => {
        let c = this.projectList.filter(i => i.cityCompanyCode == v)[0];
        this.cityOptions.push({
          region: c.region,
          regionCode: c.regionCode,
          cityCompany: c.cityCompany,
          cityCompanyCode: c.cityCompanyCode
        });
      });
    });
  },
  created() {
    this.searchReturnListOK();
    getSelect().then(res => {
      if (res.sucess) {
        this.fitment = res.data.fitment;
        this.useProperty = res.data.useProperty;
      }
    });
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.search-bar{
  min-width: 1100px;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.mb-20 {
  /* height: 400px; */
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,
.levelUpTag img {
  vertical-align: middle;
  width: 32px;
  height: 16px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
