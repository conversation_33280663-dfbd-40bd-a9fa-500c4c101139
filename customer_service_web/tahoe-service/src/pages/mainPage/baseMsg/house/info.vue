<template>
  <div>
    <div class="bg-style mt-10">
      <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
        <span class="redSide ml-10"> </span>
        <span class="ft-14 fw-bd">房屋信息</span>
        <span class="btnDown" @click="cancelLastPage" style="float:right;margin-top:12px;margin-right:20px;">返回</span>
      </div>
      <div>
        <el-form :inline-message="true" label-position="right" label-width="100px">
          <el-row>
            <el-col :span="11" :offset="0">
              <el-form-item label="房屋编号">
                <el-input disabled v-model="houseDto.houseNum"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="房屋名称">
                <el-input disabled v-model="houseDto.houseName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5" :offset="0">
              <el-form-item label="区域">
                <el-input disabled v-model="houseDto.area"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="城市">
                <el-input disabled v-model="houseDto.city"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="项目">
                <el-input disabled v-model="houseDto.project"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="楼栋">
                <el-input disabled v-model="houseDto.building"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="0">
              <el-form-item label="单元号">
                <el-input disabled v-model="houseDto.unit"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="房间号">
                <el-input disabled v-model="houseDto.roomNum"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="使用性质">
                <el-input disabled v-model="houseDto.useProperty"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="交付状态">
                <el-input disabled :value="houseDto.deliveryStatus?houseDto.deliveryStatus=='1'?'已交付':'未交付':''"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="0">
              <el-form-item label="合同交房时间">
                <el-date-picker v-model="houseDto.deliveryDate" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.deliveryDate"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="签约时间">
                <el-date-picker v-model="houseDto.signDate" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.signDate"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="预计脱保时间">
                <el-date-picker v-model="houseDto.offAidDate" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.offAidDate"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="是否精装">
                <el-input disabled :value="houseDto.fitment?houseDto.fitment='1'?'是':'否':''"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="0">
              <el-form-item label="集中交房时间">
                <el-date-picker v-model="houseDto.focusStartDate" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.focusStartDate"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="至">
                <el-date-picker v-model="houseDto.focusEndDate" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.focusEndDate"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="实际交房时间">
                <el-date-picker v-model="houseDto.actualDeliveryDate" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.actualDeliveryDate"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="入住时间">
                <el-date-picker v-model="houseDto.stayTime" type="date" disabled/>
                <!-- <el-input disabled v-model="houseDto.stayTime"></el-input> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bg-style mt-10" >
      <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
        <span class="redSide ml-10"> </span>
        <span class="ft-14 fw-bd">关联客户信息</span>
      </div>
      <div class="house_person">
        <el-table stripe :data="personList">
          <el-table-column prop="custName" label="客户姓名" show-overflow-tooltip/>
          <el-table-column prop="belong" label="个人/单位"/>
          <el-table-column prop="cusIdentity" label="客户身份"/>
          <el-table-column prop="sex" label="性别"/>
          <el-table-column prop="telephone" label="联系电话"/>
          <el-table-column prop="isVip" :formatter="isFormatter" label="VIP"/>
          <el-table-column prop="sugLeader" :formatter="isFormatter" label="意见领袖" />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { hiSelectHouseInfoById } from "@/api/port";
import { uList } from "@/api/baseMsg";
export default {
  data() {
    return {
      houseDto: Object,
      personList:[]
    };
  },
  methods: {
    cancelLastPage(){
      window.history.go(-1);
    },
    info() {
      hiSelectHouseInfoById({ id: this.$route.query.id })
        .then(result => {
          if (result.code == 200) this.houseDto = result.data;
        })
        .catch(err => {});
      uList({pageSize:0,houseNum:this.$route.query.id}).then(res=>{
        if (res.code == 200) this.personList = res.data;
      })
    },
    isFormatter(v,c){
      if(v[c.property] == '1')
        return "是";
      if(v[c.property] == '0')
        return '否';
    }

  },
  created() {
    this.info();
  }
};
</script>
<style lang="scss" scoped>
.house_person{
  padding-bottom: 10px;
}
.el-date-editor.el-input, .el-date-editor.el-input__inner{
  width: 100% !important;
}
</style>

