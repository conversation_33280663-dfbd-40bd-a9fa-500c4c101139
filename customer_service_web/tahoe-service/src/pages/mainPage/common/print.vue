<template>
    <div id="print">
        <div class="container" id="print-box" ref="table1">
            <div :class="longText(printData.customerDemand)?'table-container all-height':'table-container'" ref="tabletop">
                <table class="printTable">
                    <tr style="height:46px;">
                        <td colspan="9" style="position:relative;">
                            <span style="position:absolute;left:5px;bottom:-4px;">工单编号：{{printData.formNo}}</span>
                            <h2>报事处理记录单</h2>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:60px;">房号</td>
                        <td colspan="2">{{printData.houseName?printData.project+'-'+printData.houseName:printData.project}}</td>
                        <td>报事日期</td>
                        <td colspan="2">{{timeFilter(printData.submitDate)}}</td>
                        <td>报事责任人</td>
                        <td colspan="2">{{printData.createUserName}}</td>
                    </tr>
                    <tr>
                        <td>报修人</td>
                        <td colspan="2">{{printData.ownerName}}</td>
                        <td>联系方式</td>
                        <td colspan="2">{{printData.mobile}}</td>
                        <td>责任单位</td>
                        <td colspan="2">{{printData.mainResUnit?printData.mainResUnit:''}}</td>
                    </tr>
                    <tr>
                        <td>报修类别</td>
                        <td colspan="8" style="line-height:14px;">
                            {{
                                printData.firstSortName&&printData.secSortName&&printData.thirdSortName&&printData.fourthSortName?
                                printData.firstSortName+'->'+printData.secSortName+'->'+printData.thirdSortName+'->'+printData.fourthSortName:
                                printData.firstSortName&&printData.secSortName&&printData.thirdSortName?
                                printData.firstSortName+'->'+printData.secSortName+'->'+printData.thirdSortName:
                                printData.firstSortName&&printData.secSortName?
                                printData.firstSortName+'->'+printData.secSortName:printData.firstSortName?printData.firstSortName:''
                            }}
                        </td>
                    </tr>
                    <tr style="height:60px!important;">
                        <td colspan="9" rowspan="2" style="text-align:left;">
                            <span style="display:inline-block;min-height:50px;padding:5px 10px;line-height:16px;">
                                报修内容：{{printData.customerDemand?printData.customerDemand:''}}
                            </span>
                        </td>
                    </tr>
                    <tr>

                    </tr>
                    <tr>
                        <td colspan="7" rowspan="2" style="text-align:left;">
                            <span style="display:inline-block;min-height:50px;padding:5px 10px;line-height:16px;">
                                处理方案：
                            </span>
                        </td>
                        <td>计划完成日期</td>
                        <td><span style="display:inline-block;width:80px;"></span></td>
                    </tr>
                    <tr>
                        <td>分派确认</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="7" rowspan="2" style="text-align:left;">
                            <span style="display:inline-block;min-height:50px;padding:5px 10px;line-height:16px;">
                                报事过程记录：
                            </span>
                        </td>
                        <td>完成日期</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>完成确认</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>是否有偿</td>
                        <td>
                            <span class="check-box"></span><span class="check-text">是</span>
                            <span class="check-box"></span><span class="check-text">否</span>
                        </td>
                        <td>人工费</td>
                        <td colspan="2"></td>
                        <td rowspan="2">其他费用</td>
                        <td rowspan="2"></td>
                        <td rowspan="2">业主签字</td>
                        <td rowspan="2"></td>
                    </tr>
                    <tr>
                        <td>合计费用</td>
                        <td></td>
                        <td>材料费</td>
                        <td colspan="2"></td>
                    </tr>
                    <tr>
                        <td rowspan="2">业主</td>
                        <td rowspan="2">满意度评价</td>
                        <td><span class="check-box"></span><span class="check-text">非常满意</span></td>
                        <td><span class="check-box"></span><span class="check-text">满意</span></td>
                        <td><span class="check-box"></span><span class="check-text">一般</span></td>
                        <td><span class="check-box"></span><span class="check-text">不满意</span></td>
                        <td><span class="check-box"></span><span class="check-text">非常不满意</span></td>
                        <td rowspan="2">确认时间</td>
                        <td rowspan="2"></td>
                    </tr>
                    <tr>
                        <td>(5分)</td>
                        <td>(4分)</td>
                        <td>(3分)</td>
                        <td>(2分)</td>
                        <td>(1分)</td>
                    </tr>
                </table>
            </div>
            <div style="border:dashed 1px #000;margin:50px 0" v-if="!longText(printData.customerDemand)"></div>
            <div :class="longText(printData.customerDemand)?'table-container all-height':'table-container'" ref="tablebottom">

            </div>
        </div>
    </div>
</template>
<script>
    export default{
        props:[
            'printValue'
        ],
        watch:{
            printValue(){
                this.printData = this.printValue;
            }
        },
        data(){
            return{
                printData:Object,//{"id":1584,"formNo":"BX201811270012","deptCode":"deptDC","deptName":"地产","registCode":null,"registName":null,"processCode":"processBZ","processName":"标准流程","firstSortCode":"coBX","firstSortName":"报修","secSortCode":"bxGGQY","secSortName":"公共区域","thirdSortCode":"ggqySBSS","thirdSortName":"设备设施","fourthSortCode":"ggsbss001","fourthSortName":"同一室内底盒标高不一致、开关底盒距门口不一致","problemLevelCode":"problemMiddle","problemLevelName":"中","problemPositionCode":"posiYT","problemPositionName":"阳台","revisionClassificationCode":"reRC","revisionClassificationName":"日常报修","decorationStageCode":"decZXH","decorationStageName":"装修后","maintenancePeriodCode":"secY","maintenancePeriodName":"在保","interResDepartmentCode":null,"interResDepartmentName":null,"ownerName":"徐正","ownerType":2,"ortherMember":null,"specialUser":null,"mobile":"13656159629","nationality":null,"idCode":null,"idName":"身份证","idNo":"320219198303278512","birthDate":"1969-12-31T16:00:00.000+0000","workUnit":null,"occupation":null,"hobby":null,"faxPhone":null,"contactAddress":null,"fixedTelephone":null,"eMail":null,"postalCode":null,"housekeeperName":"陆海霞","housekeeperTel":"15106165512","remark":null,"commonProblemCode":"27","commonProblemName":null,"customerDemand":"老了哦了哦了哦了哦","houseNo":"10238","houseName":"10238","regionCode":"上海区域","region":null,"cityCode":"苏州地产","city":null,"projectCode":"197018","project":"天宇·江阴院子","buildingNo":"10号楼","buildingUnit":"238","roomNo":"2","usePropertyCode":null,"usePropertyName":null,"contractDeliveryTime":"1969-12-31T16:00:00.000+0000","signingTime":null,"estimatedReleaseTime":null,"focusDeliveryTimeFrom":null,"focusDeliveryTimeTo":null,"deliveryState":null,"hardcoverState":"-1","actualDeliverTime":null,"checkInTime":null,"assignId":"wanglei","assignName":"王磊","assignDate":"2018-11-27T08:35:59.000+0000","creationDate":"2018-11-27T08:35:58.000+0000","curAssigneeId":"zhengyong","curAssigneeName":"郑用","upgradeLevel":null,"processStateCode":"handle","processStateName":"已分派","lastUpdateDate":"2018-11-27T08:37:50.000+0000","updradeReason":null,"ownerId":"19701800000837","satisfactionCode":null,"satisfactionName":null,"upgradeFlag":null,"reworkFlag":null,"rejectFlag":null,"genderCode":null,"genderName":"男","lockUserId":null,"lockTime":null,"houseInfoId":"10238","createUserId":"liwei6","createUserName":"李伟","handleRecord":"","formGenerateId":null,"reportChannelCode":"channelRX","reportChannelName":"热线","acceptChannelCode":"400","acceptChannelName":null,"submitDate":"2018-11-27T08:37:50.000+0000","mainResUnit":null,"repairUnit":null,"thirdPartyFlag":null,"thirdPartyName":null,"thirdPartyReason":null,"changeRecord":""}
            }
        },
        methods:{
            print(){
                this.$refs.tablebottom.innerHTML = this.$refs.tabletop.innerHTML;
                Print('#print-box', {
                    onStart: function () {
                        console.log('onStart', new Date())
                    },
                    onEnd: function () {
                        console.log('onEnd', new Date())
                    }
                })
            },
            longText(text){
                if(text){
                    if(text.length<=200){
                        return false
                    }else{
                        return true
                    }
                }else{
                    return false
                }
            }
        },
        created(){
            this.printData = this.printValue;
            //console.log(this.printData)
        },
        mounted() {
            this.$refs.tablebottom.innerHTML = this.$refs.tabletop.innerHTML;
        },
    }
</script>
<style lang="scss" scoped>
    table,table tr th, table tr td { border:1px solid #dadada; }
    table { width:100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse; padding:2px;}
    .btn-container{
        text-align: center;
        padding:20px;
    }
    .all-height{
        height:100%;
    }
    .container{
    //   width:595px;
      width:100%;
      //height:842px;
    }
    .check-box{
        display:inline-block;
        width:12px;
        height:12px;
        border:solid thin #dadada;
        vertical-align: middle;
    }
    .check-text{
        vertical-align: middle;
        padding: 0 5px;
    }
    .printTable{
        font-size:12px!important;
    }
</style>

