<template>
  <div>
    <el-dialog title="提交人列表" :visible.sync="dialogTableVisible" @close="disableControl" :append-to-body="appendTrue">
      <el-table :data="gridData" @row-click="handlerRowClick">
        <el-table-column property="userName" label="处理人"></el-table-column>
        <el-table-column property="mobile" label="电话"></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="意见建议" :visible.sync="dialogAlertVisible" @close="disableControl"  :append-to-body="appendTrue">
      <el-form>
        <el-form-item label="意见建议">
          <el-input class="break-word" type="textarea" v-model.trim="comment"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="btnDown" @click="handlerAlertPrimary()">确 定</el-button>
        <el-button class="btnDown" @click="dialogAlertVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { fiSave } from "@/api/port";
import qs from 'qs';
import { pGetTaskUser, pSubmit, pSpecialEnd, pLevelUp,cfRelationFormAndFiles,updateReportState } from "@/api/wsd";
export default {
  props: ["reportForm","formCustFamilies",'fileIdList'],
  model: {
    event:'loading'
  },
  data() {
    return {
      isRun:false,
      dialogTableVisible: false,
      gridData: [],
      operateType: "submit",
      appendTrue:true,
      submitClick:false,
      // 弹出窗 输入意见
      dialogAlertVisible: false,
      comment: "",
      // rules:{
      //   comment:[{require:true,message:'意见建议不能为空！',trigger:'blur'}]
      // }
    };
  },
  watch: {
    dialogTableVisible(v) {
      if (!v) {
        this.gridData = [];
      }
    }
  },
  methods: {
    reject() {
      // 返工
      this.dialogAlertVisible = true; // 弹出意见
      this.operateType = "reject";
    },
    specialDialog(){
      this.dialogAlertVisible = true;
    },
    submit() {
      if(this.isRun){
        return;
      }
      this.isRun = true;
      this.$emit('loading',true);
      // 提交
        this.operateType = 'submit';
        let type = "submit";
        if (!this.reportForm || !this.reportForm.id) {
          type = "draft";
        }
        fiSave({
          csFormInst: this.reportForm,
          formCustFamilies: this.formCustFamilies,
          operateType: type
        }).then(res => {
          if (res.code == 200 && res.data) {
            this.reportForm.id = res.data;
            updateReportState({formInstId:this.reportForm.id}).then((res) => {
              if(res.code == 200){
                console.log('状态强制更新完成')
              }
            })
            //附件关联工单
            if(this.fileIdList && this.fileIdList.length > 0){  
              let data = qs.stringify({id:res.data,ids:JSON.stringify(this.fileIdList)})
              cfRelationFormAndFiles(data).then(res=>{
                console.log(res)
              })
            }
            if(type == 'draft'){
              this.$emit('refreshData',this.reportForm.id)
            }
            this.handlerPGetTaskUser();
            this.isRun = false;
          } else {
            this.$confirm(res.message, {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
            }).then(() => {})
            this.$emit('loading',false);
            this.isRun = false;
          }
        }).catch(err =>{
          this.$confirm('系统异常', {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$emit('loading',false);
          this.isRun = false;
        });
        
    },
    //获取人员
    handlerPGetTaskUser() {
      if (
        (this.reportForm.processStateCode == 'handle' && this.operateType!= "reject") ||
        this.reportForm.processCode == "processKSCL"||
        (this.reportForm.processStateCode == 'returnVisit' && this.operateType!='reject')
      ) {
        this.handlerSubmit({});
        return;
      }
      this.gridData = [];
      pGetTaskUser({
        formInstId: this.reportForm.id,
        processCode: this.reportForm.processCode,
        firstSortCode: this.reportForm.firstSortCode,
        operateType: this.operateType
      }).then(res => {
        if (res.code == 200 && res.data) {
          this.gridData = res.data;
          if (this.gridData.length == 1) {
            this.handlerSubmit(this.gridData[0]);
          } else {
            this.dialogTableVisible = true;
            this.$emit('loading',false);
          }
        } else {
          // this.$message.error("提交失败");
          this.$confirm('提交失败', {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$emit('disSubmit',this.submitClick)
        }
      });
    },
    // 选择人
    handlerRowClick(r, e, c) {
      this.handlerSubmit(r);
      this.dialogTableVisible = false;
    },
    handlerSubmit(p) {
      // 提交方法
      let _this = this;
      this.$emit('loading',true);
      pSubmit({
        formInstId: "" + this.reportForm.id,
        assignUserId: p.userId || "",
        assignUserName: p.userName || "",
        mobile: p.mobile || "",
        operateType: this.operateType,
        comment: this.comment
      }).then(res => {
        if (res.code == 200) {
          // this.$message.success(res.message);
          this.$confirm(res.message, {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {
            updateReportState({formInstId:this.reportForm.id}).then((res) => {
              if(res.code == 200){
                _this.$emit('refreshData', _this.reportForm.id);
                 _this.$router.push(
                   _this.$route.query.path?  _this.$route.query.path:"/"
                ); 
              }else{
                 _this.$confirm('提交错误！', {
                  confirmButtonText: '确定',
                  center: true,
                  showClose:false,
                  showCancelButton:false,
                  confirmButtonClass:'confirm-reset-style',
                }).then(() => {
                   _this.$router.push(
                     _this.$route.query.path?  _this.$route.query.path:"/"
                  );
                })
              }
            })
          })
          // this.$emit('loading',false);
        } else {
          //this.$message.error(res.message);
          this.$confirm(res.message, {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$emit('disSubmit',this.submitClick)
        }
      }).catch(err =>{
          this.$confirm('系统异常 ' + err?err.message:"", {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$emit('loading',false);
        });
    },
    handlerAlertPrimary() {
      //确认 意见
      if(this.comment==''){
        this.$confirm('意见建议不能为空！', {
          confirmButtonText: '确定',
          center: true,
          showClose:false,
          showCancelButton:false,
          confirmButtonClass:'confirm-reset-style',
        }).then(() => {})
      }else{
        this.dialogAlertVisible = false;
        if (this.operateType == "reject") {
          this.handlerPGetTaskUser();
        }else{
          this.specialEnd()
        }
      }
    },
    setLevelUp() {
      pLevelUp({
        operateType: "upgrade",
        formInstId: "" + this.reportForm.id
      }).then(res => {
        if (res.code === 200) {
          //this.$message.success("升级成功！");
          this.$confirm("升级成功！", {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          // this.$router.push(
          //   this.$route.query.path == "addReport"
          //     ? "entryReport"
          //     : this.$route.query.path
          // );
          //this.$router.go(0);
          this.$emit('refreshData',this.reportForm.id)
          this.$emit('disSubmit',this.submitClick)
        } else {
         // this.$message.error("升级失败");
          this.$confirm(res.message, {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$emit('disSubmit',this.submitClick)
        }
      });
    },
    specialEnd() {
      // 特殊关闭
      this.$router.push(
        this.$route.query.path? this.$route.query.path:"/"
      );
      pSpecialEnd({ formInstId: this.reportForm.id,comment: this.comment }).then(res => {
        if (res.code == 200) {
          //this.$message.success("特殊关闭成功");
          this.$confirm("特殊关闭成功", {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$router.push(
            this.$route.query.path? this.$route.query.path:"/"
          );
        } else {
          //this.$message.error("特殊关闭失败");
          this.$confirm("特殊关闭失败", {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          this.$emit('disSubmit',this.submitClick)
        }
      });
    },
    disableControl(){
      this.$emit('disSubmit',this.submitClick)
      //this.operateType = 'submit';
    }
  }
};
</script>
