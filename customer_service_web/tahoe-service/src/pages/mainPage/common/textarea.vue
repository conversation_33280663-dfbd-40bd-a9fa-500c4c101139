<template>
    <div id="diytextarea">
      <el-input class="break-word" type="textarea" v-model="value" resize="none" style="width:500px;height:120px;" @keyup.native="inputing"/>
      <div class="num" v-show="isShow">{{value.length}}/{{maxlength}}</div>
    </div>
</template>

<script>
    export default {
      props:[

      ],
      data(){
        return{
          value:'',
          isShow:false,
          maxlength:10,
        }
      },
      methods:{
        inputing(){
          if(this.value != '' || this.value.length != 0){
            this.isShow = true;
          }else{
            this.isShow = false;
          }
          if(this.value.length > this.maxlength){
            this.value = this.value.substring(0,this.maxlength);
          }
        }
      },
      created(){

      }
    }
</script>

<style scoped>
  #diytextarea{
    position:relative;
    width:500px;
  }
  .num{
    height:20px;
    line-height:20px;
    background-color:rgba(0,0,0,0.2);
    position: absolute;
    bottom: 0px;
    right: 0px;
    color:#E63F3C;
    padding:0 5px;
  }
</style>
