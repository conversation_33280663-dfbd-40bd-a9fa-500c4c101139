<template>
  <div id="reportingType" style="width: calc( 100% - 10px);">
    <div class="table-container">
      <div class="table-body" v-loading="loading" element-loading-text="玩命加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)">
        <el-table ref="csmTagTable" :data="tableData" style="width: 100%;margin-bottom: 20px;" @row-click="rowClick" border row-key="id" :expand-row-keys="expandIds" lazy>
          <el-table-column label="标签名称" show-overflow-tooltip>
            <template slot-scope="scope">
              <i v-if="scope.treeNode && scope.row.children" :class="scope.treeNode.expanded?'el-icon-minus':'el-icon-plus'"></i>
              {{scope.row.itemName}}
            </template>
          </el-table-column>
          <el-table-column label="标签层级" align="center" width="100">
            <template slot-scope="scope">{{scope.row.hierarchy?chnNumChar[scope.row.hierarchy]+'层':''}}</template>
          </el-table-column>
          <el-table-column prop="collection" label="收集渠道" align="center" width="100"/>
          <el-table-column prop="score" label="分值" align="center" width="100"/>
          <el-table-column label="是否启用" align="center" width="100">
            <template slot-scope="scope">{{scope.row.state == "1" ?"是":"否"}}</template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="280">
            <template slot-scope="scope">
              <el-button v-if="scope.row.hierarchy>0" size="mini" @click="e => handleEdit(scope.$index, scope.row, e)" class="btnDown">编辑</el-button>
              <el-button v-if="scope.row.hierarchy>0" size="mini" @click="e => handleDel(scope.$index, scope.row, e)" class="btnDown">删除</el-button>
              <el-button v-if="scope.row.hierarchy<5" size="mini" @click="e => addChildLevel(scope.$index, scope.row, e)" class="btnDown">添加子级</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { delLabels, saveLabel, selCustLabel } from "@/api/csmTag";
export default {
  name: "reportingType",
  data() {
    return {
      loading: true,
      tableData: [],
      expandIds: [],
      flag: true,
      chnNumChar: ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]
    };
  },
  methods: {
    selCustLabel(params) {
      this.loading = true;
      selCustLabel().then(res => {
        if (res.code == 200) {
          this.loading = false;
          this.tableData = res.data;
        }
      });
    },
    rowClick(row, event, column) {
      if(row.children)
      this.$refs.csmTagTable.store.toggleTreeExpansion(row);
    },
    handleDel(index, row, e) {
      e.stopPropagation();
      this.$confirm(
        "是否确认删除" +
          row.itemName +
          (row.children ? "及下级" : "") +
          "标签？",
        {
          cancelButtonText: "确认",
          confirmButtonText: "取消",
          center: true,
          showClose: false
        }
      )
        .then(() => {
          this.goAdd();
        })
        .catch(() => {
          delLabels({ labelId: row.id }).then(res => {
            if (res.code == 200) {
              this.selCustLabel();
              this.$message({ type: "success", message: "提交成功" });
            }
          });
        });
    },
    handleEdit(index, row, e) {
      e.stopPropagation();
      this.$router.push({
        name: "csmTagInfo",
        params: { row: row }
      });
    },
    addChildLevel(index, row, e) {
      e.stopPropagation();
      var d = {
        fatherId: row.id,
        fatherName: row.itemName,
        hierarchy: row.hierarchy + 1
      };

      this.$router.push({
        name: "csmTagInfo",
        params: { row: d }
      });
    }
  },
  created() {
    this.selCustLabel();
  }
};
</script>

<style scoped>
.el-icon-minus,
.el-icon-plus {
  font-size: 12px;
  color: #e63f3c;
  font-weight: bold;
}
</style>
<style>
#reportingType .table-body .el-table__expand-icon {
  display: none;
}
</style>