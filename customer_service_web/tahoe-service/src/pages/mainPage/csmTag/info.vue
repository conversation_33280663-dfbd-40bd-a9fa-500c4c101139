<template>
    <div id="typeEditPage">
        <div class="btn">
            <span class="title1">报事类型信息</span>
            <div class="btn1">
                <div class="btnDown" @click="submit()">提交</div>
                <div class="btnDown" @click="goBack()" style="margin: 0 10px;">返回</div>
            </div>
        </div>
        <div class="box">
            <div class="wrap">
                <el-form ref="tagForm" :model="tagInfo" :rules="rules" label-width="120px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="上级分类">
                                <span>{{tagInfo.fatherName}}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属层级">
                                <span>{{chnNumChar[tagInfo.hierarchy||0]}}层</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item prop="itemName" label="分类名称">
                                <el-input v-model="tagInfo.itemName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="收集渠道" prop="collection">
                                <el-select size="mini" v-model="tagInfo.collection" placeholder="请选择收集渠道">
                                    <el-option value="区域一"></el-option>
                                    <el-option value="区域二"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="分值" prop="score">
                                <el-input type="number" v-model="tagInfo.score"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="是否启用">
                                <el-radio v-model="tagInfo.state" label="1">是</el-radio>
                                <el-radio v-model="tagInfo.state" label="0">否</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-form-item label="定义">
                        <el-input type="textarea" v-model="tagInfo.definition"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script>
import { saveLabel } from "@/api/csmTag";
export default {
  name: "typeEditPage",
  data() {
    return {
      tagInfo: {},
      chnNumChar: ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"],
      rules: {
        itemName: [
          { required: true, message: "请输入分类名称", trigger: "blur" },
          { max: 8, message: '长度在 8 个字符内', trigger: 'blur' }
        ],
        collection: [
          { required: true, message: "请选择收集渠道", trigger: "change" }
        ],
        score: [{ required: true, message: "请输入分值", trigger: "blur" }]
      }
    };
  },
  methods: {
    submit() {
      let _f = true;
      this.$refs["tagForm"].validate(valid => {
        if (!valid) {
          _f = valid;
          return false;
        }
      });
      if (!_f) return;
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      saveLabel(this.tagInfo)
        .then(res => {
          if (res.code == 200) {
            this.goBack();
            this.$message({ type: "success", message: "提交成功" });
          }
          loading.close();
        })
        .catch(err => {
          loading.close();
        });
    },
    goBack() {
      this.$router.push({ path: "csmTag" });
    }
  },
  mounted() {
    this.tagInfo = this.$route.params.row;
    this.$set(this.tagInfo, "score", this.tagInfo.score || 0);
    this.$set(this.tagInfo, "state", this.tagInfo.state || "0");
  }
};
</script>

<style scoped>
#typeEditPage .btn span:nth-child(1) {
  display: inline-block;
  height: 13px;
  line-height: 14px;
  font-size: 14px;
  padding: 3px;
  border-left: solid 2px #e63f3c;
  margin-left: 10px;
}
#typeEditPage .btn {
  background-color: #fff;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid rgb(230, 230, 230);
  /* border-radius: 10px; */
}
#typeEditPage .btn1 {
  float: right;
}
#typeEditPage .box {
  width: 100%;
  background-color: #fff;
  padding-bottom: 30px;
}
#typeEditPage .box .wrap {
  width: 70%;
  margin: 0 auto;
}
</style>