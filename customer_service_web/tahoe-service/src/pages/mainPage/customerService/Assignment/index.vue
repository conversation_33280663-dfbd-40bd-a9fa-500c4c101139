<template>
  <div class="search-bar" v-loading.fullscreen.lock="fullLoading">
    <div class="bg-style" style="margin-right:10px;padding-bottom:5px;">
      <el-form ref="form" label-position="left" :model="searchData" label-width="80px" size="mini">
        <div style="height:10px;"></div>
        <el-row>
          <el-col :span="6" :offset="1">
            <el-form-item label="工单编号">
              <el-input v-model="searchData.number" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <!--<el-col :span="6" :offset="1">-->
          <!--<el-form-item label="流程类别:">-->
          <!--<el-select v-model="searchData.city" filterable placeholder="请选择">-->
          <!--<el-option v-for="item in LCoptions" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode"></el-option>-->
          <!--</el-select>-->
          <!--</el-form-item>-->
          <!--</el-col>-->

          <el-col :span="6" :offset="1">
            <el-form-item label="一级类别">
              <el-select v-model="searchData.project" filterable placeholder="请选择">
                <el-option v-for="(item,key) in oneoptions" :key="key" :label="item.itemValue" :value="item.itemCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <button class="btnDown" @click.prevent="searchItem()" style="float:left;margin-left:20px;margin-top:2px;">查询</button>
          <button class="btnDown" @click.prevent="reset" style="float:left;margin-left:10px;margin-top:2px;">重置</button>
        </el-row>
        <!-- <el-row class="mt-10">
          <el-col :span='6' :offset="1">
            <el-form-item label="当前处理人:">
              <el-input v-model="searchData.user" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='6' :offset="1">
            <el-form-item label="业务步骤">
              <el-select v-model="searchData.business" filterable placeholder="请选择">
                <el-option v-for="item in YWoptions" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->

      </el-form>
    </div>

    <div class="bg-style mt-10 mb-20" style="margin-right:10px;">
      <div style="height:44px;line-height:44px;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">分派列表</span>
          <button class='btnDown fr mt-10 mr-10' @click="fenpai">批量分派</button>
      </div>
      <div style="width:100%;overflow:hidden;">
        <table v-loading="loading">
          <thead>
            <tr class="odd">
              <th style="width:55px;" class="checkboxContent">
                <input type="checkbox" v-model='partChecked' class="input_check" id="selected" @click="allChecked1($event)">
                <label for="selected"></label>
              </th>
              <th style="width:100px;">工单编号</th>
              <th>特殊标签</th>
              <th>项目名称</th>
              <th>业主姓名</th>
              <th style="width:120px;">一级分类</th>
              <th style="width:120px;">二级分类</th>
              <th style="width:120px;">三级分类</th>
              <th>创建人</th>
              <th @click="changeOrder">
                <span style="display:flex;flex-direction:row;justify-content:center;height:30px;align-items:center;">
                  <span>创建时间</span>
                  <span class="flex-th">
                    <span @click.stop="changeOrder1('ASC')">
                      <img :src="orderBy == 'ASC'?require('@/assets/images/icon/up-active.png'):require('@/assets/images/icon/up.png')">
                    </span>
                    <span @click.stop="changeOrder1('DESC')">
                      <img :src="orderBy == 'DESC'?require('@/assets/images/icon/down-active.png'):require('@/assets/images/icon/down.png')">
                    </span>
                  </span>
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="list.length==0">
              <td colspan="13">暂无数据</td>
            </tr>

            <tr v-else v-for="(item,key) in list" :key='key' :class="key%2==0?'even':'odd'">
              <td style="width:55px;" class="checkboxContent">
                <input  v-if="(item.firstSortCode != 'coTS' || item.complaintHeadlines) && item.secSortCode && item.thirdSortCode && item.problemLevelCode" type="checkbox" class="input_check" :id="item.id" name="fgetChecked1ileDoc1" v-model="selected"  :value="item.id" @change="getChecked1(item)">
                <label :for="item.id" v-if="(item.firstSortCode != 'coTS' || item.complaintHeadlines) && item.secSortCode && item.thirdSortCode  && item.problemLevelCode"></label>
              </td>
              <td style="color:#1D85FE;" @click="goDetail(item)">{{item.formNo}}</td>
              <td><span class="reworkTag" v-if="item.reworkFlag=='1'"><img src="../../../../assets/images/rework2x.png" alt=""></span> <span class="levelUpTag" v-if="item.upgradeFlag=='1'"><img src="../../../../assets/images/level_up2x.png" alt=""></span></td>
              <td>{{item.project}}</td>
              <td>{{item.ownerName}}</td>
              <td>
                <div v-if="!item.firstSortName || item.firstSortName.length<=9" class="break-row">{{item.firstSortName}}</div>
                <el-tooltip v-else class="item" effect="dark" :content="item.firstSortName" placement="top">
                  <div class="break-row">{{item.firstSortName}}</div>
                </el-tooltip>
              </td>
              <td>
                <div v-if="!item.secSortName || item.secSortName.length<=9" class="break-row">{{item.secSortName}}</div>
                <el-tooltip v-else class="item" effect="dark" :content="item.secSortName" placement="top">
                  <div class="break-row">{{item.secSortName}}</div>
                </el-tooltip>
              </td>
              <td>
                <div v-if="!item.thirdSortName || item.thirdSortName.length<=9" class="break-row">{{item.thirdSortName}}</div>
                <el-tooltip v-else class="item" effect="dark" :content="item.thirdSortName" placement="top">
                  <div class="break-row">{{item.thirdSortName}}</div>
                </el-tooltip>
              </td>
              <td>{{item.createUserName}}</td>
              <td>{{item.creationDate | TimeMoment}}</td>
            </tr>
          </tbody>
        </table>
        <wcg-page style="margin-top:0px;" :pageSize="pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchItem"></wcg-page>
      </div>
    </div>

    <el-dialog title="分派人列表" :visible.sync="dialogTableVisible" >
      <el-table :data="userList" @row-click="handlerRowClick">
        <el-table-column property="userName" label="处理人"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import wcgPage from "@/components/wcgPage";
import { processType, regionCode, Assignment,QuickDispatch,userSubmit } from "@/api/port";
export default {
  props: [],
  components: { wcgPage },
  watch: {
    selected: {
      handler: function(val, oldVal) {
        if (
          (this.selected.length === this.list.filter(i=>i.firstSortCode != 'coTS' || i.complaintHeadlines).length) && this.list.length !== 0 && this.selected.length !== 0 ) {
          this.partChecked = true;
        } else {
          this.partChecked = false;
        }
      },
      deep: true
    },
    list: {
      handler: function(val, oldVal) {
        if (
          (this.selected.length === this.list.filter(i=>i.firstSortCode != 'coTS' || i.complaintHeadlines).length) &&
          this.list.length !== 0 && this.selected.length !== 0
        ) {
          this.partChecked = true;
        } else {
          this.partChecked = false;
        }
      },
      deep: true
    },
    orderBy(val){
      console.log(val);
      this.searchItem(this.rpageNum);
    }
  },
  data() {
    return {
      rpageNum:1,
      userList:[],
      dialogTableVisible:false,
      checkboxdisabled: false,
      partChecked: false,
      selected: [],
      fileLists1: [],
      loading: false,
      fullLoading: false,
      pageSize: 20,
      currentPage: 1,
      total: 0,
      cityDisabled: "",
      lcDisabled: "",
      oneDisabled: false,
      projectDisabled: false,
      DataArr:[],
      searchData: {
        business: "",
        user: "",
        number: "",
        project: ""
      },
      searchDataReset: {},
      LCoptions: [], //流程类别
      LBoptions: [], //一级类别
      list: [], //列表数据
      oneoptions: [],
      twooptions: [],
      YWoptions: {},
      allCheckedPage:[],
      clickDis:0,
      orderBy:null,
    };
  },
  created() {
    if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type == 'Assignment'){
      this.searchData = JSON.parse(sessionStorage.getItem('params')).searchData;
      this.currentPage = JSON.parse(sessionStorage.getItem('params')).pageNum;
      this.orderBy = JSON.parse(sessionStorage.getItem('params')).orderBy; 
      this.total = JSON.parse(sessionStorage.getItem('params')).total; 
      this.searchItem(this.currentPage);
    }else{
      this.searchItem();
    }
    this.lclbByCode();
  },
  methods: {
    // 选择人
    handlerRowClick(r, e, c) {
      console.log(r,e,c)
      this.clickDis++;
      if(this.clickDis==1){
        this.handlerSubmit(r)
        // console.log('1111')
      }
    },
    changeOrder1(val){
      this.orderBy = val;
    },
    changeOrder(event,val){
      if(this.orderBy == null){
        this.orderBy = 'DESC'
      }else if(this.orderBy == 'DESC'){
        this.orderBy = 'ASC'
      }else if(this.orderBy == 'ASC'){
        this.orderBy = null
      }
    },
    handlerSubmit(p) {
      // 提交方法
      userSubmit({
        formInstIds:this.selected.join(','),
        assignUserId:p.userId,
        assignUserName:p.userName,
        mobile:p.mobile
      }).then(res => {
        let that = this;
        setTimeout(() => {
          that.clickDis=0;
        }, 1000);
        console.log(res)
        if (res.code == 200) {
          this.dialogTableVisible = false;
          this.$confirm(res.message, {
                confirmButtonText: '确定',
                center: true,
                showClose:false,
                showCancelButton:false,
                confirmButtonClass:'confirm-reset-style',
                }).then(() => {})
          this.searchItem();
        } else {
          this.$confirm(res.message, {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
          let that = this;
          setTimeout(() => {
            that.clickDis=0;
          }, 1000);
        }
      });
    },
    goDetail(item) {
      let obj = {
        type:'Assignment',
        pageNum:this.currentPage,
        orderBy:this.orderBy,
        searchData:this.searchData,
        total:this.total
      }
      sessionStorage.setItem('params',JSON.stringify(obj));
      this.$router.push({
        path: item.deptName=='IT'?"itReport":"addReport",
        query: { id: item.id, title: "报事分派", path: this.$route.path }
      });
    },
    searchItem(pageNum) {
      this.currentPage = pageNum?pageNum:1;
      this.loading = true;
      this.list = [];
      //查询方法
      Assignment({
        formNo: this.searchData.number,
        firstSortCode: this.searchData.project,
        processCode: this.searchData.city,
        curAssigneeName: this.searchData.user,
        processStateCode: this.searchData.business,
        orderBy:this.orderBy,
        pageSize: this.pageSize,
        pageNum: this.currentPage
      }).then(res => {
        console.log(res)
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          // this.currentPage = res.data.pages;
          this.total = res.data.total;
          this.selected = [];
        } else {
          this.$confirm(res.message, {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
        }
      });
    },
    lclb() {
      processType().then(res => {
        (this.LCoptions = res.data.processCode),
          (this.LBoptions = res.data.processCode);
        this.YWoptions = res.data.processStateCode;
        this.YWoptions.map((item, index) => {
          if (item.itemValue == "暂存" || item.itemValue == "报事升级") {
            this.YWoptions.splice(index, 1);
          }
        });
      });
    },
    lclbByCode() {
      this.oneoptions = [];
      regionCode({ code: "processBZ" }).then(res => {
        if(res.data){
          res.data.forEach(r=>{
            if (r.itemValue != "咨询") {
              this.oneoptions.push(r);
            }
          })
          regionCode({ code: "itFscBz" }).then(res => {
            if(res.data) res.data.forEach(r=>{
                this.oneoptions.push(r);
            })
            regionCode({ code: "itFscKszx" }).then(res => {
            if(res.data) res.data.forEach(r=>{
                this.oneoptions.push(r);
            })
          });
          });
        }
      });
    },
    // lclbByCode1(code) {
    //   regionCode({ code: "processKSCL" }).then(res => {
    //     this.oneoptions = res.data;
    //   });
    // },
   // 快速分派
    fenpai(){
      if(this.selected.length==0){
          this.$confirm('请至少选择一个项目', {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
      }else{
        this.DataArr = [];
        for(let i in this.selected){
          for(let k in this.list){
            if(this.selected[i]==this.list[k].id){
              this.DataArr.push(this.list[k]);
            }
          }
        }
        console.log(this.DataArr)
        let flag = true;
        for(let i in this.DataArr){
          if(this.DataArr[0].projectCode!=this.DataArr[i].projectCode){
            flag = false;
            break;
          }
        }
        if(flag){
          this.fullLoading = true;
          QuickDispatch({
            formInstId:this.selected[0],
            processCode:'processBZ',
            firstSortCode:this.DataArr[0].firstSortCode,
            operateType:'submit'
          }).then(res=>{
              if(res.code===200){
               this.userList = res.data
               this.dialogTableVisible = true;
              }
              this.fullLoading = false;
          })
        }else{
          this.$confirm('工单不属于同一个项目', {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
        }
      }
      
      // if(flag1){
      //     QuickDispatch({
      //       formInstId:this.selected.toString(),
      //       processCode:'processBZ',
      //       firstSortCode: Array.from(new Set(arr1)).toString(),
      //       operateType:'submit'
      //     }).then(res=>{
      //         this.userList = res.data
      //     })
      // }
    },
    reset() {
      sessionStorage.removeItem('params');
      this.searchData = Object.assign({}, this.searchDataReset);
      //this.oneDisabled = true;
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.searchItem();
    },
    handleCurrentChange(val) {
      this.rpageNum = val;
      this.searchItem(val);
    },
    allChecked1(e) {
      console.log('event',e)
      //全选
      const _this = this;
      _this.selected = [];
      if (e.target.checked) {
        _this.list.forEach(function(option) {
          if(option.firstSortCode != 'coTS' || option.complaintHeadlines)
            _this.selected.push(option.id);
        });
      }
    },
    getChecked1(option){
      if (option.checked == true) {
        if (this.selected.indexOf(option.id) == -1) {
          this.selected.push(option.id);
        }
      } else if (option.checked == false) {
        let i = this.selected.indexOf(option.id);
        if (i > -1) {
          this.selected.splice(i, 1);
        }
      }
    },
  },
  mounted(){
    this.lclb();
  }
};
</script>

<style scoped>
tr th .checkboxContent {
  position: relative;
  top: 3px;
}
.input_check {
  position: absolute;
  visibility: hidden;
}
.input_check + label {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("./chekced.png") no-repeat;
  background-size: 16px;
  background-position: -31px -1px;
  border: 1px solid #4a4a4a;
  border-radius: 3px;
  cursor: pointer;
}
.input_check:checked + label {
  background-position: -2px -1px;
}

.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.mb-20 {
  /*height: 400px;*/
  background: #ffffff;
  border-radius: 10px;
  padding-bottom:10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,.levelUpTag img{
  vertical-align:middle;
  width: 32px;
  height: 16px;
}
table tr:hover{
  background: #D5DBE4 ;
}
.break-row{
    outline:none;
    display:inline-block;
    max-width:110px!important;
    display: -webkit-box!important;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 1!important;
    overflow:hidden!important;
    height:30px;
    line-height:30px!important;
  }
  .flex-th{
    height:18px;
    display:flex;
    flex-direction:column;
    justify-content:space-around;
    align-items:center;
    margin-left:5px;
  }
  .flex-th span{
    height:6px;
    width:10px;
    display:flex;
    flex-direction:row;
    justify-content:center;
    align-items:center;
    cursor:pointer;
    background:none!important;
    outline:none!important;
  }.flex-th span img{
    width:100%;
    height:100%;
    background:none!important;
    outline:none!important;
    border:none!important;
  }
</style>
