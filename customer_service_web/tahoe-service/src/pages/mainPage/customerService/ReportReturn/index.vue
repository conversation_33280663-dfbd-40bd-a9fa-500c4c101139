<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="80px" size="mini">
                <div style="height:10px;"></div>
                 <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="区域">
                        <el-select v-model="searchData.regionCode" filterable placeholder="请选择" @change="getCityList">
                            <el-option v-for="item in areaOptions" :key="item.id" :label="item.region" :value="item.regionCode">
                            </el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="城市">
                        <el-select v-model="searchData.cityCompanyCode" :disabled="cityprojectDisabled" filterable placeholder="请选择" @change="getProjectList">
                            <el-option v-for="item in cityOptions" :key="item.cityCompanyCode" :label="item.cityCompany" :value="item.cityCompanyCode">
                            </el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5"  :offset="1">
                        <el-form-item label="项目">
                        <el-select v-model="searchData.projectCode" :disabled="projectDisabled" filterable placeholder="请选择">
                            <el-option v-for="item in projectOptions" :key="item.projectCode" :label="item.project" :value="item.projectCode">
                            </el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="工单编号">
                            <el-input v-model="searchData.formNo" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            <div class="box" v-if="flag">
                <el-row class="mt-10">
                    <el-col :span="5" :offset="0">
                        <el-form-item label="报事人姓名">
                            <el-input v-model="searchData.ownerName" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="报事人电话">
                            <el-input v-model="searchData.mobile" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                            <el-form-item label="创建时间">
                            <el-date-picker prefix-icon="el-icon-date" v-model="searchData.startDate"  type="datetime" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="至">
                        <el-date-picker prefix-icon="el-icon-date" v-model="searchData.endDate"  type="datetime" value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top:10px;">
                    <el-col :span='5' :offset="0">
                        <el-form-item label="一级分类">
                            <el-select v-model="searchData.firstSortCode" filterable placeholder="待处理">
                                <el-option v-for="item in options" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                     <el-col :span='5' :offset="1">
                      <el-form-item label="回访时间">
                        <el-date-picker prefix-icon="el-icon-date" v-model="searchData.rVStartTimeBegin"  type="datetime" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                      <el-form-item label="至">
                        <el-date-picker prefix-icon="el-icon-date" v-model="searchData.rVStartTimeEnd"  type="datetime" value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59"></el-date-picker>
                      </el-form-item>
                    </el-col>

                    <!--<el-col :span='6' :offset="1">-->
                        <!--<el-form-item label="升级类别">-->
                            <!--<el-select v-model="searchData.level" filterable placeholder="待处理">-->
                                <!--<el-option v-for="item in options3" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode">-->
                                <!--</el-option>-->
                            <!--</el-select>-->
                        <!--</el-form-item>-->
                    <!--</el-col>-->
                </el-row>
            </div>
            <el-row class="mt-10">
              <el-col :span="24" style="text-align:right;padding-right:4%;">
                    <button class="btnDown" @click.prevent="searchReturnListOK()" style="margin-left:20px;margin-top:3px;" >查询</button>
                    <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
              </el-col>
            </el-row>
                <el-row>
                     <el-col :span="24">
                        <div class="ta-ct mt-10" style="position:relative;height:30px;">
                        <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                        <span class="txt" @click.prevent="change()"><i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i></span>
                        </div>
                    </el-col>
                </el-row>

            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">
            <div style="width:100%;overflow:hidden;">
                <el-table v-loading="loading" height="400" tooltip-effect="dark" stripe element-loading-text="拼命加载中" :data="list" style="width: 100%" @sort-change="sortByTime">
                    <el-table-column label="工单编号" width="180">
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.formNo}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="特殊标签" width="180">
                        <template slot-scope="scope">
                            <span class="reworkTag" v-if="scope.row.reworkFlag=='1'"><img src="../../../../assets/images/rework2x.png" alt=""></span> <span class="levelUpTag" v-if="scope.row.upgradeFlag=='1'"><img src="../../../../assets/images/level_up2x.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="deptName" label="部门" width="80" />
                    <el-table-column prop="firstSortName" label="一级分类" show-overflow-tooltip/>
                    <el-table-column prop="secSortName" label="二级分类" show-overflow-tooltip/>
                    <el-table-column prop="thirdSortName" label="三级分类" show-overflow-tooltip/>
                    <el-table-column prop="createUserName" label="创建人" />
                    <el-table-column  label="创建时间" width="160" sortable>
                        <template slot-scope="scope">
                          <span>{{scope.row.creationDate | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="assignName" label="处理人" />
                    <el-table-column label="分派时间" width="160">
                        <template slot-scope="scope">
                          <span>{{scope.row.assignDate | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="回访时间" width="160">
                        <template slot-scope="scope">
                          <span>{{scope.row.rVStartTime | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturnListOK"></wcg-page>

            </div>
        </div>

    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import { searchReport, searchC, searchReturnList, area, searchInterimReports, searchInterimReport  } from "@/api/port";
import { cfReturnVisitLockQuery } from "@/api/wsd";
export default {
  props: [],
  components: { wcgPage },
  watch: {
     "searchData.regionCode"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.floorDisabled = true;
          this.projectOptions = [];
          this.buildOptions = [];
          this.searchData.cityCompanyCode = this.searchData.projectCode = "";
          if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type == 'ReportReturn' && JSON.parse(sessionStorage.getItem('params')).searchData.regionCode){
            this.getCityList(newval);
            if(this.searchData.regionCode == newval){
              this.$set(this.searchData,'cityCompanyCode',JSON.parse(sessionStorage.getItem('params')).searchData.cityCompanyCode)
            }else{
              this.$set(this.searchData,'cityCompanyCode','')
            }
          }
        }
      } else {
        this.cityprojectDisabled = true;
        if( this.cityprojectDisabled = true){
            this.projectDisabled = true
        }
        this.floorDisabled = true;
        this.projectDisabled = true;
      }
    },
    "searchData.cityCompanyCode"(newval, oldval) {
        console.log(newval)
      if (newval != oldval) {
        this.searchData.projectCode = "";
        if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type == 'ReportReturn' && JSON.parse(sessionStorage.getItem('params')).searchData.cityCompanyCode){
          this.getProjectList(newval);
          if(this.searchData.cityCompanyCode == newval){
            this.$set(this.searchData,'projectCode',JSON.parse(sessionStorage.getItem('params')).searchData.projectCode)
          }else{
            this.$set(this.searchData,'projectCode','')
          }
        }
      }
      if (newval == "") {
        this.projectDisabled = true;
      } else {
        this.projectDisabled = false;
      }
    }
  },
  data() {
    return {
        showIf:false,
        flag:false,
      loading: false,
      rpageNum:1,
      pageSize: 20,
      currentPage: 1,
      total: 0,
      cityDisabled: "",
      lcDisabled: false,
      oneDisabled: true,
      projectDisabled: true,
      searchData: {},
      options: [],
      options1: [],
      options2: [],
      options3: [],
      areaOptions:[],
      projectOptions:[],
      cityOptions:[],
      buildprojectDisabled: true,
      cityprojectDisabled: true,
      list: []
    };
  },
  methods: {

     areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    sortByTime(column){
      if(column.order == 'ascending'){//从小到大
        this.$set(this.searchData,'orderBy','ASC');
        this.searchReturnListOK(this.rpageNum);
      }else if(column.order == 'descending'){//从大到小
        this.$set(this.searchData,'orderBy','DESC');
        this.searchReturnListOK(this.rpageNum);
      }else{
        this.$set(this.searchData,'orderBy',null);
        this.searchReturnListOK(this.rpageNum);
      }
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.regionCode
      }).then(res => {
        console.log(res.data);
        this.projectOptions = res.data;
      });
    },
      change(){
          this.flag = !this.flag
      },
    formatRole(row,column){
        return row.upgradeLevel == 1?'城市':row.upgradeLevel==2?'区域':row.upgradeLevel==3?'集团':'--'
    },
    goDetail(item) {
      // 报事回访加锁
      let obj = {
        type:'ReportReturn',
        pageNum:this.currentPage,
        searchData:this.searchData,
        total:this.total
      }
      sessionStorage.setItem('params',JSON.stringify(obj));
      cfReturnVisitLockQuery({id:item.id}).then(res =>{
        if(res.sucess){
          if(res.data.status === "0"){
            //跳转详情
            this.$router.push({
              path: item.deptName=='IT'?"itReport":"addReport",
              query: { id: item.id, title: "报事回访", path: this.$route.path }
            });
          } else {
            this.$confirm(res.data.msg, {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
            }).then(() => {})
          }
        }
      })
      
    },
    searchReportSove() {
      searchReport({}).then(res => {
         console.log(res)
        this.options1 = res.data.deptCode;
        this.options2 = res.data.processStateCode;
        this.options3 = res.data.upgradeLevel;
        this.options2.map((item,index) => {
          if(item.itemValue == '暂存' || item.itemValue== '报事升级'){
            this.options2.splice(index,1);
          }
        })
      });
    },
    searchCloseReturnOK() {
      searchC({ code: "firstSortCode" }).then(res => {
        this.options = res.data;
        this.options.map((item,index) => {
          if(item.itemValue != '投诉' && item.itemValue != '报修'){
            this.options.splice(index,1);
          }
        })
      });
    },
    searchReturnListOK(pageNum) {
      // this.pageSize = pageSize?pageSize:20;
      this.currentPage = pageNum?pageNum:1;
      this.loading = true;
      this.list = [];
      this.searchData.pageSize = this.pageSize;
      this.searchData.pageNum = this.currentPage;
      searchReturnList(this.searchData).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          this.total = res.data.total;
        }
      });
    },
    reset() {
      sessionStorage.removeItem('params');
      this.searchData = {};
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchReturnListOK();
    },
    handleCurrentChange(val) {
      this.rpageNum = val;
      this.searchReturnListOK(val);
    }
  },
  mounted() {
    this.searchReportSove();
    this.searchCloseReturnOK();
  },
  created() {
    this.areaList();
    if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type == 'ReportReturn'){
      this.searchData = JSON.parse(sessionStorage.getItem('params')).searchData;
      this.currentPage = JSON.parse(sessionStorage.getItem('params')).pageNum;
      this.total = JSON.parse(sessionStorage.getItem('params')).total; 
      this.searchReturnListOK(this.currentPage)
      return;
    }
    this.searchReturnListOK();
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.el-date-editor.el-input{
    width: 100% !important;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left:50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.mb-20 {
  /* height: 400px; */
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,.levelUpTag img{
  vertical-align:middle;
  width: 32px;
  height: 16px;
}
.el-date-editor, .el-date-editor.el-input__inner{
  width:100%!important;
}
</style>
