<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="80px" size="mini">
                <div style="height:10px;"></div>
                <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="工单编号">
                            <el-input v-model="searchData.number" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="报事人姓名">
                             <el-input v-model="searchData.city" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="报事人电话">
                           <el-input v-model="searchData.project" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label=" 房间号">
                            <el-input v-model="searchData.user" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            <div class="box" v-if="flag">
                <el-row class="mt-10">
                    <el-col :span='5' :offset="0">
                            <el-form-item label="分派时间">
                            <el-date-picker prefix-icon="el-icon-date" v-model="searchData.createDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="至">
                        <el-date-picker prefix-icon="el-icon-date" v-model="searchData.endDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
             </div>
             <el-row class="mt-10"> 
               <el-col :span="24" style="text-align:right;padding-right:4%;">
                  <button class="btnDown" @click.prevent="ReportSoveList()" style="margin-left:50px;margin-top:3px">查询</button>
                  <button class="btnDown" @click.prevent="reset" style="margin-left:10px;margin-top:3px">重置</button>
               </el-col>
             </el-row>
                <el-row>
                  <el-col :span="24">
                    <div class="ta-ct mt-10" style="position:relative;height:30px;">
                      <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                      <span class="txt" @click.prevent="change()"><i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i></span>
                    </div>
                  </el-col>
                </el-row>

            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">

            <div style="width:100%;overflow:hidden;">
                <el-table tooltip-effect="dark" v-loading="loading" stripe element-loading-text="拼命加载中"  :data="list" style="width: 100%" @sort-change="sortByTime">
                    <el-table-column label="工单编号" width="180" align="center">
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.formNo}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="特殊标签" width="180" align="center">
                        <template slot-scope="scope">
                            <span class="reworkTag" v-if="scope.row.reworkFlag=='1'"><img src="../../../../assets/images/rework2x.png" alt=""></span> <span class="levelUpTag" v-if="scope.row.upgradeFlag=='1'"><img src="../../../../assets/images/level_up2x.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="ownerName" label="业主名称" align="center"  show-overflow-tooltip/>
                    <el-table-column prop="firstSortName" label="一级分类" align="center" show-overflow-tooltip/>
                    <el-table-column prop="secSortName" label="二级分类" width="150" align="center" show-overflow-tooltip/>
                    <el-table-column prop="thirdSortName" label="三级分类" align="center" show-overflow-tooltip/>
                    <el-table-column prop="createUserName" label="创建人" align="center" />
                    <el-table-column  label="创建时间" width="180" sortable>
                        <template slot-scope="scope">
                          <span>{{scope.row.creationDate | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="assignName" label="分派人" />
                    <el-table-column label="分派时间" width="180">
                        <template slot-scope="scope">
                          <span>{{scope.row.assignDate | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop='upgradeLevel' :formatter="formatRole"  label="升级状态" align="center" />
                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="ReportSoveList"></wcg-page>

            </div>
        </div>
    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import { searchClose, onesearchClose, searchCloseList } from "@/api/port";
export default {
  props: [],
  components: { wcgPage },
  watch: {
    "searchData.city"(newval, oldval) {}
  },
  data() {
    return {
      flag:false,
      list: [],
      loading: false,
      pageSize: 20,
      rpageNum:1,
      currentPage: 1,
      total: 0,
      showIf:false,
      cityDisabled: "",
      lcDisabled: false,
      oneDisabled: true,
      projectDisabled: false,
      searchData: {
        fpuser: "",
        cjuser: "",
        business: "",
        user: "",
        number: "",
        city: "",
        project: "",
        createDate:'',
        endDate:'',
      },
      searchDataReset: {
        createDate: "",
        endDate: "",
        area: "",
        phone: "",
        step: "",
        project: ""
      },
      options1: [],
      oneoptions: [],
      YWoptions: [],
      list: []
    };
  },
  methods: {
      change(){
          this.flag = !this.flag
      },
    formatRole(row,column){
        return row.upgradeLevel == 1?'城市':row.upgradeLevel==2?'区域':row.upgradeLevel==3?'集团':'--'
    },
    sortByTime(column){
      //console.log(column)
      if(column.order == 'ascending'){//从小到大
        this.$set(this.searchData,'orderBy','ASC');
        this.ReportSoveList(this.rpageNum);
      }else if(column.order == 'descending'){//从大到小
        this.$set(this.searchData,'orderBy','DESC');
        this.ReportSoveList(this.rpageNum);
      }else{
        this.$set(this.searchData,'orderBy',null);
        this.ReportSoveList(this.rpageNum);
      }
    },
    goDetail(item) {
      let obj = {
        type:'ReportSolve',
        pageNum:this.rpageNum,
        searchData:this.searchData,
        total:this.total
      }
      sessionStorage.setItem('params',JSON.stringify(obj));
      this.$router.push({
        path: item.deptName=='IT'?"itReport":"addReport",
        query: { id: item.id, title: "报事处理", path: this.$route.path }
      });
    },
    ReportSove() {
      searchClose().then(res => {
        console.log(res);
        this.options1 = res.data.deptCode;
        this.YWoptions = res.data.processStateCode;
        this.YWoptions.map((item,index) => {
          if(item.itemValue == '暂存' || item.itemValue == '报事升级'){
            this.YWoptions.splice(index,1);
          }
        })
      });
    },

    oneReportSove(code) {
      onesearchClose({ code: "firstSortCode" }).then(res => {
        this.oneoptions = res.data;
      });
    },

    ReportSoveList(pageNum) {
      this.currentPage = pageNum?pageNum:1;
      this.loading = true;
      this.list = [];
      searchCloseList({
        formNo:this.searchData.number,
        startDate:this.searchData.createDate,
        endDate:this.searchData.endDate,
        ownerName:this.searchData.city,
        mobile:this.searchData.project,
        roomNo:this.searchData.user,
        pageNum:this.currentPage,
        pageSize:this.pageSize,
        orderBy:this.searchData.orderBy?this.searchData.orderBy:null
      }).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          //this.currentPage = res.data.pages;
          this.total = res.data.total;
        }
      });
    },
    reset() {
      sessionStorage.removeItem('params');
      this.searchData = Object.assign({}, this.searchDataReset);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.ReportSoveList();
    },
    handleCurrentChange(val) {
      this.rpageNum = val;
      this.ReportSoveList(val);
    }
  },
  mounted() {
    this.oneReportSove();
    if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type == 'ReportSolve'){
      //this.ReportSoveList(this.rpageNum);
      this.searchData = JSON.parse(sessionStorage.getItem('params')).searchData;
      this.currentPage = JSON.parse(sessionStorage.getItem('params')).pageNum;
      this.total = JSON.parse(sessionStorage.getItem('params')).total; 
      this.ReportSoveList(this.currentPage);
      return
    }
    this.ReportSoveList();
  },
  created() {
      //item.upgradeLevel == 1?'城市':item.upgradeLevel==2?'区域':item.upgradeLevel==3?'集团':'--'
    this.ReportSove();
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.mb-20 {
  /* height: 400px; */
  padding-bottom: 10px;
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
</style>
<style>
.el-table thead {
  font-family: MicrosoftYaHei-Bold;
  font-size: 12px;
  color: #000000;
  letter-spacing: 0;
  height: 30px;
  line-height: 30px;
}
.el-table th {
  background: #f1f4f8;
  padding:0 !important;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left:50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.el-table td,
.el-table th {
  /*text-align: center;*/
  height: 30px;
  line-height: 30px;
  font-family: MicrosoftYaHei;
  font-size: 12px;
  color: #000000;
  letter-spacing: 0;
}
.reworkTag img,.levelUpTag img{
  vertical-align:middle;
  width: 32px;
  height: 16px;
}
.el-date-editor, .el-date-editor.el-input__inner{
  width:100%!important;
}
</style>
