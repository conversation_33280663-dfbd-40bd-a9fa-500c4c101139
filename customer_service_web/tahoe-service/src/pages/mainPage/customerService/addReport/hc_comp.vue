<template>
    <div class="hc-comp">
        <!-- 业主信息 -->
        <div class="bg-style mt-10">
            <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
                <span class="redSide ml-10"> </span>
                <span class="ft-14 fw-bd">业主信息 &nbsp;&nbsp;&nbsp;&nbsp;<a :href="'/#/custInfo/?isReport=true&id='+reportForm.ownerId" target="blank" v-if="reportForm.ownerId">标  签</a>
                </span>
            </div>
            <div>
                <el-form :inline-message="true" :show-message="false" ref="reportForm1" label-position="right" :rules="rules" :model="reportForm" label-width="70px" class="demo-ruleForm">
                    <el-row>
                        <el-col :span="5" :offset="0">
                            <el-form-item label="业主姓名" prop="ownerName" ref="errorEl" label-width="100px">
                                <el-input maxlength="10" v-model.trim="reportForm.ownerName" :disabled="isDisabled(3)"></el-input>
                                <!-- <el-tag type="info" v-if="reportForm.ownerId" style="cursor: pointer;" @click="addTagVisible = true"></el-tag> -->
                            </el-form-item>
                            
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="个人/单位" prop="ownerType" ref="errorEl" label-width="100px">
                                <!-- <el-radio-group v-model="reportForm.ownerType" :disabled="isDisabled(3)">
                                    <el-radio :label="1">个人</el-radio>
                                    <el-radio :label="2">单位</el-radio>
                                </el-radio-group> -->
                                <el-select v-model="reportForm.ownerType" :disabled="isDisabled(3)" placeholder="请选择">
                                    <el-option label="个人" :value="1">
                                    </el-option>
                                    <el-option label="单位" :value="2">
                                    </el-option>
                                </el-select>
                            </el-form-item>

                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="性别" prop="genderName" ref="errorEl" label-width="100px">
                                <el-select v-model="reportForm.genderName" filterable :disabled="isDisabled(3)" placeholder="请选择">
                                    <el-option label="男" value="男">
                                    </el-option>
                                    <el-option label="女" value="女">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="移动电话" prop="mobile" label-width="100px">
                                <el-input v-model="reportForm.mobile" :disabled="isDisabled(3)" @keyup.native="handlerTelKeyDown"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="5" :offset="0">
                            <el-form-item label="联系电话" label-width="100px">
                                <el-input v-model="reportForm.contactTel" :disabled="isDisabled(3)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="固定电话" label-width="100px">
                                <el-input v-model="reportForm.fixedTelephone" :disabled="isDisabled(3)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                          <el-form-item label="电子邮件" prop="eMail" label-width="100px">
                            <el-input v-model="reportForm.eMail" :disabled="isDisabled(3)"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="联系地址" label-width="100px">
                                <el-input v-model="reportForm.contactAddress" :disabled="isDisabled(3)"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="5" :offset="0">
                            <el-form-item label="邮政编码" label-width="100px">
                                <el-input v-model="reportForm.postalCode" :disabled="isDisabled(3)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="管家姓名" label-width="100px">
                                <el-input maxlength="10" v-model="reportForm.housekeeperName" :disabled="isDisabled(3)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="管家电话" label-width="100px">
                                <el-input v-model="reportForm.housekeeperTel" :disabled="isDisabled(3)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="特殊客户" label-width="100px">
                                <el-select v-model="reportForm.specialUser" :disabled="isDisabled(3)" filterable placeholder="请选择">
                                    <el-option :value="1" label="是"></el-option>
                                    <el-option :value="-1" label="否"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--<div class="ta-ct mt-10" style="position:relative;height:30px;">
                        <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                        <span class="txt" @click="morePersonMsg = !morePersonMsg">更多</span>
                    </div>-->
                    <el-collapse-transition>
                        <div v-show="morePersonMsg">
                            <el-row>
                                <el-col :span="5" :offset="0">
                                    <el-form-item label="国籍" label-width="100px">
                                        <el-input v-model="reportForm.nationality" :disabled="isDisabled(3)"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="证件类型" label-width="100px">
                                        <el-select v-model="reportForm.idCode" placeholder="请选择" @change="changeSelect('idName','idCode','certificateType')" :disabled="isDisabled(3)">
                                            <el-option v-for="i in dict.filter(i=>i.dictCode =='certificateType')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="证件号码" label-width="100px">
                                        <el-input maxlength="20" v-model="reportForm.idNo" :disabled="isDisabled(3)"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="5" :offset="0">
                                    <el-form-item label="出生日期" label-width="100px">
                                        <el-date-picker v-model="reportForm.birthDate" type="date" :disabled="isDisabled(3)" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="职业" label-width="100px">
                                        <el-input v-model="reportForm.occupation" :disabled="isDisabled(3)"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="工作单位" label-width="100px">
                                        <el-input v-model="reportForm.workUnit" :disabled="isDisabled(3)"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="5" :offset="0">
                                    <el-form-item label="传真电话" label-width="100px">
                                        <el-input v-model="reportForm.faxPhone" :disabled="isDisabled(3)"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="其他板块会员" label-width="100px">
                                        <el-select v-model="reportForm.ortherMember" :disabled="isDisabled(3)" filterable placeholder="请选择">
                                            <el-option label="是" :value="1"></el-option>
                                            <el-option label="否" :value="-1"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="兴趣爱好" label-width="100px">
                                        <el-input v-model="reportForm.hobby" :disabled="isDisabled(3)"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-collapse-transition>
                    <div class="ta-ct mt-10" style="position:relative;height:30px;">
                      <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                      <span class="txt" @click="morePersonMsg = !morePersonMsg"><i style="font-size:18px;color:#E63F3C;" :class="morePersonMsg?'el-icon-caret-top':'el-icon-caret-bottom'"></i></span>
                    </div>
                    <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                    <div class="mt-10">
                        <button class="btnDown ft-12 fr mr-10" v-if="!processDisabled" @click.prevent="addPersonVisible=true" v-show="!isDisabled(3)">添加成员</button>
                        <div class="clear"></div>
                    </div>
                    <div>
                        <table class="table mt-10">
                            <thead class="odd">
                                <tr>
                                    <th width="100">家庭成员</th>
                                    <th width="100">性别</th>
                                    <th width="100">与户主关系</th>
                                    <th width="100">国籍</th>
                                    <th width="100">出生日期</th>
                                    <th width="100">证件名称</th>
                                    <th width="200">证件号码</th>
                                    <th width="100">移动电话</th>
                                    <th width="100">操作</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div>
                        <table>
                            <tbody>
                                <tr v-if="formCustFamilies.length==0">
                                    <td width="100">暂无数据</td>
                                </tr>
                                <tr v-else v-for="(item,key) in formCustFamilies" :key='key' :class="key%2==0?'even':'odd'">
                                    <td width="100">{{item.memberName}}</td>
                                    <td width="100">{{item.sex}}</td>
                                    <td width="100">{{item.householdRelation}}</td>
                                    <td width="100">{{item.nationality}}</td>
                                    <td width="100">{{item.birthday}}</td>
                                    <td width="100">{{item.certificateName}}</td>
                                    <td width="200">{{item.idNumber}}</td>
                                    <td width="100">{{item.mobile}}</td>
                                    <td width="100" v-if="processDisabled">
                                        <span>编辑 </span> &nbsp;
                                        <span>删除</span>
                                    </td>
                                    <td width="100" v-else style="color:#1D85FE;">
                                        <span @click="memberEdit(item,key)" v-show="!isDisabled(3)">编辑 </span> &nbsp;
                                        <span @click="memberDel(item,key)" v-show="!isDisabled(3)">删除</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-form>
            </div>
        </div>
        <!-- 房屋信息 -->
        <div class="bg-style mt-10">
            <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
                <span class="redSide ml-10"> </span>
                <span class="ft-14 fw-bd">房屋信息</span>
            </div>
            <div>
                <el-form :inline-message="true" :show-message="false" ref="reportForm" :model="reportForm" :rules="rules" label-position="right" label-width="70px">
                    <el-row>
                        <el-col :span="5" :offset="0">
                            <el-form-item label="区域" prop="regionCode" label-width="100px">
                                <el-select filterable :disabled="isDisabled(3) || reportForm.id?true:false || roomId ?true:false" v-model="reportForm.regionCode" placeholder="请选择" @change="projectChange(rcpBur[0].list,'regionCode','region')">
                                    <el-option v-for="i in rcpBur[0].list" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                    </el-option>
                                    <el-option v-if="reportForm.processCode == 'processKSCL'" value="其他" lable="其他" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="城市" prop="cityCode" label-width="100px">
                                <el-select filterable :disabled="isDisabled(3) || reportForm.id?true:false || roomId ?true:false || !reportForm.region" v-model="reportForm.cityCode" placeholder="请选择" @change="projectChange(rcpBur[1].list,'cityCode','city')">
                                    <el-option v-for="i in rcpBur[1].list" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                    </el-option>
                                    <el-option v-if="reportForm.processCode == 'processKSCL'" value="其他" lable="其他" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="项目" prop="projectCode" label-width="100px">
                                <el-select filterable :disabled="isDisabled(3) || reportForm.id?true:false || roomId ?true:false || !reportForm.city" v-model="reportForm.projectCode" placeholder="请选择" @change="projectChange(rcpBur[2].list,'projectCode','project')">
                                    <el-option v-for="i in rcpBur[2].list" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                    </el-option>
                                    <el-option v-if="reportForm.processCode == 'processKSCL'" value="其他" lable="其他" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="楼栋" label-width="100px" v-if="reportForm.publicArea != 1">
                                <el-select filterable :disabled="isDisabled(3) || !reportForm.projectCode" v-model="reportForm.buildingNo" placeholder="请选择" @change="projectChange">
                                    <el-option v-for="i in rcpBur[3].list" :key="i.building" :label="i.building" :value="i.building">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="5" :offset="0">
                            <el-form-item label="单元号" label-width="100px" v-if="reportForm.publicArea != 1">
                                <el-select filterable :disabled="isDisabled(3) || !reportForm.buildingNo" v-model="reportForm.buildingUnit" placeholder="请选择" @change="projectChange">
                                    <el-option v-for="i in rcpBur[4].list" :key="i.unit" :label="i.unit" :value="i.unit">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="房间号" label-width="100px" v-if="reportForm.publicArea != 1">
                                <el-select filterable :disabled="isDisabled(3) || !reportForm.buildingUnit" v-model="reportForm.roomNo" placeholder="请选择">
                                    <el-option v-for="i in rcpBur[5].list" :key="i.roomNum" :label="i.roomNum" :value="i.roomNum">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                          <el-form-item label="房屋编号" prop="houseNo" label-width="100px" v-if="reportForm.publicArea != 1">
                            <el-input v-model="reportForm.houseNo" :disabled="isDisabled(3)"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                          <el-form-item label="房屋名称" label-width="100px" v-if="reportForm.publicArea != 1">
                            <el-input v-model="reportForm.houseName" :disabled="isDisabled(3)"></el-input>
                          </el-form-item>
                        </el-col>
                    </el-row>
                    <!--<div class="ta-ct mt-10" style="position:relative;height:30px;">
                        <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                        <span class="txt" @click="moreHouseMsg=!moreHouseMsg">更多</span>
                    </div>-->
                    <el-collapse-transition v-if="reportForm.publicArea != 1">
                        <div v-show="moreHouseMsg">
                            <el-row>
                                <el-col :span="5" :offset="0">
                                    <el-form-item label="使用性质" label-width="100px">
                                        <el-select v-model="reportForm.usePropertyCode" placeholder="请选择" @change="changeSelect('usePropertyName','usePropertyCode','useProperty')" :disabled="isDisabled(3)">
                                            <el-option v-for="i in dict.filter(i=>i.dictCode =='useProperty')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="合同交房时间" label-width="100px">
                                        <el-date-picker v-model="reportForm.contractDeliveryTime" type="date" :disabled="isDisabled(3)" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="签约时间" label-width='100px'>
                                        <el-date-picker v-model="reportForm.signingTime" type="date" :disabled="isDisabled(3)" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="预计脱保时间" label-width="100px">
                                        <el-date-picker v-model="reportForm.estimatedReleaseTime" :disabled="isDisabled(3)" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="5" :offset="0">
                                    <el-form-item label="集中交房时间" label-width="100px">
                                        <el-date-picker v-model="reportForm.focusDeliveryTimeFrom" :disabled="isDisabled(3)" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="至" label-width="100px">
                                        <el-date-picker v-model="reportForm.focusDeliveryTimeTo" type="date" :disabled="isDisabled(3)" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="实际交房时间" label-width="100px">
                                        <el-date-picker v-model="reportForm.actualDeliverTime" type="date" :disabled="isDisabled(3)" value-format="yyyy-MM-dd" placeholder="选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                  <el-form-item label="入住时间" label-width="100px">
                                    <el-date-picker v-model="reportForm.checkInTime" type="date" :disabled="isDisabled(3)" value-format="yyyy-MM-dd" placeholder="选择日期">
                                    </el-date-picker>
                                  </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="5" :offset="0">
                                    <el-form-item label="交付状态" label-width="100px">
                                        <el-select v-model="reportForm.deliveryState" filterable :disabled="isDisabled(3)" placeholder="请选择">
                                            <el-option label="已交付" value="1"></el-option>
                                            <el-option label="未交付" value="-1"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <el-form-item label="是否精装" label-width="100px">
                                        <el-select v-model="reportForm.hardcoverState" :disabled="isDisabled(3)" filterable placeholder="请选择" value-key>
                                            <el-option label="是" value="1"></el-option>
                                            <el-option label="否" value="-1"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-collapse-transition>
                    <div class="ta-ct mt-10" style="position:relative;height:30px;" v-if="reportForm.publicArea != 1">
                      <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                      <span class="txt" @click="moreHouseMsg=!moreHouseMsg"><i style="font-size:18px;color:#E63F3C;" :class="moreHouseMsg?'el-icon-caret-top':'el-icon-caret-bottom'"></i></span>
                    </div>
                </el-form>
            </div>
        </div>
        <el-dialog id="dialogPane" :title="person" :visible.sync="addPersonVisible" width="40%" center>
            <el-form :inline-message="true" :show-message="false" ref="formCust" label-position="right" :rules="custRules" :model="formCust" label-width="100px">
                <el-form-item label="成员姓名" prop="memberName">
                    <el-input v-model="formCust.memberName" :disabled="isDisabled('visible')"></el-input>
                </el-form-item>
                <el-form-item label="性别" prop="sex">
                    <el-select v-model="formCust.sex" filterable placeholder="请选择" :disabled="isDisabled('visible')">
                        <el-option label="男" value="男">
                        </el-option>
                        <el-option label="女" value="女">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="与户主关系" prop="householdRelation">
                    <el-select :disabled="isDisabled('visible')" v-model="formCust.householdRelation" placeholder="请选择" @change="changeSelect('householdRelation','householdRelation','reWithHouseholder')">
                        <el-option v-for="i in dict.filter(i=>i.dictCode =='reWithHouseholder')" :key="i.itemCode" :label="i.itemValue" :value="i.itemValue">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="国籍">
                    <el-input :disabled="isDisabled('visible')" v-model="formCust.nationality"></el-input>
                </el-form-item>
                <el-form-item label="出生日期">
                    <el-date-picker v-model="formCust.birthday" type="date" :disabled="processDisabled" value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="证件名称">
                    <el-select :disabled="isDisabled('visible')" v-model="formCust.certificateName" placeholder="请选择" @change="changeSelect('certificateName','certificateCode','certificateType')">
                        <el-option v-for="i in dict.filter(i=>i.dictCode =='certificateType')" :key="i.itemCode" :label="i.itemValue" :value="i.itemValue">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码">
                    <el-input :disabled="isDisabled('visible')" v-model="formCust.idNumber"></el-input>
                </el-form-item>
                <el-form-item label="移动电话" prop="mobile">
                    <el-input :disabled="isDisabled('visible')" v-model="formCust.mobile"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button v-if="!isDisabled('visible')" class="btnDown" @click="sureAdd">确 定</el-button>
                <el-button v-if="!isDisabled('visible')" class="btnDown" @click="cancelAdd">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {
  hiSelectHouseInfoById,
  ciSelecthcfInfoByNum,
  piSelectHouseInfoRelation,
  piSelectProjectInfoRelation
} from "@/api/port";
import {isvalidPhone,isAllChinese} from "@/api/form";
import { cfSelectByCustId, project } from "@/api/wsd";
export default {
  name: "hc-comp",
  props: ["reportForm", "roomId", "personId", "dict"],
  data() {
    let that = this;
    var validPhone = (rule, value,callback)=>{
        if (!that.reportForm.mobile){
            callback(new Error('请输入电话号码'))
        }else  if (!isvalidPhone(that.reportForm.mobile)){
            callback(new Error('请输入正确的11位手机号码'))
        }else {
            callback()
        }
    }
    var isMobileNum = (rule, value,callback)=>{
        if (!that.formCust.mobile){
            callback(new Error('请输入电话号码'))
        }else  if (!isvalidPhone(that.formCust.mobile)){
            callback(new Error('请输入正确的11位手机号码'))
        }else {
            callback()
        }
    }
    var isChnName = (rule, value,callback)=>{
        if (!that.formCust.memberName){
            callback(new Error('姓名'))
        }else  if (!isAllChinese(that.formCust.memberName)){
            callback(new Error('姓名中只能有汉字'))
        }else {
            callback()
        }
    }
    return {
      person: "添加成员",
      formCust: {}, //成员信息
      addPersonVisible: false,
      processDisabled: false, //不同流程显示隐藏
      //reportForm.processStateCode=='toBeAssigned'"
      morePersonMsg: false, //点击更多显瘾
      moreHouseMsg: false,
      areaOptions: {
        area: [],
        city: [],
        project: [],
        building: [],
        cell: [],
        room: []
      },
      rcpBur: [
        { code: "regionCode", list: [] },
        { code: "cityCode", list: [] },
        { code: "projectCode", list: [] },
        { code: "buildingNo", list: [] },
        { code: "buildingUnit", list: [] },
        { code: "roomNo", list: [] }
      ],
      projectList: {},
      rules: {
        ownerName:[
          { required: true, message: '请填写业主姓名', trigger: 'blur'},
        ],
        regionCode:[
          { required: true, message: '请选择区域', trigger: 'change' },
        ],
        cityCode:[
          { required: true, message: '请选择城市', trigger: 'change' },
        ],
        projectCode:[
          { required: true, message: '请选择项目', trigger: 'change' },
        ],
        ownerType:[
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        genderName:[
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        mobile:[
          //{ required: true, message: '请填写移动电话', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validPhone }
        ],
        eMail:[
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      custRules:{
          memberName:[
            { required: true, trigger: 'blur', validator: isChnName}
          ],
          sex:[
            { required: true, message: '请选择性别', trigger: 'change' }
          ],
          householdRelation:[
            { required: true, message: '请选择与户主关系', trigger: 'change' }
          ],
          mobile:[
            { required: true, trigger: 'blur', validator: isMobileNum }
          ],
      },
      formCustFamilies: []
    };
  },
  watch: {
    "reportForm.cityCode": {
      handler(v) {
        if (v) {
          this.projectChange();
        }
      },
      deep: true
    },
    "reportForm.ownerId"(v) {
        if (v) {
            this.getCfSelectByCustId(v);
        }
    },
    formCustFamilies: {
      handler(v) {
        this.$emit("changeFamly", v)
      },
      deep: true
    }
  },
  methods: {
    handlerTelKeyDown(e){
        let keynum;
        if(window.event) // IE
            keynum = e.keyCode
        else if(e.which) // Netscape/Firefox/Opera
            keynum = e.which
        
        let mobile = this.reportForm.mobile.substring(0,this.reportForm.mobile.length-1);

        if((keynum == 49 || keynum == 97) && isvalidPhone(mobile)){
            this.$set(this.reportForm,"mobile",mobile + ',1')
        }
    },
    /*必填项校验*/
    submitForm(formName) {
      let flag = true;
      this.$refs[formName].validate((valid) => {
        if (valid) {
          flag = true;
        } else {
          flag = false;
          console.log('error',this.$refs);
        }
      });
      return flag;
    },
    /* 添加成员 */
    //processDisabled=='toBeAssigned'?true:processDisabled=='handle'?true:processDisabled=='returnVisit'?true:false
    isDisabled(type){
    //   if(this.$route.query.disabled){
    //       return true;
    //   }  
    //   let userInfo = JSON.parse(sessionStorage.getItem('userInfo'));//当前登录用户信息
    //   if(!userInfo) return true;
    //   if(userInfo.username != this.reportForm.curAssigneeId && this.reportForm.processStateCode && this.reportForm.processStateCode !='returnVisit'){//当前登录用户为非操作用户
    //       return true;
    //   }else if(type == 1){//toBeAssigned可编辑
    //     if(this.reportForm.processStateCode == 'toBeAssigned'){
    //       return false;
    //     }else{
    //       return true;
    //     }
    //   }else if(type == 2){//returnVisit&handle不可编辑
    //     if(this.reportForm.processStateCode == 'returnVisit' || this.reportForm.processStateCode == 'handle'){
    //       return true;
    //     }else{
    //       return false;
    //     }
    //   }else if(type == 3){//都不可编辑
    //     if(this.reportForm.processStateCode == 'returnVisit' || this.reportForm.processStateCode == 'handle' || this.reportForm.processStateCode == 'toBeAssigned' || this.reportForm.processStateCode == 'specialEnd' || this.reportForm.processStateCode == 'nomalEnd'){
    //       return true;
    //     }
    //   }else{
    //     return false;
    //   }
        if(this.$route.query.disabled){
            return true;
        }
        let userInfo = JSON.parse(sessionStorage.getItem('userInfo'));//当前登录用户信息
        if(!userInfo) return true;
        if(userInfo.username != this.reportForm.curAssigneeId && this.reportForm.processStateCode && this.reportForm.processStateCode !='returnVisit' && this.reportForm.processStateName !='业主报事'){//当前登录用户为非操作用户
            return true;
        }else if(type == 1 ){//toBeAssigned可编辑
            if(this.reportForm.processStateCode == 'toBeAssigned' || this.reportForm.processStateCode == undefined || this.reportForm.processStateCode=="draft"){
                return false;
            }else{
                return true;
            }
        }else if(type == 2){//returnVisit&handle不可编辑
            if(this.reportForm.processStateCode == 'returnVisit' || this.reportForm.processStateCode == 'handle'){
                return true;
            }else{
                return false;
            }
        }else if(type == 3){//都不可编辑
            if(this.reportForm.processStateCode == 'returnVisit' || this.reportForm.processStateCode == 'handle' || this.reportForm.processStateCode == 'toBeAssigned' || this.reportForm.processStateCode == 'specialEnd' || this.reportForm.processStateCode == 'nomalEnd'){
                return true;
            }
        }else if(type == 4){
            if(this.reportForm.processStateCode == 'specialEnd' || this.reportForm.processStateCode == 'nomalEnd'){
                return true;
            }
        }else if(type ==5){
            if(this.reportForm.processStateCode == 'specialEnd' || this.reportForm.processStateCode == 'nomalEnd'||this.reportForm.processStateCode=='returnVisit'){
                return true;
            }else{return false;}
        }else{
            return false;
        }
    },
    sureAdd() {
        this.$refs.formCust.validate((valid) => {
        if (valid) {
            if (this.formCust.nowKey != undefined) {
                this.formCustFamilies[this.formCust.nowKey] = this.formCust;
            } else {
                this.formCustFamilies.push(this.formCust);
            }
            this.formCust = {};
            this.addPersonVisible = false;
            // this.$emit("changeFamly", this.formCustFamilies);
        } else {
            return false;
        }
      });
    },
    cancelAdd(){
        this.addPersonVisible = false;
        this.formCust = {};
    },
    memberEdit(item, key) {
      this.addPersonVisible = true;
      this.formCust = Object.assign({}, item);
      this.$set(this.formCust, "nowKey", key);
    },
    memberDel(item, key) {
      this.formCustFamilies.splice(key, 1);
    //   this.$emit("changeFamly", this.formCustFamilies);
    },
    //房屋信息
    gethiSelectHouseInfoById() {
      if (!this.roomId || this.reportForm.id) return;
      hiSelectHouseInfoById({ id: this.roomId }).then(res => {
        if (res.code == 200 && res.data) {
          let data = res.data;
          this.$set(this.reportForm, "houseNo", data.houseNum);
          this.$set(this.reportForm, "houseInfoId", data.houseNum);
          this.$set(this.reportForm, "houseName", data.houseName);
          this.$set(this.reportForm, "regionCode", data.areaCode);
          this.$set(this.reportForm, "region", data.area);
          this.$set(this.reportForm, "cityCode", data.cityCode);
          this.$set(this.reportForm, "city", data.city);
          this.$set(this.reportForm, "project", data.project);
          this.$set(this.reportForm, "projectCode", data.projectId);
          this.$set(this.reportForm, "buildingNo", data.building);
          this.$set(this.reportForm, "buildingUnit", data.unit);
          this.$set(this.reportForm, "roomNo", data.roomNum);
          this.$set(this.reportForm, "usePropertyName", data.useProperty);
          this.$set(
            this.reportForm,
            "contractDeliveryTime",
            new Date(data.deliveryDate)
          );
          this.$set(this.reportForm, "signingTime", data.signTime);
          this.$set(this.reportForm, "estimatedReleaseTime", data.offAidDate);
          this.$set(
            this.reportForm,
            "focusDeliveryTimeFrom",
            data.focusStartDate
          );
          this.$set(this.reportForm, "focusDeliveryTimeTo", data.focusEndDate);
          this.$set(this.reportForm, "deliveryState", data.deliveryState);
          this.$set(
            this.reportForm,
            "hardcoverState",
            data.fitment == "精装房" ? "1" : "-1"
          );
          this.$set(
            this.reportForm,
            "actualDeliverTime",
            data.actualDeliveryDate
          );
          this.$set(this.reportForm, "checkInTime", data.stayTime);

          this.$set(this.reportForm, "housekeeperName", data.stewardName);
          this.$set(this.reportForm, "housekeeperTel", data.stewardTelephone);
        }
      });
    },
    //业主信息
    getciSelecthcfInfoByNum() {
      if (!this.personId || this.reportForm.id) return;
      ciSelecthcfInfoByNum({ id: this.personId }).then(res => {
        if (res.code == 200 && res.data) {
          let data = res.data;
          this.$set(this.reportForm, "ownerName", data.custName);
          this.$set(this.reportForm, "ownerId", data.custId);
          this.$set(
            this.reportForm,
            "ownerType",
            data.belong == "个人" || data.belong == "0" ? 1 : 2
          );
          this.$set(this.reportForm, "ortherMember", data.otherBoardMember);
          this.$set(this.reportForm, "specialUser", data.specialCustomer);
          this.$set(this.reportForm, "mobile", data.telephone);
          this.$set(this.reportForm, "nationality", data.national);
          this.$set(this.reportForm, "idName", data.certificateName);
          this.$set(this.reportForm, "idNo", data.certificateNum);
          this.$set(this.reportForm, "birthDate", new Date(data.birthday));
          this.$set(this.reportForm, "workUnit", data.workUnit);
          this.$set(this.reportForm, "occupation", data.profession);
          this.$set(this.reportForm, "hobby", data.hobbies);
          this.$set(this.reportForm, "faxPhone", data.fax);
          this.$set(this.reportForm, "contactAddress", data.contactAddress);
          this.$set(this.reportForm, "fixedTelephone", data.fixedTelephone);
          this.$set(this.reportForm, "eMail", data.email);
          this.$set(this.reportForm, "postalCode", data.postcode);
          //改为从房屋获取管家
        //   this.$set(this.reportForm, "housekeeperName", data.stewardName);
        //   this.$set(this.reportForm, "housekeeperTel", data.stewardTelephone);
          this.$set(this.reportForm, "openId", data.openId);
        }
      });
    },
    getCfSelectByCustId(v) {
      //获取家庭成员信息
        cfSelectByCustId({ custId: v, formId: this.reportForm.id }).then(
          res => {
            if (res.code == 200 && res.data) {
              this.formCustFamilies = res.data;
            }
          }
        );
    },
    projectChange(l, c, n) {
      if(this.reportForm[c]  == '其他'){
        this.$set(this.reportForm, n, '其他');
        return;
      }
      if (n && Array.isArray(l)) {
        let s = l.filter(i => i.itemCode == this.reportForm[c]);
        if (s && s.length == 1) this.$set(this.reportForm, n, s[0].itemValue);
      }
      let v = this.reportForm;
      if (v && !this.processDisabled) {
        this.rcpBur.forEach((e, i) => {
          let p = i != 0 ? this.rcpBur[i - 1] : null;
          if (p && (this.reportForm[p.code] != p.data || !p.data)) {
            if (p.data) {
              this.$set(this.reportForm, e.code, null);
            }
            p.data = this.reportForm[p.code];
            if (i >= 3) {
              let s = {
                projectId: this.reportForm.projectCode,
                building: i != 3 ? this.reportForm.buildingNo : null,
                unit: i == 5 ? this.reportForm.buildingUnit : null
              };
              piSelectHouseInfoRelation(s).then(res => {
                if (res.code === 200 && res.data) {
                  e.list = res.data;
                }
              });
            } else {
              if (this.projectList && Array.isArray(this.projectList[e.code]))
                e.list = this.projectList[e.code].filter(
                  i => i.parent == this.reportForm[p.code]
                );
            }
          }
        });
      }
    },
    changeSelect(k, v, d) {
      if (this.dict && Array.isArray(this.dict)) {
        let s = this.dict.filter(
          i => i.itemCode == this.reportForm[v] && i.dictCode == d
        );
        if (s && s.length == 1) this.$set(this.reportForm, k, s[0].itemValue);
      }
    },
    getProject() {
      project().then(res => {
        if (res.sucess && res.data) {
          let r = [],
            c = [],
            p = [];
          res.data.forEach(e => {
            if (
              r.length == 0 ||
              r.filter(i => i.itemCode == e.regionCode).length == 0
            )
              r.push({
                itemCode: e.regionCode,
                itemValue: e.region
              });
            if (
              c.length == 0 ||
              c.filter(i => i.itemCode == e.cityCompanyCode).length == 0
            )
              c.push({
                parent: e.regionCode,
                itemCode: e.cityCompanyCode,
                itemValue: e.cityCompany
              });

            p.push({
              parent: e.cityCompanyCode,
              itemCode: e.projectCode,
              itemValue: e.project
            });
          });
          this.projectList = {
            projectCode: p,
            cityCode: c,
            regionCode: r
          };
          this.rcpBur[0].list = this.projectList.regionCode;
          if (this.reportForm.regionCode)
            this.rcpBur[1].list = this.projectList.cityCode.filter(
              i => i.parent == this.reportForm.regionCode
            );
          if (this.reportForm.cityCode)
            this.rcpBur[2].list = this.projectList.projectCode.filter(
              i => i.parent == this.reportForm.cityCode
            );
        }
      });
    }
  },
  mounted() {
    //this.reportForm.mobile = this.$route.query.tel;
    if(this.$route.query.tel){
      this.$set(this.reportForm,'mobile',this.$route.query.tel);
    }
    this.getProject();
    this.gethiSelectHouseInfoById();
    this.getciSelecthcfInfoByNum();
    if(this.reportForm.id){
        this.getCfSelectByCustId();
    }
  }
};
</script>

<style scoped>
.hc-comp .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.hc-comp .table tbody tr:hover {
  background-color: #d5dbe4;
}
.hc-comp >>> .el-date-editor {
  width: 100%;
}
.hc-comp >>> .el-input__inner {
  height: 24px !important;
}
</style>
