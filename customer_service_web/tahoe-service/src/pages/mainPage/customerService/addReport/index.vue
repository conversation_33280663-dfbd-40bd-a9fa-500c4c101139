<template>
  <div class="add-report" id="addReportContainer" style="margin-top:40px;">
    <div class="add-report" v-loading.fullscreen.lock="fullscreenLoading">
      <div class="fr" id="flex-top" :style="'top:'+fixTop" style="margin-right:15px;">
        <!-- <span v-if="reportForm.rework=="1""><img src="../../../../assets/images/rework2x.png" alt=""></span> -->
        <span v-if="reportForm.upgradeFlag=='1'" class="mr-10">
          <img src="../../../../assets/images/level_up2x.png" alt>
        </span>
        <button
          class="btnDown"
          @click="handleCommon('submit')"
          :disabled="disClick"
          v-if="isSeatsShow() && !isDisabled('visible') && isEnterByAddList()"
        >提交</button>
        <button
          class="btnDown"
          @click="handleCommon('submit')"
          :disabled="disClick"
          v-if="reportForm.processStateCode=='toBeAssigned' && !isDisabled('visible') && isEnterByAddList()"
        >分派</button>
        <!-- <el-button v-if="reportForm && reportForm.processStateCode =='returnVisit'" @click="subHSubmit" :disabled="disClick">关闭</el-button> -->
        <button
          class="btnDown"
          v-if="reportForm && reportForm.processStateCode =='returnVisit' && !isDisabled('visible') && userInfo.seats && isEnterByAddList()"
          @click="subReject"
          :disabled="disClick"
        >返工</button>
        <button
          class="btnDown"
          v-if="reportForm && reportForm.processStateCode=='handle' &&(reportForm.firstSortCode=='coTS'||reportForm.firstSortCode=='coBX') && !isDisabled('visible') && isEnterByAddList()"
          @click="subReject"
          :disabled="disClick"
        >退回</button>
        <button
          class="btnDown"
          v-if="reportForm && reportForm.processStateCode=='handle' &&(reportForm.firstSortCode=='coTS'||reportForm.firstSortCode=='coBX') && !isDisabled('visible') && isEnterByAddList()"
          @click="subSetLevelUp"
          :disabled="disClick"
        >升级</button>
        <button
          class="btnDown"
          v-if="(isSaveShow() && !isDisabled('visible') && isEnterByAddList()) || (reportForm.processStateCode!='draft' && reportForm.reportChannelCode == 'channelWX')"
          @click="handleCommon('save')"
          :disabled="disClick"
        >{{reportForm.processStateCode=='handle'?'跟进':'暂存'}}</button>
        <button
          class="btnDown"
          v-if="reportForm.processStateCode=='draft' && reportForm.reportChannelCode == 'channelWX'"
          @click="handleCancel"
          :disabled="disClick"
        >取消报事</button>
        <button
          class="btnDown"
          v-if="reportForm && reportForm.processStateCode=='toBeAssigned' && !isDisabled('visible') && isEnterByAddList()"
          :disabled="disClick"
          @click="subSpecialEnd"
        >特殊关闭</button>
        <button
          class="btnDown"
          v-if="reportForm && reportForm.processStateCode=='handle' && !isDisabled('visible') && isEnterByAddList()"
          @click="gotoPrint"
        >打印</button>
        <button class="btnDown" @click="back">返回</button>
        <sumbit-info
          ref="sub"
          v-model="fullscreenLoading"
          :reportForm="reportForm"
          :formCustFamilies="formCustFamilies"
          :fileIdList="fileIdList"
          @disSubmit="disSubmit"
          @refreshData="refreshData"
        ></sumbit-info>
      </div>
      <div class="process-node">
        <ul>
          <li
            v-for="(item,index) in processNodeList.filter(v => v.processStateName != '报事升级')"
            :key="index"
          >
            <span
              :class="item.taskStatus==30?'yellow-bac':item.taskStatus==20?'red-bac':'grey-bac'"
            >{{item.processStateName}}</span>
            &nbsp;&nbsp;
            <i
              v-show="index != processNodeList.filter(v => v.processStateName != '报事升级').length-1"
              :style="item.taskStatus==30?'color:#f5a623;':item.taskStatus==20?'color:#e63f3c':'color:#9b9b9b'"
              class="el-icon-arrow-right"
            >&nbsp;&nbsp;</i>
          </li>
        </ul>
      </div>
      <div
        class="bg-style mt-10 mb-10"
        v-show="reportForm.processStateCode == 'returnVisit' && userInfo.seats && isEnterByAddList()"
      >
        <div style="height:44px;line-height:44px;">
          <span class="redSide ml-10"></span>
          <span class="ft-14 fw-bd">报事满意度</span>
        </div>
        <el-row>
          <el-col :span="20" :offset="1">
            <el-form ref="satisfaction" :show-message="false" :model="reportForm" :rules="rules">
              <el-form-item label="满意评分" prop="satisfactionCode">
                <el-radio-group v-model="reportForm.satisfactionCode" @change="getSatisfactionName">
                  <el-radio
                    :disabled="isDisabled('visible')"
                    :label="i.label"
                    v-for=" i in satisfactionOptions"
                    :key="i.label"
                  >{{i.value}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- <div class="ml-30 pb-10" style="box-sizing:border-box;">
                <span>满意评分：&nbsp;</span>

                <div style="height:10px;"></div>
        </div>-->
      </div>
      <hccomp
        ref="hccomp"
        :reportForm="reportForm"
        :roomId="$route.query.roomId"
        :personId="$route.query.personId"
        :dict="dict"
        @changeFamly="changeFamly"
      ></hccomp>
      <!-- 咨询报事信息 -->
      <div class="bg-style mt-10">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"></span>
          <span class="ft-14 fw-bd">{{reportForm.firstSortName}}报事信息</span>
        </div>
        <div>
          <el-form
            :inline-message="true"
            :show-message="false"
            ref="reportFormZX"
            :model="reportForm"
            :rules="rules"
            label-position="right"
            label-width="90px"
            class="demo-ruleForm"
          >
            <el-row>
              <el-col :span="5" :offset="0">
                <el-form-item label="工单编号" prop="formNo" label-width="100px">
                  <el-input disabled v-model="reportForm.formNo"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="部门" prop="deptCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.deptCode"
                    placeholder="请选择"
                    @change="changeSelect('deptName','deptCode','deptCode')"
                    :disabled="isDisabled(3)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='deptCode').filter(i =>!this.userInfo.seats || i.itemCode != 'deptIT')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                    <!-- <el-option value="deptDC" label='地产'></el-option>
                    <el-option value="deptWY" label='物业'></el-option>-->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item
                  label="是否公区"
                  prop="publicArea"
                  v-if="reportForm.deptCode == 'deptWY'"
                  label-width="100px"
                >
                  <el-select
                    v-model="reportForm.publicArea"
                    filterable
                    placeholder="请选择"
                    :disabled="isDisabled('visible') || isDisabled(3)"
                    value-key="value"
                  >
                    <el-option label="否" value="-1"></el-option>
                    <el-option label="是" value="1"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="报事渠道" prop="reportChannelCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.reportChannelCode"
                    placeholder="请选择"
                    @change="changeSelect('reportChannelName','reportChannelCode','reportChannel')"
                    :disabled="isDisabled(3)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='reportChannel')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="reportForm.deptCode == 'deptWY'?0:1">
                <el-form-item label="受理渠道" prop="acceptChannelCode" label-width="100px">
                  <el-select
                    v-model="reportForm.acceptChannelCode"
                    filterable
                    placeholder="请选择"
                    :disabled='isDisabled(3)'
                  >
                    <el-option value="400" label="400"></el-option>
                    <el-option value="项目" label="项目"></el-option>
                    <el-option value="物业" label="物业"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" v-if="reportForm.deptCode == 'deptDC'">
                <el-form-item label="流程类别" prop="processCode" label-width="100px">
                  <el-select
                    v-model="reportForm.processCode"
                    placeholder="请选择"
                    @change="changeSelect('processName','processCode','processCode')"
                    :disabled="isDisabled(3)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='processCode')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="一级分类" prop="firstSortCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.firstSortCode"
                    placeholder="请选择"
                    @change="changeSelect('firstSortName','firstSortCode',reportForm.deptCode == 'deptDC'?'firstSortCode':'wyFirstSortCode')"
                    :disabled="isDisabled(3)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>this.firstFilter(i))"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="二级分类" prop="secSortCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.secSortCode"
                    placeholder="请选择"
                    @change="changeSelect('secSortName','secSortCode',reportForm.firstSortCode)"
                    :disabled="isDisabled(1)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode == reportForm.firstSortCode)"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 报修 -->
              <el-col :span="5" :offset="1">
                <el-form-item
                  label-width="100px"
                  label="三级分类"
                  prop="thirdSortCode"
                  v-if="reportForm.firstSortCode == 'coBX' ||reportForm.firstSortCode == 'coTS'"
                >
                  <el-select
                    filterable
                    v-model="reportForm.thirdSortCode"
                    placeholder="请选择"
                    @change="changeSelect('thirdSortName','thirdSortCode',reportForm.secSortCode)"
                    :disabled="isDisabled(1)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode == reportForm.secSortCode)"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="0">
                <el-form-item
                  label-width="100px"
                  label="四级分类"
                  prop="fourthSortCode"
                  v-if="reportForm.firstSortCode == 'coBX'"
                >
                  <el-select
                    filterable
                    v-model="reportForm.fourthSortCode"
                    placeholder="请选择"
                    @change="changeSelect('fourthSortName','fourthSortCode',reportForm.thirdSortCode)"
                    :disabled="isDisabled(1)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode == reportForm.thirdSortCode)"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="5"
                :offset="reportForm.firstSortCode == 'coTS'?0:1"
                v-if="reportForm.firstSortCode == 'coTS' || reportForm.firstSortCode == 'coBX'"
              >
                <el-form-item label="问题严重级别" prop="problemLevelCode" label-width="100px">
                  <el-select
                    v-model="reportForm.problemLevelCode"
                    placeholder="请选择"
                    @change="changeSelect('problemLevelName','problemLevelCode','problemLevelCode')"
                    :disabled="isDisabled(1)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='problemLevelCode')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1" v-if="reportForm.firstSortCode == 'coBX'">
                <el-form-item label="报修部位" prop="problemPositionCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.problemPositionCode"
                    placeholder="请选择"
                    @change="changeSelect('problemPositionName','problemPositionCode','problemPositionCode')"
                    :disabled="isDisabled(1)"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='problemPositionCode')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1" v-if="reportForm.firstSortCode == 'coBX'">
                <el-form-item label="报修分类" prop="revisionClassificationCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.revisionClassificationCode"
                    :disabled="isDisabled(1)"
                    placeholder="请选择"
                    @change="changeSelect('revisionClassificationName','revisionClassificationCode','revisionClassificationCode')"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='revisionClassificationCode')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="0" v-if="reportForm.firstSortCode == 'coBX'">
                <el-form-item label="装修阶段" prop="decorationStageCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.decorationStageCode"
                    :disabled="isDisabled(1)"
                    placeholder="请选择"
                    @change="changeSelect('decorationStageName','decorationStageCode','decorationStatus')"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='decorationStatus')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="5"
                :offset="1"
                v-if="reportForm.firstSortCode == 'coTS' || reportForm.firstSortCode == 'coBX'"
              >
                <el-form-item label="内部责任部门" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.interResDepartmentCode"
                    :disabled="isDisabled(1)"
                    placeholder="请选择"
                    @change="changeSelect('interResDepartmentName','interResDepartmentCode','interResDepartmentCode')"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='interResDepartmentCode')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1" v-if="reportForm.firstSortCode == 'coBX'">
                <el-form-item label="维保期" prop="maintenancePeriodCode" label-width="100px">
                  <el-select
                    filterable
                    v-model="reportForm.maintenancePeriodCode"
                    :disabled="isDisabled(1)"
                    placeholder="请选择"
                    @change="changeSelect('maintenancePeriodName','maintenancePeriodCode','maintenancePeriodCode')"
                  >
                    <el-option
                      v-for="i in dict.filter(i=>i.dictCode =='maintenancePeriodCode')"
                      :key="i.itemCode"
                      :label="i.itemValue"
                      :value="i.itemCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <!-- 报事内容信息 -->
      <div class="bg-style mt-10">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"></span>
          <span class="ft-14 fw-bd">{{reportForm.firstSortName}}报事内容信息</span>
        </div>
        <div>
          <el-form
            :inline-message="true"
            :show-message="false"
            ref="reportFormKH"
            :rules="rules"
            :model="reportForm"
            label-position="right"
            label-width="70px"
            style="padding-bottom:20px;"
          >
            <el-row>
              <el-col
                :span="24"
                :offset="0"
                v-if="reportForm.processStateCode =='draft'||!reportForm.processStateCode"
              >
                <el-form-item label="常用问题" label-width="100px">
                  <el-select
                    style="width:200px;"
                    v-model="reportForm.commonProblemCode"
                    filterable
                    placeholder="请选择"
                    :disabled="isDisabled('visible')"
                    @change="searchDesc"
                  >
                    <el-option
                      v-for="item in allTrouble"
                      :key="item.id"
                      :label="item.questionTitle"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                  <!-- <button class="btnDown ml-10" @click.prevent="openDilog" v-if="!isDisabled('visible')">自定义</button> -->
                  <button
                    class="btnDown ml-10"
                    @click.prevent="openDilog"
                    v-if="!isDisabled('visible')"
                  >新增</button>
                  <button
                    class="btnDown ml-10"
                    @click.prevent="openEditDilog"
                    v-if="!isDisabled('visible')"
                  >编辑</button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row
              v-if="(reportForm.processStateCode && reportForm.processStateCode !='draft' && reportForm.firstSortCode == 'coTS')  "
            >
              <el-col :span="11" :offset="0">
                <el-form-item label="报事标题" prop="complaintHeadlines" label-width="100px">
                  <el-input
                    maxlength="50"
                    v-model.trim="reportForm.complaintHeadlines"
                    :disabled="isDisabled(1)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11" :offset="0" class="mt-10">
                <el-form-item label="客户诉求" prop="customerDemand" label-width="100px">
                  <el-input
                    class="break-word"
                    type="textarea"
                    :disabled="isDisabled(3)"
                    v-model.trim="reportForm.customerDemand"
                    cols="104"
                    rows="5"
                    maxlength="500"
                    @keyup="checkTextLength(reportForm.customerDemand,500)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col
                :span="11"
                :offset="0"
                v-if="reportForm.reportChannelCode == 'channelWX' || reportForm.firstSortCode == 'coBX' || reportForm.firstSortCode == 'coTS'"
              >
                <el-form-item
                  label="附件"
                  label-width="100px"
                  v-show="reportForm.reportChannelCode == 'channelWX' || !isDisabled(3) || fileList.filter(i=>i.clientPath =='content').length != 0"
                >
                  <el-upload
                    v-if="!reportForm.processStateCode || reportForm.processStateCode =='draft'"
                    action="/api/csFile/fileUpdate"
                    multiple
                    :on-success="handleSuccess"
                    :show-file-list="false"
                    :data="uploadParams"
                    name="localfile"
                  >
                    <el-button
                      class="btnDown"
                      v-if="!isDisabled('visible')"
                      v-show="!reportForm.processStateCode || reportForm.processStateCode =='draft'"
                    >点击上传</el-button>
                  </el-upload>
                  <!-- <ul>
                                    <li v-for="item in this.fileList" :key="item.id" style="cursor:pointer;color:blue;" @click.prevent="downLoadFile(item.id)">{{item.name}}</li>
                  </ul>-->
                  <table class="table" style="margin-top:5px;">
                    <tbody>
                      <tr
                        v-for="(item,index) in fileList.filter(i=>i.clientPath !='handle')"
                        :key="index"
                      >
                        <td width="100" style="color:#1D85FE;text-align:left;">
                          <span @click.prevent="downLoadFile(item)">{{item.fileName}}</span>
                          <span
                            class="ta-rt ml-30"
                            v-if="!reportForm.processStateCode || reportForm.processStateCode =='draft'"
                            @click.prevent="delFile(item,index)"
                          >删除</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <!-- 报事处理信息 -->
      <div
        class="bg-style mt-10"
        v-show="reportForm.processCode=='processKSCL'||reportForm.processStateCode=='handle'||reportForm.processStateCode=='nomalEnd'||reportForm.processStateCode=='specialEnd' || reportForm.processStateCode=='returnVisit'"
      >
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"></span>
          <span class="ft-14 fw-bd">{{reportForm.firstSortName}}报事处理信息</span>
        </div>
        <div>
          <el-form
            :inline-message="true"
            :show-message="false"
            ref="reportFormChuli"
            :rules="rules"
            :model="reportForm"
            label-position="right"
            label-width="100px"
          >
            <el-row class="mt-10 mb-10">
              <el-col :span="11" :offset="0">
                <el-form-item label="处理记录" label-width="100px" prop="handleRecord">
                  <el-input
                    class="break-word"
                    type="textarea"
                    cols="30"
                    rows="5"
                    maxlength="500"
                    v-model.trim="reportForm.handleRecord"
                    :disabled="isDisabled(5)"
                    @keyup="checkTextLength(reportForm.handleRecord,500)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <span
              v-if="(reportForm.firstSortCode=='coTS' ||reportForm.firstSortCode=='coBX') && reportForm.processStateCode!='draft'"
            >
              <el-row>
                <el-col :span="5" :offset="0">
                  <el-form-item label="主责任单位" label-width="100px">
                    <el-select
                      v-model="reportForm.mainResUnit"
                      filterable
                      placeholder="请选择"
                      value-key="value"
                      :disabled="isDisabled(5)"
                    >
                      <el-option
                        v-for="(item,index) in accountabilityUnit"
                        :key="index"
                        :label="item.supplierName"
                        :value="item.supplierName"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                  <el-form-item label="维修单位">
                    <el-select
                      v-model="reportForm.repairUnit"
                      filterable
                      placeholder="请选择"
                      value-key="value"
                      :disabled="isDisabled(5)"
                    >
                      <el-option
                        v-for="(item,index) in accountabilityUnit"
                        :key="index"
                        :label="item.supplierName"
                        :value="item.supplierName"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="5" :offset="1">
                  <el-form-item label="是否启用第三方">
                    <el-select
                      v-model="reportForm.thirdPartyFlag"
                      filterable
                      placeholder="请选择"
                      value-key="value"
                      :disabled="isDisabled(5)"
                    >
                      <el-option label="是" value="1"></el-option>
                      <el-option label="否" value="-1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="5" :offset="0" v-show="reportForm.thirdPartyFlag=='1'">
                  <el-form-item label="第三方名称">
                    <el-select
                      v-model="reportForm.thirdPartyName"
                      filterable
                      placeholder="请选择"
                      value-key="value"
                      :disabled="isDisabled(5)"
                    >
                      <el-option
                        v-for="(item,index) in accountabilityUnit"
                        :key="index"
                        :label="item.supplierName"
                        :value="item.supplierName"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="11" :offset="0" v-show="reportForm.thirdPartyFlag=='1'">
                  <el-form-item label="启用第三方原因">
                    <el-input
                      class="break-word"
                      :disabled="isDisabled(5)"
                      type="textarea"
                      cols="104"
                      rows="5"
                      maxlength="500"
                      v-model="reportForm.thirdPartyReason"
                      @keyup="checkTextLength(reportForm.thirdPartyReason,500)"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </span>
            <el-row>
              <el-col
                :span="11"
                :offset="0"
                v-if="reportForm.processStateCode=='handle' || reportForm.processStateCode=='returnVisit' || reportForm.processStateCode == 'nomalEnd'"
              >
                <el-form-item
                  label="附件"
                  label-width="100px"
                  v-show="!isDisabled(5) || fileList.filter(i=>i.clientPath =='handle').length != 0"
                >
                  <el-upload
                    v-if="reportForm.processStateCode =='handle'"
                    class="upload-demo"
                    action="/api/csFile/fileUpdate"
                    multiple
                    :on-success="handleSuccess"
                    :data="uploadParamsHandle"
                    :show-file-list="false"
                    name="localfile"
                  >
                  <el-button class="btnDown" v-if="!isDisabled('visible')">点击上传</el-button>
                  </el-upload>
                  <!-- <el-button class="btnDown" >上传附件</el-button> -->
                  <table class="table" style="margin-top:5px;">
                    <tbody>
                      <tr
                        v-for="(item,index) in fileList.filter(i=>i.clientPath =='handle')"
                        :key="index"
                      >
                        <td width="100" style="color:#1D85FE;text-align:left;">
                          <span @click.prevent="downLoadFile(item)">{{item.fileName}}</span>
                          <span
                            class="ta-rt ml-30"
                            v-if="reportForm.processStateCode =='handle' && !isDisabled('visible')"
                            @click.prevent="delFile(item,index)"
                          >删除</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <!-- 跟进记录 -->
      <div
        class="bg-style mt-10"
        v-if="reportForm.deptCode == 'deptDC' && this.reportForm.processCode == 'processBZ'"
      >
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"></span>
          <span class="ft-14 fw-bd">跟进记录</span>
        </div>
        <div>
          <table class="table mt-10" id="tableHover">
            <thead class="odd">
              <tr>
                <th width="100">节点</th>
                <th width="200">操作记录</th>
                <th width="100">操作人</th>
                <th width="100">开始时间</th>
                <th width="100">结束时间</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="recordList.length==0">
                <td colspan="4">暂无数据</td>
              </tr>
              <tr v-else v-for="(item,key) in recordList" :key="key" :class="key%2==0?'even':'odd'">
                <td width="100">{{item.processStateName}}</td>
                <td width="200" v-html="item.comment?item.comment:item.taskEndTime?'已结束':'正在处理'"></td>
                <td width="100">{{item.assignName}}</td>
                <td width="100">{{item.taskStartTime | TimeMoment}}</td>
                <td width="100">{{item.taskEndTime | TimeMoment}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <el-dialog
        title="添加常用问题"
        :visible.sync="addProblemVisible"
        width="50%"
        :append-to-body="appendTrue"
      >
        <el-form>
          <el-form-item label="标题">
            <el-input
              v-model="normalProblemTitle"
              :maxlength="100"
              :disabled="isDisabled('visible')"
            ></el-input>
          </el-form-item>
          <el-form-item label="问题描述">
            <el-input
              class="break-word"
              type="textarea"
              rows="5"
              :maxlength="500"
              v-model="normalProblemContent"
              @keyup="checkTextLength(normalProblemContent,500)"
              :disabled="isDisabled('visible')"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!isDisabled('visible')" class="btnDown" @click="sureAddProblem">确 定</el-button>
          <el-button
            v-if="!isDisabled('visible')"
            class="btnDown"
            @click="addProblemVisible = false"
          >取 消</el-button>
        </span>
      </el-dialog>
      <el-dialog
        title="常用问题列表"
        :visible.sync="editProblemVisible"
        width="50%"
        :append-to-body="appendTrue"
      >
        <el-table
          class="bordernone-input-table"
          border
          :data="problemEditData"
          height="350"
          style="width: 100%"
        >
          <el-table-column prop="questionTitle" label="标题">
            <template slot-scope="scope">
              <el-input
                class="break-word"
                type="textarea"
                size="mini"
                resize="none"
                :maxlength="100"
                :rows="3"
                v-model="scope.row.questionTitle"
              />
            </template>
          </el-table-column>
          <el-table-column prop="questionDesc" label="内容">
            <template slot-scope="scope">
              <el-input
                class="break-word"
                type="textarea"
                size="mini"
                resize="none"
                :maxlength="500"
                :rows="3"
                v-model="scope.row.questionDesc"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <button class="btnDown" @click="deleteProblem(scope.row)">删除</button>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!isDisabled('visible')" class="btnDown" @click="sureSaveProblemList">保 存</el-button>
          <el-button
            v-if="!isDisabled('visible')"
            class="btnDown"
            @click="editProblemVisible = false"
          >取 消</el-button>
        </span>
      </el-dialog>  
      <!-- 取消弹框 -->
      <el-dialog title="取消原因" :visible.sync="dialogVisible" width="40%">
        <span>
          <el-input
            class="break-word"
            type="textarea"
            rows="5"
            :maxlength="500"
            v-model="context"
            @keyup="checkTextLength(context,500)"
          ></el-input>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="affirm">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- <el-dialog title="报事处理记录单套打" class="print-dialog" :visible.sync="showPrint" width="862" :append-to-body="appendTrue" @close="closePrint"> -->
    <v-print v-show="showPrint" ref="printComponent" :printValue="printData"/>
    <!-- </el-dialog> -->
  </div>
</template>
<script>
import {
  selectOptions,
  initSelect,
  fiSave,
  initTroubleSelect,
  troubleSelectADD,
  siSelectNameByArea,
  selectQuestionById,
  getProcessNodeByFormId,
  deleteTrouble
} from "@/api/port";
import {
  fiSelectCsformInst,
  dict,
  cfRelationFormAndFiles,
  cfSelectFileByFormNo,
  cfFileDownload,
  cfReturnVisitLock,
  getFormNo,
  deleteFile
} from "@/api/wsd";
import { cancel } from "@/api/wechat";
import hccomp from "./hc_comp.vue";
import qs from "qs";
import sumbitInfo from "@/pages/mainPage/common/submit_info";
import Print from "@/pages/mainPage/common/print";
export default {
  components: {
    hccomp,
    sumbitInfo,
    "v-print": Print
  },
  watch: {
    "reportForm.deptCode"(v) {
      if (v == "deptIT") {
        let query = this.$route.query;
        this.$router.push({ path: "itReport", query: query });
      }
      if (v == "deptWY") {
        if (
          !this.reportForm.formNo ||
          v.indexOf(this.reportForm.formNo.substring(0, 2)) == -1
        ) {
          getFormNo({ firstType: "deWY" }).then(res => {
            if (res.sucess && res.data) {
              this.$set(this.reportForm, "formNo", res.data);
            }
          });
        }
      }
    },
    "reportForm.firstSortCode"(v) {
      if (v && this.reportForm.deptCode == "deptDC") {
        if (
          !this.reportForm.formNo ||
          v.indexOf(this.reportForm.formNo.substring(0, 2)) == -1
        ) {
          getFormNo({ firstType: v }).then(res => {
            if (res.sucess && res.data) {
              this.$set(this.reportForm, "formNo", res.data);
            }
          });
        }
      }
    },
    "reportForm.commonProblemCode"(val) {
      let isHasCode = false;
      this.allTrouble.map(item => {
        if (item.id == val) {
          isHasCode = true;
        }
      });
      if (!isHasCode) {
        this.$set(this.reportForm, "commonProblemCode", null);
      }
    },
    allTrouble(val) {
      let isHasCode = false;
      val.map(item => {
        if (item.id == this.reportForm.commonProblemCode) {
          isHasCode = true;
        }
      });
      if (!isHasCode) {
        this.$set(this.reportForm, "commonProblemCode", null);
      }
    }
  },
  data() {
    return {
      userInfo: JSON.parse(sessionStorage.getItem("userInfo")),
      formCustFamilies: [],
      addShow: true,
      showPrint: false,
      printData: {}, //打印数据
      ZXShow: false, //咨询显示
      disClick: false,
      appendTrue: true,
      //reportForm: {}, //所有数据
      reportForm: {
        deptCode: "deptDC",
        deptName: "地产",
        processCode: "processBZ",
        processName: "标准流程",
        firstSortCode: "",
        commonProblemCode: null
      }, //所有数据
      //附件
      fileList: [],
      fileIdList: [],
      allTrouble: [],
      recordList: [],
      processNodeList: [
        { processStateName: "报事录入", taskStatus: 20 },
        { processStateName: "报事分派", taskStatus: 10 },
        { processStateName: "报事处理", taskStatus: 10 },
        { processStateName: "报事回访", taskStatus: 10 },
        { processStateName: "报事关闭", taskStatus: 10 }
      ],
      SJShow: false,
      rejectFlg: false,
      dialogVisible: false,//取消报事弹框
      context: "",
      specialClose: false, //特殊关闭
      alertFlg: false, // 输出意见弹出
      closeFlg: false, // 正常关闭
      levelUpFlg: false, //升级
      addProblemVisible: false,
      fullscreenLoading: false,
      normalProblemContent: "", //自定义问题标题、内容
      normalProblemTitle: "",
      uploadParams: {
        clientPath: "content"
      },
      uploadParamsHandle: {
        clientPath: "handle"
      },
      fixTop: 0,
      accountabilityUnit: [],
      rules: {
        processCode: [
          { required: true, message: "请选择流程类别", trigger: "change" }
        ],
        firstSortCode: [
          { required: true, message: "请选择一级分类", trigger: "change" }
        ],
        secSortCode: [
          { required: true, message: "请选择二级分类", trigger: "change" }
        ],
        thirdSortCode: [
          { required: true, message: "请选择三级分类", trigger: "change" }
        ],
        fourthSortCode: [
          { required: true, message: "请选择四级分类", trigger: "change" }
        ],
        problemLevelCode: [
          { required: true, message: "请选择问题严重级别", trigger: "change" }
        ],
        publicArea: [
          { required: true, message: "请选择公区", trigger: "change" }
        ],
        reportChannelCode: [
          { required: true, message: "请选择报事渠道", trigger: "change" }
        ],
        complaintHeadlines: [
          {
            required: true,
            message: "请填写受理标题",
            trigger: "change"
          }
        ],
        customerDemand: [
          { required: true, message: "请填写客户诉求", trigger: "blur" }
        ],
        maintenancePeriodCode: [
          { required: false, message: "请选择维保期", trigger: "change" }
        ],
        decorationStageCode: [
          { required: false, message: "请选择装修阶段", trigger: "change" }
        ],
        revisionClassificationCode: [
          { required: false, message: "请选择报修分类", trigger: "change" }
        ],
        problemPositionCode: [
          { required: false, message: "请选择报修部位", trigger: "change" }
        ],
        satisfactionCode: [
          { required: false, message: "请选择报事满意度", trigger: "change" }
        ],
        handleRecord: [
          { required: false, message: "请填写处理记录", trigger: "change" }
        ]
      },
      satisfactionOptions: [
        { label: "5", value: "5分 非常满意" },
        { label: "4", value: "4分 比较满意" },
        { label: "3", value: "3分 一般" },
        { label: "2", value: "2分 不满意" },
        { label: "1", value: "1分 非常不满意" },
        { label: "-1", value: "不计入满意度" }
      ],
      dict: [],
      rtTimer: null, // 回访定时
      problemEditData: [],
      editProblemVisible: false,
      
    };
  },
  methods: {
    // 判断是否显示跟进
    isSaveShow() {
      if (
        !this.reportForm.id ||
        this.reportForm.processStateCode == "draft" ||
        (this.reportForm.processStateCode == "handle" &&
          this.reportForm.firstSortCode != "coZX")
      ) {
        return true;
      } else {
        return false;
      }
    },
    gotoPrint() {
      //套打
      this.printData = this.reportForm;
      console.log(this.$refs);
      setTimeout(() => {
        this.$refs.printComponent.print();
      }, 50);
      //this.showPrint = true;
    },
    closePrint() {
      this.printData = {};
    },
    isDisabled(type) {
      if (this.$route.query.disabled) {
        return true;
      }
      if (
        this.userInfo.username != this.reportForm.curAssigneeId &&
        this.reportForm.processStateCode &&
        this.reportForm.processStateCode != "returnVisit" &&
        this.reportForm.processStateName != "业主报事"
      ) {
        //当前登录用户为非操作用户
        return true;
      } else if (type == 1) {
        //toBeAssigned可编辑
        if (
          this.reportForm.processStateCode == "toBeAssigned" ||
          this.reportForm.processStateCode == undefined ||
          this.reportForm.processStateCode == "draft"
        ) {
          return false;
        } else {
          return true;
        }
      } else if (type == 2) {
        //returnVisit&handle不可编辑
        if (
          this.reportForm.processStateCode == "returnVisit" ||
          this.reportForm.processStateCode == "handle"
        ) {
          return true;
        } else {
          return false;
        }
      } else if (type == 3) {
        //都不可编辑
        if (
          this.reportForm.processStateCode == "returnVisit" ||
          this.reportForm.processStateCode == "handle" ||
          this.reportForm.processStateCode == "toBeAssigned" ||
          this.reportForm.processStateCode == "specialEnd" ||
          this.reportForm.processStateCode == "nomalEnd"
        ) {
          return true;
        }
      } else if (type == 4) {
        if (
          this.reportForm.processStateCode == "specialEnd" ||
          this.reportForm.processStateCode == "nomalEnd"
        ) {
          return true;
        }
      } else if (type == 5) {
        if (
          this.reportForm.processStateCode == "specialEnd" ||
          this.reportForm.processStateCode == "nomalEnd" ||
          this.reportForm.processStateCode == "returnVisit"
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    isSeatsShow() {
      //报事回访页面，只有400才能编辑的判断
      if (this.reportForm) {
        if (
          this.reportForm.processStateCode != "nomalEnd" &&
          this.reportForm.processStateCode != "specialEnd" &&
          this.reportForm.processStateCode != "toBeAssigned"
        ) {
          if (
            this.reportForm.processStateCode == "returnVisit" &&
            !this.userInfo.seats
          ) {
            return false;
          } else {
            return true;
          }
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    isEnterByAddList() {
      //报事录入页面， 报事列表进入的，除暂存和录入工单不可修改
      if (this.$route.query.addListDis) {
        if (this.reportForm.processStateCode == "draft") {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    submitForm(formName) {
      //验证必填项
      let flag = true;
      this.$refs[formName].validate(valid => {
        if (valid) {
          flag = true;
        } else {
          flag = false;
        }
      });
      return flag;
    },
    back() {
      this.$router.push(
        this.$route.query.path == "addReport"
          ? "entryReport"
          : this.$route.query.path
          ? this.$route.query.path
          : "/"
      );
    },
    //处理满意度code和name
    getSatisfactionName(v) {
      this.satisfactionOptions.map(item => {
        if (this.reportForm.satisfactionCode == item.label) {
          this.$set(this.reportForm, "satisfactionName", item.value);
        }
      });
    },
    disSubmit(status) {
      this.disClick = status;
    },
    changeFamly(v) {
      console.log(v);
      this.formCustFamilies = v;
    },
    //保存
    save() {
      this.fullscreenLoading = true;
      this.disClick = true;
      let type = "update";
      if (
        !this.reportForm ||
        !this.reportForm.id ||
        this.reportForm.processStateCode == "draft"
      ) {
        type = "draft";
      }
      fiSave({
        csFormInst: this.reportForm,
        formCustFamilies: this.formCustFamilies,
        operateType: type
      }).then(res => {
        //this.fullscreenLoading = false;
        this.disClick = false;
        if (res.code === 200) {
          //附件关联工单
          let data = qs.stringify({
            id: res.data,
            ids: JSON.stringify(this.fileIdList)
          });
          cfRelationFormAndFiles(data).then(res => {
            console.log(res);
          });
          this.$confirm("保存成功", {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
          if (type == "draft") {
            this.refreshData(res.data);
          }
          this.fullscreenLoading = false;
          //   this.$router.push({
          //     path: "replacePage",
          //     query: {
          //       id: res.data,
          //       title: "报事录入",
          //       path: this.$route.query.path
          //         ? this.$route.query.path
          //         : "entryReport"
          //     }
          //   });
        } else {
          this.disClick = false;
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {
            this.fullscreenLoading = false;
          });
        }
      });
    },
    //初始化常用问题
    initTroubleSelectPage(type) {
      initTroubleSelect({}).then(res => {
        if (res.sucess == true) {
          this.allTrouble = res.data;
          this.allTrouble.forEach(e => {
            e.id = e.id + "";
          });
          if (type == "add") {
            let obj = {};
            for (let i in this.allTrouble) {
              if (
                this.allTrouble[i].questionDesc == this.normalProblemContent
              ) {
                obj = this.allTrouble[i];
                break;
              }
            }

            this.$set(this.reportForm, "commonProblemCode", obj.id);
            this.$set(this.reportForm, "customerDemand", obj.questionDesc);
            this.normalProblemContent = "";
            this.normalProblemTitle = "";
          }
        }
      });
    },
    //自定义弹框
    openDilog() {
      this.addProblemVisible = true;
    },
    sureAddProblem() {
      if (this.normalProblemContent == "" || this.normalProblemTitle == "") {
        this.$confirm("标题和内容均不能为空，请完善必填信息！", {
          confirmButtonText: "确定",
          center: true,
          showClose: false,
          showCancelButton: false,
          confirmButtonClass: "confirm-reset-style"
        }).then(() => {});
        return;
      }
      troubleSelectADD({
        questionDesc: this.normalProblemContent,
        questionTitle: this.normalProblemTitle
      }).then(res => {
        if (res.code === 200) {
          console.log(res.data);
          this.$confirm("添加成功", {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
          this.initTroubleSelectPage("add");
          this.addProblemVisible = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    searchDesc(val) {
      console.log("1122334", val);
      this.allTrouble.map(item => {
        if (item.id == this.reportForm.commonProblemCode)
          this.$set(this.reportForm, "customerDemand", item.questionDesc);
      });
    },
    handleSuccess(res, file, fileList) {
      console.log(res, file, fileList);
      if (res.code == 200) {
        this.fileIdList.push(res.data[0]);
        let obj = {};
        obj.fileName = file.name;
        obj.id = res.data[0];
        obj.clientPath =
          this.reportForm.processStateCode == "handle"
            ? this.reportForm.processStateCode
            : "content";
        this.fileList.push(obj);
      }
    },
    //下载文件
    downLoadFile(item) {
      console.log(item);
      let id = item.id;
      let serverpath = item.serverPath;
      if (item.serverPath) { 
        if(serverpath.indexOf('http')==0){
          window.open(serverpath);
        }else{
          window.location = "/api/csFile/fileDownload?id=" + id;
        }
      } else {
        window.location = "/api/csFile/fileDownload?id=" + id;
      }
      // cfFileDownload({id:id}).then(res=>{
      //     window.location.href = +'?id='+id;
      // })
    },
    // 删除附件
    delFile(file, index) {
      this.$confirm(`确定删除 ${file.fileName}？`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
        showClose: false
        // confirmButtonClass:'confirm-reset-style',
      })
        .then(() => {
          this.fileList.forEach((item, key) => {
            if (item.id == file.id) this.fileList.splice(key, 1);
          });
          this.fileIdList.splice(index, 1);
          deleteFile({ id: file.id });
        })
        .catch(() => {});
    },
    getFiSelectCsformInst(id) {
      //获取流程信息
      if (this.$route.query.id || id) {
        this.fullscreenLoading = true;
        fiSelectCsformInst({
          id: id ? id : this.$route.query.id,
          source: this.$route.path
        })
          .then(res => {
            this.reportForm = {};
            this.fullscreenLoading = false;
            this.reportForm = Object.assign(res.data.csFormInst, {});        
            // 微信报事初始化数据
            if(this.$route.query.reportChannelCode){
              this.reportForm.reportChannelCode = this.$route.query.reportChannelCode;
              this.reportForm.reportChannelName = '微信';
              this.reportForm.deptCode= this.reportForm.deptCode||"deptDC";
              this.reportForm.deptName= this.reportForm.deptName||"地产";
              this.reportForm.processCode= this.reportForm.processCode||"processBZ";
              this.reportForm.processName= this.reportForm.processName||"标准流程";
              this.reportForm.firstSortCode= this.reportForm.firstSortCode||"";
              this.setUserType();
            }
            this.recordList = res.data.processWorkitem;
            if (this.reportForm.processStateCode == "returnVisit")
              this.rules.satisfactionCode[0].required = true;
            if (
              this.reportForm.processStateCode == "handle" ||
              this.reportForm.processCode == "processKSCL"
            )
              this.rules.handleRecord[0].required = true;

            //if(res.data.formCustFamilies) this.formCustFamilies = res.data.formCustFamilies;
            this.getSelectNameByArea();
            //   this.getProcessNodeList(this.reportForm.id);
            this.getProcessNodeList();
            cfSelectFileByFormNo({ formNo: this.reportForm.id }).then(res => {
                console.log(this.reportForm)
              if (res.code === 200) {
                this.fileList = res.data;
                //   for(let i in res.data){
                //       this.fileList[i].name = res.data[i].fileName;
                //       this.fileList[i].id = res.data[i].id;
                //       this.fileList[i].url = '/api/csFile/fileDownload?id='+res.data[i].id;

                //   }
              }
            });
            if (
              this.$route.query.path &&
              this.$route.query.path.indexOf("ReportReturn") > 0
            ) {
              cfReturnVisitLock({ id: this.reportForm.id });
              let _this = this;
              this.rtTimer = setInterval(function() {
                cfReturnVisitLock({ id: _this.reportForm.id });
              }, 1000 * 60 * 2);
            }
          })
          .catch(error => {
            this.fullscreenLoading = false;
          });
      }
    },
    allDictList() {
      dict().then(res => {
        if (res.sucess && res.data) {
          this.dict = res.data;
        }
      });
    },
    changeSelect(k, v, d) {
      this.resetSelect(v);
      if (this.dict && Array.isArray(this.dict)) {
        let s = this.dict.filter(
          i => i.itemCode == this.reportForm[v] && i.dictCode == d
        );
        if (s && s.length == 1) this.$set(this.reportForm, k, s[0].itemValue);
      }
      if (this.reportForm.firstSortCode == "coBX") {
        this.rules.maintenancePeriodCode[0].required = true;
        this.rules.decorationStageCode[0].required = true;
        this.rules.revisionClassificationCode[0].required = true;
        this.rules.problemPositionCode[0].required = true;
      } else {
        this.rules.maintenancePeriodCode[0].required = false;
        this.rules.decorationStageCode[0].required = false;
        this.rules.revisionClassificationCode[0].required = false;
        this.rules.problemPositionCode[0].required = false;
      }
      if (this.reportForm.processCode == "processKSCL") {
        this.rules.handleRecord[0].required = true;
      } else {
        this.rules.handleRecord[0].required = false;
      }
    },
    firstFilter(i) {
      let flg = false;
      if (
        this.reportForm.deptCode == "deptDC" &&
        i.dictCode == "firstSortCode"
      ) {
        if (this.reportForm.processCode == "processBZ")
          flg =
            i.itemCode == "coZX" ||
            i.itemCode == "coTS" ||
            i.itemCode == "coBX";
        else flg = i.itemCode == "coZX" || i.itemCode == "coJYBY";
      } else {
        flg =
          this.reportForm.deptCode == "deptWY" &&
          i.dictCode == "wyFirstSortCode";
      }
      return flg;
    },
    resetSelect(v) {
      if (
        "thirdSortCode,secSortCode,firstSortCode,processCode,deptCode".indexOf(
          v
        ) != -1
      ) {
        this.$set(this.reportForm, "fourthSortCode", null);
        this.$set(this.reportForm, "fourthSortName", null);
        if ("secSortCode,firstSortCode,processCode,deptCode".indexOf(v) != -1) {
          this.$set(this.reportForm, "thirdSortCode", null);
          this.$set(this.reportForm, "thirdSortName", null);
          if ("firstSortCode,processCode,deptCode".indexOf(v) != -1) {
            this.$set(this.reportForm, "secSortCode", null);
            this.$set(this.reportForm, "secSortName", null);
            this.$set(this.reportForm, "problemLevelCode", null);
            this.$set(this.reportForm, "problemLevelName", null);
            this.$set(this.reportForm, "problemPositionCode", null);
            this.$set(this.reportForm, "problemPositionName", null);
            this.$set(this.reportForm, "revisionClassificationCode", null);
            this.$set(this.reportForm, "revisionClassificationName", null);
            this.$set(this.reportForm, "decorationStageCode", null);
            this.$set(this.reportForm, "decorationStageName", null);
            this.$set(this.reportForm, "interResDepartmentCode", null);
            this.$set(this.reportForm, "interResDepartmentName", null);
            this.$set(this.reportForm, "maintenancePeriodCode", null);
            this.$set(this.reportForm, "maintenancePeriodName", null);
            if ("processCode,deptCode".indexOf(v) != -1) {
              this.$set(this.reportForm, "firstSortCode", null);
              this.$set(this.reportForm, "firstSortName", null);
              //   this.$set(this.reportForm, "handleRecord", null);
              if ("deptCode".indexOf(v) != -1) {
                this.$set(this.reportForm, "processCode", "processBZ");
                this.$set(this.reportForm, "processName", "标准流程");
                this.$set(this.reportForm, "formNo", "");
              }
            }
          }
        }
      }
    },

    handleCommon(type) {
      this.disClick = true;
      let isCommit = this.$refs.hccomp.submitForm("reportForm");
      let isCommit1 = this.$refs.hccomp.submitForm("reportForm1");
      let isSubmit = this.submitForm("reportFormZX");
      let isSubmit1 = this.submitForm("reportFormKH");
      let isSubmit2 = this.submitForm("satisfaction");
      let isSubmit3 = this.submitForm("reportFormChuli");
      console.log("是否可提交：", isCommit, isSubmit, isSubmit2);
      if (
        isCommit &&
        isCommit1 &&
        isSubmit &&
        isSubmit1 &&
        isSubmit2 &&
        isSubmit3
      ) {
        if (type == "save") {
          this.save();
        } else if (type == "submit") {
          this.subSubmit();
        }
      } else {
        this.disClick = false;
        setTimeout(() => {
          let errorEloffsetTop =
            $(".el-form-item.is-error")
              .eq(0)
              .offset().top -
            $("#if-rame").height() -
            $("#homepage-header").height();
          if (errorEloffsetTop < 101) {
            $("#mainScrollContainer").scrollTop(-(101 - errorEloffsetTop));
          } else {
            $("#mainScrollContainer").scrollTop(errorEloffsetTop - 101);
          }
        }, 50);
      }
    },
    subSubmit() {
      this.disClick = true;
      this.$refs.sub.submit();
    },
    subHSubmit() {
      this.disClick = true;
      this.$refs.sub.handlerSubmit({});
    },
    subReject() {
      this.disClick = true;
      this.$refs.sub.reject();
    },
    subSetLevelUp() {
      if (this.reportForm.upgradeLevel) {
        let level = "城市";
        if (this.reportForm.upgradeLevel == "2") {
          level = "区域";
        } else if (this.reportForm.upgradeLevel == "3") {
          level = "集团";
        }
        this.$confirm(`该工单已经升级至${level}负责人！`, {
          confirmButtonText: "确定",
          center: true,
          showClose: false,
          showCancelButton: false
          //confirmButtonClass:'confirm-reset-style',
        }).then(() => {});
      } else {
        this.disClick = true;
        this.$refs.sub.setLevelUp();
      }
    },
    subSpecialEnd() {
      this.disClick = true;
      this.$refs.sub.specialDialog();
    },
    refreshData(v) {
      this.getFiSelectCsformInst(v);
    },
    getSelectNameByArea() {
      //获取主责任单位，维修单位，第三方单位
      siSelectNameByArea({ supplierAreas: this.reportForm.regionCode }).then(
        res => {
          console.log(res);
          if (res.code === 200) {
            this.accountabilityUnit = res.data;
          }
        }
      );
    },
    getProcessNodeList() {
      if (this.recordList.length == 0) {
        if (this.reportForm.processCode == "processKSCL") {
          this.processNodeList = [
            {
              processStateName: "报事录入",
              taskStatus:
                this.reportForm.processStateCode.indexOf("End") > 0 ? 30 : 20
            },
            {
              processStateName: "报事关闭",
              taskStatus:
                this.reportForm.processStateCode.indexOf("End") > 0 ? 30 : 10
            }
          ];
        } else {
          this.processNodeList = [
            { processStateName: "报事录入", taskStatus: 20 },
            { processStateName: "报事分派", taskStatus: 10 },
            { processStateName: "报事处理", taskStatus: 10 },
            { processStateName: "报事回访", taskStatus: 10 },
            { processStateName: "报事关闭", taskStatus: 10 }
          ];
        }
      } else {
        this.processNodeList = [];
        let processNode = [];
        let that = this;
        if (
          this.reportForm.processCode == "processBZ" &&
          this.reportForm.firstSortCode == "coZX"
        ) {
          //标准流程咨询
          processNode = ["报事录入", "报事处理", "报事关闭"];
        } else if (this.reportForm.processCode == "processKSCL") {
          //快速流程咨询
          processNode = ["报事录入", "报事关闭"];
        } else {
          processNode = [
            "报事录入",
            "报事分派",
            "报事处理",
            "报事回访",
            "报事关闭"
          ];
        }
        let _item = this.recordList[0];
        _item.processStateName =
          _item.processStateName == "特殊关闭" ||
          _item.processStateName == "正常关闭"
            ? "报事关闭"
            : _item.processStateName;
        let flg = false;
        processNode.reverse().map(v => {
          if (v == _item.processStateName) {
            flg = true;
            this.processNodeList.splice(0, 0, {
              processStateName: v,
              taskStatus: _item.taskStatus
            });
          } else {
            if (flg) {
              this.processNodeList.splice(0, 0, {
                processStateName: v,
                taskStatus: 30
              });
            } else if (_item.processStateName == "报事升级") {
              if (v == "报事处理") {
                this.processNodeList.splice(0, 0, {
                  processStateName: v,
                  taskStatus: 20
                });
              } else if (v == "报事录入" || v == "报事分派") {
                this.processNodeList.splice(0, 0, {
                  processStateName: v,
                  taskStatus: 30
                });
              } else if (v == "报事回访" || v == "报事关闭") {
                this.processNodeList.splice(0, 0, {
                  processStateName: v,
                  taskStatus: 10
                });
              }
            } else {
              this.processNodeList.splice(0, 0, {
                processStateName: v,
                taskStatus: 10
              });
            }
          }
        });
      }
    },
    // getProcessNodeList(val){
    //     let params = {
    //         formInstId:val,
    //     }
    //     getProcessNodeByFormId(params).then((res) => {
    //         if(res.code == 200){
    //             if(res.data.length == 0){
    //                 if(this.reportForm.processCode == 'processKSCL'){
    //                     this.processNodeList = [
    //                         {processStateName:'报事录入',taskStatus:this.reportForm.processStateCode.indexOf('End')>0?30:20},
    //                         {processStateName:'报事关闭',taskStatus:this.reportForm.processStateCode.indexOf('End')>0?30:10},
    //                     ]
    //                 } else {
    //                     this.processNodeList = [
    //                         {processStateName:'报事录入',taskStatus:20},
    //                         {processStateName:'报事分派',taskStatus:10},
    //                         {processStateName:'报事处理',taskStatus:10},
    //                         {processStateName:'报事回访',taskStatus:10},
    //                         {processStateName:'报事关闭',taskStatus:10},
    //                     ]
    //                 }
    //             }else{
    //                 this.processNodeList = [];
    //                 let processNode = [];
    //                 let that = this;
    //                 if(this.reportForm.processCode == 'processBZ' && this.reportForm.firstSortCode == 'coZX'){//标准流程咨询
    //                     processNode = ['报事录入','报事处理','报事关闭']
    //                 }else if(this.reportForm.processCode == 'processKSCL'){//快速流程咨询
    //                     processNode = ['报事录入','报事关闭']
    //                 }else{
    //                     processNode = ['报事录入','报事分派','报事处理','报事回访','报事关闭']
    //                 }
    //                 if(res.data && res.data.length >0){
    //                     let i = 1;
    //                     let _item = res.data[res.data.length - i];
    //                     _item.processStateName = _item.processStateName == '特殊关闭'?'报事关闭':_item.processStateName;
    //                     let flg = false;
    //                     processNode.reverse().map(v => {
    //                         if(v == _item.processStateName){
    //                             i++;
    //                             flg = true;
    //                             this.processNodeList.splice(0, 0, {processStateName:v,taskStatus:_item.taskStatus});
    //                         } else{
    //                             if(flg)
    //                                 this.processNodeList.splice(0, 0, {processStateName:v,taskStatus:30});
    //                             else
    //                                 this.processNodeList.splice(0, 0, {processStateName:v,taskStatus:10});
    //                         }
    //                     })
    //                 }
    //             }
    //         }else{
    //             this.$message({
    //                 type:'error',
    //                 message:res.message
    //             })
    //         }
    //     })
    // },
    setUserType() {
      if (!this.reportForm.acceptChannelCode) {
        this.reportForm.acceptChannelCode = this.userInfo.seats
          ? "400"
          : "项目";
        this.reportForm.acceptChannelName = this.userInfo.seats
          ? "400"
          : "项目";
      }
    },
    openEditDilog() {
      let list = JSON.parse(JSON.stringify(this.allTrouble));
      this.problemEditData = list;
      console.log(list);
      this.editProblemVisible = true;
    },
    sureSaveProblemList() {
      let changeArr = new Array();
      let isSaveSuccess = true;
      this.allTrouble.map((aitem, aindex) => {
        this.problemEditData.map((pitem, pindex) => {
          if (
            aitem.id == pitem.id &&
            (aitem.questionDesc != pitem.questionDesc ||
              aitem.questionTitle != pitem.questionTitle)
          ) {
            changeArr.push(pitem);
          }
        });
      });
      if (changeArr.length == 0) {
        this.editProblemVisible = false;
        return;
      }
      let that = this;
      let isBlank = false;
      let syncFn = new Promise((resolve, reject) => {
        changeArr.map((item, index) => {
          if (item.questionDesc == "" || item.questionTitle == "") {
            that
              .$confirm("标题和内容均不能为空，请完善必填信息！", {
                confirmButtomText: "确定",
                center: true,
                showClose: false,
                showCancelButton: false,
                confirmButtomClass: "confirm-reset-style"
              })
              .then(() => {});
          } else {
            setTimeout(() => {
              troubleSelectADD({
                id: item.id,
                questionDesc: item.questionDesc,
                questionTitle: item.questionTitle
              }).then(res => {
                if (res.code != 200) {
                  isSaveSuccess = false;
                  resolve();
                } else {
                  if (index == changeArr.length - 1) {
                    resolve();
                  }
                }
              });
            }, 30 * index);
          }
        });
      });
      syncFn.then(() => {
        if (isSaveSuccess) {
          that
            .$confirm("保存成功！", {
              confirmButtomText: "确定",
              center: true,
              showClose: false,
              showCancelButton: false,
              confirmButtomClass: "confirm-reset-style"
            })
            .then(() => {
              that.initTroubleSelectPage("init");
              that.editProblemVisible = false;
            });
        } else {
          that
            .$confirm("保存失败！", {
              confirmButtomText: "确定",
              center: true,
              showClose: false,
              showCancelButton: false,
              confirmButtomClass: "confirm-reset-style"
            })
            .then(() => {});
        }
      });
    },
    deleteProblem(row) {
      console.log(row);
      this.$confirm("确认并删除该条常用问题？", {
        confirmButtonText: "确定",
        center: true,
        showClose: false,
        showCancelButton: true,
        confirmButtonClass: "confirm-reset-style"
      })
        .then(() => {
          let params = {
            id: row.id
          };
          deleteTrouble(params).then(res => {
            if (res.code == 200) {
              this.$confirm("删除常用问题成功！", {
                confirmButtomText: "确定",
                center: true,
                showClose: false,
                showCancelButton: false,
                confirmButtomClass: "confirm-reset-style"
              }).then(() => {
                this.initTroubleSelectPage("init");
              });
              this.problemEditData.map((item, index) => {
                if (item.id == row.id) {
                  this.problemEditData.splice(index, 1);
                }
              });
            } else {
              this.$confirm(res.message, {
                confirmButtomText: "确定",
                center: true,
                showClose: false,
                showCancelButton: false,
                confirmButtomClass: "confirm-reset-style"
              }).then(() => {
                //this.initTroubleSelectPage("init");
              });
            }
          });
        })
        .catch(() => {});
    },
    //取消报事
    handleCancel() {
      let type = "update";
      if (
        !this.reportForm ||
        !this.reportForm.id ||
        this.reportForm.processStateCode == "draft"
      ) {
        type = "draft";
      }
      fiSave({
        csFormInst: this.reportForm,
        formCustFamilies: this.formCustFamilies,
        operateType: type
      }).then(res => {
        console.log(res);
        this.fullscreenLoading = false;
        this.disClick = false;
        if (res.code === 200) {
          this.dialogVisible = true;
          //附件关联工单
          let data = qs.stringify({
            id: res.data,
            ids: JSON.stringify(this.fileIdList)
          });
          cfRelationFormAndFiles(data).then(res => {
            console.log(res);
          });
        } else {
          this.disClick = false;
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {
            this.fullscreenLoading = false;
          });
        }
      });
    },
    //确认按钮
    affirm(data) {
      if (this.context === "") {
        this.$message("不能为空");
      } else {
        this.dialogVisible = false;
        this.$confirm("提交成功", {
          confirmButtonText: "确定",
          center: true,
          showClose: false,
          showCancelButton: false,
          confirmButtonClass: "confirm-reset-style"
        }).then(() => {
          cancel({
            formInstId: this.$route.query.id,
            cancelReason: this.context
          }).then(res => {
            this.$router.push({ path: "wechatReport" });
          });
        });
      }
    }
  },
  mounted() {
    this.allDictList();
    this.initTroubleSelectPage("init");
    this.getFiSelectCsformInst();
    this.setUserType();

    let headerHeight = document.getElementById("homepage-header").offsetHeight;
    let iframeHeight =
      document.getElementById("if-rame") && this.userInfo.seats
        ? document.getElementById("if-rame").offsetHeight
        : 0;
    this.fixTop = Number(headerHeight) + Number(iframeHeight) + 8 + "px";
    $(".process-node")
      .eq(0)
      .css({
        top: Number(headerHeight) + Number(iframeHeight) + 42,
        width: $(".hc-comp")
          .eq(0)
          .width()
      });
    window.onresize = () => {
      this.fixTop = Number(headerHeight) + Number(iframeHeight) + 8 + "px";
    };
    let scrollContainer = document.getElementById("mainScrollContainer");
    scrollContainer.addEventListener("scroll", () => {
      $(".process-node")
        .eq(0)
        .css({
          top: Number(headerHeight) + Number(iframeHeight) + 42,
          width: $(".hc-comp")
            .eq(0)
            .width()
        });
      //$(".process-node").eq(0).css({top:$('#mainScrollContainer').scrollTop()-49})
    });
    if (this.$route.query.deptCode) {
      this.reportForm.deptCode = this.$route.query.deptCode;
      this.changeSelect("deptName", "deptCode", "deptCode");
    }
  },
  beforeDestroy() {
    if (this.rtTimer) {
      //如果定时器还在运行 或者直接关闭，不用判断
      clearInterval(this.rtTimer); //关闭
    }
  }
};
</script>

<style scoped>
.add-report {
  width: calc(100% - 10px);
  position: relative;
}
.add-report .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
}
.add-report .tableHover > tbody > tr:hover {
  background-color: #d5dbe4;
}
.add-report >>> .el-date-editor {
  width: 100%;
}
#app .el-button {
  margin-left: 0 !important;
}
.add-report >>> .el-input__inner {
  height: 24px;
}
.mf_big {
  margin-left: 108px;
}
#flex-top {
  position: fixed;
  right: 10px;
  z-index: 1000;
}
#flex-top .btnDown {
  margin: 0 5px;
  margin-top: 2px;
}
.process-node {
  position: fixed;
  width: 100%;
  height: 40px;
  background-color: #fff;
  top: -49px;
  z-index: 999;
  border-top: solid 1px rgba(0, 0, 0, 0.1);
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
}
.process-node ul {
  display: block;
  height: 100%;
  line-height: 40px;
  padding-left: 10px;
}
.process-node ul li {
  display: block;
  float: left;
}
.process-node ul li span {
  display: inline-block;
  height: 26px;
  line-height: 26px;
  padding: 0 30px;
  border-radius: 3px;
}
.yellow-bac {
  background-color: #f5a623;
  color: #fff;
}
.red-bac {
  background-color: #e63f3c;
  color: #fff;
}
.grey-bac {
  background-color: #f0f0f0;
  color: #9b9b9b;
}
.add-report img {
  width: 32px;
  height: 16px;
  vertical-align: middle;
}
</style>
