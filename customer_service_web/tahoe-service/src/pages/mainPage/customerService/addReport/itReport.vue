<template>
  <div class="add-report it-report" id="addReportContainer" style="margin-top:40px;">
    <div class="add-report" v-loading.fullscreen.lock="fullscreenLoading">
      <div class="fr" id="flex-top" :style="'top:'+fixTop" style="margin-right:15px;">
        <span v-if="reportForm.upgradeFlag=='1'" class="mr-10"><img src="../../../../assets/images/level_up2x.png" alt=""></span>
        <button class="btnDown" @click="handleCommon('submit')" :disabled="disClick" v-if="reportForm.processStateCode!='nomalEnd' && reportForm.processStateCode!='specialEnd' && reportForm.processStateCode !='toBeAssigned' && !isDisabled('visible')">提交</button>
        <button class="btnDown" @click="handleCommon('submit')" :disabled="disClick" v-if="reportForm.processStateCode=='toBeAssigned' && !isDisabled('visible')">分派</button>
        <button class="btnDown" v-if="reportForm && reportForm.processStateCode=='handle' && !isDisabled('visible')" @click="subReject" :disabled="disClick">退回</button>
        <!-- <button class="btnDown" v-if="reportForm && reportForm.processStateCode=='handle' &&(reportForm.firstSortCode=='coTS'||reportForm.firstSortCode=='coBX') && !isDisabled('visible')" @click="subSetLevelUp" :disabled="disClick">升级</button> -->
        <button class="btnDown" v-if="isSaveShow() && !isDisabled('visible')" @click="handleCommon('save')" :disabled="disClick">{{reportForm.processStateCode=='handle'?'跟进':'暂存'}}</button>
        <button class="btnDown" v-if="reportForm && reportForm.processStateCode=='toBeAssigned' && !isDisabled('visible')" :disabled="disClick" @click="subSpecialEnd">特殊关闭</button>
        <button class="btnDown" v-if="reportForm && reportForm.processStateCode=='handle' && !isDisabled('visible')" @click="gotoPrint">打印</button>
        <button class="btnDown" @click="back">返回</button>
        <sumbit-info ref="sub" v-model="fullscreenLoading" :reportForm="reportForm" :fileIdList="fileIdList" @disSubmit="disSubmit" @refreshData="refreshData"></sumbit-info>
      </div>
      <div class="process-node">
        <ul>
          <li v-for="(item,index) in processNodeList.filter(v => v.processStateName != '报事升级')" :key="index">
            <span :class="item.taskStatus==30?'yellow-bac':item.taskStatus==20?'red-bac':'grey-bac'">{{item.processStateName}}</span>&nbsp;&nbsp;
            <i v-show="index != processNodeList.filter(v => v.processStateName != '报事升级').length-1" :style="item.taskStatus==30?'color:#f5a623;':item.taskStatus==20?'color:#e63f3c':'color:#9b9b9b'" class="el-icon-arrow-right">&nbsp;&nbsp;</i>
          </li>
        </ul>
      </div>

      <div class="bg-style mt-10">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">客户信息</span>
        </div>
        <div>
          <el-form ref="cusForm" :inline-message="true" :show-message="false" :model="reportForm" :rules="rules" label-position="right" label-width="100px" class="demo-ruleForm">
            <el-row>
              <el-col :span="5" :offset="0">
                <el-form-item label="客户姓名" prop="ownerName">
                  <el-input maxlength="10" v-model.trim="reportForm.ownerName" :disabled="isDisabled(3)"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="性别" prop="genderName">
                  <el-select v-model="reportForm.genderName" filterable :disabled="isDisabled(3)" placeholder="请选择">
                    <el-option label="男" value="男">
                    </el-option>
                    <el-option label="女" value="女">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="移动电话" prop="mobile">
                  <el-input v-model="reportForm.mobile" :disabled="isDisabled(3)" @keyup.native="handlerTelKeyDown"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="电子邮箱" prop="eMail">
                  <el-input v-model="reportForm.eMail" :disabled="isDisabled(3)"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="17" :offset="0">
                <el-form-item label="组织机构">
                  <el-input maxlength="10" v-model="reportForm.workUnit" :disabled="isDisabled(3)"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="职位">
                  <el-input v-model="reportForm.occupation" :disabled="isDisabled(3)"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <div class="bg-style mt-10">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">{{reportForm.firstSortName}}报事信息</span>
        </div>
        <div>
          <el-form :inline-message="true" :show-message="false" ref="reportInfoForm" :model="reportForm" :rules="rules" label-position="right" label-width="100px" class="demo-ruleForm">
            <el-row>
              <el-col :span="5" :offset="0">
                <el-form-item label="工单编号" prop="formNo">
                  <el-input disabled v-model="reportForm.formNo"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="部门">
                  <el-select v-model="reportForm.deptCode" placeholder="请选择" :disabled="this.userInfo.flag == 3 || isDisabled(3)">
                      <el-option v-for="i in dict.filter(i=>i.dictCode =='deptCode').filter(i =>this.userInfo.flag != 3 || i.itemCode == 'deptIT')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                      </el-option>
                      <!-- <el-option value="deptDC" label='地产'></el-option>
                      <el-option value="deptWY" label='物业'></el-option> -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="项目" prop="projectCode">
                  <el-select v-model="reportForm.projectCode" placeholder="请选择" @change="changeSelect('project','projectCode','deptIT')" :disabled="isDisabled(3)">
                    <el-option v-for="i in dict.filter(i=>i.dictCode =='deptIT')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="报事渠道" prop="reportChannelCode">
                  <el-select v-model="reportForm.reportChannelCode" placeholder="请选择" @change="changeSelect('reportChannelName','reportChannelCode','ITreportChannel')" :disabled="isDisabled(3)">
                    <el-option v-for="i in dict.filter(i=>i.dictCode =='ITreportChannel')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="流程类别" prop="processCode">
                  <el-select v-model="reportForm.processCode" placeholder="请选择" @change="changeSelect('processName','processCode','processCode')" :disabled="isDisabled(3)">
                    <el-option v-for="i in dict.filter(i=>i.dictCode =='processCode')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="一级分类" prop="firstSortCode">
                  <el-select v-model="reportForm.firstSortCode" placeholder="请选择" @change="changeSelect('firstSortName','firstSortCode',reportForm.processCode == 'processBZ'?'itFscBz':'itFscKszx')" :disabled="isDisabled(3)">
                    <el-option v-for="i in dict.filter(i=>this.firstFilter(i))" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-form-item label="二级分类" prop="secSortCode">
                  <el-select v-model="reportForm.secSortCode" placeholder="请选择" @change="changeSelect('secSortName','secSortCode','itSec')" :disabled="isDisabled(3)">
                    <el-option v-for="i in dict.filter(i=>i.dictCode == 'itSec')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <!-- 报事内容信息 -->
      <div class="bg-style mt-10">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">{{reportForm.firstSortName}}报事内容信息</span>
        </div>
        <div>
          <el-form :inline-message="true" :show-message="false" ref="reportContentForm" :rules="rules" :model="reportForm" label-position="right" label-width="100px" style="padding-bottom:20px;">
            <el-row>
              <el-col :span="24" :offset='0' v-if="reportForm.processStateCode =='draft'||!reportForm.processStateCode">
                <el-form-item label="常用问题">
                  <el-select style="width:200px;" v-model="reportForm.commonProblemCode" filterable placeholder="请选择" :disabled="isDisabled('visible')" @change="searchDesc">
                    <el-option v-for="item in allTrouble" :key="item.id" :label="item.questionTitle" :value="item.id">
                    </el-option>
                  </el-select>
                  <button class="btnDown ml-10" @click.prevent="openDilog" v-if="!isDisabled('visible')">新增</button>
                  <button class="btnDown ml-10" @click.prevent="openEditDilog" v-if="!isDisabled('visible')">编辑</button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='11' :offset="0" class="mt-10">
                <el-form-item label="客户诉求" prop="customerDemand">
                  <el-input class="break-word" type="textarea" :disabled="isDisabled(3)" v-model.trim="reportForm.customerDemand" cols="104" rows="5" maxlength="500" @keyup="checkTextLength(reportForm.customerDemand,500)"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='11' :offset="0" v-if="reportForm.firstSortCode == 'coBX' || reportForm.firstSortCode == 'coTS' || reportForm.processCode == 'processBZ'">
                <el-form-item label="附件">
                  <el-upload v-if="(!reportForm.processStateCode || reportForm.processStateCode =='draft') && !isDisabled(3)" action="/api/csFile/fileUpdate" multiple :on-success="handleSuccess" :show-file-list="false" :data="uploadParams" name="localfile">
                    <el-button class="btnDown" v-if="!isDisabled('visible')" v-show="!reportForm.processStateCode || reportForm.processStateCode =='draft'">点击上传</el-button>
                  </el-upload>
                  <!-- <ul>
                                    <li v-for="item in this.fileList" :key="item.id" style="cursor:pointer;color:blue;" @click.prevent="downLoadFile(item.id)">{{item.name}}</li>
                                </ul> -->
                  <table class="table" style="margin-top:5px;">
                    <tbody>
                      <tr v-for="(item,index) in fileList.filter(i=>i.clientPath =='content')" :key='index'>
                        <td width="100" style="color:#1D85FE;text-align:left;">
                          <span @click.prevent="downLoadFile(item.id)">{{item.fileName}}</span>
                          <span class="ta-rt ml-30" v-if="(!reportForm.processStateCode || reportForm.processStateCode =='draft')  && !isDisabled(3)" @click.prevent="delFile(item,index)">删除</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <!-- 报事处理信息 -->
      <div class="bg-style mt-10" v-if="reportForm.processCode=='processKSCL'||reportForm.processStateCode=='handle'||reportForm.processStateCode=='nomalEnd'">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">{{reportForm.firstSortName}}报事处理信息</span>
        </div>
        <div>
          <el-form :inline-message="true" :show-message="false" ref="reportFormChuli" :rules="rules" :model="reportForm" label-position="right" label-width="100px">
            <el-row>
              <el-col :span='11' :offset="0" class="mt-10">
                <el-form-item label="处理记录" prop="handleRecord">
                  <el-input class="break-word" type="textarea" cols="30" rows="5" maxlength="500" v-model.trim="reportForm.handleRecord" :disabled="isDisabled(5)" @keyup="checkTextLength(reportForm.handleRecord,500)"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="11" :offset="0">
                <el-form-item label="附件">
                  <el-upload v-show="!isDisabled('visible') && !isDisabled(5)" class="upload-demo" action="/api/csFile/fileUpdate" multiple :on-success="handleSuccess" :data="uploadParamsHandle" :show-file-list="false" name="localfile">
                    <el-button class="btnDown" v-if="!isDisabled('visible')">点击上传</el-button>
                  </el-upload>
                  <!-- <el-button class="btnDown" >上传附件</el-button> -->
                  <table class="table" style="margin-top:5px;">
                    <tbody>
                      <tr v-for="(item,index) in fileList.filter(i=>i.clientPath =='handle')" :key='index'>
                        <td width="100" style="color:#1D85FE;text-align:left;">
                          <span @click.prevent="downLoadFile(item.id)">{{item.fileName}}</span>
                          <span class="ta-rt ml-30" v-if="(reportForm.processCode == 'processKSCL' || reportForm.processStateCode =='handle') && !isDisabled('visible') && !isDisabled(5)" @click.prevent="delFile(item,index)">删除</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <!-- 跟进记录 -->
      <div class="bg-style mt-10" v-if="reportForm.id && this.reportForm.processCode == 'processBZ'">
        <div style="height:44px;line-height:44px;border-bottom:1px solid #e6e6e6;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">跟进记录</span>
        </div>
        <div>
          <table class="table mt-10" id="tableHover">
            <thead class="odd">
              <tr>
                <th width="100">节点</th>
                <th width="200">操作记录</th>
                <th width="100">操作人</th>
                <th width="100">开始时间</th>
                <th width="100">结束时间</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="recordList.length==0">
                <td colspan="4">暂无数据</td>
              </tr>
              <tr v-else v-for="(item,key) in recordList" :key='key' :class="key%2==0?'even':'odd'">
                <td width="100">{{item.processStateName}}</td>
                <td width="200" v-html="item.comment?item.comment:item.taskEndTime?'已结束':'正在处理'"></td>
                <td width="100">{{item.assignName}}</td>
                <td width="100">{{item.taskStartTime | TimeMoment}}</td>
                <td width="100">{{item.taskEndTime | TimeMoment}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <el-dialog title="添加常用问题" :visible.sync="addProblemVisible" width="50%" :append-to-body='appendTrue'>
        <el-form>
          <el-form-item label="标题">
            <el-input v-model="normalProblemTitle" :maxlength="50" :disabled="isDisabled('visible')"></el-input>
          </el-form-item>
          <!-- <el-form-item label="问题描述">
            <el-input type="textarea" rows="5" v-model="normalProblemContent" @keyup.native="checkTextLength(normalProblemContent,10)" :disabled="isDisabled('visible')"></el-input>
          </el-form-item> -->
          <el-form-item>
            <div>问题描述</div>
            <textarea :class="isblank?'my-textarea red-border':'my-textarea'" rows="5" v-model.trim="normalProblemContent" :disabled="isDisabled('visible')"></textarea>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!isDisabled('visible')" class="btnDown" @click="sureAddProblem">确 定</el-button>
          <el-button v-if="!isDisabled('visible')" class="btnDown" @click="addProblemVisible = false">取 消</el-button>
        </span>
      </el-dialog>

      <el-dialog title="常用问题列表" :visible.sync="editProblemVisible" width="50%" :append-to-body='appendTrue'>
        <el-table class="bordernone-input-table" border :data="problemEditData" height="350" style="width: 100%">
          <el-table-column prop="questionTitle" label="标题">
            <template slot-scope="scope">
              <el-input class="break-word" type="textarea" size="mini" resize="none" :maxlength="50" :rows="3" v-model="scope.row.questionTitle"/>
            </template>
          </el-table-column>
          <el-table-column prop="questionDesc" label="内容">
            <template slot-scope="scope">
              <el-input class="break-word" type="textarea" size="mini" :maxlength="500" resize="none" :rows="3" v-model="scope.row.questionDesc"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <button class="btnDown" @click="deleteProblem(scope.row)">删除</button>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!isDisabled('visible')" class="btnDown" @click="sureSaveProblemList">保 存</el-button>
          <el-button v-if="!isDisabled('visible')" class="btnDown" @click="editProblemVisible = false">取 消</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- <el-dialog title="报事处理记录单套打" class="print-dialog" :visible.sync="showPrint" width="862" :append-to-body="appendTrue" @close="closePrint"> -->
    <v-print v-show="showPrint" ref="printComponent" :printValue="printData" />
    <!-- </el-dialog> -->
  </div>
</template>
<script>
import {
  fiSave,
  initTroubleSelect,
  troubleSelectADD,
  getProcessNodeByFormId,
  deleteTrouble
} from "@/api/port";
import {
  fiSelectCsformInst,
  dict,
  cfRelationFormAndFiles,
  cfSelectFileByFormNo,
  cfFileDownload,
  cfReturnVisitLock,
  getFormNo,
  cuSelectUserTel,
  deleteFile
} from "@/api/wsd";
import qs from "qs";
import sumbitInfo from "@/pages/mainPage/common/submit_info";
import Print from "@/pages/mainPage/common/print";
import { isvalidPhone,isAllChinese,isEmail,stripscript } from "@/api/form";

export default {
  components: {
    sumbitInfo,
    "v-print": Print
  },
  watch: {
    "reportForm.deptCode"(v) {
      if(v != 'deptIT'){
        let query = this.$route.query;
        query.deptCode = v;
        this.$router.push({
          path:'addReport',
          query:query
        });
      }
    },
    normalProblemContent(val){
      //this.normalProblemContent = stripscript(val);
      if(val.length != 0){
        this.isblank = false;
      }else{
        this.isblank = true;
      }
      if(this.normalProblemContent.length > 500){
        this.normalProblemContent = this.normalProblemContent.substring(0,500);
      }
    },
    "reportForm.commonProblemCode"(val){
        let isHasCode = false;
        this.allTrouble.map((item) => {
            if(item.id == val){
                isHasCode = true;
            }
        })
        if(!isHasCode){
            this.$set(this.reportForm,'commonProblemCode',null);
        }   
    },
    allTrouble(val){
        let isHasCode = false;
        val.map((item) => {
            if(item.id == this.reportForm.commonProblemCode){
                isHasCode = true;
            }
        })
        if(!isHasCode){
            this.$set(this.reportForm,'commonProblemCode',null);
        }
    },
  },
  data() {
    let that = this;
    var validPhone = (rule, value, callback) => {
      if (!that.reportForm.mobile) {
        callback(new Error("请输入电话号码"));
      } else if (!isvalidPhone(that.reportForm.mobile)) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    var isChnName = (rule, value,callback)=>{
        if (!that.reportForm.ownerName){
            callback(new Error('姓名'))
        }else  if (!isAllChinese(that.reportForm.ownerName)){
            callback(new Error('姓名中只能有汉字'))
        }else {
            callback()
        }
    }
    var isEmailUrl = (rule, value,callback)=>{
        if(that.reportForm.eMail){
          if (!isEmail (that.reportForm.eMail)){
            callback(new Error('邮箱格式有误'))
          }else {
            callback()
          }
        }else{
          callback()
        }
    }
    return {
      isblank:false,
      userInfo: JSON.parse(sessionStorage.getItem("userInfo")),
      addShow: true,
      showPrint: false,
      printData: {}, //打印数据
      ZXShow: false, //咨询显示
      disClick: false,
      appendTrue: true,
      reportForm: {
        deptCode: "deptIT",
        deptName: "IT",
        processCode: "processKSCL",
        processName: "标准流程",
        firstSortCode: "",
        secSortCode:"itSec_05",
        secSortName:"一般问题"
      }, //所有数据
      //附件
      fileList: [],
      fileIdList: [],
      allTrouble: [],
      recordList: [],
      processNodeList: [
        { processStateName: "报事录入", taskStatus: 20 },
        { processStateName: "报事分派", taskStatus: 10 },
        { processStateName: "报事处理", taskStatus: 10 },
        { processStateName: "报事关闭", taskStatus: 10 }
      ],
      SJShow: false,
      rejectFlg: false,
      dialogVisible: false,
      specialClose: false, //特殊关闭
      alertFlg: false, // 输出意见弹出
      closeFlg: false, // 正常关闭
      levelUpFlg: false, //升级
      addProblemVisible: false,
      fullscreenLoading: false,
      normalProblemContent: "", //自定义问题标题、内容
      normalProblemTitle: "",
      uploadParams: {
        clientPath: "content"
      },
      uploadParamsHandle: {
        clientPath: "handle"
      },
      fixTop: 0,
      accountabilityUnit: [],
      rules: {
        ownerName: [
          { required: true, trigger: "blur", message: "请输入客户姓名"}
        ],
        genderName: [
          {required: true, message: "请选择客户性别", trigger: "blur" }
        ],
        eMail: [
          {trigger: "blur", validator:isEmailUrl}
        ],
        mobile: [{ required: true, trigger: "blur", validator: validPhone }],
        projectCode: [
          { required: true, message: "请选择项目", trigger: "change" }
        ],
        processCode: [
          { required: true, message: "请选择流程类别", trigger: "change" }
        ],
        firstSortCode: [
          { required: true, message: "请选择一级分类", trigger: "change" }
        ],
        secSortCode: [
          { required: true, message: "请选择二级分类", trigger: "change" }
        ],
        reportChannelCode: [
          { required: true, message: "请选择报事渠道", trigger: "change" }
        ],
        customerDemand: [
          { required: true, message: "请填写客户诉求", trigger: "blur" }
        ],
        handleRecord: [
          { required: true, message: "请填写处理记录", trigger: "change" }
        ]
      },
      dict: [],
      rtTimer: null, // 回访定时
      problemEditData:[],
      editProblemVisible:false,

    };
  },
  methods: {
    handlerTelKeyDown(e) {
      let keynum;
      if (window.event)
        // IE
        keynum = e.keyCode;
      else if (e.which)
        // Netscape/Firefox/Opera
        keynum = e.which;

      let mobile = this.reportForm.mobile.substring(
        0,
        this.reportForm.mobile.length - 1
      );

      if ((keynum == 49 || keynum == 97) && isvalidPhone(mobile)) {
        this.$set(this.reportForm, "mobile", mobile + ",1");
      }
    },
    // 判断是否显示跟进
    isSaveShow() {
      if (
        !this.reportForm.id ||
        this.reportForm.processStateCode == "draft" ||
        (this.reportForm.processStateCode == "handle" &&
          this.reportForm.firstSortCode != "coZX")
      ) {
        return true;
      } else {
        return false;
      }
    },
    gotoPrint() {
      //套打
      this.printData = this.reportForm;
      console.log(this.$refs);
      setTimeout(() => {
        this.$refs.printComponent.print();
      }, 50);
      //this.showPrint = true;
    },
    closePrint() {
      this.printData = {};
    },
    isDisabled(type) {
      if (this.$route.query.disabled) {
        return true;
      }
      if (this.userInfo.username != this.reportForm.curAssigneeId && this.reportForm.processStateCode) {
        //当前登录用户为非操作用户
        return true;
      } else if (type == 1) {
        //toBeAssigned可编辑
        if (
          this.reportForm.processStateCode == "toBeAssigned" ||
          this.reportForm.processStateCode == undefined ||
          this.reportForm.processStateCode == "draft"
        ) {
          return false;
        } else {
          return true;
        }
      } else if (type == 2) {
        //handle不可编辑
        if (
          this.reportForm.processStateCode == "handle"
        ) {
          return true;
        } else {
          return false;
        }
      } else if (type == 3) {
        //都不可编辑
        if (
          this.reportForm.processStateCode == "handle" ||
          this.reportForm.processStateCode == "toBeAssigned" ||
          this.reportForm.processStateCode == "specialEnd" ||
          this.reportForm.processStateCode == "nomalEnd"
        ) {
          return true;
        }
      } else if (type == 4) {
        if (
          this.reportForm.processStateCode == "specialEnd" ||
          this.reportForm.processStateCode == "nomalEnd"
        ) {
          return true;
        }
      } else if (type == 5) {
        if (
          this.reportForm.processStateCode == "specialEnd" ||
          this.reportForm.processStateCode == "nomalEnd"
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    submitForm(formName) {
      //验证必填项
      let flag = true;
      this.$refs[formName].validate(valid => {
        if (valid) {
          flag = true;
        } else {
          flag = false;
        }
      });
      return flag;
    },
    back() {
      this.$router.push(
        this.$route.query.path == "addReport"
          ? "entryReport"
          : this.$route.query.path?this.$route.query.path:"/"
      );
    },
    disSubmit(status) {
      this.disClick = status;
    },
    //保存
    save() {
      this.fullscreenLoading = true;
      this.disClick = true;
      let type = "update";
      if (!this.reportForm || !this.reportForm.id || this.reportForm.processStateCode == 'draft') {
        type = "draft";
      }
      fiSave({
        csFormInst: this.reportForm,
        operateType: type
      }).then(res => {
        //this.fullscreenLoading = false;
        this.disClick = false;
        if (res.code === 200) {
          //附件关联工单
          let data = qs.stringify({
            id: res.data,
            ids: JSON.stringify(this.fileIdList)
          });
          cfRelationFormAndFiles(data).then(res => {
            console.log(res);
          });
          this.$confirm("保存成功", {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
          this.refreshData(res.data);
          // this.$router.push({
          //   path: "replacePage",
          //   query: {
          //     id: res.data,
          //     title: "报事录入",
          //     path: this.$route.query.path
          //       ? this.$route.query.path
          //       : "entryReport",
          //     fromPath: "itReport"
          //   }
          // });
        } else {
          this.disClick = false;
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {
            this.fullscreenLoading = false;
          });
        }
      });
    },
    //初始化常用问题
    initTroubleSelectPage(type) {
      initTroubleSelect({}).then(res => {
        if (res.sucess == true) {
          this.allTrouble = res.data;
          this.allTrouble.forEach(e => {
            e.id = e.id + "";
          });
          if (type == "add") {
            let obj = {};
            for (let i in this.allTrouble) {
              if (
                this.allTrouble[i].questionDesc == this.normalProblemContent
              ) {
                obj = this.allTrouble[i];
                break;
              }
            }

            // this.reportForm.commonProblemCode = obj.id
            this.$set(this.reportForm, "commonProblemCode", obj.id);
            this.$set(this.reportForm, "customerDemand", obj.questionDesc);
            //   this.reportForm.customerDemand = obj.questionDesc;
            this.normalProblemContent = "";
            this.normalProblemTitle = "";
          }
        }
      });
    },
    //自定义弹框
    openDilog() {
      this.addProblemVisible = true;
    },
    sureAddProblem() {
      if(this.normalProblemContent=='' || this.normalProblemContent == null){
        this.isblank = true;
        this.$confirm('标题和内容均不能为空，请完善必填信息！', {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
        }).then(() => {})
        return;
      }

      if(this.normalProblemTitle == "" || this.normalProblemTitle == null){
        this.$confirm('标题和内容均不能为空，请完善必填信息！', {
            confirmButtonText: '确定',
            center: true,
            showClose:false,
            showCancelButton:false,
            confirmButtonClass:'confirm-reset-style',
        }).then(() => {});
        return;
      }

      troubleSelectADD({
        questionDesc: this.normalProblemContent,
        questionTitle: this.normalProblemTitle
      }).then(res => {
        if (res.code === 200) {
          console.log(res.data);
          this.$confirm("添加成功", {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
          this.initTroubleSelectPage("add");
          this.addProblemVisible = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    searchDesc() {
      // debugger
      console.log(this.reportForm.commonProblemCode);
      this.allTrouble.map(item => {
        if (item.id == this.reportForm.commonProblemCode)
          this.$set(this.reportForm, "customerDemand", item.questionDesc);
      });
    },
    handleSuccess(res, file, fileList) {
      console.log(res, file, fileList);
      if (res.code == 200) {
        this.fileIdList.push(res.data[0]);
        let obj = {};
        obj.fileName = file.name;
        obj.id = res.data[0];
        obj.clientPath =
          this.reportForm.processStateCode == "handle" ||
          this.reportForm.processCode == "processKSCL"
            ? "handle" : "content";
        this.fileList.push(obj);
      }
    },
    // 删除附件
    delFile(file, index) {
      this.$confirm(`确定删除 ${file.fileName}？`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
        showClose: false
        // confirmButtonClass:'confirm-reset-style',
      })
        .then(() => {
          debugger;
          this.fileList.forEach((item, key) => {
            if (item.id == file.id) this.fileList.splice(key, 1);
          });
          this.fileIdList.splice(index, 1);
          deleteFile({id:file.id});
        })
        .catch(() => {});
    },
    downLoadFile(id) {
      window.location = "/api/csFile/fileDownload?id=" + id;
    },
    getFiSelectCsformInst(id) {
      //获取流程信息
      if (this.$route.query.id || id) {
        this.fullscreenLoading = true;
        fiSelectCsformInst({
          id: id?id:this.$route.query.id,
          source: this.$route.path
        }).then(res => {
            this.reportForm = {};
            this.fullscreenLoading = false;
            this.reportForm = Object.assign(res.data.csFormInst, {});
            this.recordList = res.data.processWorkitem;
            if (this.reportForm.processStateCode == "handle")
              this.rules.handleRecord[0].required = true;

            if ( this.reportForm.processCode == "processKSCL"){
              //快速流程咨询
              this.processNodeList = [
                { processStateName: "报事录入", taskStatus: this.reportForm.processStateCode=="nomalEnd"?30:20 },
                { processStateName: "报事关闭", taskStatus: this.reportForm.processStateCode=="nomalEnd"?30:10 }
              ];
            }else{
              this.getProcessNodeList(this.reportForm.id);
            }
            cfSelectFileByFormNo({ formNo: this.reportForm.id }).then(res => {
              if (res.code === 200) {
                this.fileList = res.data;
              }
            });
            if (
              this.$route.query.path &&
              this.$route.query.path.indexOf("ReportReturn") > 0
            ) {
              cfReturnVisitLock({ id: this.reportForm.id });
              let _this = this;
              this.rtTimer = setInterval(function() {
                cfReturnVisitLock({ id: _this.reportForm.id });
              }, 1000 * 60 * 2);
            }
          })
          .catch(error => {
            this.fullscreenLoading = false;
          });
      }
    },
    allDictList() {
      dict().then(res => {
        if (res.sucess && res.data) {
          this.dict = res.data;
        }
      });
    },
    changeSelect(k, v, d) {
      this.resetSelect(v);
      if (this.dict && Array.isArray(this.dict)) {
        let s = this.dict.filter(
          i => i.itemCode == this.reportForm[v] && i.dictCode == d
        );
        if (s && s.length == 1) this.$set(this.reportForm, k, s[0].itemValue);
      }
    },
    firstFilter(i) {
      let flg = false;
      let dCode =
        this.reportForm.processCode == "processBZ" ? "itFscBz" : "itFscKszx";
      if (i.dictCode == dCode) {
        flg = i.itemValue.indexOf(this.reportForm.project) == 0;
      }
      return flg;
    },
    resetSelect(v) {
      if ("projectCode,processCode".indexOf(v) != -1) {
        this.$set(this.reportForm, "firstSortCode", null);
        this.$set(this.reportForm, "firstSortName", null);
      }
    },

    handleCommon(type) {
      this.disClick = true;
      let reportFormChuli = true;
      let cusForm = this.submitForm("cusForm");
      let reportInfoForm = this.submitForm("reportInfoForm");
      let reportContentForm = this.submitForm("reportContentForm");
      if(this.reportForm.processCode=='processKSCL'||this.reportForm.processStateCode=='handle'||this.reportForm.processStateCode=='nomalEnd'){
        reportFormChuli = this.submitForm("reportFormChuli");
      }
      let flg = cusForm && reportInfoForm && reportContentForm && reportFormChuli;
      if (flg) {
        if (type == "save") {
          this.save();
        } else if (type == "submit") {
          this.subSubmit();
        }
      } else {
        this.disClick = false;
        setTimeout(() => {
          let errorEloffsetTop =
            $(".el-form-item.is-error")
              .eq(0)
              .offset().top -
            $("#if-rame").height() -
            $("#homepage-header").height();
          if (errorEloffsetTop < 101) {
            $("#mainScrollContainer").scrollTop(-(101 - errorEloffsetTop));
          } else {
            $("#mainScrollContainer").scrollTop(errorEloffsetTop - 101);
          }
        }, 50);
      }
    },
    subSubmit() {
      this.disClick = true;
      this.$refs.sub.submit();
    },
    subHSubmit() {
      this.disClick = true;
      this.$refs.sub.handlerSubmit({});
    },
    subReject() {
      this.disClick = true;
      this.$refs.sub.reject();
    },
    subSetLevelUp() {
      if (this.reportForm.upgradeLevel) {
        let level = "城市";
        if (this.reportForm.upgradeLevel == "2") {
          level = "区域";
        } else if (this.reportForm.upgradeLevel == "3") {
          level = "集团";
        }
        this.$confirm(`该工单已经升级至${level}负责人！`, {
          confirmButtonText: "确定",
          center: true,
          showClose: false,
          showCancelButton: false
          // confirmButtonClass:'confirm-reset-style',
        }).then(() => {});
      } else {
        this.disClick = true;
        this.$refs.sub.setLevelUp();
      }
    },
    subSpecialEnd() {
      this.disClick = true;
      this.$refs.sub.specialDialog();
    },
    refreshData(v) {
      this.getFiSelectCsformInst(v);
    },
    getProcessNodeList(val) {
      let params = {
        formInstId: val
      };
      getProcessNodeByFormId(params).then(res => {
        if (res.code == 200) {
          this.processNodeList = [
            { processStateName: "报事录入", taskStatus: 30 },
            { processStateName: "报事分派", taskStatus: 30 },
            { processStateName: "报事处理", taskStatus: 30 },
            { processStateName: "报事关闭", taskStatus: 30 }
          ];
          if (res.data.length != 0){
            let isRuning = true;
            let task = res.data.filter(item => item.taskStatus == 20);
            this.processNodeList.forEach(k=>{
              if(!isRuning){
                k.taskStatus = 10;
              }
              if(task && task.length > 0){
                if(k.processStateName == task[0].processStateName){
                  k.taskStatus = task[0].taskStatus;
                  isRuning = false;
                }
              }
            })
          }
        } else {
          this.$message({
            type: "error",
            message: res.message
          });
        }
      });
    },
    openEditDilog(){
      let list = JSON.parse(JSON.stringify(this.allTrouble));
      this.problemEditData = list;
      console.log(list);
      this.editProblemVisible = true;
    },
    sureSaveProblemList(){
        let changeArr = new Array();
        let isSaveSuccess = true;
        this.allTrouble.map((aitem,aindex) => {
            this.problemEditData.map((pitem,pindex) => {
                if(aitem.id == pitem.id && (aitem.questionDesc != pitem.questionDesc || aitem.questionTitle != pitem.questionTitle)){
                  changeArr.push(pitem);
                }
            })
        })
        if(changeArr.length == 0){
          this.editProblemVisible = false;
          return;
        }
        let that = this;
        let isBlank = false;
        let syncFn = new Promise((resolve,reject) => {
            changeArr.map((item,index) => {
                if(item.questionDesc == '' || item.questionTitle == ''){
                    that.$confirm('标题和内容均不能为空，请完善必填信息！',{
                        confirmButtomText: "确定",
                        center:true,
                        showClose:false,
                        showCancelButton: false,
                        confirmButtomClass:"confirm-reset-style"
                    }).then(() => {
                        
                    })
                }else{
                    setTimeout(() => {
                        troubleSelectADD({
                        id:item.id,
                        questionDesc:item.questionDesc,
                        questionTitle:item.questionTitle
                        }).then((res) => {
                        if(res.code != 200){
                            isSaveSuccess = false;
                            resolve();
                        }else{
                            if(index == changeArr.length-1){
                            resolve();
                            }
                        }
                        })
                    }, 30*index);
                }
            })
        })
        syncFn.then(() => {
          if(isSaveSuccess){
              that.$confirm('保存成功！',{
                confirmButtomText: "确定",
                center:true,
                showClose:false,
                showCancelButton: false,
                confirmButtomClass:"confirm-reset-style"
              }).then(() => {
                  that.initTroubleSelectPage("init");
                  that.editProblemVisible = false; 
              })
          }else{
              that.$confirm('保存失败！',{
                confirmButtomText: "确定",
                center:true,
                showClose:false,
                showCancelButton: false,
                confirmButtomClass:"confirm-reset-style"
              }).then(() => {
                  
              })
          }
        })
    },
    deleteProblem(row){
      console.log(row);
      this.$confirm('确认并删除该条常用问题？', {
          confirmButtonText: "确定",
          center: true,
          showClose: false,
          showCancelButton: true,
          confirmButtonClass: "confirm-reset-style"
      }).then(() => {
          let params = {
            id:row.id
          }
          deleteTrouble(params).then((res) => {
              if(res.code == 200){
                  this.$confirm('删除常用问题成功！',{
                    confirmButtomText: "确定",
                    center:true,
                    showClose:false,
                    showCancelButton: false,
                    confirmButtomClass:"confirm-reset-style"
                  }).then(() => {
                      this.initTroubleSelectPage("init"); 
                  })
                  this.problemEditData.map((item,index) => {
                      if(item.id == row.id){
                        this.problemEditData.splice(index,1);
                      }
                  })
              }else{
                  this.$confirm(res.message,{
                    confirmButtomText: "确定",
                    center:true,
                    showClose:false,
                    showCancelButton: false,
                    confirmButtomClass:"confirm-reset-style"
                  }).then(() => {
                      //this.initTroubleSelectPage("init");
                  })
              }
          })
      }).catch(() => {

      });
    }
  },
  mounted() {
    this.allDictList();
    this.initTroubleSelectPage("init");
    this.getFiSelectCsformInst();
    if (!this.reportForm.id) {
      if (this.$route.query.personId) {
        cuSelectUserTel({ username: this.$route.query.personId }).then(res => {
          this.reportForm.ownerId = res.data.fdUsername;
          this.reportForm.ownerName = res.data.fdName;
          this.reportForm.genderName = res.data.fdGender
            ? res.data.fdGender == 1 ? "男" : "女"
            : "";
          this.reportForm.mobile = res.data.fdTel;
          this.reportForm.eMail = res.data.fdEmail;
          this.reportForm.workUnit = res.data.fdOrgNameTree;
          this.reportForm.occupation = res.data.fdPosition;
        });
      }
      if (!this.$route.query.id){
        getFormNo({ firstType: "deIT" }).then(res => {
          if (res.sucess && res.data) {
            this.$set(this.reportForm, "formNo", res.data);
          }
        });
      }
    }

    
    let headerHeight = document.getElementById("homepage-header")
      .offsetHeight;
    let iframeHeight =
      document.getElementById("if-rame") && this.userInfo.seats
        ? document.getElementById("if-rame").offsetHeight
        : 0;
    this.fixTop = Number(headerHeight) + Number(iframeHeight) + 8 + "px";
    window.onresize = () => {
      this.fixTop = Number(headerHeight) + Number(iframeHeight) + 8 + "px";
    };
    let scrollContainer = document.getElementById("mainScrollContainer");
    scrollContainer.addEventListener("scroll", () => {
      $(".process-node")
        .eq(0)
        .css({ top: $("#mainScrollContainer").scrollTop() - 49 });
    });
  },
  beforeDestroy() {
    if (this.rtTimer) {
      //如果定时器还在运行 或者直接关闭，不用判断
      clearInterval(this.rtTimer); //关闭
    }
  }
};
</script>

<style scoped>
.add-report {
  width: calc(100% - 10px);
  position: relative;
}
.add-report .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
}
.add-report .tableHover > tbody > tr:hover {
  background-color: #d5dbe4;
}
.add-report >>> .el-date-editor {
  width: 100%;
}
#app .el-button {
  margin-left: 0 !important;
}
.add-report >>> .el-input__inner {
  height: 24px;
}
.mf_big {
  margin-left: 108px;
}
#flex-top {
  position: fixed;
  right: 10px;
  z-index: 1000;
}
#flex-top .btnDown {
  margin: 0 5px;
  margin-top: 2px;
}
.process-node {
  position: absolute;
  width: 100%;
  height: 40px;
  background-color: #fff;
  top: -49px;
  z-index: 999;
  border-top: solid 1px rgba(0, 0, 0, 0.1);
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
}
.process-node ul {
  display: block;
  height: 100%;
  line-height: 40px;
  padding-left: 10px;
}
.process-node ul li {
  display: block;
  float: left;
}
.process-node ul li span {
  display: inline-block;
  height: 26px;
  line-height: 26px;
  padding: 0 30px;
  border-radius: 3px;
}
.yellow-bac {
  background-color: #f5a623;
  color: #fff;
}
.red-bac {
  background-color: #e63f3c;
  color: #fff;
}
.grey-bac {
  background-color: #f0f0f0;
  color: #9b9b9b;
}
.add-report img {
  width: 32px;
  height: 16px;
  vertical-align: middle;
}
.my-textarea{
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  font-size: 12px;
  display: block;
  padding: 5px 15px;
  line-height: 1.5;
  box-sizing: border-box;
  width:100%;
  max-width:100%;
  min-width:100%;
  font-size: inherit;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  outline:0;
}
.my-textarea:focus{
  border:1px solid #E63F3C;
}
.red-border{
  border:1px solid #E63F3C;
}
</style>
