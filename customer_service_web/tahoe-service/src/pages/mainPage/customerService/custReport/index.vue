<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="80px" size="mini">
                <div style="height:10px;"></div>
                <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="区域">
                            <el-select v-model="searchData.area" filterable placeholder="请选择" @change="getCityList">
                                <el-option v-for="item in areaOptions" :key="item.id" :label="item.region" :value="item.regionCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="城市">
                            <el-select v-model="searchData.city" :disabled="cityprojectDisabled" filterable placeholder="请选择" @change="getProjectList">
                                <el-option v-for="item in cityOptions" :key="item.cityCompanyCode" :label="item.cityCompany" :value="item.cityCompanyCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="项目">
                            <el-select v-model="searchData.project" :disabled="projectDisabled" filterable placeholder="请选择">
                                <el-option v-for="item in projectOptions" :key="item.projectCode" :label="item.project" :value="item.projectCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="工单编号">
                            <el-input v-model="searchData.number" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="box" v-if="flag">
                    <el-row style="margin-top:10px;" class="mt-10">
                        <el-col :span='5' :offset="0">
                            <el-form-item label="一级分类">
                                <el-select v-model="searchData.firstSortCode" placeholder="请选择">
                                    <el-option v-for="i in dict.filter(i=>i.dictCode =='firstSortCode')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="报事人电话">
                                <el-input v-model="searchData.phone" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="创建时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.createdate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.enddate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <el-row class="mt-10">
                    <el-col :span="24" style="text-align:right;padding-right:4%;">
                        <button class="btnDown" @click.prevent="searchReturn()" style="margin-left:20px;margin-top:3px;">查询</button>
                        <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
                    </el-col>
                </el-row>
                <el-row class="mt-10">
                    <el-col :span="24">
                        <div class="ta-ct mt-10" style="position:relative;height:30px;">
                            <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                            <span class="txt" @click.prevent="change()">
                                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
                            </span>
                        </div>
                    </el-col>
                </el-row>

            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">
            <div style="width:100%;overflow:hidden;">
                <el-table v-loading="loading" stripe element-loading-text="拼命加载中" :data="list" style="width: 100%">
                    <el-table-column label="工单编号" width="130">
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.formNo}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="houseNo" label="区域" show-overflow-tooltip/>
                    <el-table-column prop="houseNo" label="城市" show-overflow-tooltip/>
                    <el-table-column prop="houseNo" label="项目" show-overflow-tooltip/>
                    <el-table-column prop="firstSortName" label="一级分类" show-overflow-tooltip/>
                    <el-table-column prop="ownerName" label="业主名称" show-overflow-tooltip/>
                    <el-table-column prop="mobile" label="业主电话" width="100" show-overflow-tooltip/>
                    <el-table-column label="创建时间" width="140">
                        <template slot-scope="scope">
                            <span>{{scope.row.creationDate | TimeMoment}}</span>
                        </template>
                    </el-table-column>

                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturnListOK"></wcg-page>
            </div>
        </div>

    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import {
  area,
  searchInterimReports,
  searchInterimReport,
  getRegionInfoByCode
} from "@/api/port";
import { dict } from "@/api/wsd";
import { rfList } from "@/api/export/exp";
export default {
  props: [],
  components: { wcgPage },
  watch: {
    "searchData.area"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.floorDisabled = true;
          this.projectOptions = [];
          this.buildOptions = [];
          this.searchData.city = this.searchData.project = "";
        }
      } else {
        this.cityprojectDisabled = true;
        if ((this.cityprojectDisabled = true)) {
          this.projectDisabled = true;
        }
        this.floorDisabled = true;
        this.projectDisabled = true;
      }
    },
    "searchData.city"(newval, oldval) {
      if (newval != oldval) {
        this.searchData.project = "";
      }
      if (newval == "") {
        this.projectDisabled = true;
      } else {
        this.projectDisabled = false;
      }
    }
  },
  data() {
    return {
      flag: false,
      loading: false,
      pageSize: 20,
      currentPage: 1,
      total: 0,
      projectDisabled: true,
      searchData: {
      },
      areaOptions: [],
      projectOptions: [],
      cityOptions: [],
      cityprojectDisabled: true,
      list: [],
      dict: []
    };
  },
  methods: {
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.area
      }).then(res => {
        console.log(res.data);
        this.projectOptions = res.data;
      });
    },
    change() {
      this.flag = !this.flag;
    },
    goDetail(item) {
      //跳转详情
      this.$router.push({
        path: "addReport",
        query: {
          id: item.id,
          title: "报事查询",
          path: this.$route.path,
          disabled: true
        }
      });
    },
    searchReturn(){
        this.currentPage = 1
        this.pageSize = 20;
        this.searchReturnListOK();
    },
    searchReturnListOK() {
      this.loading = true;
      this.list = [];
      rfList(this.searchData).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          this.currentPage = res.data.current;
          this.total = res.data.total;
        }
      });
    },
    reset() {
      this.searchData = {};
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.searchReturnListOK();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchReturnListOK();
    }
  },
  mounted() {
    dict().then(res => {
      if (res.sucess && res.data) {
        this.dict = res.data;
      }
    });
  },
  created() {
    this.areaList();
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.mb-20 {
  /* height: 400px; */
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,
.levelUpTag img {
  vertical-align: middle;
  width: 32px;
  height: 16px;
}
.el-date-editor.el-input,.el-date-editor.el-input__inner{width:100% !important}
</style>
