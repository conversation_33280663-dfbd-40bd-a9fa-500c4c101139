
<template>
  <div class="search-report" style="overflow:hidden;">
    <it v-if="this.userInfo.flag == 3"></it>
    <wy v-if="this.userInfo.flag != 3"></wy>
  </div>
</template>

<script>
import wy from "./wy";
import it from "./it";
export default {
  components: {
    wy,
    it
  },
  data() {
    return {
      userInfo: JSON.parse(sessionStorage.getItem("userInfo"))
    };
  },
  methods: {
    name() {}
  },
  created() {
    
  }
};
</script>

<style scoped>
/* 搜索部分 */
.search-report {
  width: calc(100% - 10px);
}

.search-report >>> .el-select-dropdown__item {
  font-size: 12px !important;
  padding: 0 15px;
  height: 30px;
  line-height: 30px;
}
</style>

