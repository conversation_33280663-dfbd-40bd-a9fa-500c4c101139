<template>
    <div>
        <div class="search-bar">
            <div class="bg-style">
                <el-form ref="form" label-position="left" :model="searchData" label-width="60px" :disabled="this.username?true:false">
                    <div style="height:10px;"></div>
                    <el-row>
                        <el-col :span='4' :offset="1">
                            <el-form-item label="登陆名">
                                <el-input v-model="searchData.custId" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='4' :offset="1">
                            <el-form-item label="电话">
                                <el-input v-model="searchData.telephone" placeholder="请输入" @blur="removeBlank"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4" :offset="1">
                            <el-form-item label="项目">
                                <el-select v-model="searchData.project" placeholder="请选择">
                                    <el-option v-for="item in projectList" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span='4' :offset="1">
                            <button class="btnDown" style="margin-top:9px;" @click.prevent="searchItem()">查询</button>
                            <button class="btnDown" style="margin-top:9px;margin-left:15px;" @click.prevent="reset">重置</button>
                        </el-col>
                    </el-row>

                    <el-row class="mt-10" v-show="showIf">
                        <el-col :span='4' :offset="1">
                            <el-form-item label="业务步骤">
                                <el-select v-model="searchData.processStateCode" placeholder="请选择">
                                    <el-option v-for="item in step" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span='4' :offset="1">
                            <el-form-item label="创建时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.createDateStart" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='4' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.createDateEnd" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="24">
                            <div class="ta-ct mt-10" style="position:relative;height:30px;">
                                <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                                <span class="txt" @click.prevent="showIf =!showIf">
                                    <i style="font-size:18px;color:#E63F3C;" :class="showIf?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
                                </span>
                            </div>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <!-- <div v-if="searchUser" class="el-alert el-alert--error is-center">
            <div class="el-alert__content">
                <span class="el-alert__title">内部员工：{{searchUser.fdName}}{{searchUser.fdPosition?" "+searchUser.fdPosition:""}} {{searchUser.fdOrgNameTree}} {{searchUser.fdTel}}</span>
                <i class="el-alert__closebtn is-customed" @click="searchUser = null">知道了</i>
            </div>
        </div> -->
        <!-- 报表信息 -->
        <div class="bg-style mt-10 mb-20">
            <div style="height:44px;line-height:44px;">
                <span class="redSide ml-10"> </span>
                <span class="ft-14 fw-bd">人员信息</span>
            </div>
            <el-table v-loading="user.loading" stripe :data="userList" style="width: 100%" height="200" :row-class-name="userRowStyle" @row-click="getUser">
              <el-table-column prop="fdName" label="姓名" width="80"/>
              <el-table-column prop="fdEmail" label="邮箱" width="200"/>
              <el-table-column prop="fdTel" label="电话" width="120" />
              <el-table-column prop="fdPosition" label="岗位" width="120" />
              <el-table-column prop="fdOrgNameTree" label="组织"  />
            </el-table>
            <!-- 分页 -->
            <wcg-page :disabled="this.username?true:false" :pageSize="upageSize" :currentPage="upageNum" :total="utotal" :floorList="userList" @size-change="handleUserSizeChange" @current-change="handleUserCurrentChange" @on-change="getUserList"></wcg-page>
        </div>
        <!-- 报表信息 -->
        <div class="bg-style mt-10 mb-20">
            <div style="height:44px;line-height:44px;">
                <span class="redSide ml-10"> </span>
                <span class="ft-14 fw-bd">工单信息</span>
                <button class='btnDown fr mt-10 mr-10' @click="addReport()">添加报事</button>
                <!-- <button class='btnDown fr mt-10 mr-10' v-if="searchUser" @click="addReport('itReport')">IT报事</button> -->
            </div>
            <el-table v-loading="report.loading" stripe :data="reportList" tooltip-effect="dark" style="width: 100%" :row-class-name="reportRowStyle" @row-click="getReport" @sort-change="sortByTime">
                <el-table-column label="工单编号" width="180">
                    <template slot-scope="scope">
                        <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.formNo}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="deptName" label="部门" width="80" />
                <el-table-column prop="firstSortName" label="一级分类" width="110" show-overflow-tooltip/>
                <el-table-column prop="secSortName" label="二级分类" width="110" show-overflow-tooltip/>
                <el-table-column prop="ownerName" label="业主名称" />
                <el-table-column prop="createUserName" label="创建人" />
                <el-table-column label="创建时间" width="180" sortable>
                    <template slot-scope="scope">
                        <span>{{scope.row.creationDate | TimeMoment}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="assignName" label="分派人" />
                <el-table-column label="分派时间" width="180">
                    <template slot-scope="scope">
                        <span>{{scope.row.assignDate | TimeMoment}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="curAssigneeName" label="当前处理人" />
                <!-- <el-table-column prop="assignDate" :formatter="this.$utilJs.dateForYMD" label="处理时间" /> -->
                <el-table-column prop="processStateName" label="业务步骤" />
            </el-table>
            <!-- 分页 -->
            <wcg-page :disabled="this.reportId?true:false" :pageSize="rpageSize" :currentPage="rpageNum" :total="rtotal" :floorList="reportList" @size-change="handleReportSizeChange" @current-change="handleReportCurrentChange" @on-change="reportReportList"></wcg-page>
        </div>
    </div>
</template>

<script>
import wcgPage from "@/components/wcgPage";
import { formMsg, initSelect } from "@/api/port";
import { cuSelectUserTel,userList } from "@/api/wsd";

export default {
  components: {
    wcgPage
  },
  data() {
    return {
      user: {
        loading: false,
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      report: {
        loading: false,
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      upageNum:1,
      upageSize:20,
      utotal:0,
      rpageNum:1,
      rpageSize:20,
      rtotal:0,
      username: null,
      reportId: null,
      userList: [],
      reportList: [],
      searchData: {},
      step: [],
      projectList: [],
      showIf: false,
      searchUser: null
    };
  },
  methods: {
    //搜索的信息 子路由返回
    searchItem() {
      this.showUserTel();
      this.getFloorMsg();
    },
    sortByTime(column){
      if(column.order == 'ascending'){//从小到大
        this.$set(this.searchData,'orderBy','ASC');
        //this.searchItem(this.roomRequest);
        this.reportReportList(this.rpageNum);
      }else if(column.order == 'descending'){//从大到小
        this.$set(this.searchData,'orderBy','DESC');
        //this.searchItem(this.roomRequest);
        this.reportReportList(this.rpageNum);
      }else{
        this.$set(this.searchData,'orderBy',null);
        //this.searchItem(this.roomRequest);
        this.reportReportList(this.rpageNum);
      }
    },
    removeBlank(){
      this.searchData.telephone = this.searchData.telephone.replace(/^\s*|\s*$/g,"");
    },
    reset() {
      this.searchData = {};
    },
    showUserTel() {
      this.searchUser = null;
      if (this.searchData.telephone) {
        cuSelectUserTel({ tel: this.searchData.telephone }).then(res => {
          if (res.sucess) this.searchUser = res.data;
        });
      }
    },
    addReport() {
      this.$router.push({
        path: "itReport",
        query: {
          personId: this.username,
          tel: this.searchData.telephone,
          addShow: true,
          path: this.$route.path
        }
      });
    },
    goDetail(item) {
      this.$router.push({
        path: item.deptName == "IT" ? "itReport" : "addReport",
        query: { id: item.id, title: "报事录入", path: this.$route.path ,addListDis : true}
      });
    },
    //点选人员列表判断
    getUser(item) {
      this.username = this.username && this.username == item.fdUsername ? null : item.fdUsername;
      this.getFloorMsg();
    },
    userRowStyle({ row, rowIndex }) {
      if(!this.username && this.searchData.telephone == row.fdTel){
        this.username = row.fdUsername
      }
      return this.username == row.fdUsername ? "active" : "";
    },
    //点选报表列表判断
    getReport(item) {
      this.reportId = this.reportId && this.reportId == item.id ? null : item.id;
    },
    reportRowStyle({ row, rowIndex }) {
      return this.reportId == row.id ? "active" : "";
    },
    // 获取信息
    getFloorMsg() {
      this.getUserList();
      this.reportReportList();
    },
    // 用户查询翻页
    handleUserSizeChange(val) {
      this.upageSize = val;
      this.upageNum = 1;
      this.getUserList(this.upageNum);
    },
    handleUserCurrentChange(val) {
      //this.user.currentPage = val;
      this.upageNum = val;
      this.getUserList(val);
    },
    getUserList(pageNum){
      this.userList = [];
      //this.user.total = 0;
      // if(this.searchData.custId || this.searchData.telephone){
        this.user.loading = true;
        let r = JSON.parse(JSON.stringify(this.searchData));
        r.pageNum= pageNum ? pageNum : 1;
        r.pageSize= this.upageSize;
        r.username= this.searchData.custId;
        r.tel= this.searchData.telephone;
        userList(r).then(res=>{
          this.user.loading = false;
          if (res.sucess && res.data) {
            this.userList = res.data.records;
            this.utotal = res.data.total;
          }
        })
      // }
    },
    // 工单查询翻页
    handleReportSizeChange(val) {
      this.rpageSize = val;
      this.rpageNum = 1;
      this.reportReportList(this.rpageNum);
    },
    handleReportCurrentChange(val) {
      this.rpageNum = val;
      this.reportReportList(val);
    },
    reportReportList(pageNum) {
      this.report.loading = true;
      //this.report.total = 0;
      this.reportList = [];
      let r = JSON.parse(JSON.stringify(this.searchData));
      r.pageNum = pageNum ? pageNum : 1;
      r.pageSize = this.rpageSize;
      r.deptCode = 'deptIT';
      if(this.username)
        r.custId = this.username
      formMsg(r).then(res => {
        this.report.loading = false;
        if (res.sucess && res.data) {
          this.reportList = res.data.records;
          this.rtotal = res.data.total;
        }
      });
    }
  },
  created() {
    initSelect().then(res => {
      this.step = res.data.processStateCode;
      this.step.map((item, index) => {
        if (item.itemValue == "暂存" || item.itemValue == "报事升级") {
          this.step.splice(index, 1);
        }
      });
      this.projectList = res.data.deptIT;
    });
  },
  mounted() {
    let _this = this;
    window.goEntry = function(num) {
      _this.searchData = {};
      _this.searchData.telephone = num;
      _this.getFloorMsg();
      _this.showUserTel();
    };
  }
};
</script>

<style scoped>
table tbody tr:hover {
  background-color: #d5dbe4;
}
.color {
  color: aqua;
}
</style>
<style>
.el-table .active td {
  background-color: #e63f3c !important;
  color: #fff !important;
}
.el-table tr.active:hover > td {
  background-color: #e63f3c !important;
  color: #fff !important;
}
</style>