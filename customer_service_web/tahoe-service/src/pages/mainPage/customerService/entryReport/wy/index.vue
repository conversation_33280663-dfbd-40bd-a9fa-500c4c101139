<template>
  <div>
    <searchBar @searchItem="searchItem" stepFlg :entryReport="$route.path" :disabled="(this.roomId || this.personId || this.reportId)?true:false"></searchBar>
    <div v-if="searchUser" class="el-alert el-alert--error is-center">
      <div class="el-alert__content">
        <span class="el-alert__title">内部员工：{{searchUser.fdName}}{{searchUser.fdPosition?" "+searchUser.fdPosition:""}} {{searchUser.fdOrgNameTree}} {{searchUser.fdTel}}</span>
        <i class="el-alert__closebtn is-customed" @click="searchUser = null">知道了</i>
      </div>
    </div>
    <div>
      <!-- 房屋信息 -->
      <div class="bg-style mt-10 fl" style="width:73%;">
        <div style="height:44px;line-height:44px;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">房屋信息</span>
        </div>
        <el-table v-loading="room.loading" height="180" stripe :data="floorList" style="width: 100%" :row-class-name="roomRowStyle" @row-click="getRoom">
          <el-table-column prop="area" label="区域" />
          <el-table-column prop="city" label="城市" />
          <el-table-column prop="project" label="项目" />
          <el-table-column prop="building" label="楼号" />
          <el-table-column prop="unit" label="单元号" />
          <el-table-column prop="roomNum" label="房号" />
        </el-table>
        <!-- 分页 -->
        <wcg-page :recordControl="false" :isRefresh="false" :disabled="this.roomId?true:false" paginationSize="min" :pageSize="room.pageSize" :currentPage="room.currentPage" :total="room.total" :floorList="floorList" @size-change="handleRoomSizeChange" @current-change="handleRoomCurrentChange" @on-change="reportRoomList"></wcg-page>

      </div>
      <!-- 业主信息 -->
      <div class="bg-style mt-10 fr" style="width:25%;">
        <div style="height:44px;line-height:44px;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">业主信息</span>
        </div>

        <el-table v-loading="person.loading" height="180" stripe :data="peopleList" style="width: 100%" :row-class-name="personRowStyle" @row-click="getPerson">
          <el-table-column prop="custName" label="住户" width="60" />
          <el-table-column prop="telephone" label="移动电话" />
        </el-table>
        <!-- 分页 -->
        <wcg-page :disabled="this.personId?true:false" paginationSize="min" :pagerCount="5" :showSpan="false" :pageSize="person.pageSize" :currentPage="person.currentPage" :total="person.total" :floorList="peopleList" @size-change="handlePersonSizeChange" @current-change="handlePersonCurrentChange" @on-change="reportPersonList"></wcg-page>
      </div>
      <div class="clear"></div>
    </div>
    <!-- 报表信息 -->
    <div class="bg-style mt-10 mb-20">
      <div style="height:44px;line-height:44px;">
        <span class="redSide ml-10"> </span>
        <span class="ft-14 fw-bd">工单信息</span>
        <button class='btnDown fr mt-10 mr-10' @click="addReport()">添加报事</button>
        <!-- <button class='btnDown fr mt-10 mr-10' v-if="searchUser" @click="addReport('itReport')">IT报事</button> -->
      </div>
      <el-table v-loading="report.loading" stripe :data="reportList" tooltip-effect="dark" style="width: 100%" :row-class-name="reportRowStyle" @row-click="getReport" @sort-change="sortByTime">
        <el-table-column label="工单编号" width="180">
          <template slot-scope="scope">
            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.formNo}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deptName" label="部门" width="80" />
        <el-table-column prop="firstSortName" label="一级分类" width="110" show-overflow-tooltip/>
        <el-table-column prop="secSortName" label="二级分类" width="110" show-overflow-tooltip/>
        <el-table-column prop="ownerName" label="业主名称" width="110" show-overflow-tooltip/>
        <el-table-column prop="createUserName" label="创建人" />
        <el-table-column label="创建时间" width="180" sortable>     
          <template slot-scope="scope">
            <span>{{scope.row.creationDate | TimeMoment}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assignName" label="分派人" />
        <el-table-column label="分派时间" width="180">
          <template slot-scope="scope">
            <span>{{scope.row.assignDate | TimeMoment}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="curAssigneeName" label="当前处理人" />
        <!-- <el-table-column prop="assignDate" :formatter="this.$utilJs.dateForYMD" label="处理时间" /> -->
        <el-table-column prop="processStateName" label="业务步骤" />

      </el-table>
      <!-- 分页 -->
      <wcg-page :disabled="this.reportId?true:false" :pageSize="report.pageSize" :currentPage="report.currentPage" :total="report.total" :floorList="reportList" @size-change="handleReportSizeChange" @current-change="handleReportCurrentChange" @on-change="reportReportList"></wcg-page>
    </div>
  </div>
</template>

<script>
import wcgPage from "@/components/wcgPage";
import searchBar from "@/components/searchBar";
import { floorMsg, personMsg, formMsg } from "@/api/port";
import { cuSelectUserTel, setCustPhone } from "@/api/wsd";

export default {
  components: {
    searchBar,
    wcgPage
  },
  data() {
    return {
      room: {
        loading: false,
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      person: {
        loading: false,
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      report: {
        loading: false,
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      roomId: null,
      personId: null,
      reportId: null,
      floorList: [],
      peopleList: [],
      reportList: [],
      searchData:{},
      roomRequest: {
        area: "",
        city: "",
        project: "",
        building: "",
        telephone: "",
        createDateStart: "",
        createDateEnd: "",
        processStateCode: "",
        houseNum: "",
        custId: "",
        pageSize: 20,
        pageNum: 1
      },
      sortData:{

      },
      searchUser: null
    };
  },
  methods: {
    //搜索的信息 子路由返回
    searchItem(item) {
      console.log('item',item);
      this.searchData = item;
      this.roomRequest.telephone = item.phone;
      this.roomRequest.createDateStart = item.createDate;
      this.roomRequest.area = item.area;
      this.roomRequest.city = item.city;
      this.roomRequest.project = item.project;
      this.roomRequest.building = item.floorNum;
      this.roomRequest.unit = item.unit;
      this.roomRequest.createDateEnd = item.endDate;
      this.roomRequest.processStateCode = item.step;
      this.roomRequest.sHouseNum = item.houseNum;
      this.roomRequest.sHouseName = item.houseName;
      this.roomRequest.roomNum = item.roomNum;
      this.roomRequest.pageNum = item.pageNum?item.pageNum:1,
      this.showUserTel();
      this.getFloorMsg();
    },
    sortByTime(column){
      if(column.order == 'ascending'){//从小到大
        this.$set(this.roomRequest,'orderBy','ASC');
        this.searchItem(this.roomRequest);
      }else if(column.order == 'descending'){//从大到小
        this.$set(this.roomRequest,'orderBy','DESC');
        this.searchItem(this.roomRequest);
      }else{
        this.$set(this.roomRequest,'orderBy',null);
        this.searchItem(this.roomRequest);
      }
    },
    showUserTel() {
      this.searchUser = null;
      if (this.roomRequest.telephone) {
        cuSelectUserTel({ tel: this.roomRequest.telephone }).then(res => {
          if (res.sucess) this.searchUser = res.data;
        });
      }
    },
    addReport() {
      if (
        this.personTel &&
        this.callTel &&
        this.personTel.indexOf(this.callTel) < 0
      ) {
        this.$confirm("来电电话与业主电话不一致，是否更新业主电话？", {
          cancelButtonText: "确认",
          confirmButtonText: "取消",
          center: true,
          showClose: false
        })
          .then(() => {
            this.goAdd();
          })
          .catch(() => {
            setCustPhone({
              custId: this.personId,
              telephone: this.callTel
            }).then(res => {
              if (res.code == 200) {
                this.$confirm("更新成功", {
                  confirmButtonText: "确定",
                  center: true,
                  showClose: false,
                  showCancelButton: false,
                  confirmButtonClass: "confirm-reset-style"
                }).then(() => {
                  this.goAdd();
                });
              }
            });
          });
      } else {
        this.goAdd();
      }
    },
    goAdd() {
      this.$router.push({
        path: "addReport",
        query: {
          roomId: this.roomId,
          personId: this.personId,
          tel: this.roomRequest.telephone,
          addShow: true,
          path: this.$route.path
        }
      });
    },
    goDetail(item) {
      let obj = {
        type:'entryReport',
        pageNum:this.report.currentPage,
        searchData:this.searchData,
        total:this.report.total
      }
      sessionStorage.setItem('params',JSON.stringify(obj));
      this.$router.push({
        path: item.deptName == "IT" ? "itReport" : "addReport",
        query: {
          id: item.id,
          title: "报事录入",
          path: this.$route.path,
          addListDis: true
        }
      });
    },
    // 点选房屋列表判断
    getRoom(item) {
      this.roomId =
        this.roomId && this.roomId == item.houseNum ? null : item.houseNum;
      this.getFloorMsg();
    },
    roomRowStyle({ row, rowIndex }) {
      return this.roomId == row.houseNum ? "active" : "";
    },
    //点选业主列表判断
    getPerson(item) {
      this.personId =
        this.personId && this.personId == item.custId ? null : item.custId;
      this.personTel = item.telephone;
      this.getFloorMsg();
    },
    personRowStyle({ row, rowIndex }) {
      return this.personId == row.custId ? "active" : "";
    },
    //点选报表列表判断
    getReport(item) {
      this.reportId =
        this.reportId && this.reportId == item.id ? null : item.id;
      if (this.reportId) {
        this.personId = item.ownerId;
        this.roomId = item.houseInfoId;
        if (this.personId && this.roomId) {
          this.reportRoomList(null, null, true);
          this.reportPersonList(null, null, true);
        }
      }
    },
    reportRowStyle({ row, rowIndex }) {
      return this.reportId == row.id ? "active" : "";
    },
    // 获取信息
    getFloorMsg() {
      this.reportRoomList();
      this.reportPersonList();
      this.reportReportList();
    },
    //房屋翻页
    handleRoomSizeChange(val) {
      this.pageNum = val;
      if (!this.roomId) {
        this.room.pageSize = val;
        this.reportRoomList();
      }
    },
    handleRoomCurrentChange(val) {
      if (!this.roomId) {
        this.room.currentPage = val;
        this.reportRoomList(val);
      }
    },
    reportRoomList(pageNum, formReport) {
      if (!this.roomId || formReport) {
        this.room.loading = true;
        this.floorList = [];
        let r = JSON.parse(JSON.stringify(this.roomRequest));
        r.pageNum = pageNum ? pageNum : 1;
        r.pageSize = this.room.pageSize;
        r.houseNum = this.roomId;
        r.custId = this.personId;
        floorMsg(r).then(res => {
          this.room.loading = false;
          if (res.sucess && res.data) {
            this.floorList = res.data.records;
            this.room.total = res.data.total;
          }
        });
      }
    },
    // 人员查询翻页
    handlePersonSizeChange(val) {
      if (!this.personId) {
        this.person.pageSize = val;
        this.reportPersonList();
      }
    },
    handlePersonCurrentChange(val) {
      if (!this.personId) {
        this.person.currentPage = val;
        this.reportPersonList(val);
      }
    },
    reportPersonList(pageNum, formReport) {
      if (!this.personId || formReport) {
        this.person.loading = true;
        this.peopleList = [];
        let r = JSON.parse(JSON.stringify(this.roomRequest));
        r.pageNum = pageNum ? pageNum : 1;
        r.pageSize = this.person.pageSize;
        r.houseNum = this.roomId;
        r.custId = this.personId;
        personMsg(r).then(res => {
          this.person.loading = false;
          if (res.sucess && res.data) {
            this.peopleList = res.data.records;
            this.person.total = res.data.total;
          }
        });
      }
    },
    // 工单查询翻页
    handleReportSizeChange(val) {
      this.report.pageSize = val;
      this.reportReportList();
    },
    handleReportCurrentChange(val) {
      this.report.currentPage = val;
      this.reportReportList(val);
    },
    reportReportList(pageNum) {
      this.report.loading = true;
      this.reportList = [];
      let r = JSON.parse(JSON.stringify(this.roomRequest));
      if(!r.pageNum){
        r.pageNum = pageNum ? pageNum : 1;
      }
      r.pageSize = this.report.pageSize;
      r.houseNum = this.roomId;
      r.custId = this.personId;
      formMsg(r).then(res => {
        this.report.loading = false;
        if (res.sucess && res.data) {
          this.reportList = res.data.records;
          this.report.total = res.data.total;
        }
      });
    }
  },
  created(){
    if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type=='entryReport'){
        this.$set(this.report,'currentPage',JSON.parse(sessionStorage.getItem('params')).pageNum);
      this.report.total = JSON.parse(sessionStorage.getItem('params')).total; 
    }
  },
  mounted() {
    let _this = this;
    window.goEntry = function(num) {
      _this.roomRequest = {};
      _this.roomRequest.telephone = num;
      _this.callTel = num;
      _this.getFloorMsg();
      _this.showUserTel();
    };
  },
  beforeRouteLeave(to, from, next) {
    console.log(to, from);
    next();
  }
};
</script>

<style scoped>
table tbody tr:hover {
  background-color: #d5dbe4;
}
.color {
  color: aqua;
}
</style>
<style>
.el-table .active td {
  background-color: #e63f3c !important;
  color: #fff !important;
}
.el-table tr.active:hover > td {
  background-color: #e63f3c !important;
  color: #fff !important;
}
</style>
