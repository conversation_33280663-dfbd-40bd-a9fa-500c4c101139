<template>
  <div class="interim-eport">
    <searchBar @searchItem="searchItem"></searchBar>
    <div class="bg-style mt-10">
      <div style="height:44px;line-height:44px;">
        <span class="redSide ml-10"> </span>
        <span class="ft-14 fw-bd">暂存工单</span>
      </div>
      <div style="padding-right:17px;width:100%;overflow:hidden;">
        <table v-loading="loading">
          <thead>
            <tr class="odd">
              <th width="100">工单编号</th>
              <th width="60">部门</th>
              <th width="60">一级分类</th>
              <th width="100">二级分类</th>
              <th width="60">项目</th>
              <th width="60">业主名称</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="floorList.length==0">
              <td colspan="6">暂无数据</td>
            </tr>
            <tr v-else v-for="(item,key) in floorList" :key='key' :class="key%2==0?'even':'odd'">
              <td style="color:#1D85FE;" @click="goDetail(item)">{{item.formNo}}</td>
              <td>{{item.deptName}}</td>
              <td>{{item.firstSortName}}</td>
              <td>{{item.secSortName}}</td>
              <td>{{item.project}}</td>
              <td>{{item.ownerName}}</td>
            </tr>
          </tbody>
        </table>
        <!-- 分页 -->
        <wcg-page :pageSize="pageSize" :currentPage="currentPage" :total="total" :floorList="floorList" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchInterimReportsListMonted"></wcg-page>
      </div>
    </div>
  </div>
</template>

<script>
import wcgPage from "@/components/wcgPage";
import searchBar from "@/components/searchBar";
import {
  searchInterimReport,
  areaReport,
  searchInterimReportsList
} from "@/api/port";

export default {
  components: { wcgPage, searchBar },
  data() {
    return {
      searchData: {},
      pageSize: 20,
      currentPage: 1,
      title: "报事暂存",
      floorList: [],
      options: [],
      total: 0,
      loading: false
    };
  },
  watch: {
    pageSize() {
      this.searchInterimReportsListMonted();
    }
  },
  methods: {
    goDetail(item) {
      this.$router.push({
        path: item.deptName=='IT'?"itReport":"addReport",
        query: { id: item.id, title: "报事暂存", path: this.$route.path }
      });
    },
    //搜索的信息 子组件传值
    searchItem(item) {
      this.searchData = {
        regionCode: item.area,
        cityCompanyCode: item.city,
        projectCode: item.project,
        buildingNoCode: item.floorNum,
        mobile: item.phone,
        startDate: item.createDate,
        endDate: item.endDate
      };
      //console.log(this.searchData);
      this.searchInterimReportsListMonted();
    },
    searchInterimReportsListMonted() {
      this.loading = true;
      this.floorList = [];
      this.searchData.pageSize = this.pageSize;
      this.searchData.pageNum = this.currentPage;
      searchInterimReportsList(this.searchData).then(res => {
        if (res.code == 200 && res.data) {
          this.floorList = res.data.records;
          this.currentPage = res.data.current;
          this.total = res.data.total;
          this.loading = false;
        } else {
          // this.$message({
          //   type: "error",
          //   message: res.message
          // });
          this.$confirm(res.message, {
              confirmButtonText: '确定',
              center: true,
              showClose:false,
              showCancelButton:false,
              confirmButtonClass:'confirm-reset-style',
          }).then(() => {})
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchInterimReportsListMonted();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchInterimReportsListMonted();
    }
  },
  created() {
    this.searchInterimReportsListMonted();
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.interim-eport {
  width: calc(100% - 10px);
  overflow: hidden;
}
table tbody tr:hover {
  background-color: #d5dbe4;
}
</style>
