<template>
    <div class="send-message">
        <div class="form-container">
            <el-form label-width="80px" label-position="right" :model="form" :rules="rules" ref="form" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="手机号:" prop="telNumbers">
                            <el-input class="break-word" type="textarea" v-model="form.telNumbers" @keyup.native="handlerTelKeyDown" :autosize="{ minRows: 1, maxRows: 10}"/>
                            <input type="file" @change="getExcelData" style="display:none;" ref="uploadHiddenInput">
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" style="line-height:40px;padding-left:20px;">
                        <div style="text-align:left;padding:5px 0 0 0;">
                            <a href="./static/files/telmodel.xlsx" target="_blank" class="btnDown">下载模板</a>
                            <span class="btnDown" @click="upload">上传</span>
                        </div>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="定时:">
                            <el-date-picker
                                v-model="form.settime"
                                type="datetime"
                                placeholder="选择日期时间"
                                align="right"
                                prefix-icon='el-icon-date'
                                :picker-options="pickerOptions"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="短信内容:" prop="message">
                            <el-input v-model="form.message" type="textarea" class="fixedSize break-word" :autosize="{ minRows: 5, maxRows: 10}"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <div style="text-align:left;padding:12px 0 0 10px;"><span class="btnDown" @click="saveSend">发送</span></div>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div style="padding:10px;width:100%;background-color:#ffffff;">
            <el-table :data="tableData" style="width: 100%" tooltip-effect="dark">
                <el-table-column type="index" label="序号" width="55"></el-table-column>
                <el-table-column prop="tel" label="手机号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="msg" label="短信内容" show-overflow-tooltip></el-table-column>
                <el-table-column prop="type" label="任务类型" width="80"></el-table-column>
                <el-table-column label="任务执行时间" width="200">
                    <template slot-scope="scope">
                        <span>{{scope.row.runtime | TimeMoment}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80"></el-table-column>
                <el-table-column prop="errMsg" label="失败原因" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <span class="btnDown" v-if="scope.row.status == '待发送'" @click="cancleSend(scope.row)">取消</span>
                    </template>
                </el-table-column>
            </el-table>
            <v-page
                :pageSize="pageSize"
                :total="total"
                @current-change="currentChange"
                @on-change="refreash"
                @size-change="changePageSize"
                :floorList="tableData"
            />
        </div>
    </div>
</template>

<script>
import {saveMessageInfo,getMessageInfoList,cancleSendTask} from '@/api/sendMessage'
import pageNation from '@/components/wcgPage'
import {isvalidPhone} from "@/api/form";
    export default{
        components:{
            'v-page':pageNation
        },
        data(){
            let that = this;
            var validPhone=(rule, value,callback)=>{
                if (!that.form.telNumbers){
                    callback(new Error('请输入电话号码'))
                }else  if (!isvalidPhone(that.form.telNumbers)){
                    callback(new Error('请输入正确的11位手机号码'))
                }else {
                    callback()
                }
            }
            return{
                form:{
                    telNumbers:'',
                    settime:null,
                    message:'',
                },
                pickerOptions:{
                    disabledDate(time) {
                        return time.getTime() <= Date.now() - 3600 * 1000 * 24;
                    },
                },
                tableData:[],
                pageNum:1,
                pageSize:20,
                total:0,
                rules: {
                    telNumbers: [
                        { required: true, trigger: 'blur', validator: validPhone }
                    ],
                    message: [
                        { required: true, message: '请输入发送短信内容', trigger: 'blur' },
                    ]
                }
            }
        },
        methods:{
            upload(){
                this.$refs.uploadHiddenInput.click();
            },
            currentChange(val){
               this.pageNum = val; 
               this.getPageList(val,this.pageSize);
            },
            changePageSize(val){
                this.pageSize = val;
                this.pageNum = 1
                this.getPageList(1,val);
            },
            handlerTelKeyDown(e){
                let keynum;
                if(window.event) // IE
                    keynum = e.keyCode
                else if(e.which) // Netscape/Firefox/Opera
                    keynum = e.which
                
                let telNumbers = this.form.telNumbers.substring(0,this.form.telNumbers.length-1);

                if((keynum == 49 || keynum == 97) && isvalidPhone(telNumbers)){
                    this.$set(this.form,"telNumbers",telNumbers + ',1')
                }
                this.form.telNumbers = this.form.telNumbers.replace(/，/g,",");
            },
            refreash(){
               this.getPageList(this.pageNum,this.pageSize); 
            },
            submitForm(form) {
                let flag = true;
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        flag = true;
                    } else {
                        flag =  false;
                    }
                });
                return flag;
            },
            cancleSend(row){
                this.$confirm('确定取消本条短信发送任务?',{
                    type:'warning',
                    confirmButtonText: '确定',
                    center: true,
                    showClose:false,
                    showCancelButton:false,
                    confirmButtonClass:'confirm-reset-style',
                }).then(() => {
                    let params = {
                       id:row.id 
                    }
                    cancleSendTask(params).then((res) => {
                        if(res.code == 200){
                            this.$confirm('取消短信发送任务成功!', {
                                confirmButtonText: '确定',
                                center: true,
                                showClose:false,
                                showCancelButton:false,
                                confirmButtonClass:'confirm-reset-style',
                            }).then(() => {
                                this.getPageList(1,20)
                            })
                        }else{
                            this.$confirm(res.message, {
                                confirmButtonText: '确定',
                                center: true,
                                showClose:false,
                                showCancelButton:false,
                                confirmButtonClass:'confirm-reset-style',
                            }).then(() => {

                            })
                        }
                    })
                }).catch(() => {

                })
            },
            getPageList(pageNum,pageSize){
                let params = {
                    pageNum:pageNum,
                    pageSize:pageSize
                }
                getMessageInfoList(params).then((res) => {
                    if(res.code == 200){
                        this.total = res.data.total;
                        this.tableData = res.data.records;
                    }else{
                        this.$confirm(res.message, {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {

                        })  
                    }
                })
            },
            saveSend(){
                if(this.submitForm('form')){
                    let data = {
                        tel:this.form.telNumbers,
                        msg:this.form.message,
                        runtime:this.form.settime
                    }
                    saveMessageInfo(data).then((res) => {
                        if(res.code == 200){
                            let tip = this.form.settime?'设置定时短信发送成功!':'短信发送成功!';
                            this.$confirm(tip, {
                                confirmButtonText: '确定',
                                center: true,
                                showClose:false,
                                showCancelButton:false,
                                confirmButtonClass:'confirm-reset-style',
                            }).then(() => {
                                this.form.telNumbers = '';
                                this.form.settime = '';
                                this.form.message = '';
                                this.getPageList(1,20);
                            })  
                        }
                    })
                }
            },
            getExcelData(data){
                let that = this;
                let files = data.target.files;
                let fileReader = new FileReader();
                fileReader.onload = (ev)=>{
                    try {
                        let d = ev.target.result,
                        workbook = that.$xlsx.read(d, {
                            type: 'binary'
                        }), // 以二进制流方式读取得到整份excel表格对象
                        persons = []; // 存储获取到的数据
                        var fromTo = '';
                        //遍历每张表读取
                        for (var sheet in workbook.Sheets) {
                            if (workbook.Sheets.hasOwnProperty(sheet)) {
                                fromTo = workbook.Sheets[sheet]['!ref'];
                                console.log(fromTo);
                                persons = persons.concat(that.$xlsx.utils.sheet_to_json(workbook.Sheets[sheet]));
                                // break; // 如果只取第一张表，就取消注释这行
                            }
                        }
                        console.log('读取到的数据',persons);
                        persons.map((item,index) => {
                            if(index ==0){
                                that.form.telNumbers = item.telNumber;
                            }else if(index > 0){
                                that.form.telNumbers += ','+item.telNumber;
                            }
                        })
                        data.target.value = '';
                    } catch (e) {
                        console.log('文件类型不正确');
                        return;
                    }
                }
                fileReader.readAsBinaryString(files[0]);
            }
        },
        created(){
            this.getPageList(1,20);
        }
    }
</script>

<style lang="scss" scoped="scoped">
.form-container{
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
}
.btnDown{
    margin:0 5px;
    margin-top:2px;
    text-decoration:none;
}
</style>
