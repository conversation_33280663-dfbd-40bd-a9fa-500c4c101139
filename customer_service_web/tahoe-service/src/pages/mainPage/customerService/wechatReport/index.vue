<template>
<div>
  <div class="search-bar">
    <div class="bg-style">
      <el-form
        ref="form"
        label-position="right"
        :model="searchData"
        label-width="80px"
        :disabled="disabled"
      >
        <div style="height:10px;"></div>
        <el-row>
          <el-col :span="5" :offset="0">
            <el-form-item label="区域">
              <el-select
                v-model="searchData.area"
                filterable
                placeholder="请选择"
                @change="getCityList"
              >
                <el-option
                  v-for="item in areaOptions"
                  :key="item.id"
                  :label="item.region"
                  :value="item.regionCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="城市">
              <el-select
                v-model="searchData.city"
                :disabled="cityprojectDisabled"
                filterable
                placeholder="请选择"
                @change="getProjectList"
              >
                <el-option
                  v-for="item in cityOptions"
                  :key="item.cityCompanyCode"
                  :label="item.cityCompany"
                  :value="item.cityCompanyCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="项目">
              <el-select
                v-model="searchData.project"
                :disabled="projectDisabled"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in projectOptions"
                  :key="item.projectCode"
                  :label="item.project"
                  :value="item.projectCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
            <el-col :span="5" :offset="0">
            <el-form-item label="电话">
                <el-input v-model="searchData.phone" placeholder="请输入" @blur="removeBlank"></el-input>
            </el-form-item>
            </el-col>
        </el-row>
        <el-row class="mt-10" v-show="showIf">
          <el-col :span="5">
            <el-form-item label="创建时间">
              <el-date-picker
                prefix-icon="el-icon-date"
                v-model="searchData.createDate"
                :picker-options="searchData.createDate"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="至">
              <el-date-picker
                prefix-icon="el-icon-date"
                v-model="searchData.endDate"
                :picker-options="searchData.endDate"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="23:59:59"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="mt-10">
          <el-col :span="24" style="text-align:right;padding-right:4%;">
            <button
              class="btnDown"
              style="margin-top:9px;"
              @click.prevent="searchItem(searchData)"
              :disabled="disabled"
            >查询</button>
            <button
              class="btnDown"
              style="margin-top:9px;margin-left:15px;"
              @click.prevent="reset"
            >重置</button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="ta-ct mt-10" style="position:relative;height:30px;">
              <hr style="background-color:#e6e6e6;height:1px;border:none;">
              <span class="txt" @click.prevent="showIf =!showIf">
                <i
                  style="font-size:18px;color:#E63F3C;"
                  :class="showIf?'el-icon-caret-top':'el-icon-caret-bottom'"
                ></i>
              </span>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
      <!-- 报事信息 -->
      <div class="bg-style mt-10 mb-20">
        <div style="height:44px;line-height:44px;">
          <span class="redSide ml-10"> </span>
          <span class="ft-14 fw-bd">报事信息</span>
        </div>
        <div style="width:100%;overflow:hidden;">
          <el-table 
          v-loading="report.loading"
          tooltip-effect="dark"
          stripe
          element-loading-text="拼命加载中"
          :data="reportList"
          @sort-change="sortByTime"
          style="width: 100%" @row-dblclick="rowDbclick">
          <el-table-column prop="region" label="区域" />
          <el-table-column prop="city" label="城市" />
          <el-table-column prop="project" label="项目" />
          <el-table-column prop="ownerName" label="报事人姓名" />
          <el-table-column prop="mobile" label="移动电话" />
          <el-table-column prop="creationDate" label="报事提交时间" sortable>
            <template slot-scope="scope">
              <span>{{scope.row.creationDate | TimeMoment}}</span>
            </template>
          </el-table-column>
        </el-table>
        <wcg-page :disabled="this.reportId?true:false" :pageSize="report.pageSize" :currentPage="report.currentPage" :total="report.total" :floorList="reportList" @size-change="handleReportSizeChange" @current-change="handleReportCurrentChange" @on-change="reportReportList"></wcg-page>
        </div>     
      </div>
 </div>
</template>

<script>
import {
  searchInterimReport,
  searchInterimReports,
  area,
  formMsg
} from "@/api/port";
import { cuSelectUserTel,userList } from "@/api/wsd";
import {wechatapi} from "@/api/wechat";
import wcgPage from "@/components/wcgPage";
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    wcgPage
  },
  watch: {
    "searchData.area"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.cityDisabled = false;
          this.projectOptions = [];
          this.searchData.city  = this.searchData.project = "";
          if(sessionStorage.getItem('params')){
            if(JSON.parse(sessionStorage.getItem('params')).searchData.regionCode && JSON.parse(sessionStorage.getItem('params')).type=='wechatReport'){
              this.getCityList(newval);
              if(sessionStorage.getItem('params') && newval == JSON.parse(sessionStorage.getItem('params')).searchData.regionCode){
                this.$set(this.searchData,'city',JSON.parse(sessionStorage.getItem('params')).searchData.cityCode)
              }else{
                this.$set(this.searchData,'city','')
              }
            }
          }
        }
      } else {
        this.cityprojectDisabled = true;
        this.cityDisabled = true;
        this.projectDisabled = true;
      }
    },
    "searchData.city"(newval, oldval) {
      if (newval != oldval) {
        this.searchData.project = "";
        if(sessionStorage.getItem('params')){
          if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).searchData.cityCode && JSON.parse(sessionStorage.getItem('params')).type=='wechatReport'){
            this.getProjectList(newval);
            if(newval == JSON.parse(sessionStorage.getItem('params')).searchData.cityCode){
              this.$set(this.searchData,'project',JSON.parse(sessionStorage.getItem('params')).searchData.projectCode)
            }else{
              this.$set(this.searchData,'project','')
            }
          }
        }
      }
      if (newval == "") {
        this.projectDisabled = true;
      } else {
        this.projectDisabled = false;
      }
    }
  },
  data() {
    return {
        reportList:[],
        roomRequest: {
        regionCode: "",//区域
        cityCode: "",//城市
        projectCode: "",//项目
        mobile: "",//移动电话
        creationStartDate: "",//开始时间
        creationEndDate: "",//结束时间
        pageSize: 20,
        pageNum: 1
      },
      user: {
        loading: false,
        pageSize: 20,
        total: 0
      },
      report: {
        loading: false,
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      searchData: {
        createDate: "",
        endDate: "",
        area: "",
        phone: "",
        step: "",
        city: "",
        project: "",
        orderBy:'DESC',
      },
      step: [],
      createDate: {
        disabledDate: time => {
          if (this.endDate) {
            return time.getTime() > new Date(this.endDate).getTime();
          } else {
            return time.getTime();
          }
        }
      },
      endDate: {
        disabledDate: time => {
          if (this.searchData.createDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < new Date(this.searchData.createDate).getTime()
            );
          } else {
            return time.getTime() > Date.now();
          }
        }
      },
      cityprojectDisabled: true,
      stepVisible: true,
      cityDisabled: true,
      projectDisabled: true,
      areaOptions: [],
      cityOptions: [],
      projectOptions: [],
      searchDataReset: {
        createDate: "",
        endDate: "",
        area: "",
        phone: "",
        step: "",
        city: "",
        project: ""      
    },
      showIf: false,
      reportId:null,
      username:null,
      userList: [],
      floorList: []
    };
  },
  methods: {
    //搜索的信息 子路由返回
    searchItem(item) {
      this.roomRequest.mobile = item.phone;
      this.roomRequest.creationStartDate = item.createDate;
      this.roomRequest.creationEndDate = item.endDate;
      this.roomRequest.regionCode = item.area;
      this.roomRequest.cityCode = item.city;
      this.roomRequest.projectCode= item.project;
      this.roomRequest.orderBy = item.orderBy?item.orderBy:'DESC';
      wechatapi(this.roomRequest).then((res) => {
      this.reportList=res.data.records
      this.report.total=res.data.total
    })
    },
    sortByTime(column){
      if(column.order == 'ascending'){//从小到大
        this.$set(this.searchData,'orderBy','ASC');
        this.searchItem(this.searchData);
      }else if(column.order == 'descending'){//从大到小
        this.$set(this.searchData,'orderBy','DESC');
        this.searchItem(this.searchData);
      }else{
        this.$set(this.searchData,'orderBy','DESC');
        this.searchItem(this.searchData);
      }
    },
    showUserTel() {
      this.searchUser = null;
      if (this.roomRequest.mobile) {
        cuSelectUserTel({ tel: this.roomRequest.mobile }).then(res => {
          if (res.sucess) this.searchUser = res.data;
        });
      }
    },
    // 工单查询翻页
    handleReportSizeChange(val) {
      this.report.pageSize = val;
      this.report.currentPage = 1;
      this.reportReportList();
    },
    handleReportCurrentChange(val) {
      this.report.currentPage = val;
      this.reportReportList(val);
    },
    reportReportList(pageNum) {
      this.report.loading = true;
      this.reportList = [];
      let r = JSON.parse(JSON.stringify(this.roomRequest));
      r.pageNum = pageNum ? pageNum : 1;
      r.pageSize = this.report.pageSize;
      wechatapi(r).then(res => {
        this.report.loading = false;
        if (res.sucess && res.data) {
          this.reportList = res.data.records;
          this.report.total = res.data.total;
        }
      });
    },
    removeBlank() {
      this.searchData.phone = this.searchData.phone.replace(/^\s*|\s*$/g, "");
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.area
      }).then(res => {
        // console.log(res.data);
        this.projectOptions = res.data;
      });
    },
    reset() {
      this.searchData = Object.assign({}, this.searchDataReset);
      this.$set(this.report,'currentPage',1);
      sessionStorage.removeItem('params');
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    getUser(item) {
      this.username = this.username && this.username == item.fdUsername ? null : item.fdUsername;
      this.getFloorMsg();
    },
    //跳转详情
    rowDbclick(res){
      let par = {
        type:'wechatReport',
        pageNum:this.report.currentPage,
        searchData:this.roomRequest,
        total:this.report.total
      }
      sessionStorage.setItem('params',JSON.stringify(par));
      this.$router.push({
        path: "addReport",
        query: {    
          id:res.id, 
          path: this.$route.path,
          reportChannelCode:"channelWX" //报事渠道
        }
      });
    },
    goDetail(item) {
      this.$router.push({
        path: item.deptName == "IT" ? "itReport" : "addReport",
        query: {
          id: item.id,
          title: "报事录入",
          path: this.$route.path,
          addListDis: true
        }
      });
    },
    // 获取信息
    getFloorMsg() {
    this.reportReportList();
    }
  },
  created() {
    this.areaList();
    if(sessionStorage.getItem('params') && JSON.parse(sessionStorage.getItem('params')).type == 'wechatReport'){
      //this.searchData.area
      let obj = JSON.parse(sessionStorage.getItem('params'));
      this.roomRequest = obj.searchData;
      this.$set(this.searchData,'area',obj.searchData.regionCode);
      this.$set(this.searchData,'city',obj.searchData.cityCode);
      this.$set(this.searchData,'project',obj.searchData.projectCode);
      this.$set(this.searchData,'phone',obj.searchData.mobile);
      this.$set(this.searchData,'createDate',obj.searchData.creationStartDate);
      this.$set(this.searchData,'endDate',obj.searchData.creationEndDate);
      this.$set(this.report,'currentPage',obj.pageNum);
      this.reportReportList(obj.pageNum);
      return
    }
    wechatapi(this.roomRequest).then((res) => {
      this.reportList=res.data.records
      this.report.total=res.data.total
    })
  },
  mounted() {
    let _this = this;
    window.goEntry = function(num) {
      _this.roomRequest = {};
      _this.roomRequest.telephone = num;
      _this.callTel = num;
      _this.getFloorMsg();
      _this.showUserTel();
    };
  }
};
</script>

<style scoped>
/* .search-bar >>> .el-input {
  width: 128px;
} */
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.search-bar >>> .el-input__inner {
  height: 24px !important;
  line-height: 24px;
}
.search-bar >>> .el-select-dropdown__item {
  font-size: 12px !important;
  padding: 0 15px;
  height: 30px;
  line-height: 30px;
}
.el-date-editor,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
