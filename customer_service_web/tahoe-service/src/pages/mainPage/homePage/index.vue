<template>
    <div id="homepage" style="width: calc( 100% - 10px);">
        <div class="table-container">
            <div class="table-header">
                <ul class="nav" style="position:absolute;bottom:0">
                    <li :class="tab=='total'?'navActive':''" @click="handleExchangeTab('total')"><span>报事总量</span></li>
                    <li :class="tab=='consult'?'navActive':''" @click="handleExchangeTab('consult')"><span>咨询</span></li>
                    <li :class="tab=='complain'?'navActive':''" @click="handleExchangeTab('complain')"><span>投诉</span></li>
                    <li :class="tab=='repair'?'navActive':''" @click="handleExchangeTab('repair')"><span>报修</span></li>
                </ul>
                <!-- <el-select style="width:75px;float:right;" class="reset-select-lineheight" v-model="timeFilter" placeholder="请选择" @change="filterByTime">
                    <el-option
                    v-for="item in timeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select> -->
                <el-date-picker style="float:right;width:250px!important;" class="reset-select-lineheight" @change="filterByTime"
                    v-model="timeFilter"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    >
                </el-date-picker>
            </div>
            <div class="table-body"
                v-loading="loading"
                element-loading-text="玩命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(255, 255, 255, 0.8)">
                <el-table :data="tableData" id="firstLevelTable" style="width:100%;" @row-click="rowClick" row-key="id" :expand-row-keys="expands">
                    <el-table-column type="expand" class="width0" width="0" align="left">
                        <template slot-scope="scope">
                            <!--第二层表开始-->
                            <el-table :data="scope.row.list" id="secondLevelTable" style="width: 100%" :show-header="false" @row-click="rowClick" row-key="id" :expand-row-keys="expands">
                                <el-table-column type="expand" class="width0" width="0" align="center">
                                    <template slot-scope="props">
                                    <!--第三层表开始-->
                                        <el-table :data="props.row.list" id="thirdLevelTable" style="width: 100%" :show-header="false" @row-click="rowClick" row-key="id" :expand-row-keys="expands">
                                            <el-table-column width="0" class="width0" align="right"></el-table-column>
                                            <el-table-column width="150" label="区域/城市/项目">
                                                <template slot-scope="scope">
                                                    <span style="display:inline-block;padding-left:30px;">
                                                      <span>{{scope.row.masterSlaveName}}</span>
                                                      <!-- <a class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'project')">{{scope.row.masterSlaveName}}</a> -->
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column v-for="(item,index) in tableHeader" v-if="item.isShow" :width="item.width" :label="item.label" :prop="item.prop" :key="index" align="right">
                                                <template slot-scope="scope">
                                                    <a v-if="item.prop=='totalNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'project-'+item.prop)">{{scope.row.totalNum}}</a>
                                                    <a v-if="item.prop=='finishNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'project-'+item.prop)">{{scope.row.finishNum}}</a>
                                                    <a v-if="item.prop=='doingNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'project-'+item.prop)">{{scope.row.doingNum}}</a>
                                                    <a v-if="item.prop=='levelUpNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'project-'+item.prop)">{{scope.row.levelUpNum}}</a>
                                                    <a v-if="item.prop=='reworkNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'project-'+item.prop)">{{scope.row.reworkNum}}</a>
                                                    <span v-if="item.prop=='finsihRate'">{{scope.row.finsihRate}}</span>
                                                    <span v-if="item.prop=='levelUpRate'" >{{scope.row.levelUpRate}}</span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    <!--第三层表结束-->
                                    </template>
                                </el-table-column>
                                <el-table-column width="150" label="区域/城市/项目" >
                                    <template slot-scope="scope">
                                        <span style="display:inline-block;padding-left:15px;">
                                          <i :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                                          <!-- <a class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'city')">{{scope.row.masterSlaveName}}</a> -->
                                          <span>{{scope.row.masterSlaveName}}</span>
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column v-for="(item,index) in tableHeader" v-if="item.isShow" :width="item.width" :label="item.label" :prop="item.prop" :key="index" align="right">
                                    <template slot-scope="scope">
                                        <a v-if="item.prop=='totalNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'city-'+item.prop)">{{scope.row.totalNum}}</a>
                                        <a v-if="item.prop=='finishNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'city-'+item.prop)">{{scope.row.finishNum}}</a>
                                        <a v-if="item.prop=='doingNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'city-'+item.prop)">{{scope.row.doingNum}}</a>
                                        <a v-if="item.prop=='levelUpNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'city-'+item.prop)">{{scope.row.levelUpNum}}</a>
                                        <a v-if="item.prop=='reworkNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'city-'+item.prop)">{{scope.row.reworkNum}}</a>
                                        <span v-if="item.prop=='finsihRate'">{{scope.row.finsihRate}}</span>
                                        <span v-if="item.prop=='levelUpRate'" >{{scope.row.levelUpRate}}</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!--第二层表结束-->
                        </template>
                    </el-table-column>
                    <el-table-column width="150" label="区域/城市/项目" align="left">
                        <template slot-scope="scope">
                            <i v-if="scope.row.masterSlaveName != '总计'" :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                            <!-- <a class="link-btn" v-if="scope.row.masterSlaveName != '总计'" href="javascript:;" @click.stop="toReportDetail(scope.row,'area')">{{scope.row.masterSlaveName}}</a> -->
                            <span>{{scope.row.masterSlaveName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-for="(item,index) in tableHeader" v-if="item.isShow" :width="item.width" :label="item.label" :prop="item.prop" :key="index" align="right">
                        <template slot-scope="scope">
                            <a v-if="item.prop=='totalNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'area-'+item.prop)">{{scope.row.totalNum}}</a>
                            <a v-if="item.prop=='finishNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'area-'+item.prop)">{{scope.row.finishNum}}</a>
                            <a v-if="item.prop=='doingNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'area-'+item.prop)">{{scope.row.doingNum}}</a>
                            <a v-if="item.prop=='levelUpNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'area-'+item.prop)">{{scope.row.levelUpNum}}</a>
                            <a v-if="item.prop=='reworkNum'" class="link-btn" href="javascript:;" @click.stop="toReportDetail(scope.row,'area-'+item.prop)">{{scope.row.reworkNum}}</a>
                            <!-- <span v-if="item.prop=='totalNum' && scope.row.masterSlaveName=='总计'">{{scope.row.totalNum}}</span>
                            <span v-if="item.prop=='finishNum' && scope.row.masterSlaveName=='总计'">{{scope.row.finishNum}}</span>
                            <span v-if="item.prop=='doingNum' && scope.row.masterSlaveName=='总计'">{{scope.row.doingNum}}</span>
                            <span v-if="item.prop=='levelUpNum' && scope.row.masterSlaveName=='总计'">{{scope.row.levelUpNum}}</span>
                            <span v-if="item.prop=='reworkNum' && scope.row.masterSlaveName=='总计'">{{scope.row.reworkNum}}</span> -->
                            <span v-if="item.prop=='finsihRate'">{{scope.row.finsihRate}}</span>
                            <span v-if="item.prop=='levelUpRate'" >{{scope.row.levelUpRate}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <!-- 统计报表区域开始 -->
        <div class="report-container">
            <el-row>
                <el-col :span="6">
                    <div class="single-report-container">
                        <div class="th-title-container"><span class="th-title">累计报事完成率</span></div>
                        <div id="sumFinishRate" ref="sumFinishRate" style="width:100%;height:237px;" v-loading="loading1" element-loading-text="玩命加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"></div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="single-report-container">
                        <div class="th-title-container"><span class="th-title">累计报事分类</span></div>
                        <div id="sumClassify" ref="sumClassify" style="width:100%;height:237px;" v-loading="loading1" element-loading-text="玩命加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"></div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="single-report-container">
                        <div class="th-title-container"><span class="th-title">累计各区域{{tab=='total'?'报事':tab=='consult'?'咨询':tab=='complain'?'投诉':'报修'}}</span></div>
                        <div id="sumArea" ref="sumArea" style="width:100%;height:237px;" v-loading="loading2" element-loading-text="玩命加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"></div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="single-report-container" style="margin-right:0">
                        <div class="th-title-container"><span class="th-title">报事满意度</span></div>
                        <div id="agreeRate" ref="agreeRate" style="width:100%;height:237px;" v-loading="loading1" element-loading-text="玩命加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"></div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 统计报表区域结束 -->
    </div>
</template>
<script>
import {getTableList, getReportInfo, getAreaTotal} from "@/api/homePage"
    export default{
        data(){
            return{
                tab:'total',
                loading:true,
                loading1:true,
                loading2:true,
                expands: [],
                tableData:[],
                totalTableData:[],
                timeFilter:[
                    new Date().getFullYear()+'-01-01',
                    new Date().getFullYear()+'-12-31'
                ],
                timeOptions:[
                    {label:'全部',value:1},
                    {label:'本年',value:2},
                    {label:'本月',value:3}
                ],
                tableHeader:[
                    //{label:'区域/城市/项目',prop:'masterSlaveName',width:136},
                    {label:'报事总量',prop:'totalNum', width:'',isShow:true},
                    {label:'完成总量',prop:'finishNum',width:'',isShow:true},
                    {label:'处理中总量',prop:'doingNum',width:'',isShow:true},
                    {label:'升级总量',prop:'levelUpNum',width:'',isShow:true},
                    {label:'返工总量',prop:'reworkNum',width:'',isShow:true},
                    {label:'完成率(%)',prop:'finsihRate',width:'',isShow:true},
                    {label:'升级率(%)',prop:'levelUpRate',width:'',isShow:true},
                ]
            }
        },
        //expand-change
        //row-click
        methods:{
            toReportDetail(row,type){
                if(row.masterSlaveName == '总计'){
                    this.$router.push({
                        path:'/customerService/expForm',
                        query:{
                            type:type,
                            class:this.tab,
                            //time:this.timeFilter == 1?'all':this.timeFilter == 2?'year':'month',
                            startTime:this.timeFilter?this.timeFilter[0]:null,
                            endTime:this.timeFilter?this.timeFilter[1]:null,
                            isSum:true,
                            isFromHome:1,
                        },
                    })
                }else{
                    this.$router.push({
                        path:'/customerService/expForm',
                        query:{
                            masterCode:row.masterCode,
                            type:type,
                            class:this.tab,
                            //time:this.timeFilter == 1?'all':this.timeFilter == 2?'year':'month',
                            startTime:this.timeFilter?this.timeFilter[0]:null,
                            endTime:this.timeFilter?this.timeFilter[1]:null,
                            isFromHome:1,
                        }
                    })
                }
            },
            filterByTime(val){
                this.timeFilter = val;
                this.getReportData();
                let p = this.tab=='total'?'':this.tab;
                this.getTableList(p);
            },
            getTableList(param){
                this.loading = true;
                let endTim = this.timeFilter ? new Date((new Date((new Date(this.timeFilter[1]).valueOf())/1000+86400*1)*1000)):'';//new Date((new Date(this.timeFilter[1]).valueOf())/1000+86400*1)
                let yy = this.timeFilter ? endTim.getFullYear() : '';
                let MM = this.timeFilter ? Number(endTim.getMonth() + 1) >= 10 ? Number(endTim.getMonth() + 1) : '0'+Number(endTim.getMonth() + 1) : '';
                let dd = this.timeFilter ? endTim.getDate() >=10 ? endTim.getDate() : '0'+endTim.getDate() : '';
                let params = {
                   condition:param,
                   //type:this.timeFilter
                   startDate:this.timeFilter?this.timeFilter[0]:'',
                   endDate:this.timeFilter?yy+'-'+MM+'-'+dd:'',
                }
                getTableList(params).then((res) => {
                    if(res.code == 200){
                        this.tableData = res.data;
                        //this.getSumArea(res.data);
                        this.getSetTotal(params);
                        this.loading2 = false;
                    }else{
                        this.$confirm(res.message, {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {})
                    }
                })
            },
            getSetTotal(params){
                let totalNum = 0;
                let finishNum = 0;
                let doingNum = 0;
                let levelUpNum = 0;
                let reworkNum = 0;
                let finsihRate = 0;
                let levelUpRate = 0;
                this.tableData.map((item) => {
                    totalNum += Number(item.totalNum);
                    finishNum += Number(item.finishNum);
                    doingNum += Number(item.doingNum);
                    levelUpNum += Number(item.levelUpNum);
                    reworkNum += Number(item.reworkNum);
                    //finsihRate += Number(item.finsihRate);
                    //levelUpRate += Number(item.levelUpRate);
                    finsihRate = finishNum/totalNum; 
                    levelUpRate = levelUpNum/totalNum;
                    
                    let p = JSON.parse(JSON.stringify(params))
                    p.organName = item.masterCode;
                    getTableList(p).then(o => { // 查城市
                        if(o.code == 200){
                            item.list = o.data?o.data:[];
                            item.list.map (i => { // 查项目
                                let pc = JSON.parse(JSON.stringify(p))
                                pc.cityName = i.masterCode
                                getTableList(pc).then(c => {
                                    if(c.code == 200){
                                        i.list = c.data;
                                    }
                                })
                            })
                        }
                    })
                })
                let total = {
                    masterSlaveName:'总计',
                    totalNum:totalNum,
                    finishNum:finishNum,
                    doingNum:doingNum,
                    levelUpNum:levelUpNum,
                    reworkNum:reworkNum,
                    finsihRate:finsihRate?Number(finsihRate*100).toFixed(1):'0.0',
                    levelUpRate:levelUpRate?Number(levelUpRate*100).toFixed(1):'0.0'
                }
                this.tableData.push(total);
                this.loading = false;
            },
            getReportData(){
                this.loading1 = true;
                this.loading2 = true;
                let endTim = this.timeFilter ? new Date((new Date((new Date(this.timeFilter[1]).valueOf())/1000+86400*1)*1000)) : '';//new Date((new Date(this.timeFilter[1]).valueOf())/1000+86400*1)
                let yy = this.timeFilter ? endTim.getFullYear() : '';
                let MM = this.timeFilter ? Number(endTim.getMonth() + 1) >= 10 ? Number(endTim.getMonth() + 1) : '0'+Number(endTim.getMonth() + 1) : '';
                let dd = this.timeFilter ? endTim.getDate() >=10 ? endTim.getDate() : '0'+endTim.getDate() : '';
                let params = {
                    year:(new Date().getFullYear()).toString(),
                    //type:this.timeFilter
                    startDate:this.timeFilter?this.timeFilter[0]:'',
                    endDate:this.timeFilter?yy+'-'+MM+'-'+dd:'',
                }
                getReportInfo(params).then((res) => {
                    if(res.code == 200){
                        this.getSumFinishRate({});//累计报事完成率
                        this.getSumClassify({});//累计报事分类
                        this.getAgreeRate({});//报事满意度
                        setTimeout(() => {
                            this.getSumFinishRate(res.data);//累计报事完成率
                            this.getSumClassify(res.data);//累计报事分类
                            this.getAgreeRate(res.data);//报事满意度
                            this.loading1 = false;
                        }, 200);
                    }
                })
                let p = {
                    //type:this.timeFilter
                    startDate:this.timeFilter?this.timeFilter[0]:'',
                    endDate:this.timeFilter?yy+'-'+MM+'-'+dd:'',
                }
                getAreaTotal(p).then((res) => {
                    if(res.code == 200){
                        this.getSumArea(res.data);
                    }
                })
            },
            handleExchangeTab(val){
                this.tab = val;
                if(val == 'consult'){
                    this.tableHeader.map((item) => {
                        if(item.label=='升级总量' || item.label=='返工总量' || item.label=='升级率(%)'){
                            item.isShow = false;
                        }
                    })
                }else{
                    this.tableHeader.map(item => {
                        item.isShow = true;
                    })
                }
                if(val != 'total'){
                    this.getTableList(val);
                }else{
                    this.getTableList('');
                }
            },
            rowClick(row, event, column) {
                Array.prototype.remove = function (val) {
                    let index = this.indexOf(val);
                    if (index > -1) {
                        this.splice(index, 1);
                    }
                };
                if (this.expands.indexOf(row.id) < 0 && row.list != null) {
                    this.expands.push(row.id);
                    row.expand = true
                } else {
                    this.expands.remove(row.id);
                    row.expand = false
                }

            },
            getSumClassify(param){//累计报事分类
                let myChart = this.$echarts.init(document.getElementById('sumClassify'))
                let consult = param.consult;
                let complaints = param.complaints;
                let repair = param.repair;
                let adviceAndpraise = param.adviceAndpraise

                let option = {
                    tooltip: {
                        trigger: 'item',
                        confine:true,
                        formatter: (data) => {
                          return data.seriesName +'<br/>'+data.name+' '+data.value+'('+data.percent.toFixed(1)+'%)'
                        }
                    },
                    legend: [
                        {
                            bottom:20,
                            left:'25%',
                            orient: 'horizontal',
                            data: [{name:'咨询',icon:'circle'}],
                            itemWidth:10,
                            itemHeight:10,
                            borderRadius:5,
                            selectedMode:false,
                        },
                        {
                            bottom:20,
                            left:'55%',
                            orient: 'horizontal',
                            data: [{name:'投诉',icon:'circle'}],
                            itemWidth:10,
                            itemHeight:10,
                            borderRadius:5,
                            selectedMode:false,
                        },
                        {
                            bottom:0,
                            left:'25%',
                            orient: 'horizontal',
                            data: [{name:'报修',icon:'circle'}],
                            itemWidth:10,
                            itemHeight:10,
                            borderRadius:5,
                            selectedMode:false,
                        },
                        {
                            bottom:0,
                            left:'55%',
                            orient: 'horizontal',
                            data: [{name:'表扬与建议',icon:'circle'}],
                            itemWidth:10,
                            itemHeight:10,
                            borderRadius:5,
                            selectedMode:false,
                        }
                    ],
                    series: [
                        {
                            name:'累计报事分类',
                            type:'pie',
                            radius: ['43%', '50%'],
                            avoidLabelOverlap: false,
                            hoverAnimation:false,
                            labelLine:{
                                normal: {
                                    length:5,
                                    length2:6,
                                },
                            },
                            label: {
                                normal: {
                                    show:false,
                                    position: 'inside',
                                    formatter: (data) => {
                                      return data.percent.toFixed(1)+'%'
                                    },
                                },
                                emphasis: {
                                    show: false,
                                    textStyle: {
                                        fontSize: '12',
                                    },
                                },
                            },
                            data:[
                                {value:consult, name:'咨询',labelLine:{show:false},label:{show:true,position:'outside',color:'#000',align:'center'},itemStyle:{color:'#607D8B'},},
                                {value:complaints, name:'投诉',labelLine:{show:false},label:{show:true,position:'outside',color:'#000',align:'center'},itemStyle:{color:'#CDDC39'},},
                                {value:repair, name:'报修',labelLine:{show:false},label:{show:true,position:'outside',color:'#000',align:'center'},itemStyle:{color:'#FF8F00'},},
                                {value:adviceAndpraise, name:'表扬与建议',labelLine:{show:false},label:{show:true,position:'outside',color:'#000',align:'center'},itemStyle:{color:'#FBC92D'},},
                            ]
                        }
                    ]
                }

                option.series[0].data.map((item) => {
                  if(item.value == 0){
                    item.label.show = false;
                  }else{
                    item.label.show = true;
                  }
                })

                myChart.setOption(option,true)
            },
            getSumFinishRate(param){//累计报事完成率
                let myChart = this.$echarts.init(document.getElementById('sumFinishRate'))
                let finishNum = param.finshNum;
                let doingNum = param.doingNum - param.levelUpNum;
                let levelUpNum = param.levelUpNum;
                let option = {
                    tooltip: {
                        trigger: 'item',
                        confine:true,
                        formatter: function(data){
                          if(data.name=='处理中'){
                              return data.seriesName +'<br/>'+data.name+'(不含升级)'+' '+data.value+'('+data.percent.toFixed(1)+'%)'
                          }else{
                              return data.seriesName +'<br/>'+data.name+' '+data.value+'('+data.percent.toFixed(1)+'%)'
                          }
                        }
                    },
                    legend: {
                        type:'scroll',
                        orient: 'horizontal',
                        y: 'bottom',
                        itemWidth:10,
                        itemHeight:10,
                        borderRadius:5,
                        selectedMode:false,
                        data:[
                            {name:'已完成',icon:'circle'},
                            {name:'处理中',icon:'circle'},
                            {name:'已升级',icon:'circle'}
                        ]
                    },
                    series: [
                        {
                            name:'累计报事完成率',
                            type:'pie',
                            radius: ['43%', '50%'],
                            avoidLabelOverlap: false,
                            hoverAnimation:false,
                            label: {
                                normal: {
                                    show: false,
                                    position: 'center',
                                    formatter: "{d|d}%\n{b|b}"
                                },
                                emphasis: {
                                    show: true,
                                    formatter: (data) => {
                                        return "{percent|"+data.percent.toFixed(1)+'%}\n{name|' + data.name +"}";
                                    },
                                    rich:{
                                        percent:{fontSize: 20,color:'#000000'},
                                        name:{fontSize: 12,color:'#9B9B9B'}
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false
                                }
                            },
                            data:[
                                {value:finishNum, name:'已完成',itemStyle:{color:'#4F6EF6'}},
                                {value:doingNum, name:'处理中',itemStyle:{color:'#F4B63A'}},
                                {value:levelUpNum, name:'已升级',itemStyle:{color:'#E4441E'}},
                            ]
                        }
                    ]
                }
                myChart.setOption(option,true)

                //default selected
                this.defaultSet(myChart,option);
            },
            getSumArea(arr){//累计分区域报事
                let myChart = this.$echarts.init(document.getElementById('sumArea'))
                let xAxisData = new Array();
                let finishNum = new Array();
                let noFinishNum = new Array();
                arr.map((item) => {
                   xAxisData.push(item.masterSlaveName);
                    if(item.totalNum == 0){
                       finishNum.push({value:0,itemStyle:{color:'#FBDCA8'}});
                       noFinishNum.push({value:100,itemStyle:{color:'#E6E6E6'}});
                    }else{
                       finishNum.push({value:(item.finishNum/item.totalNum)*100,itemStyle:{color:'#FBDCA8'}});
                       noFinishNum.push({value:100-((item.finishNum/item.totalNum)*100),itemStyle:{color:'#E6E6E6'}});
                   }
                })
                myChart.setOption({
                    tooltip: {
                        trigger: 'item',
                        confine:true,
                        formatter: (data) => {
                          return data.seriesName+'\n'+Number(data.value).toFixed(1)+'%'
                        }
                    },
                    legend: [
                        {
                            left:'20%',
                            orient: 'horizontal',
                            y: 'bottom',
                            data: [{name:'已完成量',icon:'circle'},],
                            itemWidth:10,
                            itemHeight:10,
                            borderRadius:5,
                            selectedMode:false,
                        },
                        {
                            left:'60%',
                            orient: 'horizontal',
                            y: 'bottom',
                            itemWidth:10,
                            itemHeight:10,
                            borderRadius:5,
                            data:[{name:'未完成量',icon:'circle'}],
                            selectedMode:false,
                        }

                    ],
                    xAxis: {
                        type:'category',
                        data:xAxisData,
                        axisLine:{
                            show:false,
                        },
                        splitLine: {
                            show: false
                        },
                        axisLabel: {
                            formatter:function(val){
                                var strs = val.split(''); //字符串数组
                                var str = ''
                                for (var i = 0, s; s = strs[i++];) { //遍历字符串数组
                                    str += s; if (!(i % 2)) str += '\n';
                                }
                                return str
                            },
                            color:'#9b9b9b'
                        },
                        axisTick: { show: false }
                    },
                    yAxis: {
                        show:false,
                        axisLine:{
                            show:false,
                        },
                        splitLine: {
                            show: false
                        },
                    },
                    series: [
                        {
                            name: '已完成量',
                            type: 'bar',
                            stack: 'one',
                            barWidth:10,
                            data: finishNum,
                            itemStyle:{color:'#FBDCA8'}
                        },
                        {
                            name: '未完成量',
                            type: 'bar',
                            stack: 'one',
                            barWidth:10,
                            data: noFinishNum,
                            itemStyle: {
                                normal: {
                                    color:'#E6E6E6',
                                    barBorderRadius:[10, 10, 0, 0],
                                },
                            },
                            label:{
                                show:true,
                                position:'top',
                                color:'#000',
                                distance:10,
                                formatter:(data) => {
                                  return (100 - Number(data.value)).toFixed(1)+'%';
                                }
                            }
                        }
                    ]
                },true)
            },
            getAgreeRate(param){//报事满意度
                let myChart = this.$echarts.init(document.getElementById('agreeRate'))
                //let finishNum = param.finshNum;
                let satisfaction = param.satisfaction;
                let noSatisfaction = 100 - satisfaction;

                let option = {
                    tooltip: {
                        trigger: 'item',
                        show:'true',
                        confine:true,
                        formatter: (data) => {
                          //"{d}%"
                          if(data.name && data.name != undefined && data.name != ''){
                            return Number(data.percent).toFixed(1);
                          }else{
                            return ''
                          }
                        }
                    },
                    legend: {
                        orient: 'horizontal',
                        y: 'bottom',
                        data:[{name:'满意度',icon:'circle'}],
                        itemWidth:10,
                        itemHeight:10,
                        borderRadius:5,
                        selectedMode:false,
                    },
                    series: [
                        {
                            name:'报事满意度',
                            type:'pie',
                            radius: ['43%', '50%'],
                            avoidLabelOverlap: false,
                            hoverAnimation:false,
                            tooltip: {
                                show: false
                            },
                            label: {
                                normal: {
                                    show: false,
                                    position: 'center',
                                    formatter: "{d|d}\n{b|b}"
                                },
                                emphasis: {
                                    show: true,
                                    formatter: (data) => {
                                        if(data.percent == satisfaction)
                                            return "{percent|"+data.percent.toFixed(1)+'}\n{name|' + data.name +"}";
                                        else
                                            return "{percent|"+(100 - data.percent).toFixed(1)+'}\n{name|' + data.name +"}";
                                    },
                                    rich:{
                                        percent:{fontSize: 20,color:'#000000'},
                                        name:{fontSize: 12,color:'#9B9B9B'}
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false
                                }
                            },
                            data:[
                                {value:satisfaction, name:'满意度',itemStyle:{color:'#9C27B0'}},
                                {value:noSatisfaction, name:'满意度',itemStyle:{color:'#F5A623'}},
                            ]
                        }
                    ]
                }

                myChart.setOption(option,true)
                //default selected
                this.defaultSet(myChart,option);

            },
            defaultSet(myChart,option){
              myChart.dispatchAction({
                  type: 'highlight',
                  seriesIndex: 0,
                  dataIndex: 0
              });

              //记录上次高亮的索引
              let lastMouseOverIndex=null;
              // mouseover事件，记录当前数据索引并取消其他高亮，over在out之后
              myChart.on('mouseover', function (params) {
                  var dataLen = option.series[0].data.length;
                  lastMouseOverIndex = params.dataIndex;
                  for(var i=0;i<dataLen;i++){
                      if(i!= params.dataIndex){
                          myChart.dispatchAction({
                              type: 'downplay',
                              seriesIndex: 0,
                              dataIndex: i
                          })
                      }
                  }
              });
              // mouseout事件，将上次的高亮
              myChart.on('mouseout', function (params) {
                  myChart.dispatchAction({
                      type: 'highlight',
                      seriesIndex: 0,
                      dataIndex: 0//lastMouseOverIndex
                  })
              });
            }

        },
        created() {
            this.getTableList('');
            this.getReportData();
        },
        mounted() {

        },
    }
</script>
<style scoped="scoped">
.table-container{
    border-radius:4px;
    background-color:#ffffff;
}
.single-report-container{
    border-radius:4px;
    background-color:#ffffff;
    margin-top:10px;
    margin-right:10px;
    height:300px;
    padding-top:10px;
    box-shadow:0 1px 6px 0 rgba(0,0,0,0.1)
}
.table-header{
    position:relative;
    padding-left:15px;
    height:30px;
    line-height:30px;
    border-bottom:solid thin #E6E6E6;
    background-color:#efefef;
}
.th-title-container{
    border-bottom:solid 1px #E6E6E6;
    margin-top:5px;
}
.th-title{
    display:inline-block;
    height:13px;
    line-height:14px;
    font-size:14px;
    padding:3px;
    border-left:solid 2px #E63F3C;
    margin-left:23px;
    margin-bottom:15px;
}
.el-icon-minus,.el-icon-plus{
 font-size:12px;
 color:#E63F3C;
 font-weight:bold;
}
.nav li{
    display:block;
    float:left;
    width:180px;
    height:30px;
    text-align:center;
    line-height:30px;
    color:#ffffff;
    font-weight:bold;
    font-size:14px;
    background:url(../../../assets/images/homePage/<EMAIL>);
    background-size:100% 100%;
    border-radius:3px;
    cursor: pointer;
}
.nav li:hover{
    background:url(../../../assets/images/homePage/<EMAIL>);
    background-size:100% 100%;
}
.nav li span{
    display:inline-block;
    height:13px;
    line-height:13px;
    padding:0 5px;
    padding-top:3px;
    font-size:13px;
}
.table-body{
    box-shadow: 0 1px 6px 0 rgba(0,0,0,0.1);
}
.navActive{
    background:url(../../../assets/images/homePage/<EMAIL>)!important;
    background-size:100% 100%!important;
    color:#4A4A4A!important;
}
.navActive span{
    border-left:solid 2px #E63F3C;
}
.link-btn{
    display:inline-block;
    min-width:25px;
    color:#1D85FE;
    font-family:'MicrosoftYaHei';
    text-decoration:none;
}
</style>