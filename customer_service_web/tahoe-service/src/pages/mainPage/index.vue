<template>
    <div class="main-page" id="main-page">
        <iframe v-if="isFourZeroZreo" id="if-rame" ref="iframeContent" src="/static/softphone/index.html" style="position:fixed;top:0px;left:0px;height:70px;width:100%;z-index:10000;" frameborder="0" scrolling="auto"></iframe>
        <div class="header" id="homepage-header" :style="isFourZeroZreo?'position:relative':'position:relative;margin-top:0'">
            <img style="display:inline-block;width:155px;height:auto; margin-top: 10px" src="@/assets/images/logo.png" alt="">
            <span class="fl">{{title}}</span>
            <h3 class="User" style="color:black;display:block">您好:&nbsp;
                <span style="color:red !important;position:relative;margin-right:5px;">{{name}}</span> <img src="../../assets/images/down.png" style="width:10px;height:10px;marginLeft:5px;marginTop:28px;float:right;" @click="change()">
                <span :class="isFourZeroZreo?'logOut400':'logOut'" v-show="flag" id="logOutBtn">
                    <img class="logOut_img" src="../../assets/images/log_out.png">
                    <a href="javascript:;" @click="setCookieClear()">退出</a>
                </span>
            </h3>

        </div>
        <div class="fl bg-style" style="width:240px;border-radius:0px;box-shadow:1px 2px 4px 0 rgba(0,0,0,0.1)" ref="leftMnueArea">
            <ul>
                <li style="position:relative;height:83px;line-height:83px;">
                    <h2 style="padding-left:20px;">客服管理系统</h2>
                    <!-- <img style="width:15px;height:15px;position:absolute;right:18px;top:21px;" :src="require('@/assets/images/menu/souqi.png')" alt=""> -->
                </li>
                <li style="position:relative;" v-for="(item,key) in mainTabs" :class="['leftMenu',activeId==item.id?'active':'']" @mouseover="showId=item.id" @mouseout="showId=''" @click="handleClick($event,item)" :id="item.id" :key="key">
                    <hr style="border:solid 1px #F0F0F0;width:221px;top:0px;right:0px;position:absolute;" v-show="activeId==item.id" />
                    <div v-show="activeId==item.id" style="height:100%;border-left:solid 3px #E63F3C;position:absolute;top:0;left:0px;"></div>
                    <img class="icon_pic" :src="!item.attributes.icoCode?'':activeId==item.id?require('@/assets/images/menu/'+item.attributes.icoCode+'_selected.png'):require('@/assets/images/menu/'+item.attributes.icoCode+'.png')">
                    <span>{{item.text}}</span>
                    <span v-show="item.text != '首页' && item.text != '系统管理'" class='fr mr-20'>></span>
                    <ul :class="isMenuScroll?'secondMenu scroll-sec-menu':'secondMenu'" v-show="showId==item.id" style="z-index:999999 !important">
                        <li v-for="(secondItem,key) in item.children.filter(i=>i.attributes && i.attributes.url != '#')" :class="activeId1==secondItem.id?'active':''" @click="handleNextClick(secondItem)" :key="key">{{secondItem.text}}</li>
                    </ul>
                </li>
            </ul>
        </div>
        <div class="fl width" id="mainScrollContainer" style="position:relative;overflow-y:auto;" ref="scrollPage">
            <div v-show="Breadcrumb.length > 1 && $route.name != 'homePage'" :style="'top:'+fixTop" class="breadContainer">
                <!--isAbsolute?BreadcrumbStyle.abs:BreadcrumbStyle.det-->
                <el-breadcrumb separator-class="el-icon-arrow-right">
                    <el-breadcrumb-item v-for="(item,index) in Breadcrumb" :key="index">{{item.name}}</el-breadcrumb-item>
                    <!--:to="{ path: '/'+item.url }"-->
                </el-breadcrumb>
            </div>
            <div v-show="$route.name != 'homePage' && Breadcrumb.length <= 1" :style="'top:'+fixTop+';background-color:#efefef;'" class="breadContainer"></div>
            <!--background-color:#efefef;-->
            <div v-show="$route.name != 'homePage'" style="height:32px;opcity:0;">占位勿删</div>
            <router-view style="margin-bottom: 100px;overflow-y: auto;"></router-view>
        </div>
        <div class="clear"></div>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
window.getLoginMsg = function() {
  var loginObj = {
    agentVue: $("#agent").val(),
    extpassVue: $("#extpass").val(),
    domainVue: $("#domain").val(),
    extnoVue: $("#extno").val()
  };
  return loginObj;
};
export default {
  data() {
    return {
      flag: false,
      title: "客服管理系统",
      activeId: "",
      showId: "",
      activeId1: "",
      name: "",
      isAbsolute: false,
      fixTop: 0,
      Breadcrumb: [],
      isFourZeroZreo: true,
      BreadcrumbStyle: {
        det: "padding:5px 5px;margin-bottom:5px;display:inline-block;",
        abs:
          "padding:5px 5px;margin-bottom:5px;display:inline-block;position:absolute;"
      },
      isMenuScroll:false,
      mainTabs: [] //[{"id":"cs_1","text":"首页","state":null,"checked":false,"attributes":{"orderNum":99,"url":"homePage"},"children":[],"parentId":"0","hasParent":false,"hasChildren":false},{"id":"cs_2","text":"客服管理","icoCode":"ico_2","state":null,"checked":false,"attributes":{"orderNum":88,"url":"#"},"children":[{"id":"cs_2_1","text":"报事暂存","state":null,"checked":false,"attributes":{"orderNum":99,"url":"interimReport"},"children":[],"parentId":"cs_2","hasParent":true,"hasChildren":false},{"id":"cs_2_2","text":"报事录入","state":null,"checked":false,"attributes":{"orderNum":88,"url":"entryReport"},"children":[],"parentId":"cs_2","hasParent":true,"hasChildren":false},{"id":"cs_2_3","text":"报事分派","state":null,"checked":false,"attributes":{"orderNum":77,"url":"Assignment"},"children":[],"parentId":"cs_2","hasParent":true,"hasChildren":false},{"id":"cs_2_4","text":"报事处理","state":null,"checked":false,"attributes":{"orderNum":66,"url":"ReportSolve"},"children":[],"parentId":"cs_2","hasParent":true,"hasChildren":false},{"id":"cs_2_6","text":"报事回访","state":null,"checked":false,"attributes":{"orderNum":55,"url":"ReportReturn"},"children":[],"parentId":"cs_2","hasParent":true,"hasChildren":false}],"parentId":"0","hasParent":false,"hasChildren":true},{"id":"cs_3","text":"报表管理","state":null,"icoCode":"ico_3","checked":false,"attributes":{"orderNum":77,"url":"#"},"children":[{"id":"cs_3_1","text":"报事查询","state":null,"checked":false,"attributes":{"orderNum":99,"url":"#"},"children":[],"parentId":"cs_3","hasParent":true,"hasChildren":false}],"parentId":"0","hasParent":false,"hasChildren":true},{"id":"cs_4","text":"系统管理","state":null,"checked":false,"attributes":{"orderNum":66,"url":"#"},"children":[],"parentId":"0","hasParent":false,"hasChildren":false}],
    };
  },
  watch: {
    $route: {
      handler: function(val, oldVal) {
        this.isAbsolute = val.path == "/customerService/addReport";
      },
      // 深度观察监听
      deep: true
    }
  },
  methods: {
    change() {
      this.flag = !this.flag;
    },
    getUser() {
      var userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
      this.name = userInfo.name
      this.isFourZeroZreo = userInfo.seats ? true : false; //是否400客服
      this.resizePage();
      this.mainTabs = userInfo.menuTree;
      for (let i = 0; i < this.mainTabs.length; i++) {
        if (this.$route.fullPath.indexOf(this.mainTabs[i].attributes.url) != -1) {
          this.Breadcrumb.push({
            name: this.mainTabs[i].text,
            url: this.mainTabs[i].attributes.url,
            id: this.mainTabs[i].id
          });
          break;
        } else {
          for (let n = 0; n < this.mainTabs[i].children.length; n++) {
            if (this.$route.fullPath.indexOf(this.mainTabs[i].children[n].attributes.url) != -1) {
              this.Breadcrumb.push({
                name: this.mainTabs[i].text,
                url: this.mainTabs[i].attributes.url,
                id: this.mainTabs[i].id
              });
              this.Breadcrumb.push({
                name: this.mainTabs[i].children[n].text,
                url: this.mainTabs[i].children[n].attributes.url,
                id: this.mainTabs[i].children[n].id
              });
              break;
            }
          }
        }
      }
      if (this.Breadcrumb[0].name == "首页") {
        this.title = "客服管理首页";
        this.activeId = this.Breadcrumb[0].id;
      } else {
        this.title = this.Breadcrumb[0].name;
        this.activeId = this.Breadcrumb[0].id;
      }

      let _this = this;
      let seats = userInfo.seats;
      setTimeout(()=>{
        if(seats){
          _this.$refs.iframeContent.contentWindow.uKeFuSoftPhone.login(seats.jobNum , seats.extNo , seats.password,'taihe.com')
        }
      },2000)
    },
    setCookieClear() {
      sessionStorage.removeItem("userInfo");
      if (location.hostname.indexOf("demo") > 0)
        window.location.href = 'http://ucsso.tahoecndemo.com:9988/logout?sysId=KEFU&ReturnURL=' + location.href;
      else
        window.location.href = 'http://ucsso.tahoecn.com:9988/logout?sysId=KEFU&ReturnURL=' + location.href;
    },
    handleClick(e, item) {
      this.activeId = item.id;
      if (item.children.length == 0) this.activeId1 = "";

      if (item.attributes.orderNum == "99") {
        this.$router.push({ name: "homePage" });
        this.Breadcrumb = [];
        this.Breadcrumb.push({ name: "首页", url: "homePage" });
      }
      if (item.text == "首页") {
        this.title = "客服管理首页";
      }
    },
    handleNextClick(item) {
      sessionStorage.removeItem('params');
      this.Breadcrumb = [];
      this.$router.push({ path: item.attributes.url });
      this.activeId1 = item.id;
      this.mainTabs.map(pitem => {
        pitem.children.map(citem => {
          if (citem.id == item.id) {
            let pobj = { name: pitem.text, url: pitem.attributes.url };
            let cobj = { name: citem.text, url: citem.attributes.url };
            this.Breadcrumb.push(pobj);
            this.Breadcrumb.push(cobj);
          }
        });
      });
      if (this.Breadcrumb[0].name == "首页") {
        this.title = "客服管理首页";
      } else {
        this.title = this.Breadcrumb[0].name;
      }
    },
    resizePage() {
      let headerHeight = document.getElementById("homepage-header").offsetHeight;
      let iframeHeight = 0;
      let documentHeight = document.body.offsetHeight;
      let mainContainer = this.$refs.scrollPage; //document.getElementById('homepage-body')
      let leftArea = this.$refs.leftMnueArea;
      localStorage.setItem("resize-page", new Date().valueOf());
      if (this.isFourZeroZreo) {
        iframeHeight = document.getElementById("if-rame").offsetHeight;
        mainContainer.style.height =
          documentHeight - headerHeight - iframeHeight + "px";
        leftArea.style.height =
          documentHeight - headerHeight - iframeHeight + "px";
        this.fixTop = Number(headerHeight) + Number(iframeHeight) + 6 + "px";
      } else {
        mainContainer.style.height = documentHeight - headerHeight + "px";
        leftArea.style.height = documentHeight - headerHeight + "px";
        this.fixTop = Number(headerHeight) + 6 + "px";
      }
      window.onresize = () => {
        documentHeight = document.body.offsetHeight;
        if (this.isFourZeroZreo) {
          iframeHeight = document.getElementById("if-rame").offsetHeight;
          mainContainer.style.height =
            documentHeight - headerHeight - iframeHeight + "px";
          leftArea.style.height =
            documentHeight - headerHeight - iframeHeight + "px";
          this.fixTop = Number(headerHeight) + Number(iframeHeight) + 6 + "px";
        } else {
          mainContainer.style.height = documentHeight - headerHeight + "px";
          leftArea.style.height = documentHeight - headerHeight + "px";
          this.fixTop = Number(headerHeight) + 6 + "px";
        }
      };
    }
  },
  mounted() {
    this.getUser();
    if(document.body.clientHeight <= 580 ){
      this.isMenuScroll = true;
    }else{
      this.isMenuScroll = false;
    }
    window.onresize = () => {
        if(document.body.clientHeight <= 580 ){
          this.isMenuScroll = true;
        }else{
          this.isMenuScroll = false;
        }
    }
  },
  created() {
  },
  computed: {
    ...mapGetters(["SET_PHONENUM"])
  }
};
</script>

<style scoped>
.User {
  float: right;
  margin-right: 20px;
  position: relative;
}
.logOut400 {
  display: block;
  width: 120px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border: solid thin rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 4px 0;
  border-radius: 5px;
  position: fixed;
  top: 113px;
  right: 12px;
  background-color: #fff;
  z-index: 9999;
}
.logOut {
  display: block;
  width: 120px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border: solid thin rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 4px 0;
  border-radius: 5px;
  position: fixed;
  top: 44px;
  right: 12px;
  background-color: #fff;
  z-index: 9999;
}
.logOut a {
  text-decoration: none;
  color: #000;
}
.logOut400 a {
  text-decoration: none;
  color: #000;
}
.logOut_img {
  width: 16px !important;
  height: 16px !important;
  /* display: inline; */
  display: block !important;
  float: left !important;
  /* margin: 9px; */
  margin-top: 5px !important;
}
.main-page .header {
  width: 100%;
  height: 67px;
  margin-top: 70px;
  background-image: url(../../assets/images/<EMAIL>);
  background-size: 100% 110%;
  line-height: 67px;
  /*overflow: hidden;*/
  position: relative;
  z-index: 1;
  box-shadow: 1px 2px 4px 0 rgba(0, 0, 0, 0.1);
}
.main-page .header img {
  width: 161px;
  height: 34px;
  margin-top: 16px;
  margin-left: 20px;
  float: left;
}
.main-page .header > span {
  font-size: 24px;
  color: #b4b4b4;
  letter-spacing: 0;
  margin-left: 85px;
  font-weight: 700;
}
/* 左侧菜单 */
.main-page .leftMenu {
  height: 50px;
  line-height: 50px;
  padding-left: 20px;
  font-size: 14px;
  color: #4a4a4a;
  cursor: pointer;
  position: relative;
  z-index: 5000;
}
.main-page .leftMenu:hover {
  background: #f5f5f5;
}
.active {
  color: #e63f3c !important;
  background: #fff5f5;
}
.secondMenu {
  position: absolute;
  left: 240px;
  top: 0px;
  z-index: 9999;
}
.secondMenu > li {
  width: 150px;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  background: #f5f5f5;
  color: #4a4a4a;
}
.secondMenu > li:hover {
  background: #e6e6e6;
}
.width {
  width: calc(100% - 270px);
  margin-left: 20px;
  margin-top: 20px;
}
.icon_pic {
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 8px;
}
.breadContainer {
  position: fixed;
  z-index: 999;
  height: 36px;
  width: 100%;
  background-color: #efefef;
  display: flex;
  align-items: center;
  border-bottom: solid thin #dadada;
  padding-left: 10px;
}
.scroll-sec-menu{
  max-height:200px!important;
  overflow-y: auto!important;
}
</style>
