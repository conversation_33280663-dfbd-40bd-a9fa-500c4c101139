<template>
    <div class="itreportperson-list" id="itreportperson">
        <el-table :data="tableData" style="width:100%" class="table-body" :stripe="true" v-loading="listLoading">
            <el-table-column label="名称" prop="">
                <template slot-scope="scope">{{scope.row.projectName}}</template>
            </el-table-column>
            <el-table-column label="分派人" prop="">
                <template slot-scope="scope">{{scope.row['itRepair-toBeAssigned']?(scope.row['itRepair-toBeAssigned'].userName?scope.row['itRepair-toBeAssigned'].userName:''):''}}</template>
            </el-table-column>
            <el-table-column label="处理人" prop="">
                <template slot-scope="scope">{{scope.row['itRepair-handle']?(scope.row['itRepair-handle'].userName?scope.row['itRepair-handle'].userName:''):''}}</template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                   <el-button size="mini" class="btnDown" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import {csSelectItUser} from '@/api/reportSetting'
export default {
    data(){
        return{    
            tableData:[
                // {name:'OA',assigner:'张三、李四',handler:'李华、王伟'},
                // {name:'流程',assigner:'张三、李四',handler:'李华、王伟'},
                // {name:'人事',assigner:'张三、李四',handler:'李华、王伟'},
                // {name:'成本系统',assigner:'张三、李四',handler:'李华、王伟'},
                // {name:'招采',assigner:'张三、李四',handler:'李华、王伟'},
                // {name:'旧业务系统',assigner:'张三、李四',handler:'李华、王伟'},
            ],
            listLoading:false,
             
        }
    },
    methods:{
        handleEdit(row){
            this.$router.push({
                path:'/customerService/ITReportPersonDetail',
                query:{
                    content:row
                },
            })
        },
        getCsSelectItUser(){
            this.listLoading = true;
            csSelectItUser({}).then(res=>{
                this.listLoading = false;
                console.log(res);
                if(res.code==200){
                    for(var i in res.data) {
                        this.tableData.push(res.data[i])
                    }
                }
            }).catch(()=>{this.listLoading = false;})
        }
    },
    mounted(){
        this.getCsSelectItUser();
    }
}
</script>
<style lang="scss" scoped="scoped">

</style>
