<template>
    <div class="itreportperson-setting">
        <div class="btn-container">
            <div class="htitle">IT项目人员信息</div>
            <el-button size="mini" class="btnDown" @click.stop="submitInfo">提交</el-button>
            <el-button size="mini" class="btnDown" @click.stop="cancel">返回</el-button>
        </div>
        <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="项目">
                        <span>{{content.projectName}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="分派人" prop="assigner">
                        <el-input type="text" v-model="formData.assigner" suffix-icon="el-icon-search" @click.native="pick('assign')" readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="处理人" prop="handler">
                        <el-input type="text" v-model.number="formData.handler" suffix-icon="el-icon-search" @click.native="pick('handle')" readonly></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <pick ref='pick' :transUser="transUser" :visible="userCon" @personContent="personContent" @show1="show1"></pick>
    </div>
</template>
<script>
import pick from "@/pages/mainPage/reportSetting/pick";
import {csSaveItUser} from '@/api/reportSetting'
import { Loading } from "element-ui";
export default {
    components:{
        pick
    },
    data(){
        return{
            formData:{
                assigner:'',
                handler:'',
                handlerId:'',
                assignerId:'',
            },
            rules:{
                assigner:[
                    {required:true,message:'分派人不能为空！',trigger:'change'}
                ],
                handler:[
                    {required:true,message:'处理人不能为空！',trigger:'change'}
                ]
            },
            content:this.$route.query.content,
            transUser:[],
            userCon:false,
            personFlag:'',
        }
    },
    methods:{
        submitInfo(){
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    let loadingInstance = Loading.service({
                        text: "正在提交...",
                        spinner: "el-icon-loading",
                        background: "rgba(255, 255, 255, 0.8)"
                    });
                    let data = {
                        assignUserId:this.formData.assignerId,
                        handleUserId:this.formData.handlerId,
                        projectCode:this.content.projectCode
                    }
                    csSaveItUser(data).then(res=>{
                        this.$nextTick(() => {
                            // 以服务的方式调用的 Loading 需要异步关闭
                            loadingInstance.close();
                        });
                        if(res.code ==200){
                            this.$router.push({path:'/customerService/ITReportPersonList'})
                            
                        }else{
                            this.$confirm(res.message, {
                                confirmButtonText: "确定",
                                center: true,
                                showClose: false,
                                showCancelButton: false,
                                confirmButtonClass: "confirm-reset-style"
                            }).then(() => {});
                        }
                    }).catch(()=>{
                        this.$nextTick(() => {
                            // 以服务的方式调用的 Loading 需要异步关闭
                            loadingInstance.close();
                        });
                    })
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
            
        },
        cancel(){
            window.history.go(-1);
        },
        pick(type){ // 显示dialog时传入的值
            this.$refs.pick.fileLists = [];
            this.$refs.pick.username = "";
            let arr = []
            if(this.formData.assignerId!='' && type =='assign'){
                let asgId = this.formData.assignerId.split(',')
                let asgName = this.formData.assigner.split(',')
                for(let i=0;i<asgId.length;i++){
                    
                    let obj = {}
                    obj.username = asgId[i]
                    obj.name = asgName[i]
                    if(obj.name){
                        arr.push(obj);
                    }
                }
            }else if(this.formData.handlerId!='' && type =='handle'){
                let hanId = this.formData.handlerId.split(',')
                let hanName = this.formData.handler.split(',')
                for(let i=0;i<hanId.length;i++){
                    let obj = {}
                    obj.username = hanId[i]
                    obj.name = hanName[i]
                    if(obj.name){
                        arr.push(obj);
                    }
                   
                }
            }
            this.transUser = arr;
            console.log(this.transUser);
            this.userCon = true;
            this.personFlag = type;
        },
        personContent(item){//获取选择人员信息
            if(this.personFlag=='assign'){
                this.formData.assigner = item.names;
                this.formData.assignerId = item.usernames;
            }else{
                this.formData.handler = item.names;
                this.formData.handlerId = item.usernames;
            }
        },
        show1(show) { //隐藏dialog置为false
            this.userCon = show;
        },
    },
    mounted(){
        this.formData.assigner = this.content['itRepair-toBeAssigned'].userName;
        this.formData.assignerId = this.content['itRepair-toBeAssigned'].userId;
        this.formData.handler = this.content['itRepair-handle'].userName;
        this.formData.handlerId = this.content['itRepair-handle'].userId;
    }
}
</script>
<style lang="scss" scoped="scoped">
.itreportperson-setting{
    background-color:#ffffff;
    border-radius:5px;
    padding-bottom:20px;
}
.btn-container{
    text-align:right;
    padding-right:45px;
    height:42px;
    line-height:42px;
    border-bottom:solid #dadada 1px;
}
.htitle{
    float:left;
    height:20px;
    line-height:20px;
    margin-left:20px;
    margin-top:7.5px;
    border-left:solid #e63f3c 2px;
    padding-left:5px;
    font-size:14px;
}
.demo-ruleForm{
    padding-right:50px;
}
</style>