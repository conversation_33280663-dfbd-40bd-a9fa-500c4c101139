<template>
    <div class="itreporttype-list" id="itReportType">
        <el-table tooltip-effect="dark" :data="tableData" class="table-body" @row-click="rowClick" row-key="id" :expand-row-keys="expands" style="width: 100%"
        v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)">
            <el-table-column type="expand" width="0">
                <template slot-scope="scope">
                    <el-table tooltip-effect="dark" :data="scope.row.secDictItem" style="width:100%" :show-header="false">
                        <el-table-column label="名称" prop="itemValue" width="310px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span style="display:inline-block;padding-left:36px;">
                                    {{scope.row.itemValue}}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="层级" prop="cell"></el-table-column>
                        <el-table-column label="流程类别" prop="type"></el-table-column>
                        <el-table-column label="是否启用">
                            <template slot-scope="scope">
                                {{scope.row.status == '1'?'是':'否'}}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button size="mini" class="btnDown" @click.stop="handleEdit(scope.row)">编辑</el-button>
                                <!-- <el-button v-if="isShowDelete" size="mini" class="btnDown" @click.stop="deleteType(scope.row)">删除</el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column label="名称" prop="itemValue" align="left" width="300px" show-overflow-tooltip>
                <template slot-scope="scope">
                    <span style="display:inline-block;">
                        <i :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                        {{scope.row.itemValue}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="层级" prop="cell"></el-table-column>
            <el-table-column label="流程类别" prop="type"></el-table-column>
            <el-table-column label="是否启用">
                <template slot-scope="scope">
                    {{scope.row.status == '1'?'是':'否'}}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button size="mini" class="btnDown" @click.stop="handleEdit(scope.row)">编辑</el-button>
                    <el-button size="mini" class="btnDown" @click.stop="handleAddChild(scope.row)">添加子级</el-button>
                    <!-- <el-button v-if="isShowDelete" size="mini" class="btnDown" @click.stop="deleteType(scope.row)">删除</el-button> -->
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import { getItReportTypeList, deleteItReportType} from '@/api/reportSetting'
export default {
    data(){
        return{
            expands: [],
            loading:false,
            tableData:[],
            isShowDelete:false,
        }
    },
    methods:{
        rowClick(row, event, column) {
            Array.prototype.remove = function(val) {
                let index = this.indexOf(val);
                if (index > -1) {
                this.splice(index, 1);
                }
            };
            if (this.expands.indexOf(row.id) < 0 && row.secDictItem.length > 0) {
                this.expands.push(row.id);
                row.expand = true;
            } else {
                this.expands.remove(row.id);
                row.expand = false;
            }
        },
        handleEdit(row){
            this.$router.push({
                path:'/customerService/ITReportTypeDetail',
                query:{
                    type:'edit',
                    lastLevel:'',
                    level:row.cell,
                    itemValue:row.itemValue,
                    itemCode:row.itemCode,
                    typeCode:row.typeCode,
                    status:row.status
                },
            })
        },
        handleAddChild(row){
            this.$router.push({
                path:'/customerService/ITReportTypeDetail',
                query:{
                    type:'add',
                    level:row.cell,
                    itemValue:row.itemValue,
                },
            })
        },
        getTableList(){
            this.loading = true;
            getItReportTypeList().then((res) => {
                console.log('列表数据：',res);
                if(res.code == 200){
                    this.tableData = res.data;
                    this.loading = false;
                }
            })
        },
        deleteType(row){
            this.$confirm('确定要删除分类：'+row.itemValue,{confirmButtonClass:'confirm-reset-style',center: true,}).then(() => {
                deleteItReportType({itemCode:row.itemCode}).then((res) => {
                    if(res.code == 200){
                        this.$confirm('删除成功', {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {
                            this.getTableList();
                        })
                    }
                })
            }).catch(() => {

            })
        }
    },
    created(){
        this.getTableList();
        if(window.winHref.indexOf('tahoecndemo') == -1){
            this.isShowDelete = false;
        }else{
            this.isShowDelete = true;
        }
    }
}
</script>
<style lang="scss" scoped="scoped">
    .el-icon-minus,.el-icon-plus {
        font-size: 12px;
        color: #e63f3c;
        font-weight: bold;
    }
</style>