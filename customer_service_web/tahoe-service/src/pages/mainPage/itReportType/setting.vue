<template>
    <div class="itreporttype-setting">
        <div class="btn-container">
            <div class="htitle">IT项目类型设置</div>
            <el-button size="mini" class="btnDown" @click="submitInfo">提交</el-button>
            <el-button size="mini" class="btnDown" @click="cancel">返回</el-button>
        </div>
        <el-form :model="formData" status-icon ref="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="上级名称">
                        <span>{{formData.lastCell}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="所属层级">
                        <span>{{formData.cell}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="名称" prop="itemValue">
                        <el-input type="text" v-model="formData.itemValue"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12" v-if="formData.cell=='二级'">
                    <el-form-item label="流程类别" prop="dictCode">
                        <el-select v-model="formData.dictCode" multiple placeholder="请选择流程类别">
                            <el-option
                            v-for="item in processOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="是否启用">
                        <el-radio v-model="formData.status" label="1">是</el-radio>
                        <el-radio v-model="formData.status" label="0">否</el-radio>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import { saveEditItReportType, addItReportTypeChild } from '@/api/reportSetting'
export default {
    data(){
        return{
            formData:{
                lastCell:'',
                cell:'',
                itemValue:'',
                dictCode:[],
                status:'1',
            },
            processOption:[
                {label:'标准处理流程',value:'itFscBz'},
                {label:'快速处理流程',value:'itFscKszx'}
            ],
            processType:'',
            rules:{
                itemValue:[
                    { required: true, message: '请输入名称', trigger: 'blur' },
                ],
                dictCode:[
                    { required: true, message: '请选择流程类型', trigger: 'blur' },                 
                ]
            }
        }
    },
    methods:{
        submitForm(formName) {
            let flag;
            this.$refs[formName].validate((valid) => {
            if (valid) {
                flag = true;
            } else {
                flag = false;
            }
            });
            return flag;
        },
        submitInfo(){
            let params = new Object();
            if(!this.submitForm('ruleForm')) return;
            if(this.$route.query.type == 'edit'){//编辑
                if(this.formData.cell == '二级'){
                    if(this.formData.itemValue.indexOf(this.formData.lastCell+'：') != 0){
                        this.formData.itemValue = this.formData.lastCell+'：'+this.formData.itemValue;
                    }
                    params.dictCode = this.formData.dictCode.toString();
                    params.itemValue = this.formData.itemValue;
                    params.itemCode = this.formData.itemCode;
                    params.status = this.formData.status;
                }else{
                    params.dictCode = 'deptIT';
                    params.itemValue = this.formData.itemValue;
                    params.itemCode = this.formData.itemCode;
                    params.status = this.formData.status;
                }
                saveEditItReportType(params).then((res) => {
                    if(res.code == 200){
                        this.$confirm('信息保存成功', {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {
                            window.history.go(-1);
                        })
                    }else{
                        this.$confirm(res.message, {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {
                            
                        })
                    }
                })
            }else if(this.$route.query.type == 'add'){//添加子集
                if(this.formData.itemValue.indexOf(this.formData.lastCell+'：') != 0){
                    this.formData.itemValue = this.formData.lastCell+'：'+this.formData.itemValue;
                }
                params.dictCode = this.formData.dictCode.toString();
                params.itemValue = this.formData.itemValue;
                params.status = this.formData.status;
                addItReportTypeChild(params).then((res) => {
                    if(res.code == 200){
                        this.$confirm('添加子集成功', {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {
                            window.history.go(-1);
                        })
                    }else{
                        this.$confirm(res.message, {
                            confirmButtonText: '确定',
                            center: true,
                            showClose:false,
                            showCancelButton:false,
                            confirmButtonClass:'confirm-reset-style',
                        }).then(() => {
                            
                        })
                    }
                })
            }
        },
        cancel(){
            window.history.go(-1);
        },
    },
    created(){
        if(this.$route.query.type == 'edit'){
            this.formData.cell = this.$route.query.level;
            this.formData.itemValue = this.$route.query.itemValue;
            this.formData.itemCode = this.$route.query.itemCode;
            this.formData.status = this.$route.query.status;
            if(this.$route.query.itemValue.indexOf('：') > 0){
                this.formData.lastCell = this.$route.query.itemValue.split('：')[0];
            }
            if(this.$route.query.typeCode){
                this.formData.dictCode = this.$route.query.typeCode.split(',');
            }
            if(this.formData.cell == '二级'){
                this.rules = {
                    itemValue:[
                        { required: true, message: '请输入名称', trigger: 'blur' },
                    ],
                    dictCode:[
                        { required: true, message: '请选择流程类型', trigger: 'blur' },                 
                    ]
                }
            }else{
                this.rules = {
                    itemValue:[
                        { required: true, message: '请输入名称', trigger: 'blur' },
                    ],
                }
            }
        }else if(this.$route.query.type == 'add'){
            this.formData.cell = '二级';
            this.formData.lastCell = this.$route.query.itemValue;
        }
    },
}
</script>
<style lang="scss" scoped="scoped">
.itreporttype-setting{
    background-color:#ffffff;
    border-radius:5px;
    padding-bottom:20px;
}
.btn-container{
    text-align:right;
    padding-right:45px;
    height:42px;
    line-height:42px;
    border-bottom:solid #dadada 1px;
}
.htitle{
    float:left;
    height:20px;
    line-height:20px;
    margin-left:20px;
    margin-top:7.5px;
    border-left:solid #e63f3c 2px;
    padding-left:5px;
    font-size:14px;
}
.demo-ruleForm{
    padding-right:50px;
}
</style>