<template>
  <div class="search-bar">
    <div class="bg-style" style="margin-right:10px;">
      <el-form ref="form" label-position="right" :model="searchData" label-width="80px" size="mini">
        <div style="height:10px;"></div>
        <el-row>
          <el-col :span="5" :offset="0">
            <el-form-item label="创建时间">
              <el-date-picker prefix-icon="el-icon-date" v-model="searchData.startDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="至">
              <el-date-picker prefix-icon="el-icon-date" v-model="searchData.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="一级分类">
              <el-select v-model="searchData.firstSortCode" placeholder="请选择">
                <el-option value="coTS" label='投诉'></el-option>
                <el-option value="coBX" label='报修'></el-option>
                <el-option value="coZX" label='咨询'></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
              <el-form-item label="区域">
                <el-select v-model="searchData.regionCode" placeholder="请选择">
                  <el-option v-for="item in areaOptions" :key="item.id" :label="item.region" :value="item.regionCode"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
        </el-row>
        <!-- <div class="box" v-if="flag">
          <el-row class="mt-10">
            
          </el-row>
        </div> -->
        <el-row class="mt-10">
          <el-col :span="24" style="text-align:right;padding-right:4%;">
            <button class="btnDown" @click.prevent="searchReturn" style="margin-left:20px;margin-top:3px;">查询</button>
            <button class="btnDown" @click.prevent="searchExp" style="margin-left:10px;">导出</button>
            <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
          </el-col>
        </el-row>
        <el-row class="mt-10">
          <el-col :span="24">
            <div class="ta-ct mt-10" style="position:relative;height:30px;">
              <hr style='background-color:#e6e6e6;height:1px;border:none;' />
              <span class="txt" @click.prevent="change()">
                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
              </span>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="bg-style mt-10 mb-20" style="margin-right:10px">
      <div style="width:100%;overflow:hidden;">
        <el-table v-loading="loading" stripe element-loading-text="拼命加载中" :data="pageList" style="width: 100%">
          <el-table-column prop="region" label="区域"></el-table-column>
          <el-table-column prop="city" label="城市"></el-table-column>
          <el-table-column prop="project" label="项目"></el-table-column>
          <el-table-column prop="firstSortName" label="一级分类"></el-table-column>
          <el-table-column prop="reportChannelName" label="报事渠道"></el-table-column>
          <el-table-column prop="reportChannelNum" label="数量"></el-table-column>
        </el-table>
        <!-- 分页 -->
        <wcg-page :pageSize="pageSize" :currentPage="pageNum" :total="list.length" :floorList="pageList" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturn"></wcg-page>
      </div>
    </div>
  </div>
</template>

<script>
import wcgPage from "@/components/wcgPage";
import { area, searchInterimReports } from "@/api/port";
import { channel, channelExport } from "@/api/export/exp";
export default {
  components: { wcgPage },
  data() {
    return {
      pageSize:20,
      pageNum:1,
      searchData: {},
      areaOptions: [],
      loading: false,
      flag: false,
      list: [],
      pageList:[]
    };
  },
  methods: {
    ifnull(row, column) {
      return row[column.property] ? row[column.property] : "——";
    },
    change() {
      this.flag = !this.flag;
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    searchReturn() {
      this.pageNum = 1;
      this.loading = true;
      this.list = [];
      channel(this.searchData)
        .then(res => {
          if (res.code == 200) {
            this.list = res.data;
            this.pageList = this.list.slice(0,this.pageSize);
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.pageList = this.list.slice((this.pageNum-1)*this.pageSize,this.pageNum*this.pageSize);
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.pageList = this.list.slice((this.pageNum-1)*this.pageSize,this.pageNum*this.pageSize);
    },
    searchExp() {
      let url = channelExport(this.searchData);
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
    },
    reset() {
      this.searchData = {};
    }
  },
  created() {
    this.areaList();
  },
  mounted() {}
};
</script>

<style scoped>
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
