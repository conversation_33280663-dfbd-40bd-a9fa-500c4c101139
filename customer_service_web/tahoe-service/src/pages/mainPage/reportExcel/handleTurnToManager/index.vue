<template>
    <div class="search-bar">
        <div class="bg-style" style="margin-right:10px;">
            <el-form ref="form" label-position="right" :model="searchData" label-width="80px" size="mini">
                <div style="height:10px;"></div>
                <el-row>
                    <el-col :span="5" :offset="0">
                        <el-form-item label="区域">
                            <el-select v-model="searchData.area" filterable placeholder="请选择" @change="getCityList">
                                <el-option v-for="item in areaOptions" :key="item.id" :label="item.region" :value="item.regionCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="城市">
                            <el-select v-model="searchData.city" :disabled="cityprojectDisabled" filterable placeholder="请选择" @change="getProjectList">
                                <el-option v-for="item in cityOptions" :key="item.cityCompanyCode" :label="item.cityCompany" :value="item.cityCompanyCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-form-item label="项目">
                            <el-select v-model="searchData.project" :disabled="projectDisabled" filterable placeholder="请选择">
                                <el-option v-for="item in projectOptions" :key="item.projectCode" :label="item.project" :value="item.projectCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='5' :offset="1">
                        <el-form-item label="工单编号">
                            <el-input v-model="searchData.number" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="box" v-if="flag">
                    <el-row style="margin-top:10px;" class="mt-10">
                        <el-col :span='5' :offset="0">
                            <el-form-item label="创建人">
                                <el-input v-model="searchData.createUserName" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="业主姓名">
                                <el-input v-model="searchData.user" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5" :offset="1">
                            <el-form-item label="业主电话">
                                <el-input v-model="searchData.phone" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="业务步骤">
                                <el-select 
                                    v-model="searchData.step" 
                                    filterable 
                                    multiple
                                    collapse-tags 
                                    placeholder="请选择">
                                    <el-option v-for="item in step" :key="item.itemCode" :label="item.itemValue" :value="item.itemCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top:10px;">                      
                        <el-col :span="5" :offset="0">
                            <el-form-item label="一级分类">
                                <el-select v-model="searchData.firstSortCode" placeholder="请选择">
                                    <el-option v-for="i in dict.filter(i=>i.dictCode =='firstSortCode')" :key="i.itemCode" :label="i.itemValue" :value="i.itemCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" :offset="1">
                            <el-form-item label="当前处理人">
                                <el-input v-model="searchData.curAssigneeName" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <el-row class="mt-10">
                    <el-col :span="24" style="text-align:right;padding-right:4%;">
                        <button class="btnDown" @click.prevent="searchReturn()" style="margin-left:20px;margin-top:3px;">查询</button>
                        <!-- <button class="btnDown" @click.prevent="searchExp()" style="margin-left:10px;">导出</button> -->
                        <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
                    </el-col>
                </el-row>
                <el-row class="mt-10">
                    <el-col :span="24">
                        <div class="ta-ct mt-10" style="position:relative;height:30px;">
                            <hr style='background-color:#e6e6e6;height:1px;border:none;' />
                            <span class="txt" @click.prevent="change()">
                                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
                            </span>
                        </div>
                    </el-col>
                </el-row>

            </el-form>
        </div>

        <div class="bg-style mt-10 mb-20" style="margin-right:10px">
            <div style="width:100%;overflow:hidden;">
                <el-table v-loading="loading" ref="multipleTable" stripe element-loading-text="拼命加载中" :data="list" style="width: 100%" tooltip-effect="dark" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55"/>
                    <el-table-column label="工单编号" width="130">
                        <template slot-scope="scope">
                            <span style="color:#1D85FE;" @click="goDetail(scope.row)">{{scope.row.formNo}}</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="houseNo" label="房屋编号" show-overflow-tooltip/> -->
                    <el-table-column prop="houseName" label="房屋名称" show-overflow-tooltip/>
                    <el-table-column prop="ownerName" label="业主名称" show-overflow-tooltip/>
                    <el-table-column prop="mobile" label="业主电话" width="100" show-overflow-tooltip/>
                    <el-table-column prop="firstSortName" label="一级分类" show-overflow-tooltip/>
                    <el-table-column prop="secSortName" label="二级分类" show-overflow-tooltip/>
                    <el-table-column prop="thirdSortName" label="三级分类" show-overflow-tooltip/>
                    <el-table-column prop="fourthSortName" label="四级分类" show-overflow-tooltip/>
                    <el-table-column prop="customerDemand" label="客户诉求" show-overflow-tooltip/>
                    <el-table-column label="创建时间" width="140">
                        <template slot-scope="scope">
                            <span>{{scope.row.creationDate | TimeMoment}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="processStateName" label="业务步骤" />
                    <el-table-column prop="curAssigneeName" label="当前处理人">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.curAssigneeName" :readonly="true" @focus="showPicker(scope.row)"/>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <wcg-page :pageSize="pageSize" :currentPage="currentPage" :total="total" :floorList="list" @size-change="handleSizeChange" @current-change="handleCurrentChange" @on-change="searchReturnListOK"></wcg-page>
            </div>
        </div>
        <picker ref='pick' :transUser="transUser" :visible="isShowPicker" @personContent="personContent" @show1="show1" :singleSelect="true"></picker>
    </div>

</template>

<script>
import wcgPage from "@/components/wcgPage";
import {
  area,
  searchInterimReports,
  searchInterimReport,
  ReportStep,
  getRegionInfoByCode
} from "@/api/port";
import { dict } from "@/api/wsd";
import { rfList, rfExport, editRecentHandle } from "@/api/export/exp";
import { getServerDate } from "@/api/common"
import picker from '@/pages/mainPage/reportSetting/pick'
export default {
  props: [],
  components: { wcgPage, picker },
  watch: {
    "searchData.area"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.floorDisabled = true;
          this.projectOptions = [];
          this.buildOptions = [];
          this.searchData.city = this.searchData.project = "";
        }
      } else {
        this.cityprojectDisabled = true;
        if ((this.cityprojectDisabled = true)) {
          this.projectDisabled = true;
        }
        this.floorDisabled = true;
        this.projectDisabled = true;
      }
    },
    "searchData.city"(newval, oldval) {
      if (newval != oldval) {
        this.searchData.project = "";
      }
      if (newval == "") {
        this.projectDisabled = true;
      } else {
        this.projectDisabled = false;
      }
    }
  },
  data() {
    return {
      showIf: false,
      flag: false,
      loading: false,
      pageSize: 20,
      currentPage: 1,
      total: 0,
      nowDate:getServerDate(),
      cityDisabled: "",
      lcDisabled: false,
      oneDisabled: true,
      projectDisabled: true,
      isShowPicker:false,
      transUser: [],
      selectUserArr:[],
      searchData: {
        createate: "",
        enddate: "",
        area: "",
        user: "",
        phone: "",
        lb: "",
        business: "",
        number: "",
        city: "",
        project: "",
        level: "",
        step:[],
      },
      searchDataReset: {
        createDate: "",
        endDate: "",
        area: "",
        phone: "",
        step: "",
        project: "",
        step:[],
      },
      step: [],
      areaOptions: [],
      projectOptions: [],
      cityOptions: [],
      buildprojectDisabled: true,
      cityprojectDisabled: true,
      list: [],
      dict: [],
      isClearArr:true,
      jArr:[],
    };
  },
  methods: {
    handleSelectionChange(val){
      console.log(val);
      this.selectUserArr = val;
      let a = JSON.stringify(val);
      this.jArr = JSON.parse(a);                                       
    },
    personContent(item){
        console.log('persionContent:',item);
        //更改保存操作
        let params = new Array();
        this.selectUserArr.map((mitem) => {
            let obj = new Object();
            obj.id = mitem.id;
            obj.username = item[0].username;
            params.push(obj);
        })
        editRecentHandle(params).then(res => {
            //console.log(res);
            if(res.code == 200 && res.sucess){
                this.$message({
                    type:'success',
                    message:'修改成功!'
                })
                this.searchReturn();
            }else{
                this.$message({
                    type:'error',
                    message:res.message
                })
            }
        })
    },
    show1(show) {
        console.log('show:',show)
        this.isShowPicker = show;
    },
    showPicker(row){
        this.isShowPicker = true;
        if(this.jArr.length != this.selectUserArr.length){
          this.selectUserArr.splice(this.selectUserArr.length-1,1);
        }
        this.selectUserArr.push(row);
        this.selectUserArr = this.selectUserArr.filter((element,index,self) => {//去重
            return self.indexOf(element) === index;
        })
        let arr = new Array();
        //console.log(this.selectUserArr);
        this.selectUserArr.map((item,index) => {
            if(arr.indexOf(item) == -1){
                let obj = {
                    name:item.curAssigneeName,
                    username:item.curAssigneeId
                }
                arr.push(obj);
            }
        })
        for(var i = 0; i < arr.length-1; i++){
            for(var j = i+1; j < arr.length; j++){
                if(arr[i].name==arr[j].name){
                    arr.splice(j,1);
                    j--;
                }
            }
        }
        this.transUser = arr;
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.area
      }).then(res => {
        console.log(res.data);
        this.projectOptions = res.data;
      });
    },
    change() {
      this.flag = !this.flag;
    },
    goDetail(item) {
      //跳转详情
      console.log(item)
      console.log(item.id)
      this.$router.push({
        path: item.deptName=='IT'?"itReport":"addReport",
        query: {
          id: item.id,
          title: "报事查询",
          path: this.$route.path,
          disabled: true
        }
      });
    },
    searchReturn(){
        this.currentPage = 1
        this.pageSize = 20;
        this.searchReturnListOK();
    },
    searchReturnListOK() {
      this.loading = true;
      this.list = [];
      rfList({
        signal:true,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        formNo: this.searchData.number,
        firstSortCode: this.searchData.business,
        regionCode: this.searchData.area,
        cityCompanyCode: this.searchData.city,
        projectCode: this.searchData.project,
        startDate: this.searchData.createdate,
        endDate: this.searchData.enddate,
        astartDate: this.searchData.acreatedate,
        aendDate: this.searchData.aenddate,
        rstartDate: this.searchData.rcreatedate,
        rendDate: this.searchData.renddate,
        ownerName: this.searchData.user,
        mobile: this.searchData.phone,
        customerDemand: this.searchData.customerDemand,
        processStateCode: this.searchData.step?this.searchData.step.toString():'',
        reworkFlag: this.searchData.reworkFlag,
        upgradeFlag: this.searchData.upgradeFlag,
        acceptChannelCode: this.searchData.acceptChannelCode,
        deptCode: this.searchData.deptCode,
        firstSortCode: this.searchData.firstSortCode,
        createUserName: this.searchData.createUserName,
        curAssigneeName: this.searchData.curAssigneeName,
        isAdmin: JSON.parse(sessionStorage.getItem('userInfo')).isAdmin,
        isSiBaiSeats: JSON.parse(sessionStorage.getItem('userInfo')).isSiBaiSeats?1:0,
        isFromHome:this.$route.query.isFromHome?1:0
      }).then(res => {
        this.loading = false;
        if (res.sucess && res.data) {
          this.list = res.data.records;
          this.currentPage = res.data.current;
          this.total = res.data.total;
        }
      });
    },
    searchExp() {
      let url = rfExport({
        formNo: this.searchData.number,
        firstSortCode: this.searchData.business,
        regionCode: this.searchData.area,
        cityCompanyCode: this.searchData.city,
        projectCode: this.searchData.project,
        startDate: this.searchData.createdate,
        endDate: this.searchData.enddate,
        astartDate: this.searchData.acreatedate,
        aendDate: this.searchData.aenddate,
        rstartDate: this.searchData.rcreatedate,
        rendDate: this.searchData.renddate,
        ownerName: this.searchData.user,
        mobile: this.searchData.phone,
        processStateCode: this.searchData.step?this.searchData.step.toString():'',
        reworkFlag: this.searchData.reworkFlag,
        upgradeFlag: this.searchData.upgradeFlag,
        acceptChannelCode: this.searchData.acceptChannelCode,
        deptCode: this.searchData.deptCode,
        firstSortCode: this.searchData.firstSortCode,
        createUserName: this.searchData.createUserName,
        isAdmin: JSON.parse(sessionStorage.getItem('userInfo')).isAdmin,
        isSiBaiSeats: JSON.parse(sessionStorage.getItem('userInfo')).isSiBaiSeats?1:0,
        isFromHome:this.$route.query.isFromHome?1:0
      });
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
    },
    reset() {
      this.searchData = Object.assign({}, this.searchDataReset);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.searchReturnListOK();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchReturnListOK();
    },
    ReportStepMonted(val) {
      ReportStep({ itemCode: val }).then(res => {
        this.step = res.data.processStateCode;
        this.step.map((item, index) => {
          if (item.itemValue == "报事升级") {//item.itemValue == "暂存" ||
            this.step.splice(index, 1);
          }
        });
      });
    }
  },
  mounted() {
    this.ReportStepMonted();
    dict().then(res => {
      if (res.sucess && res.data) {
        this.dict = res.data;
      }
    });
  },
  created() {
    this.areaList();
    let that = this;
    let filterPageData = new Promise((resolve,reject) => {
        if(that.$route.query.masterCode){
            if(that.$route.query.class == 'consult'){
                that.$set(that.searchData,'firstSortCode','coZX');
            }else if(that.$route.query.class == 'complain'){
                that.$set(that.searchData,'firstSortCode','coTS');
            }else if(that.$route.query.class == 'repair'){
                that.$set(that.searchData,'firstSortCode','coBX');
            }
            //that.searchData.firstSortCode = that.$route.query.class == 'consult'?'coZX':that.$route.query.class == 'complain'?'coTS':that.$route.query.class == 'repair'?'coBX':'';
            let _date = new Date(that.nowDate);
            let _year = _date.getFullYear();
            let _month = _date.getMonth() + 1 >= 10 ? _date.getMonth() + 1 : + '0' + Number(_date.getMonth() + 1);
            // if(that.$route.query.time == 'year'){
            //     // that.searchData.createdate = (_year + '-' + '01' + '-' + '01' + ' '+'00:00:00').toString();
            //     that.$set(that.searchData,'createdate',(_year + '-' + '01' + '-' + '01' + ' '+'00:00:00').toString())
            // }else if(that.$route.query.time == 'month'){
            //     // that.searchData.createdate = (_year + '-' + _month + '-' + '01' + ' '+'00:00:00').toString();
            //     that.$set(that.searchData,'createdate',(_year + '-' + _month + '-' + '01' + ' '+'00:00:00').toString())
            // }
            if(that.$route.query.startTime){
                that.$set(that.searchData,'createdate',(that.$route.query.startTime+' 00:00:00').toString())
            }
            if(that.$route.query.endTime){
                that.$set(that.searchData,'enddate',(that.$route.query.endTime+' 23:59:59').toString())
            }
            that.$set(that.searchData,'deptCode','deptDC');
            let formType = that.$route.query.type.split('-');
            if(formType[0] == 'area'){
                that.$set(that.searchData,'area',that.$route.query.masterCode);
                that.getCityList(that.searchData.area);
            }else{
                let params = {
                    code:that.$route.query.masterCode,
                    codeType:formType[0]
                }
                getRegionInfoByCode(params).then((res) => {
                    if(res.code == 200){
                        if(res.data.region_code){
                            that.getCityList(res.data.region_code)
                            that.$set(that.searchData,'area',res.data.region_code)
                        }
                        if(res.data.city_company_code){
                            that.getProjectList(res.data.city_company_code);
                            setTimeout(() => {
                                that.$set(that.searchData,'city',res.data.city_company_code)
                            },100);
                        }
                        if(res.data.project_code){
                            setTimeout(() => {
                                that.$set(that.searchData,'project',res.data.project_code)
                            }, 200);
                        }
                    }
                })
            }
            if(formType[1]){
                if(formType[1] == 'finishNum'){//已完成
                    that.$set(that.searchData,'step',['specialEnd','nomalEnd']); 
                }else if(formType[1] == 'doingNum'){//处理中
                    that.$set(that.searchData,'step',['toBeAssigned','handle','returnVisit']);
                }else if(formType[1] == 'levelUpNum'){//升级
                    that.$set(that.searchData,'upgradeFlag','1');
                }else if(formType[1] == 'reworkNum'){//返工
                    that.$set(that.searchData,'reworkFlag','1');
                }
            }
            let _Interval = setInterval(() => {
                if(formType[0] == 'area' && that.searchData.area){
                    resolve();
                    clearInterval(_Interval);
                }else if(formType[0] == 'city' && that.searchData.city){
                    resolve();
                    clearInterval(_Interval);
                }else if(formType[0] == 'project' && that.searchData.project){
                    resolve();
                    clearInterval(_Interval);
                }
            }, 10);
        }else if(that.$route.query.isSum){
            if(that.$route.query.class == 'consult'){
                that.$set(that.searchData,'firstSortCode','coZX');
            }else if(that.$route.query.class == 'complain'){
                that.$set(that.searchData,'firstSortCode','coTS');
            }else if(that.$route.query.class == 'repair'){
                that.$set(that.searchData,'firstSortCode','coBX');
            }
            let _date = new Date(that.nowDate);
            let _year = _date.getFullYear();
            let _month = _date.getMonth() + 1 >= 10 ? _date.getMonth() + 1 : + '0' + Number(_date.getMonth() + 1);
            // if(that.$route.query.time == 'year'){
            //     that.$set(that.searchData,'createdate',(_year + '-' + '01' + '-' + '01' + ' '+'00:00:00').toString());
            // }else if(that.$route.query.time == 'month'){
            //     that.$set(that.searchData,'createdate',(_year + '-' + _month + '-' + '01' + ' '+'00:00:00').toString());
            // }
            if(that.$route.query.startTime){
                that.$set(that.searchData,'createdate',(that.$route.query.startTime+' 00:00:00').toString())
            }
            if(that.$route.query.endTime){
                that.$set(that.searchData,'enddate',(that.$route.query.endTime+' 23:59:59').toString())
            }
            that.$set(that.searchData,'deptCode','deptDC');
            let formType = that.$route.query.type.split('-');
            if(formType[1]){
                if(formType[1] == 'finishNum'){//已完成
                    that.$set(that.searchData,'step',['specialEnd','nomalEnd']); 
                }else if(formType[1] == 'doingNum'){//处理中
                    that.$set(that.searchData,'step',['toBeAssigned','handle','returnVisit']);
                }else if(formType[1] == 'levelUpNum'){//升级
                    that.$set(that.searchData,'upgradeFlag','1');
                }else if(formType[1] == 'reworkNum'){//返工
                    that.$set(that.searchData,'reworkFlag','1');
                }
            }
            resolve();
        }else{
            resolve();
        }
    })
    filterPageData.then(() => {
        this.searchReturnListOK();
    })
  }
};
</script>

<style scoped>
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.mb-20 {
  /* height: 400px; */
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,
.levelUpTag img {
  vertical-align: middle;
  width: 32px;
  height: 16px;
}
.el-date-editor.el-input,.el-date-editor.el-input__inner{width:100% !important}
</style>
