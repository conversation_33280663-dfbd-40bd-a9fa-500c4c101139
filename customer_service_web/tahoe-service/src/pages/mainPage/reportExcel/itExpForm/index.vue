<template>
  <div class="search-bar">
    <div class="bg-style" style="margin-right:10px;">
      <el-form ref="form" label-position="right" :model="searchData" label-width="80px" size="mini">
        <div style="height:10px;"></div>
        <el-row>
          <el-col :span="5" :offset="0">
            <el-form-item label="项目">
              <el-select v-model="searchData.projectCode" filterable placeholder="请选择" @change="clearFirst">
                <el-option
                  v-for="i in dict.filter(i=>i.dictCode =='deptIT')"
                  :key="i.itemCode"
                  :label="i.itemValue"
                  :value="i.itemCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="一级分类">
              <el-select v-model="searchData.firstSortCode" placeholder="请选择">
                <el-option
                  v-for="(i,index) in arrayUniq(dict.filter(i=> this.searchData.projectCode && (i.dictCode == 'itFscBz' || i.dictCode == 'itFscKszx') && i.itemValue.indexOf(this.dict.filter(j => j.itemCode == this.searchData.projectCode)[0].itemValue) == 0))"
                  :key="index"
                  :label="i.itemValue"
                  :value="i.itemCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="二级分类">
              <el-select v-model="searchData.secSortCode" placeholder="请选择">
                <el-option
                  v-for="i in dict.filter(i=>i.dictCode =='itSec')"
                  :key="i.itemCode"
                  :label="i.itemValue"
                  :value="i.itemCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="报事渠道">
              <el-select v-model="searchData.reportChannelCode" placeholder="请选择">
                <el-option
                  v-for="i in dict.filter(i=>i.dictCode =='ITreportChannel')"
                  :key="i.itemCode"
                  :label="i.itemValue"
                  :value="i.itemCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="box" v-if="flag">
          <el-row class="mt-10">
            <el-col :span="5" :offset="0">
              <el-form-item label="报事人">
                <el-input v-model="searchData.ownerName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="业务步骤">
                <el-select
                  v-model="searchData.processStateCode"
                  multiple
                  collapse-tags
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dict.filter(i=>i.dictCode =='processStateCode')"
                    :key="item.itemCode"
                    :label="item.itemValue"
                    :value="item.itemCode"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="报事时间">
                <el-date-picker
                  prefix-icon="el-icon-date"
                  v-model="searchData.creationStartDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="至">
                <el-date-picker
                  prefix-icon="el-icon-date"
                  v-model="searchData.creationEndDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  default-time="23:59:59"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="mt-10">
            <el-col :span="5" :offset="0">
              <el-form-item label="客户诉求">
                <el-input v-model="searchData.customerDemand"/>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="处理意见">
                <el-input v-model="searchData.handleRecord"/>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1">
              <el-form-item label="工单编号">
                <el-input v-model="searchData.formNo" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" :offset="1"></el-col>
          </el-row>
          <!-- <el-row class="mt-10">
                        <el-col :span='5' :offset="1">
                            <el-form-item label="报事时间">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.creationStartDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span='5' :offset="1">
                            <el-form-item label="至">
                                <el-date-picker prefix-icon="el-icon-date" v-model="searchData.creationEndDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-col>
          </el-row>-->
        </div>
        <el-row class="mt-10">
          <el-col :span="24" style="text-align:right;padding-right:4%;">
            <button
              class="btnDown"
              @click.prevent="search"
              style="margin-left:20px;margin-top:3px;"
            >查询</button>
            <button class="btnDown" @click.prevent="searchExp()" style="margin-left:10px;">导出</button>
            <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
          </el-col>
        </el-row>
        <el-row class="mt-10">
          <el-col :span="24">
            <div class="ta-ct mt-10" style="position:relative;height:30px;">
              <hr style="background-color:#e6e6e6;height:1px;border:none;">
              <span class="txt" @click.prevent="change()">
                <i
                  style="font-size:18px;color:#E63F3C;"
                  :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"
                ></i>
              </span>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="bg-style mt-10 mb-20" style="margin-right:10px">
      <div style="width:100%;overflow:hidden;">
        <el-table
          v-loading="loading"
          tooltip-effect="dark"
          stripe
          element-loading-text="拼命加载中"
          :data="list"
          style="width: 100%"
        >
          <el-table-column prop="formNo" label="工单编号" width="120">
            <template slot-scope="scope">
              <a
                href="javascript:;"
                class="link-btn"
                @click="toFormDetail(scope.row)"
              >{{scope.row.formNo}}</a>
            </template>
          </el-table-column>
          <el-table-column prop="ownerName" label="用户名" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column width="100" prop="mobile" label="手机号"></el-table-column>
          <el-table-column prop="project" label="项目" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="firstSortName" label="一级分类" show-overflow-tooltip></el-table-column>
          <el-table-column prop="secSortName" label="二级分类" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="customerDemand" label="诉求" show-overflow-tooltip></el-table-column>
          <el-table-column prop="curAssigneeName" label="处理人" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="processStateName" label="业务步骤" width="70" show-overflow-tooltip></el-table-column>
          <el-table-column prop="creationDate" label="创建时间" width="150">
            <template slot-scope="scope">{{scope.row.creationDate | TimeMoment}}</template>
          </el-table-column>
          <!-- <el-table-column prop="assignDate" label="分派时间" width="150">
                        <template slot-scope="scope">
                            {{scope.row.assignDate | TimeMoment}}
                        </template>
                    </el-table-column>
                    <el-table-column label="处理时间" width="150">
                        <template slot-scope="scope">
                            {{scope.row.runTime | TimeMoment}}
                        </template>
                    </el-table-column>
          <el-table-column prop="reportChannelName" label="报事渠道"></el-table-column>-->
        </el-table>
        <!-- 分页 -->
        <wcg-page
          :pageSize="pageSize"
          :currentPage="currentPage"
          :total="total"
          :floorList="list"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @on-change="getPageList"
        ></wcg-page>
      </div>
    </div>
  </div>
</template>

<script>
import wcgPage from "@/components/wcgPage";
import {
  area,
  searchInterimReports,
  searchInterimReport,
  getItExportList
} from "@/api/port";
import { dict } from "@/api/wsd";
import { rfList, rfExport, exportFiles } from "@/api/export/exp";
export default {
  components: { wcgPage },
  watch: {},
  data() {
    return {
      searchData: {
        area: "",
        city: "",
        projectCode: "",
        processStateCode: [],
        formNo: "",
      },
      step: [],
      areaOptions: [],
      projectOptions: [],
      cityOptions: [],
      flag: false,
      dict: [],
      loading: false,
      list: [],
      pageSize: 20,
      currentPage: 1,
      total: 0
    };
  },
  methods: {
    toFormDetail(item) {
      console.log(item);
      this.$router.push({
        path: "itReport",
        query: {
          id: item.id,
          title: "报事查询",
          path: this.$route.path,
          disabled: true
        }
      });
    },
    arrayUniq(arr){//去重
      let len = arr.length;
      let newArr = new Array();
      let isPush = true;
      for(let i=0;i<len;i++){
        isPush = true;
        for(let n=0;n<newArr.length;n++){
          if(newArr[n].itemCode == arr[i].itemCode){
            isPush = false;
          }
        }
        if(isPush){
          newArr.push({itemCode:arr[i].itemCode,itemValue:arr[i].itemValue});
        }
      }
      return newArr;
    },
    searchExp() {
      exportFiles(this.searchData, "/repForm/exportIT");
    },
    clearFirst(){
      this.$set(this.searchData,'firstSortCode',null);
    },
    search() {
      this.pageNum = 1;
      this.getPageList();
    },
    reset() {
      this.searchData = {
        processStateCode: []
      };
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    getProjectList(val) {
      searchInterimReport({
        cityCompanyCode: val,
        regionCode: this.searchData.area
      }).then(res => {
        console.log(res.data);
        this.projectOptions = res.data;
      });
    },
    change() {
      this.flag = !this.flag;
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getPageList();
    },
    getPageList() {
      this.loading = true;
      let params = JSON.parse(JSON.stringify(this.searchData));
      params.isAdmin = JSON.parse(sessionStorage.getItem("userInfo")).isAdmin;
      params.pageNum = this.currentPage;
      params.pageSize = this.pageSize;
      if (params.processStateCode) {
        params.processStateCode = params.processStateCode.toString();
      }
      delete params.area;
      delete params.city;
      getItExportList(params).then(res => {
        if (res.code == 200) {
          this.list = res.data.records;
          this.total = res.data.total;
          this.loading = false;
        }
      });
    }
  },
  created() {
    this.areaList();
    dict().then(res => {
      if (res.sucess && res.data) {
        this.dict = res.data;
      }
    });
    this.getPageList();
  }
};
</script>

<style lang="scss" scoped="scoped">
.demonstration {
  float: left;
  line-height: 32px;
  padding-right: 18%;
}
.block {
  margin-top: 30px;
  width: 100%;
}
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.mb-20 {
  /* height: 400px; */
  background: #ffffff;
  border-radius: 10px;
}
.el-select-dropdown {
  z-index: 1 !important;
}
.reworkTag img,
.levelUpTag img {
  vertical-align: middle;
  width: 32px;
  height: 16px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
.link-btn {
  display: inline-block;
  min-width: 25px;
  color: #1d85fe;
  font-family: "MicrosoftYaHei";
  text-decoration: none;
}
</style>