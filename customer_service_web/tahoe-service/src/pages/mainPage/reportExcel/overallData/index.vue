<template>
  <div class="search-bar">
    <div class="bg-style" style="margin-right:10px;">
      <el-form ref="form" label-position="left" :model="searchData" label-width="80px" size="mini">
        <div style="height:10px;"></div>
        <el-row>
          <el-col :span="5" :offset="1">
            <el-form-item label="创建时间">
              <el-date-picker prefix-icon="el-icon-date" v-model="searchData.startDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-form-item label="至">
              <el-date-picker prefix-icon="el-icon-date" v-model="searchData.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
          </el-col>

          <button class="btnDown" @click.prevent="searchReturn" style="margin-left:20px;margin-top:3px;">查询</button>
          <button class="btnDown" @click.prevent="searchExp" style="margin-left:10px;">导出</button>
          <button class="btnDown" @click.prevent="reset" style="margin-left:10px">重置</button>
        </el-row>
        <div class="box" v-if="flag">
          
        </div>
        <el-row class="mt-10">
          <el-col :span="24">
            <div class="ta-ct mt-10" style="position:relative;height:30px;">
              <hr style='background-color:#e6e6e6;height:1px;border:none;' />
              <span class="txt" @click.prevent="change()">
                <i style="font-size:18px;color:#E63F3C;" :class="flag?'el-icon-caret-top':'el-icon-caret-bottom'"></i>
              </span>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="bg-style mt-10 mb-20" style="margin-right:10px">
      <div style="width:100%;overflow:hidden;">
        <el-table v-loading="loading" stripe element-loading-text="拼命加载中" :data="list" style="width: 100%">
           <el-table-column label="报修数据统计" align="center">
                <el-table-column label="维修工单" align="left">
                    <el-table-column prop="region" label="区域" align="left"></el-table-column>
                    <el-table-column prop="city" label="城市" align="left" :formatter="ifnull"></el-table-column>
                    <el-table-column prop="fzzNum" label="400" align="left"></el-table-column>
                    <el-table-column prop="total" label="工单总量" align="left"></el-table-column>
                    <el-table-column prop="timelyClosureRate" label="及时关闭率（%）" align="left"></el-table-column>
                    <el-table-column prop="closureSort" label="名次" align="left" :formatter="ifnull"></el-table-column>
                    <el-table-column prop="specialClosureNum" label="特殊关闭单数" align="left"></el-table-column>
                </el-table-column>
                <el-table-column label="400维修满意度" align="left">
                    <el-table-column prop="satisfaction" label="回访满意度" align="left"></el-table-column>
                    <el-table-column prop="satisfactionSort" label="名次" align="left" :formatter="ifnull"></el-table-column>
                </el-table-column>
            </el-table-column>
        </el-table>
       </div>
    </div>
  </div>
</template>

<script>
import { area, searchInterimReports } from "@/api/port";
import { repairWhole, repairWholeExport } from "@/api/export/exp";
export default {
  watch: {
    "searchData.area"(newval, oldval) {
      if (newval != "") {
        this.cityprojectDisabled = false;
        if (newval != oldval) {
          this.floorDisabled = true;
          this.buildOptions = [];
          this.searchData.city = this.searchData.project = "";
        }
      } else {
        this.cityprojectDisabled = true;
      }
    }
  },
  data() {
    return {
      searchData: {},
      areaOptions: [],
      cityOptions: [],
      loading: false,
      flag:false,
      list: [],
      pageList:[]
    };
  },
  methods: {
    ifnull(row, column) {
      return row[column.property] ? row[column.property] : "——";
    },
    change() {
      this.flag = !this.flag;
    },
    getCityList(val) {
      searchInterimReports({ regionCode: val }).then(res => {
        if (res.code == 200) {
          this.cityOptions = res.data;
        }
      });
    },
    areaList() {
      area().then(res => {
        this.areaOptions = res.data;
      });
    },
    searchReturn() {
      this.loading = true;
      this.list = [];
      repairWhole(this.searchData)
        .then(res => {
          if (res.code == 200) {
            this.list = res.data;
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    searchExp() {
      let url = repairWholeExport(this.searchData);
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
    },
    reset() {
      this.searchData = {};
    }
  },
  created() {
    this.areaList();
  },
  mounted() {}
};
</script>

<style scoped>
.search-bar .txt {
  position: absolute;
  top: -9px;
  left: 50%;
  color: rgb(100, 98, 98);
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  color: #1d85fe;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
