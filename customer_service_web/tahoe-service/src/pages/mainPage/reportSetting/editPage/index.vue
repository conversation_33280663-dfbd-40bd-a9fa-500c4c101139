<template>
  <div id="setEditPage">
    <div class="btn">
      <span></span>
      <span class="title1">机构人员信息</span>
      <div class="btn1">
        <div class="btnDown" @click="submit()">提交</div>
        <div class="btnDown" @click="goBack()">返回</div>
      </div>
    </div>
    <div class="mechanism">
      <span style="margin-left: 127px;">机构</span>
      <span style="margin-left: 16px;">{{this.$route.query.treePath}}</span>
    </div>
    <div class="wrap" v-if="personInChargeIsShow">
      <div class="bigBox">
        <div class="box">
          <div class="title">
            <span>客服负责人</span>
            <span @click="pick('1')">
              <el-input
                type="textarea"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input"
                class="break-word"
              ></el-input>
            </span>
          </div>
        </div>
        <div class="box" v-if="personInChargeIsShow">
          <div class="title">
            <span>客服处理人</span>
            <span @click="pick('2')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input1"
              ></el-input>
            </span>
          </div>
        </div>
      </div>
      <div class="bigBox">
        <div class="box">
          <div class="title">
            <span>房修负责人</span>
            <span @click="pick('3')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input2"
              ></el-input>
            </span>
          </div>
        </div>
        <div class="box" v-if="personInChargeIsShow">
          <div class="title">
            <span>房修处理人</span>
            <span @click="pick('4')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input3"
              ></el-input>
            </span>
          </div>
        </div>
      </div>
      <div class="bigBox">
        <div class="box" v-if="personInChargeIsShow">
          <div class="title">
            <span>房修录入员</span>
            <span @click="pick('5')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input4"
              ></el-input>
            </span>
          </div>
        </div>
        <div class="box" v-if="personInChargeIsShow" style="opacity: 0">
          <div class="title">
            <span>房修录入员</span>
            <span @click="pick('5')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input4"
              ></el-input>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="wrap" v-if="!personInChargeIsShow">
      <div class="bigBox">
        <div class="box">
          <div class="title">
            <span>客服负责人</span>
            <span @click="pick('1')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input"
              ></el-input>
            </span>
          </div>
        </div>
        <div class="box">
          <div class="title">
            <span>房修负责人</span>
            <span @click="pick('3')">
              <el-input
                type="textarea"
                class="break-word"
                autosize
                style="width: 60%;margin-left:13px"
                placeholder
                v-model="input2"
              ></el-input>
            </span>
          </div>
        </div>
      </div>
    </div>
    <pick ref='pick' :transUser="transUser" :visible="userCon" @personContent="personContent" @show1="show1"></pick>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import pick from "../pick";
import { orgTree, submitList } from "@/api/reportSetting";
let [arr, arr1, arr2, arr3, arr4] = [[], [], [], [], []];
let [brr, brr1, brr2, brr3, brr4] = [[], [], [], [], []];
export default {
  name: "setEditPage",
  components: {
    pick
  },
  data() {
    return {
      transUser: [],
      userCon: false,
      personFlag: "",
      setData: {},
      input: "",
      input1: "",
      input2: "",
      input3: "",
      input4: "",
      getId: "",
      getId1: "",
      getId2: "",
      getId3: "",
      getId4: "",
      orgOptions: [], //组织机构
      projectRoleAttrDO: {
        serviceLeaders: [],
        servicesWorks: [],
        repairsLeaders: [],
        repairsWorks: [],
        fixInputUsers: []
      },
      personInChargeIsShow: true,
      itemData: {},
      flag1: true,
      flag2: true,
      flag3: true,
      flag4: true,
      flag5: true
    };
  },
  methods: {
    goBack() {
      this.input = "";
      this.input1 = "";
      this.input2 = "";
      this.input3 = "";
      this.input4 = "";
      this.getId = "";
      this.getId1 = "";
      this.getId2 = "";
      this.getId3 = "";
      this.getId4 = "";
      [arr, arr1, arr2, arr3, arr4] = [[], [], [], [], []];
      [brr, brr1, brr2, brr3, brr4] = [[], [], [], [], []];
      this.$router.push({
        path: "ReportSetting",
        query: {}
      });
    },
    submit() {
      let loadingInstance = Loading.service({
        text: "正在提交...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.8)"
      });
      let row = this.$route.query.row;
      let obj = {};
      if (this.$route.query.type == "1") {
        obj = {
          type: row.type,
          jtCustomerUserId: this.getId,
          jtFangxiuUserId: this.getId2
        };
      } else if (this.$route.query.type == "2") {
        obj = {
          type: row.type,
          regionCode: row.typeName,
          qyCustomerUserId: this.getId,
          qyFangxiuUserId: this.getId2
        };
      } else if (this.$route.query.type == "3") {
        obj = {
          type: row.type,
          regionCode: this.$route.query.typeName,
          cityCode: row.typeName,
          csCustomerUserId: this.getId,
          csFangxiuUserId: this.getId2
        };
      } else if (this.$route.query.type == "4") {
        obj = {
          type: row.type,
          projectCode: row.typeName,
          xmCustomerUserId: this.getId,
          xmProcessingCustomerUserId: this.getId1,
          xmProcessingFangxiuLRYUserId: this.getId4,
          xmFangxiuUserId: this.getId2,
          xmProcessingFangxiuUserId: this.getId3
        };
      }
      this.$emit("loading", true);
      submitList(obj).then(res => {
        if (res.code == 200) {
          this.$emit("loading", false);
          this.$message.success(res.message);
          this.$router.push({
            path: "ReportSetting",
            query: {}
          });
          this.$nextTick(() => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
          this.$emit("loading", false);
          this.$nextTick(() => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
        }
        this.input = "";
        this.input1 = "";
        this.input2 = "";
        this.input3 = "";
        this.input4 = "";
        this.getId = "";
        this.getId1 = "";
        this.getId2 = "";
        this.getId3 = "";
        this.getId4 = "";
        [arr, arr1, arr2, arr3, arr4] = [[], [], [], [], []];
        [brr, brr1, brr2, brr3, brr4] = [[], [], [], [], []];
      });
    },
    pick(type) {
      this.$refs.pick.fileLists = [];
      this.$refs.pick.username = "";
      if (this.$route.query.data.projectRoleAttrDO) {
        switch (type) {
          case "1":
            this.transUser = this.projectRoleAttrDO.serviceLeaders;
            break;
          case "2":
            this.transUser = this.projectRoleAttrDO.servicesWorks;
            break;
          case "3":
            this.transUser = this.projectRoleAttrDO.repairsLeaders;
            break;
          case "4":
            this.transUser = this.projectRoleAttrDO.repairsWorks;
            break;
          case "5":
            this.transUser = this.projectRoleAttrDO.fixInputUsers;
            break;
        }
      }
      console.log(this.transUser);
      this.userCon = true;
      this.personFlag = type;
    },
    personContent(item) {
      console.log(item);
      this.itemData = item;
      switch (this.personFlag) {
        case "1":
          (this.input = item.names),
            (this.getId = item.usernames),
            (this.transUser = item.list),
            (this.projectRoleAttrDO.serviceLeaders = item.list);
          break;
        case "2":
          (this.input1 = item.names),
            (this.getId1 = item.usernames),
            (this.transUser = item.list),
            (this.projectRoleAttrDO.servicesWorks = item.list);
          break;
        case "3":
          (this.input2 = item.names),
            (this.getId2 = item.usernames),
            (this.transUser = item.list),
            (this.projectRoleAttrDO.repairsLeaders = item.list);
          break;
        case "4":
          (this.input3 = item.names),
            (this.getId3 = item.usernames), 
            (this.transUser = item.list),
            (this.projectRoleAttrDO.repairsWorks = item.list);
          break;
        case "5":
          (this.input4 = item.names),
            (this.getId4 = item.usernames),
            (this.transUser = item.list),
            (this.projectRoleAttrDO.fixInputUsers = item.list);
          break;
      }
      console.log(this.transUser);
    },
    show1(show) {
      this.userCon = show;
    },
    filterData(data) {
      let arr = [];
      let arr1 = [];
      let arr2 = [];
      let obj = {};
      for (let i in data) {
        arr.push(data[i].sid);
        arr1.push(data[i].name);
        arr2.push(data[i].username);
      }
      obj = {
        id: arr.join(","),
        name: arr1.join(","),
        username: arr2.join(",")
      };
      return obj;
    }
  },
  mounted() {
    this.input = "";
    this.input1 = "";
    this.input2 = "";
    this.input3 = "";
    this.input4 = "";
    this.getId = "";
    this.getId1 = "";
    this.getId2 = "";
    this.getId3 = "";
    this.getId4 = "";
    [arr, arr1, arr2, arr3, arr4] = [[], [], [], [], []];
    [brr, brr1, brr2, brr3, brr4] = [[], [], [], [], []];
    if (
      this.$route.query.type == "1" ||
      this.$route.query.type == "2" ||
      this.$route.query.type == "3"
    ) {
      this.personInChargeIsShow = false;
    } else {
      this.personInChargeIsShow = true;
    }
    this.$route.query.data.projectRoleAttrDO.serviceLeaders.forEach(item => {
      arr.push(item.userName);
      brr.push(item.userId);
      this.projectRoleAttrDO.serviceLeaders.push({
        name: item.userName,
        username: item.userId,
        sid: item.orgId
      });
    });
    this.$route.query.data.projectRoleAttrDO.servicesWorks.forEach(item => {
      arr1.push(item.userName);
      brr1.push(item.userId);
      this.projectRoleAttrDO.servicesWorks.push({
        name: item.userName,
        username: item.userId,
        sid: item.orgId
      });
    });
    this.$route.query.data.projectRoleAttrDO.repairsLeaders.forEach(item => {
      arr2.push(item.userName);
      brr2.push(item.userId);
      this.projectRoleAttrDO.repairsLeaders.push({
        name: item.userName,
        username: item.userId,
        sid: item.orgId
      });
    });
    this.$route.query.data.projectRoleAttrDO.repairsWorks.forEach(item => {
      arr3.push(item.userName);
      brr3.push(item.userId);
      this.projectRoleAttrDO.repairsWorks.push({
        name: item.userName,
        username: item.userId,
        sid: item.orgId
      });
    });
    this.$route.query.data.projectRoleAttrDO.fixInputUsers.forEach(item => {
      arr4.push(item.userName);
      brr4.push(item.userId);
      this.projectRoleAttrDO.fixInputUsers.push({
        name: item.userName,
        username: item.userId,
        sid: item.orgId
      });
    });
    // str.substr(str.length-1,1) str.charAt(str.length – 1)
    [this.input, this.input1, this.input2, this.input3, this.input4] = [
      arr.join(","),
      arr1.join(","),
      arr2.join(","),
      arr3.join(","),
      arr4.join(",")
    ];
    // if (this.input.charAt(this.input.length - 1) == ",") {
    //   alert(1);
    // }
    [this.getId, this.getId1, this.getId2, this.getId3, this.getId4] = [
      brr.join(","),
      brr1.join(","),
      brr2.join(","),
      brr3.join(","),
      brr4.join(",")
    ];
  }
};
</script>

<style scoped>
#setEditPage {
  /* background-color: #fff; */
  border-radius: 5px;
}
#setEditPage .btn {
  background-color: #fff;
  height: 45px;
  line-height: 45px;
  border-bottom: 1px solid rgb(230, 230, 230);
}
#setEditPage .btn span:nth-child(2) {
  display: inline-block;
  height: 13px;
  line-height: 14px;
  font-size: 14px;
  padding: 3px;
  border-left: solid 2px #e63f3c;
  margin-left: 10px;
}
#setEditPage .mechanism {
  line-height: 43px;
  /* margin-left: 6px; */
  background-color: #fff;
  /* margin: 10px 0; */
  padding-top: 9px;
}
#setEditPage .btn .btnDown {
  /* float: right; */
  margin: 12px 10px;
}
#setEditPage .btn .btn1 {
  float: right;
}
#setEditPage .wrap {
  width: 100%;
  background-color: #fff;
  /* padding: 10px; */
  /* margin-top: 10px; */
  /* border-radius: 5px; */
  padding-bottom: 30px;
}
#setEditPage .wrap .bigBox {
  display: flex;
}
#setEditPage .wrap .bigBox .box {
  flex: 1;
}
#setEditPage .wrap .box {
  /* padding: 10px 0; */
  margin-left: 94px;
  margin-top: 10px;
}
#setEditPage .wrap .box .title span:nth-child(1) {
  vertical-align: super;
}
/* #setEditPage .wrap .box {
  width: 100%;
  padding: 10px;
  background-color: #fff;
  box-sizing: border-box;
  margin-top: 10px;
  border-radius: 5px;
}
#setEditPage .wrap .box:nth-child(1) {
  margin-top: 0;
}
#setEditPage .wrap .box .title {
  line-height: 32px;
  margin-bottom: 10px;
} */
#setEditPage .wrap .box .title {
  /* width: 2px;
  height: 15px;
  background-color: red; */
  /* display: inline-block; */
  /* vertical-align: middle; */
  /* position: relative;
  top: 3px; */
}
#setEditPage .wrap .box .title span:nth-child(1) {
  /* font-size: 14px;
  font-weight: bold; */
}
</style>

<style>
#setEditPage .el-input__prefix {
  right: 10px;
  top: -8px;
}
</style>

