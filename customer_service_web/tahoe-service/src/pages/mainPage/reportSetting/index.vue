<template>
  <div id="reportSetting" style="width: calc( 100% - 10px);">
    <div class="table-container">
      <div class="table-body" v-loading="loading" element-loading-text="玩命加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)">
        <el-table ref="csmTagTable" :data="tableData" style="width: 100%;margin-bottom: 20px;" @row-click="rowClick" border row-key="id" :expand-row-keys="expandIds" stripe >
          <el-table-column label="机构" show-overflow-tooltip>
            <template slot-scope="scope">
              <i v-if="scope.treeNode && scope.row.children" :class="scope.treeNode.expanded?'el-icon-minus':'el-icon-plus'"></i>
              {{scope.row.text}}
            </template>
          </el-table-column>
          <el-table-column v-for="(item,index) in tableHeader" v-if="item.isShow" :width="item.width" :label="item.label" :prop="item.prop" :key="index" align="right"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="czscope1">
              <el-button size="mini" @click="handleEdit(czscope1.$index, czscope1.row)" class="btnDown">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getReportSettingList,
  getReportSettingEdit
} from "@/api/reportSetting";
export default {
  name: "reportSetting",
  data() {
    return {
      loading: true,
      tableData: [],
      // expands: [],
      expandIds: [],
      tableHeader: [
        //{label:'区域/城市/项目',prop:'masterSlaveName',width:136},
        {
          label: "客服负责人",
          prop: "serviceLeaders",
          width: "",
          isShow: true
        },
        { label: "客服处理人", prop: "servicesWorks", width: "", isShow: true },
        { label: "房修录入员", prop: "fixInputUsers", width: "", isShow: true },
        {
          label: "房修负责人",
          prop: "repairsLeaders",
          width: "",
          isShow: true
        },
        { label: "房修处理人", prop: "repairsWorks", width: "", isShow: true }
      ]
    };
  },
  methods: {
    getReportSettingList(params) {
      this.loading = true;
      getReportSettingList(params).then(res => {
        if (res.code == 200) {
          // delete res.data[0].hasChildren;
          res.data.forEach(item => {
            delete item.hasChildren;
            item.children.forEach(item1 => {
              delete item1.hasChildren;
              item1.children.forEach(item2 => {
                delete item2.hasChildren;
                item2.children.forEach(item3 => {
                  delete item3.hasChildren;
                  delete item3.children;
                });
              });
            });
          });
          this.tableData = res.data;
          
          this.loading = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },

    rowClick(row, event, column) {
      if (row.children && row.children.length > 0){
        console.log(row)
        this.$refs.csmTagTable.store.toggleTreeExpansion(row);
      }
    },
    // rowClick(row, event, column) {
    //   Array.prototype.remove = function(val) {
    //     let index = this.indexOf(val);
    //     if (index > -1) {
    //       this.splice(index, 1);
    //     }
    //   };
    //   if (this.expands.indexOf(row.id) < 0 && row.children.length > 0) {
    //     this.expands.push(row.id);
    //     row.expand = true;
    //   } else {
    //     this.expands.remove(row.id);
    //     row.expand = false;
    //   }
    // },
    handleEdit(idx, row) {
      let typeName = "";
      if (row.type == "3") {
        this.tableData.map((v, i) => {
          if (row.parentId == v.id) {
            typeName = v.typeName;
          }
          if (v.children) {
            v.children.map((v, i) => {
              if (row.parentId == v.id) {
                typeName = v.typeName;
              }
              if (v.children) {
                v.children.map((v, i) => {
                  if (row.parentId == v.id) {
                    typeName = v.typeName;
                  }
                  if (v.children) {
                    v.children.map((v, i) => {
                      if (row.parentId == v.id) {
                        typeName = v.typeName;
                      }
                    });
                  }
                });
              }
            });
          }
        });
      }
      getReportSettingEdit({
        type: row.type,
        typeName: row.typeName,
        treePath: row.treePath
      }).then(res => {
        if (res.code == 200) {
          this.$router.push({
            path: "ReportSettingEdit",
            query: {
              treePath: res.data.treePath,
              type: res.data.type,
              row: row,
              data: res.data,
              typeName: typeName
            }
          });
        }
      });
    }
  },
  created() {
    this.getReportSettingList();
  }
};
</script>

<style scoped>
.el-icon-minus,
.el-icon-plus {
  font-size: 12px;
  color: #e63f3c;
  font-weight: bold;
}
</style>
<style>
/* #reportSetting .table-body .el-icon-arrow-right {
  display: none;
} */
#reportSetting .table-body .el-table__expand-icon {
  display: none;
}
</style>
