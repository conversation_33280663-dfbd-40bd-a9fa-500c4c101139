<template>
  <div id="reportSetting" style="width: calc( 100% - 10px);">
    <div class="table-container">
      <div
        class="table-body"
        v-loading="loading"
        element-loading-text="玩命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <el-table
          :data="tableData"
          id="firstLevelTable"
          style="width:100%;"
          @row-click="rowClick"
          row-key="id"
          :expand-row-keys="expands"
          :default-expand-all="true"
          stripe
          ref="table"
        >
          <el-table-column type="expand" class="width0" width="0" align="left">
            <template slot-scope="scope">
              <!--第二层表开始-->
              <el-table :data="scope.row.children" id="secondLevelTable" style="width: 100%" :show-header="false" @row-click="rowClick"
                row-key="id"
                :expand-row-keys="expands"
                stripe>
                <el-table-column type="expand" class="width0" width="0" align="center">
                  <template slot-scope="tscope">
                    <!--第三层表开始-->
                    <el-table
                      :data="tscope.row.children"
                      id="thirdLevelTable"
                      style="width: 100%"
                      :show-header="false"
                      @row-click="rowClick"
                      row-key="id"
                      :expand-row-keys="expands"
                      stripe
                    >
                      <el-table-column type="expand" class="width0" width="0" align="center">
                        <template slot-scope="fscope">
                          <!--第四层表开始-->
                          <el-table
                            :data="fscope.row.children"
                            id="forthLevelTable"
                            style="width: 100%"
                            :show-header="false"
                            @row-click="rowClick"
                            row-key="id"
                            :expand-row-keys="expands"
                            stripe
                          >
                            <el-table-column type="expand" class="width0" width="0" align="right"></el-table-column>
                            <el-table-column width="210" label="机构" align="left">
                              <template slot-scope="scope1">
                                <span
                                  style="display:inline-block;padding-left:80px;"
                                >{{scope1.row.text}}</span>
                              </template>
                            </el-table-column>
                            <el-table-column
                              v-for="(item,index) in tableHeader"
                              v-if="item.isShow"
                              :width="item.width"
                              :label="item.label"
                              :prop="item.prop"
                              :key="index"
                              align="right"
                            ></el-table-column>
                            <el-table-column label="操作" align="center">
                              <template slot-scope="czscope1">
                                <el-button
                                  size="mini"
                                  @click="handleEdit(czscope1.$index, czscope1.row)"
                                  class="btnDown"
                                >编辑</el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                          <!-- 第四层表结束 -->
                        </template>
                      </el-table-column>
                      <el-table-column width="210" label="机构" align="left">
                        <template slot-scope="scope2">
                          <span style="display:inline-block;padding-left:50px;">
                            <i :class="scope2.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                            {{scope2.row.text}}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-for="(item,index) in tableHeader"
                        v-if="item.isShow"
                        :width="item.width"
                        :label="item.label"
                        :prop="item.prop"
                        :key="index"
                        align="right"
                      ></el-table-column>
                      <el-table-column label="操作" align="center">
                        <template slot-scope="czscope2">
                          <el-button
                            size="mini"
                            @click="handleEdit(czscope2.$index, czscope2.row)"
                            class="btnDown"
                          >编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!-- 第三层表结束 -->
                  </template>
                </el-table-column>
                <el-table-column width="210" label="机构" align="left">
                  <template slot-scope="scope3">
                    <span style="display:inline-block;padding-left:30px;">
                      <i :class="scope3.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                      {{scope3.row.text}}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item,index) in tableHeader"
                  v-if="item.isShow"
                  :width="item.width"
                  :label="item.label"
                  :prop="item.prop"
                  :key="index"
                  align="right"
                ></el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="cscope">
                    <el-button
                      size="mini"
                      @click="handleEdit(cscope.$index, cscope.row)"
                      class="btnDown"
                    >编辑</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- -第二层表结束 -->
            </template>
          </el-table-column>
          <el-table-column width="210" label="机构" align="left">
            <template slot-scope="scope4">
              <span style="display:inline-block;padding-left:5px;">
                <i :class="scope4.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                {{scope4.row.text}}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item,index) in tableHeader"
            v-if="item.isShow"
            :width="item.width"
            :label="item.label"
            :prop="item.prop"
            :key="index"
            align="right"
          >
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="czscope3">
              <el-button
                @click="handleEdit(czscope3.$index, czscope3.row)"
                type="text"
                size="small"
                class="btnDown"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getReportSettingList,
  getReportSettingEdit
} from "@/api/reportSetting";
export default {
  name: "reportSetting",
  data() {
    return {
      loading: true,
      tableData: [],
      expands: [],
      tableHeader: [
        //{label:'区域/城市/项目',prop:'masterSlaveName',width:136},
        {
          label: "客服负责人",
          prop: "serviceLeaders",
          width: "",
          isShow: true
        },
        { label: "客服处理人", prop: "servicesWorks", width: "", isShow: true },
        { label: "房修录入员", prop: "fixInputUsers", width: "", isShow: true },
        {
          label: "房修负责人",
          prop: "repairsLeaders",
          width: "",
          isShow: true
        },
        { label: "房修处理人", prop: "repairsWorks", width: "", isShow: true }
      ]
    };
  },
  methods: {
    getReportSettingList(params) {
      this.loading = true;
      getReportSettingList(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data;
          res.data.forEach(item => {
            this.expands.push(item.id);
          });
          this.loading = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    rowClick(row, event, column) {
      Array.prototype.remove = function(val) {
        let index = this.indexOf(val);
        if (index > -1) {
          this.splice(index, 1);
        }
      };
      if (this.expands.indexOf(row.id) < 0 && row.children.length > 0) {
        this.expands.push(row.id);
        row.expand = true;
      } else {
        this.expands.remove(row.id);
        row.expand = false;
      }
    },
    handleEdit(idx, row) {
      let typeName = "";
      if (row.type == "3") {
        this.tableData.map((v, i) => {
          if (row.parentId == v.id) {
            typeName = v.typeName;
          }
          if (v.children) {
            v.children.map((v, i) => {
              if (row.parentId == v.id) {
                typeName = v.typeName;
              }
              if (v.children) {
                v.children.map((v, i) => {
                  if (row.parentId == v.id) {
                    typeName = v.typeName;
                  }
                  if (v.children) {
                    v.children.map((v, i) => {
                      if (row.parentId == v.id) {
                        typeName = v.typeName;
                      }
                    });
                  }
                });
              }
            });
          }
        });
      }
      getReportSettingEdit({
        type: row.type,
        typeName: row.typeName,
        treePath: row.treePath
      }).then(res => {
        if (res.code == 200) {
          this.$router.push({
            path: "ReportSettingEdit",
            query: {
              treePath: res.data.treePath,
              type: res.data.type,
              row: row,
              data: res.data,
              typeName: typeName
            }
          });
        }
      });
    }
  },
  created() {
    this.getReportSettingList();
  }
};
</script>

<style scoped>
.el-icon-minus,
.el-icon-plus {
  font-size: 12px;
  color: #e63f3c;
  font-weight: bold;
}
</style>
<style>
#reportSetting .table-body .el-icon-arrow-right {
  display: none;
}
</style>
