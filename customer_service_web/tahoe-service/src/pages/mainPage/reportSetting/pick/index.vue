<template>
  <div class="add-adm">
    <el-dialog title="选择授权人员" :visible.sync="personVisible" width="1000px" @close="close">
      <div class="fl ml-10">
        <el-input class="ft-14" placeholder="输入部门名称过滤" maxlength="25" v-model="filterText"></el-input>
        <el-tree
          v-loading="listLoading"
          :props="defaultProps"
          ref="orgTree"
          node-key="sid"
          check-on-click-node
          highlight-current
          :expand-on-click-node="false"
          @check="getUser"
          :filter-node-method="filterNode"
          :default-expanded-keys="['15a7f350f4b7b738c095a894d7cb9566']"
          :data="userOptions"
        ></el-tree>
      </div>
      <div class="fl ml-30">
        <el-input
          class="ft-14"
          placeholder="输入用户名或账号搜索"
          v-model="username"      
          @keyup.enter.native="search"
        ></el-input>
        <el-table
          stripe
          :data="fileLists"
          height="300"
          :border="true"
          @selection-change="handleSelectionChange"
          :header-cell-style="tableHeaderColor"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="待选人员" align="left" width="200">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <div slot="content">{{scope.row.orgNameTree}}</div>
                <span>{{scope.row.name}}({{scope.row.username}})</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="fl ml-10" style="padding-top:10%;">
        <button class="btnDown" @click="selectOrg">→</button>
        <button class="btnDown" @click="toUnselect">←</button>
      </div>
      <div class="fl ml-10">
        <!-- <el-input style="opacity:0;"></el-input> -->
        <el-table
          stripe
          :data="fileLists1"
          height="322"
          :border="true"
          @selection-change="handleSelectionChange1"
          :header-cell-style="tableHeaderColor"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="已选人员" align="left" width="200">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <div slot="content">{{scope.row.orgNameTree}}</div>
                <span>{{scope.row.name}}({{scope.row.username}})</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="fl ml-10" style="padding-top:6%;">
      </div>-->
      <div class="clear ta-ct">
        <div style="height:10px;"></div>
        <el-button style="background:#E63F3C;color:white;" @click="sureSelect()">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { orgTree, userList, userListBySearch } from "@/api/reportSetting";
export default {
  name: "addUser",
  props: {
    // orgOptions:{
    //     type:Array,
    //     default:[],
    // },
    transUser: {
      type: Array,
      default: []
    },
    visible: {
      type: Boolean,
      default: false
    },
    singleSelect:{
      type: Boolean,
      default: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.orgTree.filter(val);
    },
    transUser: {
      handler: function(v, o) {
        console.log(v);
        this.fileLists1 = v || [];
      },
      deep: true
    },
    visible: {
      immediate: true,
      handler: function(v, o) {
        console.log(v);
        this.personVisible = v;
      },
      deep: true
    },
    singleSelect(val){
      this.isSingleSel = val;
    }
  },
  data() {
    return {
      k: 0,
      filterText: "",
      username: "",
      listLoading: false,
      defaultProps: {
        label: "name",
        children: "childs"
      },
      userOptions: [],
      unselected: [],
      selected: [],
      fileLists: [],
      fileLists1: [],
      // partChecked:false,
      // partChecked1:false,
      personVisible: false,
      isSingleSel:false,
    };
  },
  methods: {
    //搜索候选人
    search() {
      this.getUserListBySearch();
    },
    sureSelect() {
      //确认选择
      let authorizationId = [];
      let authorization = [];
      let usernames = [];
      let personContent = {};
      for (let i in this.fileLists1) {
        authorization.push(this.fileLists1[i].name);
        authorizationId.push(this.fileLists1[i].sid);
        usernames.push(this.fileLists1[i].username);
      }
      personContent.ids = authorizationId.join(",");
      personContent.names = authorization.join(",");
      personContent.usernames = usernames.join(",");
      personContent.list = this.fileLists1;
      if(personContent.names.charAt(personContent.names.length-1) == ",") {
         personContent.names = personContent.names.substring(0, personContent.names.lastIndexOf(','));
      }
      if(this.isSingleSel){
        if(this.selected.length > 1){
          this.$message({
            type:'warning',
            message:'只能选择单个人员，请勿多选!',
          })
        }else if(this.selected.length == 0){
          this.$message({
            type:'warning',
            message:'您未选中任何人员，请先勾选人员!'
          })
        }else{
          this.$emit("personContent",this.selected);
          this.$emit("show1", false);
        }
      }else{
        this.$emit("personContent", personContent);
        this.$emit("show1", false);
      }
      //this.$emit("show1", false);
      this.$refs.orgTree.setCheckedKeys([]);
      this.fileLists = [];
      this.unselected = [];
    },
    cancel() {
      this.$emit("show1", false);
      this.personVisible = false;
    },
    handleSelectionChange(val) {
      this.unselected = val;
    },
    handleSelectionChange1(val) {
      this.selected = val;
    },
    tableHeaderColor({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0) {
        return "background-color: #F1F4F8;font-weight: normal;color:black;";
      }
    },
    close() {
      this.$emit("show1", false);
      // this.fileLists1 = []
    },
    //tree过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    //点击部门获取用户
    getUser(data, node) {
      console.log(data);
      this.getUserList(data.sid);
      console.log('获取用户');
    },
    // 获取部门列表
    getOrgTree() {
      this.listLoading = true;
      orgTree()
        .then(res => {
          if (res.code === 200) {
            console.log(res);
            this.userOptions = res.data;
            this.listLoading = false;
          }
        })
        .catch(error => {
          this.listLoading = false;
        });
    },
    //通过部门id获取用户列表
    getUserList(id) {
      userList({ orgId: id }).then(res => {
        console.log(res);
        if (res.code === 200) {
          this.fileLists = res.data;
        } else if (res.code === 500) {
          this.$message.warning(res.message);
        }
      });
    },
    // 搜索获取用户列表
    getUserListBySearch() {
      userListBySearch({ username: this.username }).then(res => {
        console.log(res);
        if (res.code === 200) {
          this.fileLists = res.data;
        } else if (res.code === 500) {
          this.$message.warning(res.message);
        }
      });
    },
    selectOrg() {
      let arr = [];
      console.log(this.fileLists1);
      for (let i in this.unselected) {
        //去重
        let flag = true;
        for (let k in this.fileLists1) {
          if (this.fileLists1[k].sid == this.unselected[i].sid) {
            flag = false;
            break;
          }
        }
        if (flag) {
          arr.push(this.unselected[i]);
        }
      }
      if (arr.length > 0) {
        console.log(arr);
        this.fileLists1 = this.fileLists1.concat(arr);
        console.log(this.fileLists1);
      }
    },
    toUnselect() {
      if(this.fileLists1[0].sid){
        this.fileLists1 = this.fileLists1.filter(
          t => !this.selected.some(s => s.sid === t.sid)
        );
      }else{
        this.fileLists1 = this.fileLists1.filter(
          t => !this.selected.some(s => s.username === t.username)
        );
      }
      
    }
  },
  created(){
      this.isSingleSel = this.singleSelect;
  },
  mounted() {
    // if(this.userOptions.length){
    //     // return;
    // }else{

    // }
    if (this.transUser.length) {
      
    }
    this.fileLists1 = this.transUser;
    this.getOrgTree();
    console.log(this.fileLists1);
  }
};
</script>

<style scoped>
.add-adm >>> .el-tree {
  width: 300px;
  overflow-y: scroll;
  overflow-x: hidden;
  height: 300px;
}
.add-adm >>> .el-dialog .el-input__inner {
  font-size: 14px;
}
.add-adm >>> .el-table {
  font-size: 14px !important;
}
.btnDown {
  width: 40px;
  display: block;
  margin-top: 10px;
}
</style>
