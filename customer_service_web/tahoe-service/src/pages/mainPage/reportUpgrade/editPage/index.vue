<template>
  <div id="upgradeEditPage">
    <div class="btn">
      <span class="title1">报事升级信息</span>
      <div class="btn1">
        <div class="btnDown" @click="submit()">提交</div>
        <div class="btnDown" @click="goBack()" style="margin: 0 10px;">返回</div>
      </div>
    </div>
    <div class="box">
      <div class="wrap" v-if="this.$route.query.data.codeName.substring(0, 2) == '报修'">
        <div style="padding-bottom: 20px;">
          <span style="float: left;">
            分类名称
            <span class="ml">{{this.$route.query.data.codeName}}</span>
          </span>
          <span style="float: right;margin-right: 278px;">
            所属层级
            <span class="ml">{{this.$route.query.data.level}}</span>
          </span>
        </div>
        <div>
          <div style="margin-top: 10px;">
            <span
              v-if="this.$route.query.data.codeName.substring(0, 2) == '报修'"
              style="float: left;"
            >
              城市（天）
              <span>
                <el-input style="width: 300px;margin-left: 10px;" v-model="input"></el-input>
              </span>
            </span>
            <span style=" float: right">
              区域（天）
              <span>
                <el-input style="width: 300px;margin-left: 10px;" v-model="input1"></el-input>
              </span>
            </span>
          </div>
          <div style="clear:both;"></div>
          <div style="margin-top: 10px;">
            <span>
              集团（天）
              <span>
                <el-input style="width: 300px;margin-left: 10px;" v-model="input2"></el-input>
              </span>
            </span>
          </div>
        </div>
      </div>
      <div class="wrap" v-if="this.$route.query.data.codeName.substring(0, 2) == '投诉'">
        <div style="padding-bottom: 20px;">
          <span style="float: left;">
            分类名称
            <span class="ml">{{this.$route.query.data.codeName}}</span>
          </span>
          <span style="float: right;margin-right: 278px;">
            所属层级
            <span class="ml">{{this.$route.query.data.level}}</span>
          </span>
        </div>
        <div style="padding: 10px 0;">
          <span style=" float: left">
            区域（天）
            <span>
              <el-input style="width: 300px;margin-left:10px;" v-model="input1"></el-input>
            </span>
          </span>
          <span style="float: right">
            集团（天）
            <span>
              <el-input style="width: 300px;margin-left:10px;" v-model="input2"></el-input>
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import { reportUpgradeEditSubmit } from "@/api/reportUpgrade";
export default {
  name: "upgradeEditPage",
  data() {
    return {
      input: "",
      input1: "",
      input2: "",
      codeName: ""
    };
  },
  methods: {
    trim(str) {
      //删除左右两端的空格
      return str.replace(/(^\s*)|(\s*$)/g, "");
    },
    submit() {
      console.log(this.$route.query.row.sc);
      if (this.input == null) {
        this.input = "";
      }
      if (this.input1 == null) {
        this.input1 = "";
      }
      if (this.input2 == null) {
        this.input2 = "";
      }

      // this.trim(this.input1);
      // this.trim(this.input2);
      if (this.input != "") {
        let reg = /^\+?[1-9][0-9]*$/;
        if (!reg.test(this.input)) {
          this.$message("请输入整数数字的城市天数");
          return;
        }
      }
      if (this.input1 != "") {
        let reg = /^\+?[1-9][0-9]*$/;
        if (!reg.test(this.input1)) {
          this.$message("请输入整数数字的区域天数");
          return;
        }
      }
      if (this.input2 != "") {
        let reg = /^\+?[1-9][0-9]*$/;
        if (!reg.test(this.input2)) {
          this.$message("请输入整数数字的集团天数");
          return;
        }
      }
      let loadingInstance = Loading.service({
        text: "正在提交...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.8)"
      });
      if (this.$route.query.row.sc) {
        this.$confirm("提交后将修改当前类型下所有子类型的处理时长", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            // this.$message({
            //   type: 'success',
            //   message: '删除成功!'
            // });
            reportUpgradeEditSubmit({
              type: this.$route.query.type,
              code: this.$route.query.row.sortCode,
              groupDasy: this.input2,
              regionDays: this.input1,
              cityDays: this.input
            }).then(res => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.$router.push({
                  path: "ReportUpgrade"
                });
                this.$nextTick(() => {
                  // 以服务的方式调用的 Loading 需要异步关闭
                  loadingInstance.close();
                });
              } else {
                this.$confirm(res.message, {
                  confirmButtonText: "确定",
                  center: true,
                  showClose: false,
                  showCancelButton: false,
                  confirmButtonClass: "confirm-reset-style"
                }).then(() => {});
                this.$nextTick(() => {
                  // 以服务的方式调用的 Loading 需要异步关闭
                  loadingInstance.close();
                });
              }
            });
          })
          .catch(() => {
            this.$nextTick(() => {
              // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
            // this.$message({
            //   type: 'info',
            //   message: '已取消删除'
            // });
          });
      } else {
        reportUpgradeEditSubmit({
          type: this.$route.query.type,
          code: this.$route.query.row.sortCode,
          groupDasy: this.input2,
          regionDays: this.input1,
          cityDays: this.input
        }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.message);
            this.$router.push({
              path: "ReportUpgrade"
            });
            this.$nextTick(() => {
              // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
          } else {
            this.$confirm(res.message, {
              confirmButtonText: "确定",
              center: true,
              showClose: false,
              showCancelButton: false,
              confirmButtonClass: "confirm-reset-style"
            }).then(() => {});
            this.$nextTick(() => {
              // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
          }
        });
      }
    },
    goBack() {
      this.$router.push({
        path: "ReportUpgrade"
      });
    }
  },
  mounted() {
    //   投诉没有城市
    if (this.$route.query.data.codeName.substring(0, 2) == "报修") {
      this.input = this.$route.query.data.cityDays;
      this.input1 = this.$route.query.data.regionDays;
      this.input2 = this.$route.query.data.groupDasy;
    } else {
      this.input1 = this.$route.query.data.regionDays;
      this.input2 = this.$route.query.data.groupDasy;
    }
  }
};
</script>

<style scoped>
#upgradeEditPage .btn span:nth-child(1) {
  display: inline-block;
  height: 13px;
  line-height: 14px;
  font-size: 14px;
  padding: 3px;
  border-left: solid 2px #e63f3c;
  margin-left: 10px;
}
#upgradeEditPage .btn {
  background-color: #fff;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid rgb(230, 230, 230);
  /* border-radius: 5px; */
}
#upgradeEditPage .btn1 {
  float: right;
}
#upgradeEditPage .box {
  width: 100%;
  /* padding: 30px; */
  background-color: #fff;
  /* margin-top: 30px; */
  /* height: 130px; */
  /* border-radius: 5px; */
  padding: 30px 0;
}
#upgradeEditPage .box .wrap {
  width: 80%;
  margin: 0 auto;
}
#upgradeEditPage .ml {
  margin-left: 21px;
}
</style>

<style>
#upgradeEditPage .el-input__prefix,
.el-input__suffix {
  top: -8px;
}
</style>
