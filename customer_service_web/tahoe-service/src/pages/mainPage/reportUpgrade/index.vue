<template>
  <div id="reportUpgrade" style="width: calc( 100% - 10px);">
    <div class="table-container">
      <div
        class="table-body"
        v-loading="loading"
        element-loading-text="玩命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <el-table
          :data="tableData"
          id="firstLevelTable"
          style="width:100%;"
          @row-click="rowClick"
          row-key="sortCode"
          :expand-row-keys="expands"
        >
          <el-table-column type="expand" class="width0" width="0" align="left">
            <template slot-scope="scope">
              <!-- 第二层表开始 -->
              <el-table
                :data="scope.row.sc"
                id="secondLevelTable"
                style="width: 100%"
                :show-header="false"
                @row-click="rowClick"
                row-key="sortCode"
                :expand-row-keys="expands"
              >
                <el-table-column type="expand" class="width0" width="0" align="center">
                  <template slot-scope="scope">
                    <!-- 第三层表开始 -->
                    <el-table
                      :data="scope.row.sc"
                      id="secondLevelTable"
                      style="width: 100%"
                      :show-header="false"
                      @row-click="rowClick"
                      row-key="sortCode"
                      :expand-row-keys="expands"
                    >
                      <el-table-column type="expand" class="width0" width="0" align="center">
                        <template slot-scope="scope">
                          <!-- 第四层表开始 -->
                          <el-table
                            :data="scope.row.sc"
                            id="secondLevelTable"
                            style="width: 100%"
                            :show-header="false"
                            @row-click="rowClick"
                            row-key="sortCode"
                            :expand-row-keys="expands"
                          >
                            <el-table-column type="expand" class="width0" width="0" align="center"></el-table-column>
                            <el-table-column width="150" label="分类名称" align="left">
                              <template slot-scope="scope">
                                <span
                                  style="display:inline-block;padding-left:45px;"
                                >{{scope.row.sortName}}</span>
                              </template>
                            </el-table-column>
                            <el-table-column
                              v-for="(item,index) in tableHeader"
                              v-if="item.isShow"
                              :width="item.width"
                              :label="item.label"
                              :prop="item.prop"
                              :key="index"
                              align="center"
                            ></el-table-column>
                            <el-table-column label="操作" align="center">
                              <template slot-scope="scope">
                                <el-button
                                  @click="handleEdit(scope.$index, scope.row)"
                                  type="text"
                                  size="small"
                                  class="btnDown"
                                >编辑</el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                          <!-- 第四层表结束 -->
                        </template>
                      </el-table-column>
                      <el-table-column width="150" label="分类名称" align="left">
                        <template slot-scope="scope">
                          <span style="display:inline-block;padding-left:30px;">
                            <i v-if="scope.row.sc" :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                            {{scope.row.sortName}}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-for="(item,index) in tableHeader"
                        v-if="item.isShow"
                        :width="item.width"
                        :label="item.label"
                        :prop="item.prop"
                        :key="index"
                        align="center"
                      ></el-table-column>
                      <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                          <el-button
                            @click="handleEdit(scope.$index, scope.row)"
                            type="text"
                            size="small"
                            class="btnDown"
                          >编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!-- 第三层表结束 -->
                  </template>
                </el-table-column>
                <el-table-column width="150" label="分类名称" align="left">
                  <template slot-scope="scope">
                    <span style="display:inline-block;padding-left:15px;">
                      <i :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
                      {{scope.row.sortName}}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item,index) in tableHeader"
                  v-if="item.isShow"
                  :width="item.width"
                  :label="item.label"
                  :prop="item.prop"
                  :key="index"
                  align="center"
                ></el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button
                      @click="handleEdit(scope.$index, scope.row)"
                      type="text"
                      size="small"
                      class="btnDown"
                    >编辑</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 第二层表结束 -->
            </template>
          </el-table-column>
          <el-table-column width="150" label="分类名称" align="left">
            <template slot-scope="scope">
              <i v-if="scope.row.sc.length>0" :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
              {{scope.row.sortName}}
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item,index) in tableHeader"
            v-if="item.isShow"
            :width="item.width"
            :label="item.label"
            :prop="item.prop"
            :key="index"
            align="center"
          >
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                @click="handleEdit(scope.$index, scope.row)"
                type="text"
                size="small"
                class="btnDown"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getReportUpgradeList,
  reportUpgradeEdit
} from "@/api/reportUpgrade";
// 报修四级 投诉2级和三级
export default {
  name: "reportUpgrade",
  data() {
    return {
      loading: true,
      expands: [],
      tableData: [],
      tableHeader: [
        //{label:'区域/城市/项目',prop:'masterSlaveName',width:136},
        { label: "分类层级", prop: "levelName", width: "", isShow: true },
        { label: "城市（天）", prop: "cityDays", width: "", isShow: true },
        { label: "区域（天）", prop: "regionDays", width: "", isShow: true },
        { label: "集团（天）", prop: "groupDasy", width: "", isShow: true }
      ]
    };
  },
  methods: {
    rowClick(row, event, column) {
      Array.prototype.remove = function(val) {
        let index = this.indexOf(val);
        if (index > -1) {
          this.splice(index, 1);
        }
      };
      if (this.expands.indexOf(row.sortCode) < 0 && row.sc!=null) {
        this.expands.push(row.sortCode);
        row.expand = true;
      } else {
        this.expands.remove(row.sortCode);
        row.expand = false;
      }
    },
    getReportUpgradeList() {
      getReportUpgradeList().then(res => {
        if (res.code == 200) {
          this.tableData = res.data;
          this.tableData.map((item) => {
            item.cityDays = item.cityDays==-1?'':item.cityDays;
            if(item.sc && item.sc.length>0){
              item.sc.map((sitem) => {
                sitem.cityDays = sitem.cityDays==-1?'':sitem.cityDays;
                if(sitem.sc && sitem.sc.length>0){
                  sitem.sc.map((titem) => {
                    titem.cityDays = titem.cityDays==-1?'':titem.cityDays;
                    if(titem.sc && titem.sc.length>0){
                      titem.sc.map((fitem) =>{
                        fitem.cityDays = fitem.cityDays==-1?'':fitem.cityDays;
                      })
                    }
                  })
                }
              })
            }
          })
          this.loading = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    handleEdit(index, row) {
      let level = "";
      if (row.levelName == "一级") {
        level = "1";
      } else if (row.levelName == "二级") {
        level = "2";
      } else if (row.levelName == "三级") {
        level = "3";
      } else if (row.levelName == "四级") {
        level = "4";
      }
      reportUpgradeEdit({ type: level, code: row.sortCode}).then(res => {
        if (res.code == 200) {
          this.$router.push({
            path: "ReportUpgradeEdit",
            query: {
              // treePath: res.data.treePath,
              // type: res.data.type,
              row: row,
              type: level,
              data: res.data
            }
          });
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    }
  },
  created() {
    this.getReportUpgradeList();
  }
};
</script>

<style scoped>
.el-icon-minus,
.el-icon-plus {
  font-size: 12px;
  color: #e63f3c;
  font-weight: bold;
}
</style>
<style>
#reportUpgrade .table-body .el-icon-arrow-right {
  display: none;
}
</style>
