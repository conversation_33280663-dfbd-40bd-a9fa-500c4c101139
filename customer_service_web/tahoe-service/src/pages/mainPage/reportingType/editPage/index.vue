<template>
  <div id="typeEditPage">
    <div class="btn">
      <span class="title1">报事类型信息</span>
      <div class="btn1">
        <div class="btnDown" @click="submit()">提交</div>
        <div class="btnDown" @click="goBack()" style="margin: 0 10px;">返回</div>
      </div>
    </div>
    <div class="box">
      <div class="wrap">
        <div style="padding: 20px 10px;">
          <span style="display: inline-block;width: 500px;">
            <span>上级分类</span>
            <span class="ml">{{categoryParent}}</span>
          </span>
          <span>
            <span>所属层级</span>
            <span class="ml">{{hierarchy}}</span>
          </span>
        </div>
        <div style="padding: 20px 10px;">
          <span style="display: inline-block;width: 500px;">
            <span>分类名称</span>
            <span @click="clickPick()" class="ml">
              <el-input style="width: 60%;" v-model="input"></el-input>
            </span>
          </span>
          <span>
            <span>是否启用</span>
            <span class="ml">
              <el-radio v-model="radio" label="1">是</el-radio>
              <el-radio v-model="radio" label="2">否</el-radio>
            </span>
          </span>
        </div>
        <div style="padding: 20px 10px;">
          <span style="display: inline-block;width: 500px;">
            <span>是否抢单</span>
            <span class="ml">
              <el-radio v-model="isGrab" :label="true">是</el-radio>
              <el-radio v-model="isGrab" :label="false">否</el-radio>
            </span>
          </span>
        </div>
        <div style="padding: 20px 10px;" v-if="isGrab">
          <span style="display: inline-block;">
            <span>抢单项目</span>
            <span class="ml">
              <el-tag type="info" style="cursor: pointer;" @click="addProject">+ 添加项目</el-tag>
              <el-tag :key="tag.projectCode" style="margin: 5px;color:#000;" v-for="(tag,index) in projects" closable :disable-transitions="false" @close="handleClose(index)">
                {{tag.project}}
              </el-tag>
            </span>
          </span>
        </div>
      </div>
      <el-dialog id="addTagPage" title="添加项目" :visible.sync="addTagVisible" width="40%">
        <el-input placeholder="输入关键字进行过滤" v-model="search">
        </el-input>
        <el-table height="400" ref="grabTable" :data="pSearchList" style="width: 100%" tooltip-effect="dark">
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column prop="region" label="区域" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cityCompany" label="城市" show-overflow-tooltip></el-table-column>
          <el-table-column prop="project" label="项目"></el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button class="btnDown" @click="tagAdd">确 定</el-button>
          <el-button class="btnDown" @click="addTagVisible = false">取 消</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import { reportTypeEditSubmit } from "@/api/reportType";
import { project } from "@/api/wsd";
import { pList, delectCode, insertCode } from "@/api/csGrab";
export default {
  name: "typeEditPage",
  data() {
    return {
      input: "",
      radio: "",
      categoryParent: "",
      hierarchy: "",
      projects: [],
      projectList: [],
      pSearchList: [],
      isGrab: false,
      search: "",
      addTagVisible: false
    };
  },
  watch: {
    isGrab(v) {
      if (v == 2) this.projects = "";
    },
    search(v) {
      this.pSearchList = this.projectList.filter(
        data =>
          !v ||
          data.region.includes(v) ||
          data.cityCompany.includes(v) ||
          data.project.includes(v)
      );
      this.$nextTick(_ => {
        this.toggleSelection(this.projects);
      });
    }
  },
  methods: {
    submit() {
      if (this.input == "") {
        this.$message("请填写分类名称");
        return;
      }
      let loadingInstance = Loading.service({
        text: "正在提交...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.8)"
      });
      reportTypeEditSubmit({
        type: this.$route.query.type, // 编辑1 添加2
        itemCode: this.$route.query.row.itemCode,
        itemValue: this.input,
        status: this.radio
      }).then(res => {
        if (this.isGrab) {
          let projectCodes = "";
          this.projects.forEach(p => {
            projectCodes += p.projectCode + ",";
          });
          insertCode({
            itemCode: this.$route.query.row.itemCode,
            projectCodes: projectCodes.substring(0, projectCodes.length - 1)
          });
        }
        if (res.code == 200) {
          this.$message.success(res.message);
          this.$router.push({
            path: "ReportingType",
            query: {}
          });
          this.$nextTick(() => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
          this.$router.push({
            path: "ReportingType",
            query: {}
          });
          this.$nextTick(() => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
        }
      });
    },
    goBack() {
      this.$router.push({
        path: "ReportingType",
        query: {}
      });
    },
    clickPick() {},
    handleClose(index) {
      this.projects.splice(index, 1);
    },
    addProject() {
      this.addTagVisible = true;
      this.$nextTick(_ => {
        this.toggleSelection(this.projects);
      });
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.grabTable.toggleRowSelection(this.projectList.filter(i => i.projectCode == row.projectCode)[0]);
        });
      } else {
        this.$refs.grabTable.clearSelection();
      }
    },
    tagAdd() {
      this.projects = this.$refs.grabTable.store.states.selection;
      this.addTagVisible = false;
    }
  },
  mounted() {
    project().then(res => {
      if (res.code == "200") {
        this.projectList = res.data;
        this.pSearchList = this.projectList;
      }
    });
    pList({ itemCode: this.$route.query.row.itemCode }).then(res => {
      if (res.code == "200") {
        this.projects = res.data;
        this.isGrab = this.projects.length > 0;
      }
    });
    if (this.$route.query.type == "1") {
      if (this.$route.query.data.hc == "一级") {
        this.categoryParent = "无";
      } else {
        this.categoryParent = this.$route.query.data.itemValue;
      }
      if (this.$route.query.row.status == "1") {
        this.radio = "1";
      } else {
        this.radio = "2";
      }
      this.input = this.$route.query.row.dictName;
      this.hierarchy = this.$route.query.data.hc;
    } else {
      this.categoryParent = this.$route.query.data.itemValue;
      if (this.$route.query.data.hc == "一级") {
        this.hierarchy = "二级";
      } else if (this.$route.query.data.hc == "二级") {
        this.hierarchy = "三级";
      } else if (this.$route.query.data.hc == "三级") {
        this.hierarchy = "四级";
      }
      this.input = "";
      this.radio = "1";
    }
  }
};
</script>

<style scoped>
#typeEditPage .btn span:nth-child(1) {
  display: inline-block;
  height: 13px;
  line-height: 14px;
  font-size: 14px;
  padding: 3px;
  border-left: solid 2px #e63f3c;
  margin-left: 10px;
}
#typeEditPage .btn {
  background-color: #fff;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid rgb(230, 230, 230);
  /* border-radius: 10px; */
}
#typeEditPage .btn1 {
  float: right;
}
#typeEditPage .box {
  width: 100%;
  /* float: left; */
  background-color: #fff;
  /* margin-left: 10%; */
  /* margin-top: 10px; */
  /* height: 80px; */
  /* border-radius: 10px; */
  padding-bottom: 30px;
}
#typeEditPage .box .wrap {
  width: 70%;
  margin: 0 auto;
}
#typeEditPage .box b {
  font-size: 14px;
}
#typeEditPage .ml {
  margin-left: 27px;
}
</style>

<style>
#typeEditPage .el-input__prefix,
.el-input__suffix {
  top: 8px;
}

#typeEditPage .el-select .el-tag {
  background-color: transparent;
}
</style>
