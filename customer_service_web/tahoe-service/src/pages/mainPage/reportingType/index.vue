<template>
  <div id="reportingType" style="width: calc( 100% - 10px);">
    <div class="table-container">
      <div
        class="table-body"
        v-loading="loading"
        element-loading-text="玩命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <el-table
          :data="tableData"
          id="firstLevelTable"
          style="width:100%;"
          @row-click="rowClick"
          row-key="itemCode"
          :expand-row-keys="expands"
        >
          <el-table-column type="expand" class="width0" width="0" align="left">
            <template slot-scope="scope">
              <!-- 第二层表开始 -->
              <el-table
                :data="scope.row.sc"
                id="secondLevelTable"
                style="width: 100%"
                :show-header="false"
                @row-click="rowClick"
                row-key="itemCode"
                :expand-row-keys="expands"
              >
                <el-table-column type="expand" class="width0" width="0" align="left">
                  <template slot-scope="scope">
                    <!-- 第三层表开始 -->
                    <el-table
                      :data="scope.row.sc"
                      id="secondLevelTable"
                      style="width: 100%"
                      :show-header="false"
                      @row-click="rowClick"
                      row-key="itemCode"
                      :expand-row-keys="expands"
                    >
                      <el-table-column type="expand" class="width0" width="0" align="left">
                        <template slot-scope="scope">
                          <!-- 第四层表开始 -->
                          <el-table
                            :data="scope.row.sc"
                            id="secondLevelTable"
                            style="width: 100%"
                            :show-header="false"
                            @row-click="rowClick"
                            row-key="itemCode"
                            :expand-row-keys="expands"
                          >
                            <el-table-column class="width0" width="0" align="left"></el-table-column>
                            <el-table-column width="250" label="分类名称" align="left">
                              <template slot-scope="scope">
                                <span style="display:inline-block;padding-left:45px;">
                                  <i
                                    v-if="scope.row.sc"
                                    :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"
                                  ></i>
                                  {{scope.row.dictName}}
                                </span>
                              </template>
                            </el-table-column>
                            <el-table-column label="分类层级" prop="hc" align="center" width="150"></el-table-column>
                            <el-table-column label="是否启用" width="350" align="center">
                              <template slot-scope="scope">
                                <span v-if="scope.row.status==1">是</span>
                                <span v-else>否</span>
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center">
                              <template slot-scope="scope">
                                <el-button
                                  size="mini"
                                  @click="handleEdit(scope.$index, scope.row)"
                                  class="btnDown"
                                >编辑</el-button>
                                <!-- <el-button
                                  v-if="scope.row.sc"
                                  size="mini"
                                  @click="addChildLevel(scope.$index, scope.row)"
                                  class="btnDown"
                                >添加子级</el-button> -->
                              </template>
                            </el-table-column>
                          </el-table>
                          <!-- 第四层表结束 -->
                        </template>
                      </el-table-column>
                      <el-table-column width="250" label="分类名称" align="left">
                        <template slot-scope="scope">
                          <span style="display:inline-block;padding-left:30px;">
                            <i
                              v-if="scope.row.sc"
                              :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"
                            ></i>
                            {{scope.row.dictName}}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="分类层级" prop="hc" align="center" width="150"></el-table-column>
                      <el-table-column label="是否启用" width="350" align="center">
                        <template slot-scope="scope">
                          <span v-if="scope.row.status==1">是</span>
                          <span v-else>否</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                          <el-button
                            size="mini"
                            @click="handleEdit(scope.$index, scope.row)"
                            class="btnDown"
                          >编辑</el-button>
                          <el-button
                            v-if="scope.row.parentName=='报修'"
                            size="mini"
                            @click="addChildLevel(scope.$index, scope.row)"
                            class="btnDown"
                          >添加子级</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!-- 第三层表结束 -->
                  </template>
                </el-table-column>
                <el-table-column width="250" label="分类名称" align="left">
                  <template slot-scope="scope">
                    <span style="display:inline-block;padding-left:15px;">
                      <i
                        v-if="scope.row.parentName=='投诉' || scope.row.parentName=='报修'"
                        :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"
                      ></i>
                      {{scope.row.dictName}}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="分类层级" prop="hc" align="center" width="150"></el-table-column>
                <el-table-column label="是否启用" width="350" align="center">
                  <template slot-scope="scope">
                    <span v-if="scope.row.status==1">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      @click="handleEdit(scope.$index, scope.row)"
                      class="btnDown"
                    >编辑</el-button>
                    <el-button
                      v-if="scope.row.parentName=='投诉' || scope.row.parentName=='报修'"
                      size="mini"
                      @click="addChildLevel(scope.$index, scope.row)"
                      class="btnDown"
                    >添加子级</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 第二层表结束 -->
            </template>
          </el-table-column>
          <el-table-column width="250" label="分类名称" align="left">
            <template slot-scope="scope">
              <i :class="scope.row.expand?'el-icon-minus':'el-icon-plus'"></i>
              {{scope.row.dictName}}
            </template>
          </el-table-column>
          <el-table-column label="分类层级" prop="hc" align="center" width="150"></el-table-column>
          <el-table-column label="是否启用" width="350" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.status==1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleEdit(scope.$index, scope.row)" class="btnDown">编辑</el-button>
              <el-button
                size="mini"
                @click="addChildLevel(scope.$index, scope.row)"
                class="btnDown"
              >添加子级</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getReportTypeList, getReportTypeEdit } from "@/api/reportType";
export default {
  name: "reportingType",
  data() {
    return {
      loading: true,
      tableData: [],
      expands: [],
      tableHeader: [
        // { label: "分类层级", prop: "hc", width: "", isShow: true },
        { label: "是否启用", prop: "status", width: "", isShow: true }
      ],
      flag: true,
    };
  },
  methods: {
    getReportTypeList(params) {
      this.loading = true;
      getReportTypeList(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data;
          this.tableData.map((v) => {
            if(v.dictName == '咨询') {
              v.sc.map((v) => {
                v.parentName = '咨询'
              })
            }
            if(v.dictName == '投诉') {
              v.sc.map((v) => {
                v.parentName = '投诉';
              })
            }
            if(v.dictName == '报修') {
              v.sc.map((v) => {
                v.parentName = '报修';
                v.sc.map((v) => {
                  v.parentName = '报修';
                })
              })
            }
            if(v.dictName == '建议表扬') {
              v.sc.map((v) => {
                v.parentName = '建议表扬'
              })
            }
          })
          console.log(this.tableData);
          // this.tableHeader.forEach((item, index) => {
          //   if (item.label == "是否启用") {
          //     item.prop = "123";
          //   }
          // });
          this.loading = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    rowClick(row, event, column) {
      Array.prototype.remove = function(val) {
        let index = this.indexOf(val);
        if (index > -1) {
          this.splice(index, 1);
        }
      };
      if (this.expands.indexOf(row.itemCode) < 0 && row.sc!=null) {
        this.expands.push(row.itemCode);
        row.expand = true;
      } else {
        this.expands.remove(row.itemCode);
        row.expand = false;
      }
    },
    handleEdit(index, row) {
      this.loading = true;
      getReportTypeEdit({ itemCode: row.itemCode }).then(res => {
        if (res.code == 200) {
          this.$router.push({
            path: "ReportingTypeEdit",
            query: { data: res.data, row: row, type: "1"}
          });
          this.loading = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    },
    addChildLevel(index, row) {
      this.loading = true;
      getReportTypeEdit({ itemCode: row.itemCode }).then(res => {
        if (res.code == 200) {
          this.$router.push({
            path: "ReportingTypeEdit",
            query: { data: res.data, row: row, type: "2"}
          });
          this.loading = false;
        } else {
          this.$confirm(res.message, {
            confirmButtonText: "确定",
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "confirm-reset-style"
          }).then(() => {});
        }
      });
    }
  },
  created() {
    this.getReportTypeList();
  }
};
</script>

<style scoped>
.el-icon-minus,
.el-icon-plus {
  font-size: 12px;
  color: #e63f3c;
  font-weight: bold;
}
</style>
<style>
#reportingType .table-body .el-icon-arrow-right {
  display: none;
}
</style>
