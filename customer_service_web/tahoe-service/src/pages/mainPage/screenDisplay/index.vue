<template>
    <div class="screen-container" @click="displaySetting">
        <el-carousel ref="carousel" indicator-position="none" :interval="changeRate*1000" :loop="true" :autoplay="isAutoPlay" arrow="never" @change="handelCarousel">
            <el-carousel-item name="traffic">
                <div class="screen-title" >
                    <h3>
                        天宇集团客户联络中心 - <span class="second-title">话务监控</span>
                        <span class="date-time"><span>{{ndate}}</span> <span>{{ntime}}</span>:<span>{{nsec}}</span></span>
                    </h3>
                </div>
                <div class="screen-info">
                    <div class="flex-container-row">
                        <div class="color-card purple-card">
                            <div class="color-card-title">系统呼入总量</div>
                            <div class="color-card-content">{{currentData.SysCallinCount}}  </div>
                        </div>
                        <div class="color-card pink-card">
                            <div class="color-card-title">人工接听数量</div>
                            <div class="color-card-content">{{currentData.SysAgentSuccTalkCount}}</div>
                        </div>
                        <div class="color-card yellow-card">
                            <div class="color-card-title">人工接起率</div>
                            <div class="color-card-content">{{currentData.SysSuccLV}}%</div>
                        </div>
                        <div class="color-card blue-card">
                            <div class="color-card-title">在线人数</div>
                            <div class="color-card-content">{{currentData.OnlineCount}}</div>
                        </div>
                    </div>
                    <div class="flex-container-row">
                        <div class="black-card">
                            <span class="black-card-title">队列呼入数量</span>
                            <span class="black-card-content">{{currentData.QueueCount}}</span>
                        </div>
                        <div class="black-card">
                            <span class="black-card-title">示忙人数</span>
                            <span class="black-card-content">{{currentData.SleepCount}}</span>
                        </div>
                    </div>
                    <div class="flex-container-row">
                        <div class="black-card">
                            <span class="black-card-title">人工接起率</span>
                            <span class="black-card-content">{{currentData.SysSuccLV}}%</span>
                        </div>
                        <div class="black-card">
                            <span class="black-card-title">通话人数</span>
                            <span class="black-card-content">{{currentData.TalkCount}}</span>
                        </div>
                    </div>
                    <div class="flex-container-row">
                        <div class="black-card">
                            <span class="black-card-title">排队数量</span>
                            <span class="black-card-content">{{currentData.QueueWaitCount}}</span>
                        </div>
                        <div class="black-card">
                            <span class="black-card-title">话后人数</span>
                            <span class="black-card-content">{{currentData.AfterCount}}</span>
                        </div>
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">呼入量统计</div>
                        <div class="chart-container" id="callinCount">

                        </div>
                    </div>
                </div>
            </el-carousel-item>


            <!------------------------------------------ 第   二   页 ------------------------------------------>


            <el-carousel-item name="report">
                <div class="screen-title report-screen-title">
                    <h3>
                        天宇集团客户联络中心 - <span class="second-title">报事管理</span>
                        <span class="date-time"><span>{{ndate}}</span> <span>{{ntime}}</span>:<span>{{nsec}}</span></span>
                    </h3>
                </div>
                <div class="screen-info flex-container-row" style="padding-bottom:0;">
                    <div class="map-card">
                        <img class="map-gif" :src="require('@/assets/images/chnmap2.png')">
                    </div>
                    <div class="area-card-container">
                        <div class="area-card" v-for="(item,index) in reportAreaInfoList" :key="index">
                            <div :class="'area-title '+item.color">{{item.name}}</div>
                            <div class="area-info-content" v-for="(row,key) in item.data" :key="key"><span>{{row.typeName}}</span><span>{{row.allCount}}</span></div>
                        </div>
                    </div>
                    <div class="report-card-container">
                        <div class="report-card pink-card">
                            <div class="report-card-title">当日报事</div>
                            <div class="report-card-row">
                                <span class="row-title grey-white align-left">分类</span>
                                <span class="row-title grey-white align-center">报事</span>
                                <span class="row-title grey-white align-center">处理中</span>
                                <span class="row-title grey-white align-right">处理完成</span>
                            </div>
                            <div class="report-card-row" v-for="(item,index) in dayList" :key="index">
                                <span class="row-title grey-white">{{item.typeName}}</span>
                                <span class="row-content white align-center">{{item.allCount}}</span>
                                <span class="row-content white align-center">{{item.processCount}}</span>
                                <span class="row-content white align-right">{{item.finishCount}}</span>
                            </div>
                        </div>
                        <div class="report-card yellow-card">
                            <div class="report-card-title">当月报事</div>
                            <div class="report-card-row">
                                <span class="row-title grey-white align-left">分类</span>
                                <span class="row-title grey-white align-center">报事</span>
                                <span class="row-title grey-white align-center">处理中</span>
                                <span class="row-title grey-white align-right">处理完成</span>
                            </div>
                           <div class="report-card-row" v-for="(item,index) in monthList" :key="index">
                                <span class="row-title grey-white">{{item.typeName}}</span>
                                <span class="row-content white align-center">{{item.allCount}}</span>
                                <span class="row-content white align-center">{{item.processCount}}</span>
                                <span class="row-content white align-right">{{item.finishCount}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </el-carousel-item>
        </el-carousel>
        <div id="mask">
            <i class="el-icon-setting" v-if="isShowSetting" @click="showSettingDialog"></i>
            <!-- <span class="date-time"><span>{{ndate}}</span> <span>{{ntime}}</span>:<span>{{nsec}}</span></span> -->
            <el-dialog :append-to-body="true" title="显示设置" :visible.sync="dialogVisible" width="30%" :show-close="false" top="30vh">
                <div>
                    <el-form label-position="right" label-width="125px">
                        <el-form-item label="自动切换:">
                            <el-switch
                                v-model="isAutoPlay"
                                active-text="是"
                                inactive-text="否">
                            </el-switch>
                        </el-form-item>
                        <el-form-item v-if="!isAutoPlay" label="默认展示:">
                            <el-radio v-model="defaultDisplay" label="traffic">话务中心</el-radio>
                            <el-radio v-model="defaultDisplay" label="report">报事管理</el-radio>
                        </el-form-item>
                        <el-form-item v-if="isAutoPlay" label="切换频率(秒):">
                            <el-slider ref="crateControler" :min="1" :show-tooltip="true" v-model="changeRate" show-input></el-slider>
                        </el-form-item>
                        <el-form-item label="数据刷新率(分钟):">
                            <el-slider ref="drateControler" :min="2" :step="2" v-model="refreshRate" :format-tooltip="formatTooltip" show-input></el-slider>
                        </el-form-item>
                    </el-form>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="cancelChange">取 消</el-button>
                    <el-button type="primary" @click="confirmChange">确 定</el-button>
                </span>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import {getServerDate} from '@/api/common'
import { getScreenReportData,getCurrentSysCall,getCurrentSumSysCall } from '@/api/screenPort'
export default {
    watch:{
        defaultDisplay(val){
            this.defaultDisplay = val;
            if(val){
                this.$refs.carousel.setActiveItem(val);
            }
        }
    },
    data(){
        return{
            currentData:{
                OnlineCount: "9",//在线人数
                QueueCount: "0",//队列呼入数量
                SleepCount: "2",//示忙人数
                AfterCount: "2",//话后人数
                TalkCount: "0",//正在通话地人数
                QueueWaitCount: "0",//排队人数
                SucctalkLv: "0.00",//当前人工接起率
                SysCallinCount: "19",//系统呼入数量
                SysAgentSuccTalkCount: "11", //人工接起数量
                SysSuccLV: "57" //人工接起率
            },
            // sysCallIn:[0,10,8,16,25,35,46,58,96,110,130,150,175,145,130],
            // perCallIn:[0,2,3,3,10,18,24,34,68,82,100,125,144,121,105],
            sysCallIn:[],//系统呼入量
            perCallIn:[],//人工接听量
            ndate:'',
            ntime:'',
            nsec:'',
            wholePoint:0,
            isShowSetting:false,
            dialogVisible:false,
            isAutoPlay:true,
            changeRate:10,
            refreshRate:3,
            defaultDisplay:null,
            monthList:[],
            dayList:[],
            reportAreaInfoList:[],
            myChart:null,
            option:{
                grid:{
                    left:'8%',
                    right:'6%',
                    top:'4%',
                    bottom:80,
                },
                legend: [
                    {
                        left:'28.0%',
                        bottom:15,
                        orient: 'horizontal',
                        data: [{name:'系统呼入数量',icon:'rect'},],
                        textStyle:{
                            fontSize:20,
                            fontWeight:'bold',
                            color:'#838DC3'
                        },
                        itemWidth:40,
                        itemHeight:4,
                        borderRadius:5,
                        selectedMode:false,
                    },
                    {
                        right:'28.0%',
                        bottom:15,
                        orient: 'horizontal',
                        itemWidth:40,
                        itemHeight:4,
                        borderRadius:5,
                        data:[{name:'人工接听数量',icon:'rect'}],
                        textStyle:{
                            fontSize:20,
                            fontWeight:'bold',
                            color:'#838DC3'
                        },
                        selectedMode:false,
                    }

                ],
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine:{
                        show:false,
                    },
                    splitLine:{
                        show:true,
                        interval:0,
                        lineStyle:{
                            type:'dashed',
                            color:'#9B9B9B',
                            opacity:0.5
                        }
                    },
                    data: []
                },
                yAxis: {
                    type: 'value',
                    offset:45,
                    axisLine:{
                        show:false,
                        lineStyle:{
                            color:'#86A0BD'
                        }
                    },
                    splitLine:{
                        show:false,
                    },
                },
                series: [
                    {
                        name:'系统呼入数量',
                        data: [],
                        type: 'line',
                        itemStyle:{
                            color:'#7762E3',
                        },
                        lineStyle:{
                            width:4
                        },
                        smooth: true,
                        symbol:'none',
                    },
                    {
                        name:'人工接听数量',
                        data: [],
                        type: 'line',
                        itemStyle:{
                            color:'#F2BA5D'
                        },
                        lineStyle:{
                            width:4
                        },
                        smooth: true,
                        symbol:'none',
                    }
                ]
            }
        }
    },
    methods:{
         rendEchart(option){
             option.series[0].data = [];
             option.series[1].data = [];
             option.xAxis.data = [];
             for(let i=0;i<24;i++){
                 let obj = {
                    value:i+':00',
                    textStyle:{
                        color:'#86A0BD'
                    }
                }
                 option.xAxis.data.push(obj);
             }
             this.myChart = this.$echarts.init(document.getElementById('callinCount'));
             this.myChart.setOption(option,true);
             this.resetEchart();
         },
         resetEchart(){
            if(!this.myChart) return;
            let option = this.myChart.getOption();
            option.series[0].data = [];
            option.series[1].data = [];
            for(let n=0;n<this.sysCallIn.length;n++){
                let obj = {
                    value:this.sysCallIn[n],
                    textStyle:{
                        color:'#86A0BD'
                    }
                }
                option.series[0].data.push(obj);
            }
            for(let n=0;n<this.perCallIn.length;n++){
                let obj = {
                    value:this.perCallIn[n],
                    textStyle:{
                        color:'#86A0BD'
                    }
                }
                option.series[1].data.push(obj);
            }
            this.myChart.setOption(option);
         },
         clock(){
             let nowDate = new Date(getServerDate());
             let dateString = nowDate.valueOf();;
             this.getDate(dateString);
             this.getCurrentSysCall();
             setInterval(() => {
                dateString += 1000;
                this.getDate(dateString);
             }, 1000);
         },
         getDate(dateString){
            let y,M,d,H,m,s;
            let nowDate = new Date(dateString);
            y = nowDate.getFullYear();
            M = nowDate.getMonth() + 1 >= 10 ? nowDate.getMonth() + 1 : '0' + Number(nowDate.getMonth() + 1);
            d = nowDate.getDate() >= 10 ? nowDate.getDate() : '0' + nowDate.getDate();
            H = nowDate.getHours() >=10 ? nowDate.getHours() : '0' + nowDate.getHours();
            m = nowDate.getMinutes() >= 10 ? nowDate.getMinutes() : '0' + nowDate.getMinutes();
            if(this.sysCallIn.length == 0 || nowDate.getMinutes()%15 == 0){
                this.getCurrentSumSysCall(nowDate.getHours())
            }
            s = nowDate.getSeconds();
            this.ndate = y+'-'+M+'-'+d;
            this.ntime = H+':'+m;
            this.nsec = s >= 10 ? s : '0' + s;
         },
         displaySetting(){
             if(this.isShowSetting){
                 return;
             }else{
                this.isShowSetting = true;
                setTimeout(() => {
                    this.isShowSetting = false;
                },3000);
             }
         },
         cancelChange(){
             if(localStorage.getItem('screenSetting')){
                 let setting = JSON.parse(localStorage.getItem('screenSetting'));
                 this.isAutoPlay = setting.isAutoPlay;
                 this.changeRate = setting.changeRate;
                 this.defaultDisplay = setting.defaultDisplay;
                 this.refreshRate = setting.refreshRate;
             }else{
                this.isAutoPlay = true;
                this.changeRate = 5;
                this.defaultDisplay = null;
                this.refreshRate = 10;
             }
             this.dialogVisible = false;
         },
         confirmChange(){
             this.dialogVisible = false;
             let setting = {
                 isAutoPlay:this.isAutoPlay,
                 changeRate:this.changeRate,
                 defaultDisplay:this.defaultDisplay,
                 refreshRate:this.refreshRate,
             }
             localStorage.setItem('screenSetting',JSON.stringify(setting));
             window.location.reload();
         },
         showSettingDialog(){
             this.dialogVisible = true;
             setTimeout(() => {
                $('input').attr('disabled','disabled');
             }, 100);
         },
         getReportData(){//获取报事管理数据
            getScreenReportData().then((res) => {
                if(res.code == 200){
                    this.monthList = res.data.monthReportCount;
                    this.dayList = res.data.dayReportCount;
                    res.data.regionReportCount.map((item) => {
                        if(item.name == '北京区域'){
                            item.color = 'pink';
                        }else if(item.name == '上海区域'){
                            item.color = 'green';
                        }else if(item.name == '广深区域'){
                            item.color = 'blue';
                        }else if(item.name == '福州区域'){
                            item.color = 'yellow';
                        }else if(item.name == '武汉区域'){
                            item.color = 'purple';
                        }
                    })
                    this.reportAreaInfoList = res.data.regionReportCount;
                }
            })
        },
        formatTooltip(val) {
            return val / 20;
        },
        handelCarousel(val){
            if(val == 0){
                this.getCurrentSysCall();
            }
        },
        getCurrentSysCall(){
            if(this.ndate && this.ntime && this.nsec){
                let date = this.ndate + ' ' + this.ntime + ':' + this.nsec;
                getCurrentSysCall(date).then(res => {
                    if(res)
                    this.currentData = res[0];
                    this.currentData.SysSuccLV = parseInt(this.currentData.SysSuccLV);
                })
            }
        },
        getCurrentSumSysCall(now){
            this.sysCallIn = [];
            this.perCallIn = [];
            let date = this.ndate + ' ' + (now < 10? '0' + now :now ) + ':00:00';
            getCurrentSumSysCall(date).then(res => {
                if(res){
                    res = res.sort((a,b) =>{
                        return new Date(a.BeginTime)>new Date(b.BeginTime)? 1:new Date(a.BeginTime)<new Date(b.BeginTime)?-1:0;
                    })
                    res.forEach((i,k) =>{
                        this.sysCallIn[k] = i.SysCallInCount;
                        this.perCallIn[k] = i.AgentCallInCount;
                    })
                    this.resetEchart();
                }
            })
        }
    },
    created(){
        let that = this;
        this.clock();
        if(localStorage.getItem('screenSetting')){
            let setting = JSON.parse(localStorage.getItem('screenSetting'));
            this.isAutoPlay = setting.isAutoPlay;
            this.changeRate = setting.changeRate;
            this.defaultDisplay = setting.defaultDisplay;
            this.refreshRate = setting.refreshRate;
        }
        this.getReportData();
        setInterval(() => {
            this.getReportData();
        },that.refreshRate*60000);
    },
    mounted(){
        setTimeout(() => {
            this.rendEchart(this.option);
            if(this.defaultDisplay){
                this.$refs.carousel.setActiveItem(this.defaultDisplay);
            }
        }, 1000);
    }
}
</script>

<style lang="scss" scoped="scoped">
    .screen-container{
        background-color:#2e2e3a;
    }
    .screen-title{
        width:100vw;
        height:7.4vw;
        line-height:7.4vw;
        h3{
            font-size:2.6vw;
            color:#ffffff;
            text-align:center;
            position:relative;
            margin:0;
            padding:0;
            .second-title{
                display:inline-block;
                height:7.4vw;
                font-size:1.6vw;
                line-height:7.4vw;
            }
            .date-time{
                display:inline-block;
               font-size:1.46vw;
               height:1.46vw;
               line-height:1.46vw;
               color:#848dc4;
               position:absolute;
               top:3.8vw;
               right:2.6vw;
            }
        }
    }
    .screen-info{
        padding:2.6vw;
        padding-top:0;
    }
    .flex-container-row{
        display:flex;
        flex-direction:row;
        justify-content:space-between;
        padding-bottom:1.04vw;
    }
    .color-card{
        width:23vw;
        height:9.4vw;
        color:#ffffff;
    }
    .black-card{
        width:43.6vw;
        height:4.167vw;
        line-height:4.167vw;
        background-color:#000000;
        box-shadow:0 0 1.04vw 0 rgba(0,0,0,0.05);
        padding-left:1.042vw;
        padding-right:2.34375vw;
        display:flex;
        flex-direction:row;
        justify-content:space-between;
        align-items:center;
    }
    .chart-card{
        background-color:#000000;
        height:20.52vw;
        box-shadow:0 0 1.04vw 0 rgba(0,0,0,0.05);
    }
    .purple-card{
       background: linear-gradient(to bottom, #8265E5, #7359E0);
       box-shadow:0 0.1vw 0.5vw 0 #785DE2;
    }
    .pink-card{
       background: linear-gradient(to bottom, #EF7477, #EC627B);
       box-shadow:0 0.1vw 0.5vw 0 #ED6979;
    }
    .yellow-card{
       background: linear-gradient(to bottom, #FEC388, #FFBD78);
       box-shadow:0 0.1vw 0.5vw 0 #FEBF7F;
    }
    .blue-card{
       background: linear-gradient(to bottom, #7F95FF, #5F77F7);
       box-shadow:0 0.1vw 0.5vw 0 #6E84FA;
    }
    .color-card-title{
        padding-left:1.04vw;
        padding-top:0.52vw;
        font-size:1.354vw;
        font-weight:bold;
    }
    .color-card-content{
        text-align:center;
        margin-top:0.833vw;
        height:3.6vw;
        font-size:2.71vw;
        font-weight:bold;
    }
    .black-card-title{
        font-size:1.354vw;
        color:#838DC3;
        font-weight:bold;
    }
    .black-card-content{
        font-size:2.1875vw;
        color:#ffffff;
        font-weight:bold;
    }
    .chart-title{
        height:2.083vw;
        text-align:center;
        padding:1.3vw 0;
        color:#ffffff;
        font-size:1.56vw;
        font-weight:bold;
    }
    .chart-container{
       height:15.83vw;
    }
    .map-card{
        width:36.67vw;
        height:40.63vw;
        background-color:#000000;
        border:none;
    }
    .area-card-container{
        width:13.07vw;
        height:40.63vw;
        display:flex;
        flex-direction:column;
        justify-content:space-between;
        align-items:center;
        border:none;
        .area-card{
            width:12.03vw;
            height:7.19vw;
            background-color:#000000;
            border:none;
            padding-top:0.52vw;
            padding-left:1.04vw;
            .area-title{
                font-size:0.94vw;
                font-weight:bold;
                margin-bottom:0.3vw;
            }
            .area-info-content{
                padding-right:1.04vw;
                display:flex;
                flex-direction:row;
                justify-content:space-between;
                margin-bottom:0.2vw;
                color:#ffffff;
                font-size:0.73vw;
                font-weight:bold;
            }
        }
    }
    .report-card-container{
        width:44.01vw;
        height:40.63vw;
        display:flex;
        flex-direction:column;
        justify-content:space-between;
        align-items:center;
        border:none;
        .report-card{
            width:44.01vw;
            height:20.05vw;
            border:none;
            .report-card-title{
                height:5.47vw;
                line-height:5.47vw;
                text-align:center;
                font-size:2.19vw;
                color:rgba(255,255,255,0.8);
                font-weight:bold;
            }
        }
    }
    .map-card{
        display:flex;
        flex-direction:row;
        justify-content:center;
        align-items:center;
        .map-gif{
            width:32.45vw;
            height:26.93vw;
        }
    }
    .row-content,.row-title{
        display:inline-block;
        width:6.25vw;
        font-size:1.56vw;
        font-weight:bold;
    }
    .report-card-row{
        display:flex;
        flex-direction:row;
        justify-content:space-around;
        margin-bottom:0.35vw;
    }
    .align-center{
        text-align:center;
    }
    .align-left{
        text-align:left;
    }
    .align-right{
        text-align:right;
    }
    .grey-white{
        color:rgba(255,255,255,0.8);
    }
    .white{
        color:rgba(255,255,255,1);
    }
    .pink{
        color:#FF8A9B;
    }
    .blue{
        color:#5F94FF;
    }
    .yellow{
        color:#FFB75F;
    }
    .green{
        color:#34B4D9;
    }
    .purple{
        color:#AF54FF;
    }
    .el-icon-setting{
        position:fixed;
        top:15px;
        right:25px;
        color:#ffffff;
        font-size:16px;
        opacity:0.5;
        z-index:99999;
    }
    .dialog-footer{
        text-align:center;
    }
    .el-dialog__footer{
        text-align:center;
    }
    #mask{
        width:100vw;
        height:100vh;
        position:fixed;
        top:0;left:0;right:0;bottom:0;
        background-color:rgba(0,0,0,0);
        z-index:999;
        .date-time{
                display:inline-block;
                font-size:1.46vw;
                height:1.46vw;
                line-height:1.46vw;
                color:#848dc4;
                position:fixed;
                top:3.8vw;
                right:2.6vw;
                font-weight:bold;
            }
    }
    .report-screen-title{
        margin-top:2vw;
    }
</style>
