import Vue from 'vue'
import Router from 'vue-router'
import {
  userCookie
} from '@/api/port'
import mainPage from '@/pages/mainPage' //主页面
import homePage from '@/pages/mainPage/homePage' //首页
import customerService from '@/pages/mainPage/customerService' //客服管理 包括报事录入、报事暂存、报事分派、报事处理、报事回访
import wechatReport from '@/pages/mainPage/customerService/wechatReport/index' //微信报事
import wechatReportDetail from '@/pages/mainPage/customerService/wechatReport/wechatdetail/index'
import entryReport from '@/pages/mainPage/customerService/entryReport' //报事录入
import interimReport from '@/pages/mainPage/customerService/interimReport' //报事暂存
import addReport from '@/pages/mainPage/customerService/addReport' //添加报事
import ITReport from '@/pages/mainPage/customerService/addReport/itReport' //添加IT报事
import Assignment from '@/pages/mainPage/customerService/Assignment' //报事分派
import ReportReturn from '@/pages/mainPage/customerService/ReportReturn' //报事回访
import ReportSolve from '@/pages/mainPage/customerService/ReportSolve' //报事处理

import noJurisdiction from '@/components/noJurisdiction'
import replacePage from '@/pages/mainPage/customerService/addReport/replace' //空白刷新页

import expForm from '@/pages/mainPage/reportExcel/expForm' // 报表管理
import turnToManager from '@/pages/mainPage/reportExcel/handleTurnToManager'
import itExpForm from '@/pages/mainPage/reportExcel/itExpForm' //it报事查询
import sendMessage from '@/pages/mainPage/customerService/sendMessage'
import overallData from '@/pages/mainPage/reportExcel/overallData' // 报修整体数据统计
import channelStatistics from '@/pages/mainPage/reportExcel/channelStatistics' // 报事渠道统计
import closeUpgradeRepair from '@/pages/mainPage/reportExcel/closeUpgradeRepair' // 关闭升级返修报表
import classify from '@/pages/mainPage/reportExcel/classify' // 报事分类数据统计
import upgrade from '@/pages/mainPage/reportExcel/upgrade' // 报事升级统计

import ReportSetting from '@/pages/mainPage/reportSetting' //报事设置
import ReportSettingEdit from '@/pages/mainPage/reportSetting/editPage' //报事设置编辑
import ReportingType from '@/pages/mainPage/reportingType' //报事类型
import ReportingTypeEdit from '@/pages/mainPage/reportingType/editPage' //报事类型编辑
import ReportUpgrade from '@/pages/mainPage/reportUpgrade' // 报事升级
import ReportUpgradeEdit from '@/pages/mainPage/reportUpgrade/editPage' // 报事升级编辑
import ITReportTypeList from '@/pages/mainPage/itReportType/list' //it报事类型设置列表
import ITReportTypeDetail from '@/pages/mainPage/itReportType/setting' //it报事类型设置详情
import ITReportPersonList from '@/pages/mainPage/itReportPerson/list' //it报事项目人员设置列表
import ITReportPersonDetail from '@/pages/mainPage/itReportPerson/setting' //it报事类型设置详情
import csmTag from '@/pages/mainPage/csmTag'; // 客户标签
import csmTagInfo from '@/pages/mainPage/csmTag/info'; // 客户标签
import appNotice from '@/pages/mainPage/appNotice';//app公告

import houseIndex from '@/pages/mainPage/baseMsg/house' // 房屋管理列表
import houseInfo from '@/pages/mainPage/baseMsg/house/info' // 房屋管理页
import custIndex from '@/pages/mainPage/baseMsg/cust' // 业主管理列表
import custInfo from '@/pages/mainPage/baseMsg/cust/info' // 业主管理页
import abnormalIndex from '@/pages/mainPage/baseMsg/abnormal' // 异常客户列表
import abnormalInfo from '@/pages/mainPage/baseMsg/abnormal/info' // 异常客户详情
import deliver from '@/pages/mainPage/baseMsg/deliver'

import screen from '@/pages/mainPage/screenDisplay' //大屏显示首页
import login from '@/pages/login'

Vue.use(Router)

const router = new Router({
  routes: [{
      path: '/',
      component: mainPage,
      children: [{
        path: 'customerService',
        component: customerService,
        children: [{
            path: 'homePage',
            name: 'homePage',
            component: homePage
          },
          {
            path: 'sendMessage',
            name: 'sendMessage',
            component: sendMessage
          },
          {
            path: 'wechatReport',
            component: wechatReport
          },
          {
            path: 'wechatReportDetail',
            component: wechatReportDetail
          },
          {
            path: 'entryReport',
            component: entryReport
          },
          {
            path: 'interimReport',
            component: interimReport
          },
          {
            path: 'addReport',
            component: addReport
          },
          {
            path: 'itReport',
            component: ITReport
          },
          {
            path: 'Assignment',
            component: Assignment
          },
          {
            path: 'ReportReturn',
            component: ReportReturn
          },
          {
            path: 'ReportSolve',
            component: ReportSolve
          },
          {
            path: "replacePage",
            component: replacePage
          },
          {
            path: 'expForm',
            component: expForm
          },
          {
            path: 'turnToManager',
            component: turnToManager
          },
          {
            path: 'itExpForm',
            component: itExpForm
          },
          {
            path: 'overallData',
            component: overallData
          },
          {
            path: 'channelStatistics',
            component: channelStatistics
          },
          {
            path: 'closeUpgradeRepair',
            component: closeUpgradeRepair
          },
          {
            path: 'classify',
            component: classify
          },
          {
            path: 'upgrade',
            component: upgrade
          },
          {
            path: 'ReportSetting',
            component: ReportSetting
          },
          {
            path: 'ReportSettingEdit',
            component: ReportSettingEdit
          },
          {
            path: 'ReportingType',
            component: ReportingType
          },
          {
            path: 'ReportingTypeEdit',
            component: ReportingTypeEdit
          },
          {
            path: 'custIndex',
            component: custIndex
          },
          {
            path: 'custInfo',
            component: custInfo
          },
          {
            path: 'houseIndex',
            component: houseIndex
          },
          {
            path: 'houseInfo',
            component: houseInfo
          },
          {
            path: 'abnormalIndex',
            component: abnormalIndex
          },
          {
            path: 'abnormalInfo',
            component: abnormalInfo
          }, {
            path: 'deliver',
            component: deliver
          }, {
            path: 'ReportUpgrade',
            component: ReportUpgrade
          }, {
            path: 'ReportUpgradeEdit',
            component: ReportUpgradeEdit
          }, {
            path: 'ITReportTypeList',
            component: ITReportTypeList
          }, {
            path: 'ITReportTypeDetail',
            component: ITReportTypeDetail
          }, {
            path: 'ITReportPersonList',
            component: ITReportPersonList
          }, {
            path: 'ITReportPersonDetail',
            component: ITReportPersonDetail
          }, {
            path: 'csmTag',
            component: csmTag
          }, {
            path: 'csmTagInfo',
            name: 'csmTagInfo',
            component: csmTagInfo
          },{
            path: 'appNotice',
            name: 'appNotice',
            component: appNotice
          }
        ]
      }]
    },
    {
      path: '/custInfo',
      component: custInfo
    },
    {
      path: '/screen',
      name: 'screen',
      component: screen,
    },
    {
      path: '/no',
      name: 'noJurisdiction',
      component: noJurisdiction
    },
    {
      path: '/login',
      name: 'login',
      component: login
    },
  ]
})

export default router;

router.beforeEach((to, from, next) => {
  // debugger;
  if (to.path.indexOf('/login') >= 0) {
    next();
    return;
  }
  // 判断是否存在用户信息；如果不存在则去请求用户信息
  let winHash = window.location.hash;
  if (winHash.indexOf('#/screen') == -1) {
    if (!sessionStorage.getItem('userInfo')) {
      userCookie().then(res => {
        if (res == null || res.data == null) { 
          next('login');
          return;
        }
        sessionStorage.setItem("userInfo", JSON.stringify(res.data));
        nextTo(to, next);
      });
    } else {
      nextTo(to, next);
    }
  } else {
    next();
  }
})

// 页面跳转判断；
function nextTo(to, next) {
  let userInfo = JSON.parse(sessionStorage.getItem('userInfo'));
  var menus = userInfo.menuTree;
  if (menus.length == 0) { // 无用户权限
    next('login');
    return;
  }

  // 如果是 / 则返回对应权限；其他页面判断是否拥有该页面权限
  if (to.path == '/') {
    for (let i = 0; i < menus.length; i++) {
      let m = menus[i];
      if (m.attributes.url != '#') {
        next('/customerService/' + m.attributes.url);
        return;
      } else {
        for (let j = 0; j < m.children.length; j++) {
          let mc = m.children[j];
          if (mc.attributes.url != '#') {
            next('/customerService/' + mc.attributes.url);
            return;
          }
        }
      }
    }
  } else {
    if (("csm.tahoecn.com").indexOf(location.hostname) < 0) {
      next();
      return;
    }
    let p = to.path.split('/');
    // 过滤拦截
    if ("addReport/itReport/replacePage/ReportSettingEdit/houseInfo/custInfo/ITReportTypeDetail/ITReportPersonDetail/csmTagInfo/abnormalInfo".indexOf(p[p.length - 1]) >= 0) {
      next();
      return;
    }
    let flg = false;
    userInfo.menuTree.forEach(v => {
      if (to.path.indexOf(v.attributes.url) >= 0) {
        flg = true;
      } else {
        v.children.forEach(e => {
          if (to.path.indexOf(e.attributes.url) >= 0) {
            flg = true;
          }
        });
      }
    })
    next(flg);
  }
}
