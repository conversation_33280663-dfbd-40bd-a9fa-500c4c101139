import Vue from "vue"
import Vuex from "vuex"
import getters from './getters';

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    userBtn: 'init',
    token: '',
    phoneNum: '',
  },
  mutations: {
    setUserBtn(state, data) {
      state.userBtn = data;
    },
    SET_TOKEN(state, data) {
      state.token = data;
    },
    SET_USERNAME(state, data) {
      state.username = data;
    },
    SET_PHONENUM(state, data) {
      state.phoneNum = data;
    }
  },
  getters
})

export default store;
