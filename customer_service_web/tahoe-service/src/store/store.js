import Vue from "vue"
import Vuex from "vuex"

Vue.use(Vuex);


const store = new Vuex.Store({
  state: {
    userBtn : 'init',
    token: '',
    phoneNum:'',
  },
  mutations: {
    setUserBtn(state,data) {
      state.userBtn = data;
    },
    SET_TOKEN(state, data) {
      state.token = data;
    },
    SET_USERNAME(state, data) {
      state.username = data;
    },
    SET_PHONENUM(state, data) {
      state.phoneNum = data;
    }
  }
})

export default store;
