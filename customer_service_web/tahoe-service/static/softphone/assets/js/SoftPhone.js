// var ws_address = "************" , ws_port = "5066" , keep_alive_t1  , keep_alive_num , trans_num="" ,tpe="", trans_type="" ,trans_phone,cdrId;	
var ws_address = "*************" , ws_port = "5066" , keep_alive_t1  , keep_alive_num , trans_num="" ,tpe="", trans_type="" ,trans_phone,cdrId;	
$(document).ready(function(){
	$(document).on("click" , '[data-toggle="soft-function"]' , function(e){
		if($(this).closest(".disabled").length == 0){
			var name = $(this).data("action");
			if(name == "login"){
				uKeFuSoftPhone.input();
			}else if(name == "logout"){
				uKeFuSoftPhone.logout();
			}else if(name == "ready"){
				uKeFuSoftPhone.ready();
			}else if(name == "notready"){
				uKeFuSoftPhone.notready();
			}else if(name == "answer"){
				uKeFuSoftPhone.answer();
			}else if(name == "hungup"){
				uKeFuSoftPhone.hungup();
			}else if(name == "hold"){
				uKeFuSoftPhone.hold();
			}else if(name == "unhold"){
				uKeFuSoftPhone.unhold();
			}else if (name == "trans") {
				uKeFuSoftPhone.trans();
				var obj=document.getElementById('select_ivr');
				$.ajax({
					type : "POST", //请求方式
					url : 'http://*************:12000/callcenter' + '/callIvr/findName', //地址，就是json文件的请求路径
					dataType : "json", //数据类型可以为 text xml json  script  jsonp
					success : function(result) { //返回的参数就是 action里面所有的有get和set方法的参数
						for(var i=0;i<result.length;i++){ 
							 obj.options[i]=new Option(result[i].ivrMenuName,result[i].ivrMenuExtension); 
							 }
						}
					});
				 
				$(document).ready(function(){
					   $("#select_id").change(function(){
					       var selected=$(this).children('option:selected').val()
					       if(selected=="saab"){
					    	  // console.log( $('#trans_num'))
					    	   $('#trans_num').css('display','block');
					    	   $('#select_ivr').css('display','none');
					       }else{
					    	   $('#trans_num').css('display','none');
					    	   $("#select_ivr")
								.attr("style",
										"display:block;margin-left:80px;margin-top:-31.5px;height: 31.5px;width:100px");

					       }
					   });
					}); 
			}else if (name == "listen") {
				uKeFuSoftPhone.listen();
			}else if (name == "monitor") {
				uKeFuSoftPhone.monitor();
			}
		}		
	});
	var ondial = false ;
	$('#softphone-makecall').click(function(){
		if($(this).closest(".disabled").length == 0){
			window.frameElement.style.height = '370px';
			$('#dialpad').show();
		}
	}) ;
	$('#dialpad .number').on("mousedown" , function(e){
		$(this).css("background-color" , "#1E90FF") ;
	}).on("mouseup" , function(e){
		$(this).css("background-color" , "#FFFFFF") ;
	}).on("click" , function(e){
		$("#dialpad-input").val($("#dialpad-input").val() + $(this).attr("id"));
	});
	$("#dialpad-del").on("click" , function(){
		$("#dialpad-input").val($("#dialpad-input").val().substr(0,$("#dialpad-input").val().length - 1))
	});
	$("#makecall").on("click" , function(){
		if(new RegExp('^[0-9]*$').test($('#dialpad-input').val())){
			uKeFuSoftPhone.invite($('#dialpad-input').val());
			$('#dialpad-input').val("") ;
			$('#dialpad').hide();
		}else{
			top.layer.msg("无效的号码，请重新输入");
		}
	});
	$('#dialpad').hover(function(){
		ondial = true ;
	}, function(){
		ondial = false ;
		setTimeout(function(){
			if(ondial == false){
				window.frameElement.style.height = '70px';
				$('#dialpad').hide();
			}
		} , 1000);
	});
});



var softPhoneUA , currentSession , mediaStream;
var agent , domain,extno,extpass;
var uKeFuSoftPhone = {
	input : function(){
		// login();
		top.layer.msg($('#ukefu-login-html').html(), {
		  time: 0 //不自动关闭
		  ,btn: ['登录', '关闭']
		  ,yes: function(index){
			$(':focus').blur();
		  	// agent = $("#agent").val();
			// extno = $("#extno").val();
			// domain = $("#domain").val();
			// var extpass = $("#extpass").val();
			agent = window.parent.getLoginMsg().agentVue;
			extno = window.parent.getLoginMsg().extnoVue;
			domain =window.parent.getLoginMsg().domainVue;
			extpass = window.parent.getLoginMsg().extpassVue;
			top.layer.close(index);
			if(agent==''){
				top.layer.msg("工号不能为空");
			}else if(extno==''){
				top.layer.msg("分机号不能为空");
			}else if(extpass == ''){
				top.layer.msg("密码不能为空");
			}
			else{
				uKeFuSoftPhone.login(agent , extno , extpass,domain);
			}
		  }
		});
		$("#agent").focus();
		
	},
	login(agent , extno , extpass , domain){
		console.log(ws_address)
		//登录地址拼串
		// domain = "taihe.com";
		// agent = "6002";
		// extno = "6002";
		// extpass = "123456";
		
		domain = domain;
		agent  = agent;
		extno = extno;
		extpass = extpass;
		console.log(agent,extno,extpass,domain)
		var login_url =  'http://'+ws_address+':8088/v1/login/?query=agent:'+agent+',extension:'+extno+',password:'+extpass +',domain:'+domain;
		//var login_url =  'http://'+ws_address+':8088/v1/login/?query=agent:6002,extension:6002,password:1234,domain:tcql.com';
			//话机注册后再调用api 来将工号和话机绑定
			$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: login_url,                                                                             
			        dataType:"jsonp",                              
			        success: function(data){
			        	//console.log(data);
			        		if(data == 'OK'){
			        			top.layer.msg('登录成功');
			        			uKeFuSoftPhone.status.ready();
			        			setExt(extno,domain,ws_address,'8800');
								keep_alive_num = 0;
								window.agent = agent;
								window.domain = domain;
			        			keep_alive_t1 = window.setInterval(keepAlive,3000); 
								doSocket();
			        		}else{
			        			top.layer.msg('登录失败:'+data);	
			        			uKeFuSoftPhone.status.logout();
			        		}
			      		},
        			error:function(e) {
						top.layer.msg("注册异常:"+e);
                    }  
            });
        
	},
	ready:function(){
		$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: 'http://'+ws_address+':8088/v1/ready/?query=extension:'+extno+',domain:'+domain+',type:success',                                                                             
			        dataType:"jsonp",                              
			        success: function(data){
			        //	console.log(data);
			        		if(data == 'ok'){
			        			top.layer.msg('就绪成功');
			        			uKeFuSoftPhone.status.ready();
			        		}else{
			        			top.layer.msg('就绪失败:'+data);	
			        			//uKeFuSoftPhone.status.notready();
			        		}
			      		},
        			error:function(e) {
        				console.log(JSON.stringify(e))
						top.layer.msg("就绪异常:"+e);
                    }  
            });
		 //uKeFuSoftPhone.status.ready();
		//softPhoneUA.register({register:true});
	},
	invite:function(pnumber){
		$.ajax({                                                                              
        type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: false,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',
        url: 'http://'+ws_address+':8088/v1/originate/?query=aleg:'+extno+',bleg:'+pnumber+',domain:'+domain,                                                                         
        dataType:"jsonp",                              
        success: function(data){
        		if("OK"==data){
        			call_Event("callOut",pnumber);
        		}else{
        			top.layer.msg("外呼失败:"+data);
        		}
        	},
        error:function(e) {
			top.layer.msg("外呼失败:"+JSON.stringify(e));
        }                                                         
 		});

	},
	logout:function(){
		$.ajax({                                                                              
        type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: false,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',     
        url: 'http://'+ws_address+':8088/v1/logout/?query=agent:'+agent+',domain:'+domain,                                                                             
        dataType:"jsonp",                              
        success: function(data){
        		if (data == 'OK'){
        			top.layer.msg('退出');
        			socket.close();
        			window.clearInterval(keep_alive_t1);
        			uKeFuSoftPhone.status.logout();

        		}else{
        			top.layer.msg("退出异常:"+data);
        		}
        		
        	},
        error:function(e) {
			top.layer.msg("退出异常:"+JSON.stringify(e));
                    }                                                         
 		});
	},
	answer:function(){
		if(currentSession){			
			currentSession.accept(uKeFuSoftPhone.getOptions());
		}
	},
	hungup:function(){
		$.ajax({                                                                              
        type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: false,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',     
        url: 'http://'+ws_address+':8088/v1/kill/?query=leg_uuid:'+$('#trans-numA').val(),                                                                             
        dataType:"jsonp",                              
        success: function(data){
        		if (data == 'OK'){
        			top.layer.msg('挂机');
        			uKeFuSoftPhone.status.hungup();

        		}else{
        			top.layer.msg("挂机异常:"+data);
        			uKeFuSoftPhone.status.hungup();
        		}
        	},
        error:function(e) {
			top.layer.msg("挂机异常:"+e);
                uKeFuSoftPhone.status.hungup();
                 }  
        });
	},
	hold:function(){
		$('#softphone-status .hold').addClass("disabled")
		setTimeout(holdd,1000); 
		
	},
	unhold:function(){
		$('#softphone-status .unhold').addClass("disabled")
		setTimeout(unholdd,1000); 

	},
	notready:function(){
			$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: 'http://'+ws_address+':8088/v1/sleep/?query=agent:'+agent+',domain:'+domain,                                                                             
			        dataType:"jsonp",                              
			        success: function(data){
			        //	console.log(data);
			        		if(data == 'OK'){
			        			top.layer.msg('小休成功');
			        			uKeFuSoftPhone.status.notready();
			        		}else{
			        			top.layer.msg('小休失败:'+data);	
			        			//uKeFuSoftPhone.status.notready();
			        		}
			      		},
        			error:function(e) {
						top.layer.msg("就绪异常:"+e);
                    }  
            });
		 //uKeFuSoftPhone.status.ready();
		//softPhoneUA.register({register:true});
	},
	/*trans:function(){
		top.layer.open({
		type: 1,
		skin: 'layui-layer-rim', //加上边框
        area: ['260px', '100px'], //宽高
  		title: '转移',
  		content: '<select style="margin-left: 15px;margin-top: 15px; height:30px ; width: 60px ;"><option value ="volvo">队列</option><option value ="saab">号码</option></select>&nbsp;<input style="margin-top: 14px;height:23px" "border: 1px solid #ccc;padding: 7px 0px;border-radius: 3px;padding-left:5px;-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);box-shadow: inset 0 1px 1px rgba(0,0,0,.075);-webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out ."-o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;"transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s" type="text" />'
});
	},*/

	trans : function(){
	
		top.layer.msg($('#ukefu-trans-html').html(),{
		  time: 0 //不自动关闭
		  ,btn: ['转接', '取消']
		  ,yes: function(index){					  
					       var selected=$("#select_id").children('option:selected').val()
					       if(selected=="saab"){
					    	 //查询输入号码 
								   trans_num = $("#trans_num").val();
					       }else{
					    	   //查询ivr流程
								   trans_num = $("#select_ivr").val();
								   trans_type = 'IVR'
					       }
					      if(type=="in"){
					    	  uuid = $('#trans-numA').val();
					      }else{
					    	  uuid = $('#trans-numB').val();
					      }
					       
						  top.layer.close(index);
			  uKeFuSoftPhone.transnum();
		  }
		});
		$("#agent").focus();
	},
    transnum:function(){
    	//console.log('http://'+ws_address+':8088/v1/Transfer/?query=uuid:'+uuid+',domain:'+domain+',dest-exten:'+trans_num+',transfer-exten:'+trans_phone+',type:'+trans_type)
		$.ajax({                                                                              
        type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: false,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',
       // url: 'http://'+ws_address+':8088/v1/Transfer/?query=aleg_uuid:'+$('#trans-numA').val()+',bleg_uuid:'+$('#trans-numB').val()+',a-dest-exten:'+trans_num+',b-dest-exten:'+num+',domain:'+domain,   
        url:'http://'+ws_address+':8088/v1/Transfer/?query=uuid:'+uuid+',domain:'+domain+',dest-exten:'+trans_num+',transfer-exten:'+trans_phone+',type:'+trans_type,
        dataType:"jsonp",                              
        success: function(data){
			top.layer.msg("转接:"+data);
        	},
        error:function(e) {
			top.layer.msg("转接失败:"+JSON.stringify(e));
                    }                                                         
 		});

	},
	
	listen:function(){
		top.layer.open({
			type: 2 ,
			skin: 'layui-layer-rim', //加上边框
	        area: ['1000px', '500px'], //宽高
	  		title: '监听',
	  		content: 'http://*************:12000/callcenter/static/softphone/jsonptest.html?extno='+extno+ '&domain='+ domain,
	/*  		 success: function(layero, index){
	           window.setTimeout("layer.iframeSrc("+index+", 'jsonptest.html?extno="+extno+"')",5000);
	  		 }*/
	  	});

	

	},
	monitor:function(){
		var Domain = $("#domain").val();
		top.layer.open({
			type: 2 ,
			skin: 'layui-layer-rim', //加上边框
	        area: ['1015px', '520px'], //宽高
	  		title: '监控',
	  		content: 'http://*************:12000/callcenter/static/softphone/monitor.html?extno='+extno+ '&domain='+ Domain,
	/*  		 success: function(layero, index){
	           window.setTimeout("layer.iframeSrc("+index+", 'jsonptest.html?extno="+extno+"')",5000);
	  		 }*/
	  	});

	

	},
	sessionEvent:function(session){
		session.on("rejected" , function (response, cause){
			uKeFuSoftPhone.status.hungup();
		});
		session.on("bye" , function (response, cause){
			uKeFuSoftPhone.status.hungup();
		});
		session.on("hold" , function (response, cause){
			uKeFuSoftPhone.status.hold();
		});
		session.on("unhold" , function (response, cause){
			uKeFuSoftPhone.status.unhold();
		});
		session.on("accepted" , function (response, cause){
			uKeFuSoftPhone.status.accepted();
		});
		session.on("cancel" , function (response, cause){
			uKeFuSoftPhone.status.hungup();
		});
		uKeFuSoftPhone.status.initCallStatus(session) ;
	},
	status : {
		login:function(){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').addClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").show();
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");


			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').removeClass("disabled").show();

			$('#softphone-status .ready').removeClass("disabled").show();
			$('#softphone-status .notready').addClass("disabled").hide();
		},
		ready : function(){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').addClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").show();
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').removeClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').removeClass("disabled").show();

			$('#softphone-status .ready').addClass("disabled").hide();
			$('#softphone-status .notready').removeClass("disabled").show();
		},
		notready : function(){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').addClass("disabled");

			$('#softphone-status .hold').addClass("disabled").show();
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').removeClass("disabled").show();

			$('#softphone-status .ready').removeClass("disabled").show();
			$('#softphone-status .notready').addClass("disabled").hide();

		},
		callIn : function(){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').removeClass("disabled");
			$('#softphone-hungup').removeClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").show();;
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').addClass("disabled").show();

			$('#softphone-status .ready').addClass("disabled").hide();
			$('#softphone-status .notready').addClass("disabled").show();
		},
		callOut : function(){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').removeClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").show();;
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').addClass("disabled").show();

			$('#softphone-status .ready').addClass("disabled").hide();
			$('#softphone-status .notready').addClass("disabled").show();
		},
		hungup : function(){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').addClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").show();;
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').removeClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').removeClass("disabled");
			$('#softphone-status .ready').removeClass("disabled").show();
			$('#softphone-status .notready').addClass("disabled").hide();
		},
		accepted : function (){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').removeClass("disabled");
			
			$('#softphone-status .hold').removeClass("disabled").show();
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').removeClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').addClass("disabled").show();

			$('#softphone-status .ready').addClass("disabled").hide();
			$('#softphone-status .notready').addClass("disabled").show();
		},
		hold : function (){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').addClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").hide();
			$('#softphone-status .unhold').removeClass("disabled").show();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').removeClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').addClass("disabled").show();

			$('#softphone-status .ready').addClass("disabled").hide();
			$('#softphone-status .notready').addClass("disabled").show();
		},
		unhold : function (){
			$('.soft-function,.status').removeClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').removeClass("disabled");
			
			$('#softphone-status .hold').removeClass("disabled").show();
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').removeClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");

			$('#ukefu_account .login').hide();
		//	$('#ukefu_account .logout').show();
			$('#ukefu_account .logout').addClass("disabled").show();

			$('#softphone-status .ready').addClass("disabled").hide();
			$('#softphone-status .notready').addClass("disabled").show();
		},
		logout : function (){	
			$('.status').addClass("disabled");	
			$('#softphone-answer').addClass("disabled");
			$('#softphone-hungup').addClass("disabled");
			
			$('#softphone-status .hold').addClass("disabled").show();
			$('#softphone-status .unhold').addClass("disabled").hide();

			$('#softphone-trans').addClass("disabled");
			$('#softphone-makecall').addClass("disabled");
			$('#softphone-maketrans').addClass("disabled");
			$('#softphone-listen').addClass("disabled");

			$('#softphone-status .ready').addClass("disabled").show();
			$('#softphone-status .notready').addClass("disabled").hide();

			$('#ukefu_account .login').removeClass("disabled").show();
			$('#ukefu_account .logout').addClass("disabled").hide();

		},
		initCallStatus:function(session , called){
			$('#caller .number').text(session.request.from.uri.user);
			if(called){
				$('#called .number').text(called);
			}
		}
	}
	
}
function getUserMediaSuccess (stream) {
	console.log('getUserMedia succeeded', stream);
	mediaStream = stream;
}
function getUserMediaFailure (e) {
	console.error('getUserMedia failed:', e);
}



function holdd(){
	console.log("holdd");
	$.ajax({                                                                              
		type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: false,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',     
        url: 'http://'+ws_address+':8088/v1/hold/?query=leg_uuid:'+$('#trans-numA').val()+',op:on',                                                                             
        dataType:"jsonp",                              
        success: function(data){
        //	console.log(data);
        		if(data == 'OK'){
        			top.layer.msg('保持成功');
        			uKeFuSoftPhone.status.hold();
        		}else{
        			top.layer.msg('保持失败:'+data);	
        			//uKeFuSoftPhone.status.notready();
        		}
      		},
		error:function(e) {
			console.log(JSON.stringify(e));
			top.layer.msg("保持异常:"+e);
        }  
});
}

function unholdd(){
	console.log("unholdd");
	$.ajax({                                                                              
		type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: false,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',     
        url: 'http://'+ws_address+':8088/v1/hold/?query=leg_uuid:'+$('#trans-numA').val()+',op:off',                                                                             
        dataType:"jsonp",                              
        success: function(data){
        //	console.log(data);
        		if(data == 'OK'){
        			top.layer.msg('取消保持成功');
        			uKeFuSoftPhone.status.unhold();
        		}else{
        			top.layer.msg('取消保持失败:'+data);	
        			//uKeFuSoftPhone.status.notready();
        		}
      		},
		error:function(e) {
			top.layer.msg("取消保持异常:"+e);
        }  
});
}

function keepAlive(){
	console.log("心跳："+agent,extno,domain)

	$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: true,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: 'http://'+ws_address+':8088/v1/Hearbeat/?query=agent:'+agent+',extension:'+extno+',domain:'+domain,                                                                             
			        dataType:"jsonp",
			        timeout: 3000,
			        success: function(data){
			        	// console.log(data);
			        		if(data == 'OK'){
			        		//	console.log('在线');
			        			keep_alive_num = 0;
			        		}else{
			        			keep_alive_num++;
			        			if(keep_alive_num>3){
			        				socket.close();
			            			window.clearInterval(keep_alive_t1);
			            			call_Event('connectionError');
			        			}
			        			console.log('keep_alive出现异常');	
			        			//uKeFuSoftPhone.status.notready();
			        		}
			      		},
			      	error    : function(XMLHttpRequest,textStatus ,errorThrown){
			      	        	console.log('keep_alive error:'+keep_alive_num);
			      	        	keep_alive_num++;
			        			if(keep_alive_num>3){
			        				socket.close();
			            			window.clearInterval(keep_alive_t1);
			            			call_Event('connectionError');
			        			}
			      	          
                    }
        });
}


function after_call(){
	$.ajax({                                                                              
		type: "GET",        //jsonp跨域请求只能是get请求                                                
        async: true,       //jsonp跨域请求只能是异步请求                                                                 
        contentType:'application/x-javascript;charset=utf-8',     
        url: 'http://'+ws_address+':8088/v1/After/?query=agent:'+agent+',extension:'+extno+',domain:'+domain,                                                                             
        dataType:"jsonp",
        success: function(data){
        	//console.log('http://'+ws_address+':8088/v1/After/?query=agent:'+agent+',extension:'+extno+',domain:'+domain);
        		if(data == 'OK'){
        			//console.log(1);
        			top.layer.msg('话后成功');
        			uKeFuSoftPhone.status.notready();
        		}else{
        			top.layer.msg('话后失败：'+data);
        		}
      		},
      	error: function(data){
      	        	console.log('after error:'+data);
        }
});
}




function call_Event(event,arg1,arg2,cdrid){
	console.log("NEGEID:"+cdrid)
	cdrId = cdrid;
	switch (event){
		case 'callIn':
		top.layer.msg(arg1+'来电');
			type="in";
			trans_phone = arg1;
			$('#caller').children('span').html(arg1);
			$('#called').children('span').html(extno);
			var url = window.location.href; 
			var url1 = url.split("/");
			//addTab({"url" : url1[0]+"//"+url1[2]+"/callcenter/workOrder/manager?queue=154&AGENTID=agentid&caller="+arg1+"&cdrid="+arg2+"","title" : "工单管理","iconCls" : "icon-folder"});
			uKeFuSoftPhone.status.callIn()
			break;
		case 'forecast_callIn':
		top.layer.msg(arg1+'来电了');
			type="in";
			trans_phone = arg1;
			$('#caller').children('span').html(arg1);
			$('#called').children('span').html(extno);
			//window.parent.getNum(arg1);
			// var url = window.location.href; 
			// var url1 = url.split("/"); 
			// addTab({"url" : url1[0]+"//"+url1[2]+"/callcenter/workOrder/manager?queue=154&AGENTID=agentid&caller="+arg1+"&cdrid="+arg2+"","title" : "工单管理","iconCls" : "icon-folder"});
			/*top.layer.open({
				  type: 2,
				  title: '工单管理',
				  shadeClose: true,
				  shade: 0.8,
				  area: ['90%', '90%'],
				  content: 'http://192.168.1.113:8081/callcenter/workOrder/manager?queue=154&AGENTID=agentid&caller="+arg1+"&cdrid="+arg2+"' //iframe的url
				}); */
			uKeFuSoftPhone.status.callIn()
			break;
		case 'callOut':
		top.layer.msg(arg1);
			type="out";
			trans_phone = arg1;
		//	layer.msg(trans_phone)
			$('#caller').children('span').html(extno);
			$('#called').children('span').html(arg1);
			uKeFuSoftPhone.status.callOut()
			break;
		case 'answered':
		//	layer.msg('应答');
			uKeFuSoftPhone.status.accepted()
			let tel = $('#caller').children('span').html();
			window.parent.goEntry(tel);
			break;
		case 'hangup':
		top.layer.msg('挂机');
			$('#caller').children('span').html("");
			$('#called').children('span').html("");
			//判断是否自动就绪
			if($("#isAutoReady").is(':checked')){
				uKeFuSoftPhone.ready();
			}else{
				uKeFuSoftPhone.status.hungup();
				//挂机后调用话后状态接口
				console.log('after');
				after_call();
			}
			break;
		case 'listen':
		top.layer.msg('监听');
			uKeFuSoftPhone.status.callOut();
			break;
		case 'setUUID':
			$('#trans-numA').val(arg1);
			$('#trans-numB').val(arg2);
			break;
		case 'setCDRID':
			CDRID = cdrid;
			break;
		case 'getDTMF':
		top.layer.open({
			  type: 1 //Page层类型
			  ,area: ['200px', '100px']
			  ,title: 'DTMF'
			  ,shade: 0.2 //遮罩透明度
			  ,maxmin: false //允许全屏最小化
			  ,anim: 1 //0-6的动画形式，-1不开启
			  ,content: '<div style="padding:10px;">'+arg1+'</div>'
			}); 
			break;
		case 'connectionError':
		top.layer.msg('与服务器连接异常,请检查');
			uKeFuSoftPhone.status.logout();
			break;	
	}

}
