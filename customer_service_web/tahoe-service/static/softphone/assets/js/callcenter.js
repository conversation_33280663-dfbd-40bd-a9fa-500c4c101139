 var extno = undefined;
 var domain = undefined;
 var socket_url = undefined;
 var socket_port = undefined;
 var cccId = "";
 
 $(window).on('beforeunload', function() {
		if (socket) {
			alert(socket.close);
			socket.close();
		}
	});
 
  function setExt(ext,domain,url,port){
      extno = ext ;
      domain = domain ;
      socket_url = url ;
      socket_port = port ;
  }
 // var port = document.location.port;
  // var port = 8081; // for test
  //  var socket = new WebSocket("ws://" + document.location.hostname + ":" + port + "/socket", "websocket");
  if(window.WebSocket){
	  console.log('This browser supports WebSocket');
  }else{
	  console.log('This browser does not supports WebSocket');
  }
  var socket;//防止关闭时没有定义
  function doSocket(){
	  socket = new WebSocket("ws://"+socket_url+":"+socket_port+"/socket", "websocket");
	  console.log(socket)
	  try {
	    socket.onopen = function() {
	      socket.send("event json CHANNEL_CREATE");
	      socket.send("event json CHANNEL_BRIDGE");
	      socket.send("event json CHANNEL_HANGUP_COMPLETE");
	      socket.send("event json CHANNEL_CALLSTATE");
	     // socket.send("event json CUSTOM");
	    }
	
	    socket.onmessage =function(msg) {
	     // console.log(msg.data);
	      var data = JSON.parse(msg.data);
	      eventCallback(data);
	    }
	
	    socket.onclose = function(){
	      //$('#ws-status').html('Socket Disconnected!').css("color", "red");
	      console.log("socket disconnected, fallback to event_sink");
	      //setInterval("App.channelsController.checkXMLEvent()", 2000);
	    }
	    
	    socket.onerror = function (e) {
	        console.log("出现错误"+JSON.stringify(e));
	        
	    };
	  } catch(exception) {
	    alert('Error' + exception);
	  }
  }
  

var uuid = undefined;
var is_aleg = false;
var bleg_uuid = undefined;
var is_listen = false;

function eventCallback(data)  {
	  //打印date
	  console.log(data)
	  	//接口外呼拨打的时候出发次判断,从而获取a_leg_uuid
	    if (uuid == undefined && data["Event-Name"] == "CHANNEL_CREATE" && data["Caller-Destination-Number"] == extno && data["variable_a_leg_uuid"] == undefined && domain == data["variable_tcql_domain"]) {  
	        uuid = data["Caller-Unique-ID"]
	 //       console.log("uuid1:"+uuid);
	        call_Event('setUUID',uuid,bleg_uuid);
	        
	        if ("1" == data['variable_is_eavesdrop']){
	          is_listen = true;
	        }
	    } 
        if(uuid == undefined && data["Event-Name"] == "CHANNEL_CREATE" && data["Caller-Destination-Number"] == extno && domain == data["variable_dialed_domain"] && data["variable_is_forecast"]== "1") {  
	        uuid = data["Caller-Unique-ID"]
	        bleg_uuid =  data["variable_a_leg_uuid"]
	   	 //       console.log("uuid1:"+uuid);
	   	        call_Event('setUUID',uuid,bleg_uuid);
/*	   	        if ("1" == data['variable_is_eavesdrop']){
	   	          is_listen = true;
	   	        }*/
	        call_Event('forecast_callIn',data['Caller-Orig-Caller-ID-Number'])
	   	    } 
	  
	    if (data["Event-Name"] == "CHANNEL_CREATE" && data["Caller-Destination-Number"] == extno && data["variable_a_leg_uuid"] != undefined && data["Answer-State"] == "ringing" && domain == data["variable_tcql_domain"]) {
	        uuid = data["Caller-Unique-ID"];
	       //  console.log("uuid2:"+uuid);
	        cdrid = data["variable_cdrid"];
	        call_Event('setUUID',uuid,bleg_uuid);
	        is_aleg = true ;
	    } 

	    if( uuid !=undefined && data["Event-Name"] == "CHANNEL_BRIDGE" && data["Bridge-B-Unique-ID"] ==  uuid ){
	        bleg_uuid = data["Bridge-A-Unique-ID"]
	        //设置uuid
	        cdrid = data["variable_cdrid"];
	        call_Event('setUUID',uuid,bleg_uuid);
	//        console.log("bleg:"+bleg_uuid);
	    }

	    if(data["Event-Name"] == "CHANNEL_CREATE" && data["variable_a_leg_uuid"] == uuid && domain == data["variable_tcql_domain"]){
	      bleg_uuid = data["Caller-Unique-ID"]
	      cdrid = data["variable_cdrid"];
	      console.log("CDRID"+cdrid)
	      call_Event('setCDRID',"S","S",cdrid);
	    }

	    if(uuid != undefined && data["Command"] == "sendevent CUSTOM" &&  data["aleg_uuid"] == uuid ){
	      call_Event('getDTMF',data["dtmf"]);
	    }

	    if (uuid == data["Caller-Unique-ID"]  && data["Event-Name"] == "CHANNEL_CALLSTATE"){
	      switch (data["Answer-State"]){
	          case 'ringing': 
	        //	  console.log("ringing:"+data["Caller-Destination-Number"]);
	              if (data["Caller-Destination-Number"] == extno && is_aleg == true &&  data["variable_is_forecast"]== undefined) {
	                //alert(data['Caller-Caller-ID-Number']+'来电');
	                 call_Event('callIn',data['Caller-Caller-ID-Number'],cdrid)
	              }else{
	                  if (is_listen){
	                      call_Event('listen','');
	                  }else{
	                      call_Event('callOutSuccess','');
	                  }
	              }
	              break;
	              //判断对方接听需要知道对方号码
	          case 'answered':
	              if(data["Caller-Callee-ID-Number"] == extno){
	            	  //alert(data["variable_cdrid"]);
	                  call_Event('answered','');
	              }
	              break;
	      }
	    }
	        if (bleg_uuid == data["Caller-Unique-ID"]  && data["Event-Name"] == "CHANNEL_CALLSTATE"){
	      switch (data["Answer-State"]){
	          case 'ringing': 
	              if (data["Caller-Destination-Number"] == extno && is_aleg == true &&  data["variable_is_forecast"]== "") {
	                //alert(data['Caller-Caller-ID-Number']+'来电');
	                  call_Event('callIn',data['Caller-Caller-ID-Number'],cdrid)
	              }else{
	                  if (is_listen){
	                      call_Event('listen','');
	                  }else{
	                      call_Event('callOutSuccess','');
	                  }
	              }
	              break;
	          case 'answered':
	            if(data["Caller-Destination-Number"] == extno){
	                  call_Event('answered','');
	                }
	              break;
	      }
	    }


	    //呼入时挂机的时候没有 data["Caller-Unique-ID"] 和他人的uuid都为undefined.防止出错加了不为undefined的判断
	    if (uuid == data["Caller-Unique-ID"] && uuid != undefined  &&  data["Event-Name"] == "CHANNEL_HANGUP_COMPLETE"){
	    	//console.log('uuid:'+uuid)
	    	console.log('Caller-Unique-ID:'+uuid,bleg_uuid)
	        uuid = undefined ; 
	        is_aleg = false ;
	        bleg_uuid = undefined;
	        is_listen = false;
	        call_Event('hangup','');
	        call_Event('setUUID',uuid,bleg_uuid);
           
	    }

	}