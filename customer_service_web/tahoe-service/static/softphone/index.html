<!doctype html>
<html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="Generator" content="EditPlus®">
        <meta name="Author" content="">
        <meta name="Keywords" content="">
        <meta name="Description" content="">
		<link rel="stylesheet" href="assets/css/ukefu.css">

		<script src="assets/js/jquery-1.10.2.min.js"></script>
		<script src="assets/js/layer/layer.js"></script>
        <script src="assets/js/sip-0.7.7.js"></script>
		<script src="assets/js/SoftPhone.js"></script>

		<script src="assets/js/callcenter.js"></script>
        <title>SoftPhone（软电话工具条）</title>
	</head>
	
	<script>
		console.log(window);
		// window.parent.getNum(12313213213);
	</script>
    
    <body style="margin:0px;">
        <div class="header header-ukefu">
			<div class="ukefu-softphone-head">
				<!-- <a class="logo" href="/"><img src="assets/images/logo.png"></a> -->
				<div class="ukefu-softphone" style="margin-top:10px;">
					<div class="softphone-stream" style="display:none;">
						<audio  id="remoteAudio"></audio >
						<audio  id="localAudio" muted="muted"></audio>
					</div>
					<div class="status disabled">
						<div class="text" id="caller">主叫号码：<span class="number"></span></div>
						<div class="text" id="called">被叫号码：<span class="number"></span></div>
					</div>
					<div class="soft-function disabled" id="softphone-status">
						<div class="soft-function ready" data-toggle="soft-function" data-action="ready">
							<div class="icon"><i class="kfont">&#xe503;</i></div>
							<div class="text">就绪</div>
						</div>
						<div class="soft-function notready" style="display:none;" data-toggle="soft-function" data-action="notready">
							<div class="icon"><i class="kfont">&#xe638;</i></div>
							<div class="text">小休</div>
						</div>
					</div>
					<div class="soft-function disabled" id="softphone-answer" data-toggle="soft-function" data-action="answer">
						<div style="margin-top:2px" class="icon"><i class="kfont">&#xe509;</i></div>
						<div class="text">接听</div>
					</div>
					<div class="soft-function disabled" id="softphone-hungup" data-toggle="soft-function" data-action="hungup">
						<div style="margin-top:2px" class="icon"><i class="kfont">&#xe848;</i></div>
						<div class="text">挂断</div>
					</div>
					
					<div class="soft-function disabled diafunction" id="softphone-makecall">
						<div style="margin-top:2px" class="icon"><i class="kfont" >&#xe840;</i></div>
						<div class="text">拨打</div>
						<div class="dialpad" id="dialpad">
							<div class="dialnum">
								<input class="dialpad-input" type="text" id="dialpad-input" value="">
								<a id = "dialpad-del"><i class="kfont">X</i></a>
							</div>
							<button class="number" id="1">1</button>
							<button class="number" id="2">2</button>
							<button class="number" id="3">3</button>
							<button class="number" id="4">4</button>
							<button class="number" id="5">5</button>
							<button class="number" id="6">6</button>
							<button class="number" id="7">7</button>
							<button class="number" id="8">8</button>
							<button class="number" id="9">9</button>
							<button class="number" id="*">*</button>
							<button class="number" id="0">0</button>
							<button class="number" id="#">#</button>
							<button id="makecall" class="call"><i class="kfont">&#xe840;</i></button>
						</div>
					</div>
					


					<div class="soft-function disabled" id="softphone-status">
						<div class="soft-function hold" data-toggle="soft-function" data-action="hold">
							<div class="icon"><i class="kfont">&#xe637;</i></div>
							<div class="text">保持</div>
						</div>
						<div class="soft-function unhold" data-toggle="soft-function" data-action="unhold" style="display:none;">
							<div class="icon"><i class="kfont">&#xe615;</i></div>
							<div class="text">取回</div>
						</div>
					</div>
					<div class="soft-function disabled" id="softphone-trans" data-toggle="soft-function" data-action="trans">
						<div style="margin-top:1px" class="icon"><i class="kfont">&#xe616;</i></div>
						<div style="margin-top:1px"class="text">转接</div>
					</div>
					<div class="soft-function" id="ukefu_account">
						<div class="soft-function login" data-toggle="soft-function" data-action="login">
							<div class="icon"><i class="kfont">&#xe610;</i></div>
							<div class="text">登录</div>
						</div>
						<div class="soft-function logout" style="display:none;" data-toggle="soft-function" data-action="logout">
							<div class="icon"><i class="kfont">&#xe63b;</i></div>
							<div class="text">登出</div>
						</div>
					</div>
					<div class="soft-function " id="softphone-listen" data-toggle="soft-function" data-action="listen">
						<div style="margin-top:2px" class="icon"><i class="kfont">&#xe840;</i></div>
						<div class="text">监听</div>
					</div>
					<script id="ukefu-login-html" type="text/html">
						<div class="ukefu-login-title">请输入登陆信息</div>
						<div class="ukefu-login-info">
							<div class="ukefu-login-agent">工号：</div>
							<div class="ukefu-login-block">
								<input class="ukefu-login-input" type="text" id="agent" value="">
							</div>						
						</div>
						<div class="ukefu-login-info">
							<div class="ukefu-login-extno">分机号：</div>
							<div class="ukefu-login-block">
								<input class="ukefu-login-input" type="text" id="extno" value="">
							</div>						
						</div>
						<div class="ukefu-login-info">
							<div class="ukefu-login-password">密码：</div>
							<div class="ukefu-login-block">
								<input class="ukefu-login-input" type="password" id="extpass" type="text" value="">
							</div>	
						</div>
						<div class="ukefu-login-info">
							<div class="ukefu-login-extno">域：</div>
							<div class="ukefu-login-block">
								<input class="ukefu-login-input" type="domain" id="domain" type="text" value="taihe.com">
							</div>	
						</div>
					</script>

				    <script id="ukefu-trans-html " type="text/html">
						<div class="ukefu-login-title">转接</div> 
						<div class="ukefu-login-info"> 
							<select style="margin-top:5px;height: 33px;width: 60px" id="select_id"> 
								<option id="ivr" value="volvo">ivr</option> 
								<option id="sabb" value="saab">号码</option> 
							</select> 
							<input style="margin-left:70px;margin-top:-31.8px;height: 29px;width: 100px;display:none" class="ukefu-login-input" type="text" id="trans_num" value=""/> 
							<select id="select_ivr" name="ivr" style="width: 100px;position:absolute;height: 31.5px;margin-left:20px;margin-top:6.5px" panelheight="auto" class="easyui-combobox" data-options="editable:false"></select> 
						</div>
					</script>
					<!-- <script id="ukefu-call-html" type="text/html">
						<div class="dialpad" id="dialpad">
							<div class="dialnum">
								<input class="dialpad-input" type="text" id="dialpad-input" value="">
								<a id = "dialpad-del"><i class="kfont">X</i></a>
							</div>
							<button class="number" id="1">1</button>
							<button class="number" id="2">2</button>
							<button class="number" id="3">3</button>
							<button class="number" id="4">4</button>
							<button class="number" id="5">5</button>
							<button class="number" id="6">6</button>
							<button class="number" id="7">7</button>
							<button class="number" id="8">8</button>
							<button class="number" id="9">9</button>
							<button class="number" id="*">*</button>
							<button class="number" id="0">0</button>
							<button class="number" id="#">#</button>
							<button id="makecall" class="call"><i class="kfont">&#xe840;</i></button>
						</div>
					</script> -->
				</div>
			</div>
		</div>		
    </body>

</html>

</html>