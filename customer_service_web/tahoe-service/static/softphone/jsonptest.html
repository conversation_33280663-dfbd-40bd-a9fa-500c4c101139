<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="assets/layui/css/layui.css"  media="all">
  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
<script src="assets/js/jquery-1.10.2.min.js"></script>
<script src="assets/js/layer/layer.js"></script>
<title>文档的标题</title>
</head>
    <script type="text/javascript">

    //监听弹出框选中坐席展示左上角功能按钮
        function showdiv(){
         
	        if($("#btn").css("display")=='none'){//如果show是隐藏的
	         
	        $("#btn").css("display","block");//show的display属性设置为block（显示）
	         
	        }else{//如果show是显示的
	         
	        $("#btn").css("display","block");//show的display属性设置为none（隐藏）
	         
	        }
         
        }
    </script>
    <script type="text/javascript">
        function listen(){
        	//console.log("http://************:8088/v1/eavesdrop/?query=extension:"+extno+",eavesdrop_uuid:"+select_UUID)
       			$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: "http://*************:8088/v1/eavesdrop/?query=extension:"+extno+",eavesdrop_uuid:"+select_UUID+",domain:"+domain,                                                                             
			        dataType:"jsonp",                              
			        success: function(data){
			        	//console.log(data);
			        		if(data == 'OK'){
			        			layer.msg('成功');
			        		}else{
			        			layer.msg('失败:'+data);	
			        		}
			      		},
        			error:function(e) {
        				console.log("1234"+JSON.stringify(e));
                    layer.msg("注册异常:"+e);
                    }  
            });
       }
        
        function split(){
        	//console.log("http://************:8088/v1/eavesdrop/?query=extension:"+extno+",eavesdrop_uuid:"+select_UUID)
       			$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: "http://*************:8088/v1/kill/?query=leg_uuid:"+select_UUID,                                                                            
			        dataType:"jsonp",                              
			        success: function(data){
			        	//console.log(data);
			        		if(data == 'OK'){
			        			layer.msg('强拆成功');
			        		}else{
			        			layer.msg('失败:'+data);	
			        		}
			      		},
        			error:function(e) {
        				console.log("1234"+JSON.stringify(e));
                    layer.msg("注册异常:"+e);
                    }  
            });
       }
    </script>

	<script type="text/javascript">
	var extno = window.location.href.match(/=(\S*)&/)[1];
	var domain = window.location.href.split("=")[2];
	var selectno ;
	var select_UUID;
	var	state;
	doThread(extno);
	setInterval("doThread(extno)",5000);
	function doThread(extno){
		//console.log("sssssssss"+extno)
		//console.log("aaa"+domain)
		var ws_address = "*************" 
		$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: 'http://'+ws_address+':8088/v1/Agent/?query=domain:'+domain,
			        dataType:"jsonp",                              
			        success: function(json){
			        	if (json != null){
				        	for (var i = 0; i <= json.length- 1 ; i++) {
				        	var time = Math.round((new Date(json[i].Server_time).getTime() - new Date(json[i].Change_time).getTime())/1000);
				        	json[i].status_Time = time;
				        	}
			        	//console.log(json);
			        	}
			        	var lis = [];
		        for(var i = 0; i  <= json.length- 1; i++){
		        	if(selectno == json[i].Agent_number){
		        		
		        		 if(json[i].Agent_state == 2){
			        			json[i].Agent_state= '空闲';
			        		}else if(json[i].Agent_state == 3){
			        			json[i].Agent_state= '通话中';
			        		}else if(json[i].Agent_state == 7){
			        			json[i].Agent_state= '忙';
			        		}
		          	lis.push('<li id = "selected">'+ ( '<br>工号:'+json[i].Agent_number+'<br>分机:'+json[i].Extension_number + '<br>状态:'+json[i].Agent_state + '<span style="display:none">UUID:'+json[i].UUID + '</span><br>时间:'+(Math.floor(json[i].status_Time/60))+':'+(json[i].status_Time-Math.floor(json[i].status_Time/60)*60)+'' ) +'</li>')
		          }else{
		        	  if(json[i].Agent_state == 2){
		        			json[i].Agent_state= '空闲';
		        		}else if(json[i].Agent_state == 3){
		        			json[i].Agent_state= '通话中';
		        		}else if(json[i].Agent_state == 7){
		        			json[i].Agent_state= '忙';
		        		}
		          	lis.push('<li>'+ ( '<br>工号:'+json[i].Agent_number+'<br>分机:'+json[i].Extension_number + '<br>状态:'+json[i].Agent_state + '<span style="display:none">UUID:'+json[i].UUID+ '</span><br>时间:'+(Math.floor(json[i].status_Time/60))+':'+(json[i].status_Time-Math.floor(json[i].status_Time/60)*60)+'' ) +'</li>')
		          }
		        }
		        $('#LAY_demo1').html(lis);
		        //获取所有li的节点
		var list = document.getElementsByTagName("li");

		
		//给每个li绑定事件
		for(var i = 0;i<list.length;i++){
			switch(json[i].Agent_state){
                    		case '空闲':
                    		list[i].style.color="";
                        	list[i].style.background="#D1E9E9";
                        	$(list[i]).prepend('<img src="assets/images/kx.png">');
                        	break;
                    		case '通话中':
                        		list[i].style.color="";
                            	list[i].style.background="#D1E9E9";
                            	$(list[i]).prepend('<img src="assets/images/th.png">');
                            	break;
                        	case '忙':
                    		list[i].style.color="";
                        	list[i].style.background="#D1E9E9";
                        	$(list[i]).prepend('<img src="assets/images/hh.png">');
                        	break;
                        	default:
                        	list[i].style.color="";
                        	list[i].style.backgroundColor="#D1E9E9";

                    	}
       	list[i].setAttribute("index",i);
				list[i].onclick = function(){
				//弹出对应的li节点里面的内容
				onclick:showdiv();
				//将节点的颜色变成红色
			 for(var i=0;i<list.length;i++){
                    if(this.getAttribute("index")==i){
                    	selectno = json[i].Agent_number;
                    	select_UUID = json[i].UUID;
                        list[i].style.color="";
                        list[i].style.backgroundColor="red";
                    }else{
                    	switch(json[i].Agent_state){
                    		case '2':
                    		list[i].style.color="";
                        	list[i].style.backgroundColor="#D1E9E9";
                        	break;
                        	default:
                        	list[i].style.color="";
                        	list[i].style.backgroundColor="#D1E9E9";

                    	}
                        
                    }
                }
				}
		}	

		if(document.getElementById("selected")){
			document.getElementById("selected").style.color="#fff";
			document.getElementById("selected").style.backgroundColor="red";
		}		        
			      		},
        			error:function(e) {
                    layer.msg("就绪异常:"+e);
                    }  
            });
	}
	</script>
	<script src="assets/layui/layui.js" charset="utf-8"></script>
<!-- 注意：如果你直接复制所有代码到本地，上述js路径需要改成你本地的 -->
	
<body>

<table class="layui-hide" id="demo" lay-filter="demo1"></table>
<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="listen" style="width: 70px; height: 40px">监听</a>
</script>

<table class="layui-hide" id="demo"></table>

<div id="btn" style="display: none;margin-top: 10px;margin-left: 5.5px">
  <a id="listen" class="layui-btn layui-btn-danger layui-bg-green layui-btn-xs" onclick="listen()" style="width: 70px; height: 25px">监听</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs"  onclick="split()" style="width: 70px; height: 25px">强拆</a>
 <!--  <a class="layui-btn layui-btn-danger layui-bg-blue layui-btn-xs" lay-event="intercept" style="width: 70px; height: 25px">拦截</a>
  <a class="layui-btn layui-btn-danger layui-bg-cyan layui-btn-xs" lay-event="insert" style="width: 70px; height: 25px">强插</a> -->
</div>
<ul class="flow-default" id="LAY_demo1" style="margin-top: 15px"></ul>
	<style type="text/css">
		.flow-default{ overflow: auto; font-size: 0;}
		.flow-default li{display: inline-block; margin: 0 5px; font-size: 10px; width: 8.8%;  margin-bottom: 10px; height: 150px; line-height: 20px; text-align: center; background-color: #eee;}
		.flow-default img{width: 45%; height: 30%;}
		.site-demo-flow{width: 600px; height: 300px; overflow: auto; text-align: center;}
		.site-demo-flow img{width: 40%; height: 200px; margin: 0 2px 5px 0; border: none;}
	</style>
</body>

</html>