<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="assets/layui/css/layui.css"  media="all">
  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
 <script  src="/callcenter/static/easyui/jquery.min.js" charset="utf-8"></script>
 <script type="text/javascript" src="/callcenter/static/easyui/jquery.easyui.min.js" charset="utf-8"></script>
<script src="assets/js/layer/layer.js"></script>
   <link id="easyuiTheme" rel="stylesheet" type="text/css" href="/callcenter/static/easyui/themes/gray/easyui.css" />
    <link id="easyuiTheme" rel="stylesheet" type="text/css" href="/callcenter/static/easyui/themes/icon.css" />
<title>文档的标题</title>
</head>
    <script type="text/javascript">
    
    
    $(function() {
    	//隐藏预约时间
    	$('#select_demand1')
    			.combobox(
    					{
    						url : '${path }' + '/shiroDomain/findorder1',
    						valueField : 'domainName',
    						textField : 'domainName',
    						onSelect : function() {
    							var xqmc = $('#select_demand1').combobox(
    									'getText');//获取当前选中的
    							var pccode = $('#select_demand1').combobox(
    									'getValue');//获取当前选
    							//alert(pccode);
    					
    						}
    					})
    	});
    
    
  //----------------------------------------easyui formatter 格式化------------------------------------------------------------------------
    function state(value,row){
    if(value==1){
      return "占用";
      }if(value==2){
   		return "空闲";
   	}else if(value==3){
   		return "通话中";
   	}else if(value==4){
   		return "错误";
   	}else if(value==5){
   		return "已登录";
   	}else if(value==6){
   		return "未登录";
   	}else if(value==7){
   		return "小休";
   	}else if(value==8){
   		return "话后";
   	}else if(value==11){
   		return "其他";
   	}   
   } 
    

    //监听弹出框选中坐席展示左上角功能按钮
        function showdiv(){
         
	        if($("#btn").css("display")=='none'){//如果show是隐藏的
	         
	        $("#btn").css("display","block");//show的display属性设置为block（显示）
	         
	        }else{//如果show是显示的
	         
	        $("#btn").css("display","block");//show的display属性设置为none（隐藏）
	         
	        }
         
        }
    
        function fLoadTable(){
            $('#dg').datagrid({
                width: 1015,
                height: 442,
               fitColumns: true,
               remoteSort: false,
               sortable:true,
               idField:'AgentNumber', 
               //控制分页工具栏是否展示
               pagination:false,
               //对应json数据中的每一列
               columns : [ [ { 
                   field : 'AgentName', 
                   title : '坐席名称', 
                   width : '80',
                   sortable:true
                },{ 
                    field : 'AgentNumber', 
                    title : '坐席工号', 
                    width : '80',
                    sortable:true
                 }, { 
                     field : 'GroupName', 
                     title : '队列', 
                     width : '80',
                     sortable:true
                 }, { 
                   field : 'ExtensionNumber', 
                   title : '分机', 
                   width : '80',
                   sortable:true,
                   
               },{ 
                   field : 'AgentState', 
                   title : '坐席状态', 
                   width : '100',
                   sortable:true,
                   formatter:function(value, row, index){ return state(value, row);}
                },{ 
                   field : 'ExtensionRegState', 
                   title : '分机注册状态', 
                   width : '100', 
                   sortable:true,
                   //formatter:function(value, row, index){ return state(value, row);}
              },{ 
                  field : 'ExtensionState', 
                  title : '分机状态', 
                  width : '100', 
                  sortable:true,
                  //formatter:function(value, row, index){ return state(value, row);}
             },{ 
                 field : 'RegeistState', 
                 title : '坐席登录状态', 
                 width : '100', 
                 sortable:true,
                 formatter:function(value, row, index){ return state(value, row);}
            },{ 
                  field : 'OPTime', 
                  title : '登入/登出时间', 
                  width : '120',
                  sortable:true,
               }
            ] ],

         
         rowStyler:function(index,row){
        	if (row.AgentState==1){
          	return 'background-color:#CC66FF';
            }else if (row.AgentState==2 && row.RegeistState==5 && row.ExtensionState=="success"){
     		return 'background-color:#00FF7F;';
     		}else if(row.AgentState==3){
     			return 'background-color:orange';
     		}else if(row.AgentState==4){
     			return 'background-color:DarkRed';
     		}else if(row.RegeistState==6){
     			return 'background-color:#666699;';
     		}else if(row.AgentState==7){
     			return 'background-color:brown';
     		}else if(row.AgentState==8){
     			return 'background-color:Red';
     		}else if(row.AgentState==11){
     			return 'background-color:#00FFFF;';
     		}else if(row.ExtensionState=="ringing" && row.RegeistState==5 && row.AgentState==2){
     			return 'background-color:Gold';
     		}else{
     			return 'background-color:#DCB5FF;';
     		}
     		},
    });
    }

        function listen(){
       			$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: "http://*************:8088/v1/eavesdrop/?query=extension:"+extno+",eavesdrop_uuid:"+select_UUID,                                                                             
			        dataType:"jsonp",                              
			        success: function(data){
			        	console.log(data);
			        		if(data == 'OK'){
			        			layer.msg('成功');
			        		}else{
			        			layer.msg('失败:'+data);	
			        		}
			      		},
        			error:function(e) {
        				console.log("1234"+JSON.stringify(e));
                    layer.msg("注册异常:"+e);
                    }  
            });
       }
    
	var extno = window.location.href.split("=")[1];
	var domain = window.location.href.split("=")[2];
	var selectno ;
	var select_UUID;
	doThread(extno);
	setInterval("doThread(extno)",5000);
	function doThread(extno){
		console.log("是"+domain)
		var ws_address = "*************" 
		$.ajax({                                                                              
        			type: "GET",        //jsonp跨域请求只能是get请求                                                
			        async: false,       //jsonp跨域请求只能是异步请求                                                                 
			        contentType:'application/x-javascript;charset=utf-8',     
			        url: 'http://'+ws_address+':8088/v1/AgentMonitor/?query=domain:'+domain,
			        dataType:"jsonp",                              
			        success: function(json){
			        	console.log(json);
			        	if (json != null){
			             
				        	var qianru = 0;
			                var kongxian = 0;
			                var zhenling = 0;
			                var tonghua = 0;
			                var xiaoxiu = 0;
			                var huahou = 0;
			                
			                //迁入数量
				        	for (var i = 0; i <= json.length- 1 ; i++) {
					        	if(json[i].RegeistState==5){
					        		qianru++;
					        	}
					        	$('#qianru').html('<span>'+ qianru +'</span>');
				        	}
			                
				        	 //振铃数量
				        	for (var i = 0; i <= json.length- 1 ; i++) {
					           if(json[i].ExtensionState=="ringing"){
					        		zhenling++;
					        	}
					   		 $('#zhenling').html('<span>'+ zhenling +'</span>');
				        	}
			        	} 
			        	
			        	    //空闲数量
			        	for (var i = 0; i <= json.length- 1 ; i++) {
				        	 if(json[i].AgentState==2 && json[i].RegeistState==5){
				        		kongxian++;
				        	}
				        	$('#kongxian').html('<span>'+ kongxian +'</span>');
			        	}
			        	
			        	 //通话数量
			        	for (var i = 0; i <= json.length- 1 ; i++) {
				        	if(json[i].AgentState==3){
				        		tonghua++;
				        	}
				   		 $('#tonghua').html('<span>'+ tonghua +'</span>');
			        	}
			        	
			        	 //小休数量
			        	for (var i = 0; i <= json.length- 1 ; i++) {
				        	if(json[i].AgentState==7){
				        		xiaoxiu++;
				        	}
				   		 $('#xiaoxiu').html('<span>'+ xiaoxiu +'</span>');
			        	}
			        	
			        	 //话后数量
			        	for (var i = 0; i <= json.length- 1 ; i++) {
				        	 if(json[i].AgentState==8){
				        		huahou++;
				        	}
				   		 $('#huahou').html('<span>'+ huahou +'</span>');
			        	}
			        	
			       $('#dg').datagrid('loadData',json)
			      		},
        			error:function(e) {
                    layer.msg("就绪异常:"+e);
                    }  
            });
		
		
		
		
		 $('#paidui').html('<span>'+ 11 +'</span>');
		 $('#shijian').html('<span>'+ 16 +'</span>');
	}
	
	
	$(document).ready(function(){
		fLoadTable();
		doThread();
	})
	
	

	</script>
	<script src="assets/layui/layui.js" charset="utf-8"></script>
<!-- 注意：如果你直接复制所有代码到本地，上述js路径需要改成你本地的 -->
	
<body>
<div>
<table  style="border-collapse:separate; border-spacing:0px 8px;">
              <tr>
               <td><span style="color:blue;margin-left:15px">签入:  </span></td> <td id="qianru"></td>
               <td><span style="color:green;margin-left:15px">空闲:  </span></td> <td id="kongxian"></td>
               <td><span style="color:Gold;margin-left:15px">振铃:</span></td> <td id="zhenling"></td>
               <td><span style="color:orange;margin-left:15px">通话:  </span></td> <td id="tonghua"></td>
               <td><span style="color:brown;margin-left:15px">小休:  </span></td> <td id="xiaoxiu"></td>
               <td><span style="color:red;margin-left:15px">话后:  </span></td> <td id="huahou"></td>
               <td><span style="color:#9932CC;margin-left:15px">排队人数: </span></td> <td id="paidui"></td>
               <td><span style="color:#9932CC;margin-left:15px">最大等待时间: </span></td> <td id="shijian"></td>
               <td><span style="color:#9932CC;margin-left:15px">域: </span></td>
                <td><select id="select_demand1" name="domain"
			 	         style="width: 70px;" panelHeight="auto" class="easyui-combobox" 
				         data-options="editable:false"></select></td>     
				</tr>
</table>
</div>
<div>
<table id="dg" class="easyui-datagrid" style="width:auto;fit:true " data-options="rownumbers:true,singleSelect:true,autoRowHeight:false,pagination:true" ></table>
</div>

</body>

</html>