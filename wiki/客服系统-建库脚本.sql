/*
Navicat MySQL Data Transfer

Source Server         : 客服系统测试
Source Server Version : 50718
Source Host           : ************:3306
Source Database       : customer_service

Target Server Type    : MYSQL
Target Server Version : 50718
File Encoding         : 65001

Date: 2020-02-11 13:07:30
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for cs_announcement
-- ----------------------------
DROP TABLE IF EXISTS `cs_announcement`;
CREATE TABLE `cs_announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announce_ment` varchar(1000) DEFAULT NULL COMMENT '公告内容',
  `state` varchar(50) DEFAULT NULL COMMENT '显示状态1显示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_common_question
-- ----------------------------
DROP TABLE IF EXISTS `cs_common_question`;
CREATE TABLE `cs_common_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户ID',
  `question_desc` varchar(1500) DEFAULT NULL COMMENT '常见问题描述',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_date` datetime DEFAULT NULL COMMENT '最后更新日期',
  `order_by` bigint(20) DEFAULT NULL COMMENT '排序',
  `first_sort_code` varchar(10) DEFAULT NULL COMMENT '一级分类编码',
  `question_title` varchar(300) DEFAULT NULL COMMENT '常见问题标题',
  `is_delete` smallint(1) DEFAULT '0' COMMENT '是否删除   0否   1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=177 DEFAULT CHARSET=utf8 COMMENT='常见问题表';

-- ----------------------------
-- Table structure for cs_customer_extension
-- ----------------------------
DROP TABLE IF EXISTS `cs_customer_extension`;
CREATE TABLE `cs_customer_extension` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `job_num` varchar(50) DEFAULT NULL COMMENT '工号',
  `ext_no` varchar(50) DEFAULT NULL COMMENT '分机号',
  `telphone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `org_name` varchar(50) DEFAULT NULL COMMENT '组织名称',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户名',
  `password` varchar(100) DEFAULT NULL COMMENT '密码',
  `flag` int(11) DEFAULT NULL COMMENT '角色：1:400坐席2:房修录入员',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8 COMMENT='客服座机表';

-- ----------------------------
-- Table structure for cs_cust_family
-- ----------------------------
DROP TABLE IF EXISTS `cs_cust_family`;
CREATE TABLE `cs_cust_family` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_id` varchar(100) NOT NULL COMMENT '客户家庭成员唯一标识',
  `household_relation` varchar(20) DEFAULT NULL COMMENT '与户主关系',
  `certificate_name` varchar(20) DEFAULT NULL COMMENT '证件名称',
  `member_name` varchar(20) DEFAULT NULL COMMENT '成员名称',
  `id_number` varchar(30) DEFAULT NULL COMMENT '证件号码',
  `sex` varchar(100) DEFAULT NULL COMMENT '性别',
  `birthday` varchar(100) DEFAULT NULL COMMENT '出生日期',
  `nationality` varchar(100) DEFAULT NULL COMMENT '国籍',
  `mobile` varchar(200) DEFAULT NULL COMMENT '移动电话',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `other_board_member` int(11) DEFAULT NULL COMMENT '是否为其他版块会员',
  `special_customer` int(11) DEFAULT NULL COMMENT '是否为特殊客户',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(11) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `cust_id` varchar(100) DEFAULT NULL COMMENT '业主ID',
  `form_inst_id` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `open_id` varchar(200) DEFAULT NULL COMMENT 'openId',
  `we_chat` varchar(200) DEFAULT NULL COMMENT '微信',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uk_cs_cust_id` (`cust_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4787 DEFAULT CHARSET=utf8 COMMENT='客户家庭成员表';

-- ----------------------------
-- Table structure for cs_cust_house_info
-- ----------------------------
DROP TABLE IF EXISTS `cs_cust_house_info`;
CREATE TABLE `cs_cust_house_info` (
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `house_num` varchar(500) DEFAULT NULL COMMENT '房屋编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='业务房屋';

-- ----------------------------
-- Table structure for cs_cust_info
-- ----------------------------
DROP TABLE IF EXISTS `cs_cust_info`;
CREATE TABLE `cs_cust_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `house_num` varchar(500) DEFAULT NULL COMMENT '房屋编号',
  `certificate_num` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `other_board_member` varchar(10) DEFAULT NULL COMMENT '是否为其他版块会员',
  `special_customer` varchar(10) DEFAULT NULL COMMENT '是否为特殊客户',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(11) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `cus_identity` varchar(50) DEFAULT NULL COMMENT '客户身份',
  `is_vip` varchar(1) DEFAULT NULL COMMENT 'VIP',
  `sug_leader` varchar(1) DEFAULT NULL COMMENT '意见领袖',
  `province` varchar(50) DEFAULT NULL COMMENT '省(直辖市、自治区)',
  `city` varchar(50) DEFAULT NULL COMMENT '市',
  `area` varchar(50) DEFAULT NULL COMMENT '区/县',
  `years_receive` varchar(50) DEFAULT NULL COMMENT '年收入',
  `is_f_zgold_card` varchar(1) DEFAULT NULL COMMENT '福州金卡用户',
  `is_has_more_house` varchar(1) DEFAULT NULL COMMENT '泰禾拥有多套房屋',
  `is_Medical_Care_User` varchar(1) DEFAULT NULL COMMENT '医疗板块客户',
  `is_finance_user` varchar(1) DEFAULT NULL COMMENT '金融板块客户',
  `is_real_estate_user` varchar(1) DEFAULT NULL COMMENT '地产板块客户',
  `is_education_user` varchar(1) DEFAULT NULL COMMENT '教育板块客户',
  `is_cinema_user` varchar(1) DEFAULT NULL COMMENT '影院教育板块客户',
  `has_car` varchar(1) DEFAULT NULL COMMENT '拥有车辆',
  `contact_tel` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `we_chat` varchar(100) DEFAULT NULL COMMENT '微信',
  `open_id` varchar(200) DEFAULT NULL COMMENT 'openId',
  `label_id` varchar(2000) DEFAULT NULL COMMENT '标签',
  `label_name` varchar(2000) DEFAULT NULL COMMENT '标签名称',
  `integral` varchar(20) DEFAULT NULL COMMENT '客户积分',
  `head_portrait` varchar(500) DEFAULT NULL COMMENT '客户头像',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `house_num` (`house_num`) USING BTREE,
  KEY `cust_id` (`cust_id`) USING BTREE,
  KEY `steward_name` (`steward_name`) USING BTREE,
  KEY `telephone` (`telephone`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19703723551195 DEFAULT CHARSET=utf8 COMMENT='客户信息';

-- ----------------------------
-- Table structure for cs_cust_info_teltemp
-- ----------------------------
DROP TABLE IF EXISTS `cs_cust_info_teltemp`;
CREATE TABLE `cs_cust_info_teltemp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `house_num` varchar(500) DEFAULT NULL COMMENT '房屋编号',
  `certificate_num` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `other_board_member` varchar(10) DEFAULT NULL COMMENT '是否为其他版块会员',
  `special_customer` varchar(10) DEFAULT NULL COMMENT '是否为特殊客户',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(11) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `cus_identity` varchar(50) DEFAULT NULL COMMENT '客户身份',
  `is_vip` varchar(1) DEFAULT NULL COMMENT 'VIP',
  `sug_leader` varchar(1) DEFAULT NULL COMMENT '意见领袖',
  `province` varchar(50) DEFAULT NULL COMMENT '省(直辖市、自治区)',
  `city` varchar(50) DEFAULT NULL COMMENT '市',
  `area` varchar(50) DEFAULT NULL COMMENT '区/县',
  `years_receive` varchar(50) DEFAULT NULL COMMENT '年收入',
  `is_f_zgold_card` varchar(1) DEFAULT NULL COMMENT '福州金卡用户',
  `is_has_more_house` varchar(1) DEFAULT NULL COMMENT '泰禾拥有多套房屋',
  `is_Medical_Care_User` varchar(1) DEFAULT NULL COMMENT '医疗板块客户',
  `is_finance_user` varchar(1) DEFAULT NULL COMMENT '金融板块客户',
  `is_real_estate_user` varchar(1) DEFAULT NULL COMMENT '地产板块客户',
  `is_education_user` varchar(1) DEFAULT NULL COMMENT '教育板块客户',
  `is_cinema_user` varchar(1) DEFAULT NULL COMMENT '影院教育板块客户',
  `has_car` varchar(1) DEFAULT NULL COMMENT '拥有车辆',
  `contact_tel` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `we_chat` varchar(100) DEFAULT NULL COMMENT '微信',
  `open_id` varchar(200) DEFAULT NULL COMMENT 'openId',
  `label_id` varchar(2000) DEFAULT NULL COMMENT '标签',
  `label_name` varchar(2000) DEFAULT NULL COMMENT '标签名称',
  `integral` varchar(20) DEFAULT NULL COMMENT '客户积分',
  `head_portrait` varchar(500) DEFAULT NULL COMMENT '客户头像',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `house_num` (`house_num`) USING BTREE,
  KEY `cust_id` (`cust_id`) USING BTREE,
  KEY `steward_name` (`steward_name`) USING BTREE,
  KEY `telephone` (`telephone`) USING BTREE,
  KEY `certificate_num` (`certificate_num`)
) ENGINE=InnoDB AUTO_INCREMENT=19703723410062 DEFAULT CHARSET=utf8 COMMENT='客户信息';

-- ----------------------------
-- Table structure for cs_cust_info_teltemp_copy1
-- ----------------------------
DROP TABLE IF EXISTS `cs_cust_info_teltemp_copy1`;
CREATE TABLE `cs_cust_info_teltemp_copy1` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `house_num` varchar(500) DEFAULT NULL COMMENT '房屋编号',
  `certificate_num` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `other_board_member` varchar(10) DEFAULT NULL COMMENT '是否为其他版块会员',
  `special_customer` varchar(10) DEFAULT NULL COMMENT '是否为特殊客户',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(11) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `cus_identity` varchar(50) DEFAULT NULL COMMENT '客户身份',
  `is_vip` varchar(1) DEFAULT NULL COMMENT 'VIP',
  `sug_leader` varchar(1) DEFAULT NULL COMMENT '意见领袖',
  `province` varchar(50) DEFAULT NULL COMMENT '省(直辖市、自治区)',
  `city` varchar(50) DEFAULT NULL COMMENT '市',
  `area` varchar(50) DEFAULT NULL COMMENT '区/县',
  `years_receive` varchar(50) DEFAULT NULL COMMENT '年收入',
  `is_f_zgold_card` varchar(1) DEFAULT NULL COMMENT '福州金卡用户',
  `is_has_more_house` varchar(1) DEFAULT NULL COMMENT '泰禾拥有多套房屋',
  `is_Medical_Care_User` varchar(1) DEFAULT NULL COMMENT '医疗板块客户',
  `is_finance_user` varchar(1) DEFAULT NULL COMMENT '金融板块客户',
  `is_real_estate_user` varchar(1) DEFAULT NULL COMMENT '地产板块客户',
  `is_education_user` varchar(1) DEFAULT NULL COMMENT '教育板块客户',
  `is_cinema_user` varchar(1) DEFAULT NULL COMMENT '影院教育板块客户',
  `has_car` varchar(1) DEFAULT NULL COMMENT '拥有车辆',
  `contact_tel` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `we_chat` varchar(100) DEFAULT NULL COMMENT '微信',
  `open_id` varchar(200) DEFAULT NULL COMMENT 'openId',
  `label_id` varchar(2000) DEFAULT NULL COMMENT '标签',
  `label_name` varchar(2000) DEFAULT NULL COMMENT '标签名称',
  `integral` varchar(20) DEFAULT NULL COMMENT '客户积分',
  `head_portrait` varchar(500) DEFAULT NULL COMMENT '客户头像',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `house_num` (`house_num`) USING BTREE,
  KEY `cust_id` (`cust_id`) USING BTREE,
  KEY `steward_name` (`steward_name`) USING BTREE,
  KEY `telephone` (`telephone`) USING BTREE,
  KEY `certificate_num` (`certificate_num`)
) ENGINE=InnoDB AUTO_INCREMENT=19703714299641 DEFAULT CHARSET=utf8 COMMENT='客户信息';

-- ----------------------------
-- Table structure for cs_cust_tel
-- ----------------------------
DROP TABLE IF EXISTS `cs_cust_tel`;
CREATE TABLE `cs_cust_tel` (
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户电话';

-- ----------------------------
-- Table structure for cs_dict_item
-- ----------------------------
DROP TABLE IF EXISTS `cs_dict_item`;
CREATE TABLE `cs_dict_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dict_id` bigint(20) DEFAULT NULL COMMENT '字典组id',
  `dict_code` varchar(100) DEFAULT NULL COMMENT '字典组编码',
  `dict_name` varchar(200) DEFAULT NULL COMMENT '字典组名称',
  `item_code` varchar(50) DEFAULT NULL COMMENT '字典项编码',
  `item_value` varchar(200) DEFAULT NULL COMMENT '字典项名称',
  `display_order` bigint(4) DEFAULT NULL COMMENT '显示顺序',
  `status` varchar(10) DEFAULT NULL COMMENT '可用状态',
  `remarks` varchar(500) DEFAULT NULL COMMENT '字典组描述',
  `creation_date` datetime DEFAULT NULL COMMENT '创建日期',
  `last_update_date` datetime DEFAULT NULL COMMENT '最后更新日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1100568270513893380 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_file
-- ----------------------------
DROP TABLE IF EXISTS `cs_file`;
CREATE TABLE `cs_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `form_generate_id` varchar(100) DEFAULT NULL COMMENT '表单id',
  `file_name` varchar(200) DEFAULT NULL COMMENT '文件名称',
  `file_type` varchar(10) DEFAULT NULL COMMENT '文件类型 csv excel等等',
  `server_path` varchar(500) DEFAULT NULL COMMENT '文件服务器端路径',
  `client_path` varchar(200) DEFAULT NULL COMMENT '文件客户端路径',
  `status` varchar(10) DEFAULT NULL COMMENT '是否删除 1是，-1否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1453 DEFAULT CHARSET=utf8 COMMENT='附件表';

-- ----------------------------
-- Table structure for cs_form_estate_info
-- ----------------------------
DROP TABLE IF EXISTS `cs_form_estate_info`;
CREATE TABLE `cs_form_estate_info` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `form_inst_id` bigint(20) DEFAULT NULL COMMENT '表单ID',
  `form_status_code` bigint(20) DEFAULT NULL COMMENT '物业工单状态编码',
  `form_status_name` varchar(50) DEFAULT NULL COMMENT '物业工单状态名称',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='物业工单日志表';

-- ----------------------------
-- Table structure for cs_form_inst
-- ----------------------------
DROP TABLE IF EXISTS `cs_form_inst`;
CREATE TABLE `cs_form_inst` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `form_no` varchar(100) DEFAULT NULL COMMENT '工单编号',
  `dept_code` varchar(10) DEFAULT NULL COMMENT '部门类型编码',
  `dept_name` varchar(50) DEFAULT NULL COMMENT '部门类型编码',
  `regist_code` varchar(50) DEFAULT NULL COMMENT '登记类型编码',
  `regist_name` varchar(100) DEFAULT NULL COMMENT '登记类型编码',
  `process_code` varchar(50) DEFAULT NULL COMMENT '流程类别编码',
  `process_name` varchar(100) DEFAULT NULL COMMENT '流程类别编码',
  `first_sort_code` varchar(50) DEFAULT NULL COMMENT '一级分类编码',
  `first_sort_name` varchar(100) DEFAULT NULL COMMENT '一级分类名称',
  `sec_sort_code` varchar(10) DEFAULT NULL COMMENT '二级分类编码',
  `sec_sort_name` varchar(50) DEFAULT NULL COMMENT '二级分类名称',
  `third_sort_code` varchar(50) DEFAULT NULL COMMENT '三级分类编码',
  `third_sort_name` varchar(100) DEFAULT NULL COMMENT '三级分类名称',
  `fourth_sort_code` varchar(50) DEFAULT NULL COMMENT '四级分类编码',
  `fourth_sort_name` varchar(100) DEFAULT NULL COMMENT '四级分类名称',
  `problem_level_code` varchar(50) DEFAULT NULL COMMENT '问题严重级别编码',
  `problem_level_name` varchar(100) DEFAULT NULL COMMENT '问题严重级别名称',
  `problem_position_code` varchar(50) DEFAULT NULL COMMENT '部位编码',
  `problem_position_name` varchar(100) DEFAULT NULL COMMENT '部位名称',
  `revision_classification_code` varchar(50) DEFAULT NULL COMMENT '报修分类编码',
  `revision_classification_name` varchar(100) DEFAULT NULL COMMENT '报修分类名称',
  `decoration_stage_code` varchar(50) DEFAULT NULL COMMENT '装修阶段编码',
  `decoration_stage_name` varchar(100) DEFAULT NULL COMMENT '装修阶段名称',
  `maintenance_period_code` varchar(50) DEFAULT NULL COMMENT '维保期编码',
  `maintenance_period_name` varchar(100) DEFAULT NULL COMMENT '维保期名称',
  `inter_res_department_code` varchar(50) DEFAULT NULL COMMENT '内部责任部门编码',
  `inter_res_department_name` varchar(100) DEFAULT NULL COMMENT '内部责任部门名称',
  `owner_name` varchar(50) DEFAULT NULL COMMENT '业主名称',
  `owner_type` int(11) DEFAULT NULL COMMENT '业主类型编码1：个人2：单位',
  `orther_member` int(11) DEFAULT NULL COMMENT '其他版块会员-1：否，1：是',
  `special_user` int(11) DEFAULT NULL COMMENT '特殊客户-1：否，1：是',
  `mobile` varchar(200) DEFAULT NULL COMMENT '移动电话',
  `nationality` varchar(100) DEFAULT NULL COMMENT '国籍',
  `id_code` varchar(50) DEFAULT NULL COMMENT '证件类型编码',
  `id_name` varchar(100) DEFAULT NULL COMMENT '证件类型名称',
  `id_no` varchar(100) DEFAULT NULL COMMENT '证件号码',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `work_unit` varchar(500) DEFAULT NULL COMMENT '工作单位',
  `occupation` varchar(100) DEFAULT NULL COMMENT '职业',
  `hobby` varchar(200) DEFAULT NULL COMMENT '爱好',
  `fax_phone` varchar(100) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `e_mail` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `postal_code` varchar(50) DEFAULT NULL COMMENT '邮政编码',
  `housekeeper_name` varchar(50) DEFAULT NULL COMMENT '管家姓名',
  `housekeeper_tel` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `remark` varchar(2000) DEFAULT NULL COMMENT '备注',
  `common_problem_code` varchar(50) DEFAULT NULL COMMENT '常见问题编码',
  `common_problem_name` varchar(100) DEFAULT NULL COMMENT '常见问题名称',
  `customer_demand` varchar(2000) DEFAULT NULL COMMENT '客户需求',
  `house_no` varchar(100) DEFAULT NULL COMMENT '房屋编号',
  `house_name` varchar(200) DEFAULT NULL COMMENT '房屋名称',
  `region_code` varchar(50) DEFAULT NULL COMMENT '区域编码',
  `region` varchar(200) DEFAULT NULL COMMENT '区域',
  `city_code` varchar(50) DEFAULT NULL COMMENT '城市编码',
  `city` varchar(200) DEFAULT NULL COMMENT '城市',
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `project` varchar(200) DEFAULT NULL COMMENT '项目',
  `building_no` varchar(200) DEFAULT NULL COMMENT '楼栋',
  `building_unit` varchar(200) DEFAULT NULL COMMENT '单元号',
  `room_no` varchar(200) DEFAULT NULL COMMENT '房间号',
  `use_property_code` varchar(50) DEFAULT NULL COMMENT '使用性质编码',
  `use_property_name` varchar(100) DEFAULT NULL COMMENT '使用性质名称',
  `contract_delivery_time` date DEFAULT NULL COMMENT '合同交房时间',
  `signing_time` date DEFAULT NULL COMMENT '签约时间',
  `estimated_release_time` date DEFAULT NULL COMMENT '预计脱保时间',
  `focus_delivery_time_from` date DEFAULT NULL COMMENT '集中交房时间from',
  `focus_delivery_time_to` date DEFAULT NULL COMMENT '集中交房时间to',
  `delivery_state` varchar(10) DEFAULT NULL COMMENT '交付状态1:已交付 -1未交付',
  `hardcover_state` varchar(10) DEFAULT NULL COMMENT '是否精装1:是 -1：否',
  `actual_deliver_time` date DEFAULT NULL COMMENT '实际交房时间',
  `check_in_time` date DEFAULT NULL COMMENT '入住时间',
  `assign_id` varchar(50) DEFAULT NULL COMMENT '分派人',
  `assign_name` varchar(50) DEFAULT NULL COMMENT '分派人',
  `assign_date` datetime DEFAULT NULL COMMENT '分派时间',
  `creation_date` datetime DEFAULT NULL COMMENT '创建日期',
  `cur_assignee_id` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `cur_assignee_name` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `upgrade_level` bigint(20) DEFAULT NULL COMMENT '升级级别',
  `process_state_code` varchar(100) DEFAULT NULL COMMENT '业务步骤',
  `process_state_name` varchar(100) DEFAULT NULL COMMENT '业务步骤',
  `last_update_date` datetime DEFAULT NULL COMMENT '最后更新时间',
  `updrade_reason` varchar(1000) DEFAULT NULL COMMENT '升级原因',
  `owner_id` varchar(100) DEFAULT NULL COMMENT '业主ID',
  `satisfaction_code` varchar(10) DEFAULT NULL COMMENT '满意度名称',
  `satisfaction_name` varchar(50) DEFAULT NULL,
  `upgrade_flag` bigint(10) DEFAULT NULL COMMENT '升级标识1：是',
  `rework_flag` bigint(10) DEFAULT NULL COMMENT '返工标识1:是',
  `reject_flag` bigint(10) DEFAULT NULL COMMENT '退回标识1:是',
  `gender_code` varchar(10) DEFAULT NULL COMMENT '性别编码',
  `gender_name` varchar(100) DEFAULT NULL COMMENT '性别名称',
  `lock_user_id` varchar(50) DEFAULT NULL COMMENT '锁用户',
  `lock_time` datetime DEFAULT NULL COMMENT '锁时间',
  `house_info_id` varchar(100) DEFAULT NULL COMMENT '房屋ID',
  `create_user_id` varchar(100) DEFAULT NULL,
  `create_user_name` varchar(100) DEFAULT NULL,
  `handle_record` varchar(2000) DEFAULT NULL COMMENT '处理记录',
  `form_generate_id` varchar(100) DEFAULT NULL,
  `report_channel_code` varchar(100) DEFAULT NULL COMMENT '报事渠道编码',
  `report_channel_name` varchar(200) DEFAULT NULL COMMENT '报事渠道名称',
  `accept_channel_code` varchar(100) DEFAULT NULL COMMENT '受理渠道编码',
  `accept_channel_name` varchar(200) DEFAULT NULL COMMENT '受理渠道名称',
  `submit_date` datetime DEFAULT NULL,
  `main_res_unit` varchar(200) DEFAULT NULL COMMENT '主责任单位',
  `repair_unit` varchar(200) DEFAULT NULL COMMENT '维修单位',
  `third_party_flag` varchar(10) DEFAULT NULL COMMENT '是否启用第三方 -1否 1是',
  `third_party_name` varchar(200) DEFAULT NULL COMMENT '第三方名称',
  `third_party_reason` varchar(1000) DEFAULT NULL COMMENT '第三方原因',
  `change_record` varchar(2000) DEFAULT NULL,
  `schedual_upgrade_level` bigint(20) DEFAULT NULL,
  `complaint_headlines` varchar(255) DEFAULT NULL COMMENT '投诉标题',
  `wy_form_no` varchar(100) DEFAULT NULL COMMENT '物业工单编号',
  `source` varchar(100) DEFAULT NULL COMMENT '工单来源（仅用于外部系统）',
  `public_area` varchar(200) DEFAULT NULL COMMENT '是否公区',
  `contact_tel` varchar(32) DEFAULT NULL COMMENT '联系电话',
  `is_owner` varchar(10) DEFAULT NULL COMMENT '是否为业主报事1：是',
  `open_id` varchar(200) DEFAULT NULL COMMENT 'openId',
  `qd_form_no` varchar(100) DEFAULT NULL COMMENT '千丁工单编号',
  `qd_create_user_id` varchar(100) DEFAULT NULL COMMENT '千丁报事人id',
  `qd_problem_type_id` varchar(100) DEFAULT NULL COMMENT '千丁原因大类id',
  `qd_region_id` varchar(100) DEFAULT NULL COMMENT '千丁项目id',
  `qd_region_name` varchar(100) DEFAULT NULL COMMENT '千丁项目名称',
  `qd_back_mes` varchar(255) DEFAULT NULL COMMENT '回调千丁返回信息',
  `form_user_name` varchar(50) DEFAULT NULL COMMENT '报事人',
  `form_user_tel` varchar(50) DEFAULT NULL COMMENT '报事人电话',
  `is_grab` varchar(10) DEFAULT NULL COMMENT '是否抢单1是0否',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_cs_form_inst_no` (`form_no`) USING BTREE,
  KEY `uk_cs_cust_id` (`owner_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21937 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_grab
-- ----------------------------
DROP TABLE IF EXISTS `cs_grab`;
CREATE TABLE `cs_grab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `first_sort_code` varchar(50) DEFAULT NULL COMMENT '一级分类编码',
  `sec_sort_code` varchar(10) DEFAULT NULL COMMENT '二级分类编码',
  `third_sort_code` varchar(50) DEFAULT NULL COMMENT '三级分类编码',
  `fourth_sort_code` varchar(50) DEFAULT NULL COMMENT '四级分类编码',
  PRIMARY KEY (`id`),
  KEY `project_code` (`project_code`) USING BTREE,
  KEY `first_sort_code` (`first_sort_code`) USING BTREE,
  KEY `sec_sort_code` (`sec_sort_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42424 DEFAULT CHARSET=utf8 COMMENT='抢单配置表';

-- ----------------------------
-- Table structure for cs_house_info
-- ----------------------------
DROP TABLE IF EXISTS `cs_house_info`;
CREATE TABLE `cs_house_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` varchar(100) NOT NULL COMMENT '项目ID',
  `house_id` varchar(100) NOT NULL COMMENT '房屋ID',
  `house_num` varchar(100) DEFAULT NULL COMMENT '房屋编号',
  `house_name` varchar(100) DEFAULT NULL COMMENT '房屋名称',
  `area` varchar(50) DEFAULT NULL COMMENT '区域',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `unit` varchar(50) DEFAULT NULL COMMENT '单元号',
  `building` varchar(50) DEFAULT NULL COMMENT '楼栋',
  `room_num` varchar(50) DEFAULT NULL COMMENT '房间号',
  `use_property` varchar(50) DEFAULT NULL COMMENT '使用性质',
  `obtain_time` varchar(50) DEFAULT NULL COMMENT '取证时间',
  `delivery_date` varchar(50) DEFAULT NULL COMMENT '合同交房时间',
  `sign_date` varchar(50) DEFAULT NULL COMMENT '签约时间',
  `focus_start_date` datetime DEFAULT NULL COMMENT '集中交房时间从',
  `focus_end_date` datetime DEFAULT NULL COMMENT '集中交房时间到',
  `delivery_status` int(11) DEFAULT NULL COMMENT '交付状态',
  `fitment` varchar(11) DEFAULT NULL COMMENT '精装情况',
  `actual_delivery_date` datetime DEFAULT NULL COMMENT '实际交房时间',
  `stay_time` datetime DEFAULT NULL COMMENT '入住时间',
  `off_aid_date` datetime DEFAULT NULL COMMENT '预计脱保时间',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(11) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `house_num` (`house_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16831594 DEFAULT CHARSET=utf8 COMMENT='房屋信息表';

-- ----------------------------
-- Table structure for cs_house_info_copy1
-- ----------------------------
DROP TABLE IF EXISTS `cs_house_info_copy1`;
CREATE TABLE `cs_house_info_copy1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` varchar(100) NOT NULL COMMENT '项目ID',
  `house_id` varchar(100) NOT NULL COMMENT '房屋ID',
  `house_num` varchar(100) DEFAULT NULL COMMENT '房屋编号',
  `house_name` varchar(100) DEFAULT NULL COMMENT '房屋名称',
  `area` varchar(50) DEFAULT NULL COMMENT '区域',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `unit` varchar(50) DEFAULT NULL COMMENT '单元号',
  `building` varchar(50) DEFAULT NULL COMMENT '楼栋',
  `room_num` varchar(50) DEFAULT NULL COMMENT '房间号',
  `use_property` varchar(50) DEFAULT NULL COMMENT '使用性质',
  `obtain_time` varchar(50) DEFAULT NULL COMMENT '取证时间',
  `delivery_date` varchar(50) DEFAULT NULL COMMENT '合同交房时间',
  `sign_date` varchar(50) DEFAULT NULL COMMENT '签约时间',
  `focus_start_date` datetime DEFAULT NULL COMMENT '集中交房时间从',
  `focus_end_date` datetime DEFAULT NULL COMMENT '集中交房时间到',
  `delivery_status` int(11) DEFAULT NULL COMMENT '交付状态',
  `fitment` varchar(11) DEFAULT NULL COMMENT '精装情况',
  `actual_delivery_date` datetime DEFAULT NULL COMMENT '实际交房时间',
  `stay_time` datetime DEFAULT NULL COMMENT '入住时间',
  `off_aid_date` datetime DEFAULT NULL COMMENT '预计脱保时间',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(11) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `house_num` (`house_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13558769 DEFAULT CHARSET=utf8 COMMENT='房屋信息表';

-- ----------------------------
-- Table structure for cs_label
-- ----------------------------
DROP TABLE IF EXISTS `cs_label`;
CREATE TABLE `cs_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `father_id` varchar(100) DEFAULT NULL COMMENT '父id',
  `father_name` varchar(200) DEFAULT NULL COMMENT '父标签名称',
  `item_name` varchar(200) DEFAULT NULL COMMENT '标签名称',
  `state` varchar(50) DEFAULT NULL COMMENT '状态1启用0不启用',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_date` datetime DEFAULT NULL COMMENT '最后更新时间',
  `hierarchy` int(50) DEFAULT NULL COMMENT '所属层级',
  `collection` varchar(100) DEFAULT NULL COMMENT '收集渠道',
  `score` varchar(100) DEFAULT NULL COMMENT '分值',
  `definition` varchar(255) DEFAULT NULL COMMENT '定义',
  `type` varchar(50) DEFAULT NULL COMMENT '客户活动标志cust客户activity',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8 COMMENT='标签';

-- ----------------------------
-- Table structure for cs_menu_cfg
-- ----------------------------
DROP TABLE IF EXISTS `cs_menu_cfg`;
CREATE TABLE `cs_menu_cfg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `menu_code` varchar(50) DEFAULT NULL COMMENT '菜单编码',
  `menu_url` varchar(100) DEFAULT NULL COMMENT '菜单url',
  `creation_date` datetime DEFAULT NULL COMMENT '创建日期',
  `order_num` int(11) DEFAULT NULL COMMENT '排序',
  `name` varchar(50) DEFAULT NULL COMMENT '菜单名称',
  `ico_code` varchar(50) DEFAULT NULL COMMENT '图标编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8 COMMENT='菜单URL配置表';

-- ----------------------------
-- Table structure for cs_message
-- ----------------------------
DROP TABLE IF EXISTS `cs_message`;
CREATE TABLE `cs_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `form_id` varchar(20) DEFAULT NULL,
  `message` varchar(1000) DEFAULT NULL COMMENT '消息',
  `state` varchar(5) DEFAULT NULL COMMENT '未读状态0已读1未读',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update` datetime DEFAULT NULL COMMENT '更新时间',
  `login_name` varchar(100) DEFAULT NULL COMMENT '消息接收人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=690 DEFAULT CHARSET=utf8 COMMENT='APP消息';

-- ----------------------------
-- Table structure for cs_process_workitem
-- ----------------------------
DROP TABLE IF EXISTS `cs_process_workitem`;
CREATE TABLE `cs_process_workitem` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `form_inst_id` bigint(20) DEFAULT NULL COMMENT '表单id',
  `task_status` bigint(20) DEFAULT NULL COMMENT '审批状态(20:待办,30:已办)',
  `assign_id` varchar(50) DEFAULT NULL COMMENT '处理人',
  `task_start_time` datetime DEFAULT NULL COMMENT '审批开始时间',
  `task_end_time` datetime DEFAULT NULL COMMENT '审批结束时间',
  `create_by_user_id` varchar(50) DEFAULT NULL COMMENT '创建人',
  `COMMENT` varchar(1000) DEFAULT NULL COMMENT '审批意见',
  `creation_date` datetime DEFAULT NULL COMMENT '创建日期',
  `last_update_date` datetime DEFAULT NULL COMMENT '最后更新时间',
  `assign_name` varchar(50) DEFAULT NULL COMMENT '处理人名称',
  `process_state_code` varchar(50) DEFAULT NULL COMMENT '业务步骤编码',
  `process_state_name` varchar(100) DEFAULT NULL COMMENT '业务步骤姓名',
  `assign_mobile` varchar(50) DEFAULT NULL COMMENT '分派人电话',
  `th_platform` varchar(20) DEFAULT NULL COMMENT 'app 处理记录',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `form_inst_id` (`form_inst_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60602 DEFAULT CHARSET=utf8 COMMENT='待办表';

-- ----------------------------
-- Table structure for cs_project_idinfo
-- ----------------------------
DROP TABLE IF EXISTS `cs_project_idinfo`;
CREATE TABLE `cs_project_idinfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `project_source` int(11) DEFAULT NULL COMMENT '项目来源1:物业2:明源',
  `project_id` varchar(50) DEFAULT NULL COMMENT '项目id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `projectidinfocode` (`project_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=359 DEFAULT CHARSET=utf8 COMMENT='初始化项目id对应关系表';

-- ----------------------------
-- Table structure for cs_project_info
-- ----------------------------
DROP TABLE IF EXISTS `cs_project_info`;
CREATE TABLE `cs_project_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `region_code` varchar(50) DEFAULT NULL COMMENT '区域编码',
  `region` varchar(500) DEFAULT NULL COMMENT '区域',
  `city_company_code` varchar(50) DEFAULT NULL COMMENT '城市公司编码',
  `city_company` varchar(500) DEFAULT NULL COMMENT '城市公司',
  `city_code` varchar(50) DEFAULT NULL COMMENT '城市编码',
  `city` varchar(500) DEFAULT NULL COMMENT '城市',
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `project_source` int(11) DEFAULT NULL COMMENT '项目来源1:物业2:明源',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_code` (`project_code`) USING BTREE,
  KEY `project_source` (`project_source`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=158 DEFAULT CHARSET=utf8 COMMENT='初始化项目信息表';

-- ----------------------------
-- Table structure for cs_project_qdtowy_idinfo
-- ----------------------------
DROP TABLE IF EXISTS `cs_project_qdtowy_idinfo`;
CREATE TABLE `cs_project_qdtowy_idinfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `project_qd_id` varchar(50) DEFAULT NULL COMMENT '千丁项目id',
  `project` varchar(50) DEFAULT NULL COMMENT '项目名称',
  `project_wy_id` varchar(50) DEFAULT NULL COMMENT '物业项目id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `projectidinfocode` (`project_qd_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=385 DEFAULT CHARSET=utf8 COMMENT='初始化项目id对应关系表';

-- ----------------------------
-- Table structure for cs_sendtoall
-- ----------------------------
DROP TABLE IF EXISTS `cs_sendtoall`;
CREATE TABLE `cs_sendtoall` (
  `id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'id',
  `telephone` varchar(100) DEFAULT NULL COMMENT '表单id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_send_sms_log
-- ----------------------------
DROP TABLE IF EXISTS `cs_send_sms_log`;
CREATE TABLE `cs_send_sms_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户姓名',
  `send_mobiles` varchar(15000) DEFAULT NULL COMMENT '手机号',
  `content_desc` varchar(2000) DEFAULT NULL COMMENT '发送内容',
  `send_result` varchar(100) DEFAULT NULL COMMENT '返回信息',
  `create_date` datetime DEFAULT NULL COMMENT '发送时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1091879499997573127 DEFAULT CHARSET=utf8 COMMENT='短信发送日志表';

-- ----------------------------
-- Table structure for cs_sms_tark
-- ----------------------------
DROP TABLE IF EXISTS `cs_sms_tark`;
CREATE TABLE `cs_sms_tark` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tel` varchar(4000) DEFAULT NULL COMMENT '电话',
  `msg` varchar(500) DEFAULT NULL COMMENT '消息',
  `type` varchar(10) DEFAULT NULL COMMENT '类型',
  `status` varchar(10) DEFAULT NULL COMMENT '状态',
  `runtime` datetime DEFAULT NULL COMMENT '执行时间',
  `err_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_user_name` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_real_name` varchar(100) DEFAULT NULL COMMENT '创建人',
  `creation_date` datetime DEFAULT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8 COMMENT='短信推送任务';

-- ----------------------------
-- Table structure for cs_sms_template
-- ----------------------------
DROP TABLE IF EXISTS `cs_sms_template`;
CREATE TABLE `cs_sms_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `template_code` varchar(100) DEFAULT NULL COMMENT '模板编码',
  `template_name` varchar(100) DEFAULT NULL COMMENT '模板名称',
  `template_content` varchar(1000) DEFAULT NULL COMMENT '模板内容',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_supplier_info
-- ----------------------------
DROP TABLE IF EXISTS `cs_supplier_info`;
CREATE TABLE `cs_supplier_info` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `supplier_name` varchar(200) DEFAULT NULL COMMENT '供应商名称',
  `supplier_contact_name` varchar(200) DEFAULT NULL COMMENT '供应商联系人',
  `supplier_contact_mobile` varchar(50) DEFAULT NULL COMMENT '供应商联系人电话',
  `supplier_classification` varchar(200) DEFAULT NULL COMMENT '供应商分类',
  `supplier_areas` varchar(200) DEFAULT NULL COMMENT '区域（隶属多个区域用","分隔）',
  `creation_date` datetime DEFAULT NULL COMMENT '创建日期',
  `last_update_date` datetime DEFAULT NULL COMMENT '最后更新日期',
  `supplier_areas_name` varchar(200) DEFAULT NULL COMMENT '区域名称（隶属多个区域用","分隔）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供方数据表';

-- ----------------------------
-- Table structure for cs_sync_cust
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_cust`;
CREATE TABLE `cs_sync_cust` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `certificate_num` varchar(60) NOT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `is_delete` varchar(10) DEFAULT NULL COMMENT '物业删除',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(1) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `abnormal` int(1) DEFAULT NULL COMMENT '异常数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `certificate_num` (`certificate_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4128867 DEFAULT CHARSET=utf8 COMMENT='客户信息';

-- ----------------------------
-- Table structure for cs_sync_cust2house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_cust2house`;
CREATE TABLE `cs_sync_cust2house` (
  `house_code` varchar(255) DEFAULT NULL,
  `certificate_num` varchar(255) DEFAULT NULL,
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(1) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  UNIQUE KEY `hc_code` (`house_code`,`certificate_num`) USING BTREE,
  KEY `house_code` (`house_code`),
  KEY `certificate_num` (`certificate_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sync_cust_abnormal
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_cust_abnormal`;
CREATE TABLE `cs_sync_cust_abnormal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `certificate_num` varchar(60) NOT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `is_delete` varchar(10) DEFAULT NULL COMMENT '物业删除',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(1) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `abnormal` int(1) DEFAULT NULL COMMENT '异常数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `certificate_num` (`certificate_num`)
) ENGINE=InnoDB AUTO_INCREMENT=4128867 DEFAULT CHARSET=utf8 COMMENT='客户信息';

-- ----------------------------
-- Table structure for cs_sync_cust_center
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_cust_center`;
CREATE TABLE `cs_sync_cust_center` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `certificate_num` varchar(60) NOT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `is_delete` varchar(10) DEFAULT NULL COMMENT '物业删除',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(1) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `certificate_num` (`certificate_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4128870 DEFAULT CHARSET=utf8 COMMENT='客户信息';

-- ----------------------------
-- Table structure for cs_sync_house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_house`;
CREATE TABLE `cs_sync_house` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` varchar(100) NOT NULL COMMENT '项目ID',
  `house_id` varchar(100) NOT NULL COMMENT '房屋ID',
  `house_num` varchar(100) NOT NULL COMMENT '房屋编号',
  `house_name` varchar(100) DEFAULT NULL COMMENT '房屋名称',
  `area` varchar(50) DEFAULT NULL COMMENT '区域',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `unit` varchar(50) DEFAULT NULL COMMENT '单元号',
  `building` varchar(50) DEFAULT NULL COMMENT '楼栋',
  `room_num` varchar(50) DEFAULT NULL COMMENT '房间号',
  `use_property` varchar(50) DEFAULT NULL COMMENT '使用性质',
  `obtain_time` varchar(50) DEFAULT NULL COMMENT '取证时间',
  `delivery_date` varchar(50) DEFAULT NULL COMMENT '合同交房时间',
  `sign_date` varchar(50) DEFAULT NULL COMMENT '签约时间',
  `focus_start_date` datetime DEFAULT NULL COMMENT '集中交房时间从',
  `focus_end_date` datetime DEFAULT NULL COMMENT '集中交房时间到',
  `delivery_status` int(11) DEFAULT NULL COMMENT '交付状态',
  `fitment` varchar(11) DEFAULT NULL COMMENT '精装情况',
  `actual_delivery_date` datetime DEFAULT NULL COMMENT '实际交房时间',
  `stay_time` datetime DEFAULT NULL COMMENT '入住时间',
  `off_aid_date` datetime DEFAULT NULL COMMENT '预计脱保时间',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `my_status` varchar(10) DEFAULT NULL COMMENT '明源状态',
  `is_delete` varchar(10) DEFAULT NULL COMMENT '物业删除',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `source` int(1) DEFAULT NULL COMMENT '来源：1.物业2.明源',
  `abnormal` int(1) DEFAULT NULL COMMENT '异常数据',
  `region_code` varchar(50) DEFAULT NULL COMMENT '区域编码',
  `city_company_code` varchar(50) DEFAULT NULL COMMENT '城市公司编码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `house_num` (`house_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16890652 DEFAULT CHARSET=utf8 COMMENT='房屋信息表';

-- ----------------------------
-- Table structure for cs_sync_my_cust
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_my_cust`;
CREATE TABLE `cs_sync_my_cust` (
  `cust_id` varchar(255) DEFAULT NULL,
  `cust_name` varchar(255) DEFAULT NULL,
  `certificate_name` varchar(255) DEFAULT NULL,
  `certificate_num` varchar(255) DEFAULT NULL,
  `fixed_telephone` varchar(255) DEFAULT NULL,
  `telephone` varchar(255) DEFAULT NULL,
  `fax_telephone` varchar(255) DEFAULT NULL,
  `contact_address` varchar(255) DEFAULT NULL,
  `PostCode` varchar(255) DEFAULT NULL,
  `Email` varchar(255) DEFAULT NULL,
  `belong` varchar(255) DEFAULT NULL,
  `sex` varchar(255) DEFAULT NULL,
  `national` varchar(255) DEFAULT NULL,
  `birthday` varchar(255) DEFAULT NULL,
  `work_unit` varchar(255) DEFAULT NULL,
  `profession` varchar(255) DEFAULT NULL,
  `update_date` varchar(255) DEFAULT NULL,
  `projid` varchar(255) DEFAULT NULL,
  `is_sync` int(1) unsigned zerofill DEFAULT '0',
  KEY `certificate_num` (`certificate_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sync_my_cust2house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_my_cust2house`;
CREATE TABLE `cs_sync_my_cust2house` (
  `house_id` varchar(255) DEFAULT NULL,
  `room_code` varchar(255) DEFAULT NULL,
  `cust_id` varchar(255) DEFAULT NULL,
  `certificate_num` varchar(255) DEFAULT NULL,
  `cq_date` varchar(255) DEFAULT NULL,
  UNIQUE KEY `house_id` (`house_id`,`cust_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sync_my_house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_my_house`;
CREATE TABLE `cs_sync_my_house` (
  `project_id` varchar(255) DEFAULT NULL,
  `house_code` varchar(255) DEFAULT NULL,
  `house_id` varchar(255) NOT NULL,
  `house_num` varchar(255) DEFAULT NULL,
  `room_code` varchar(255) DEFAULT NULL,
  `area` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `project` varchar(255) DEFAULT NULL,
  `building` varchar(255) DEFAULT NULL,
  `Unit` varchar(255) DEFAULT NULL,
  `room_num` varchar(255) DEFAULT NULL,
  `use_property` varchar(255) DEFAULT NULL,
  `fitment` varchar(255) DEFAULT NULL,
  `update_date` varchar(255) DEFAULT NULL,
  `projid` varchar(255) DEFAULT NULL,
  `obtain_time` varchar(255) DEFAULT NULL,
  `Status` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`house_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sync_wy_cust
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_wy_cust`;
CREATE TABLE `cs_sync_wy_cust` (
  `CommID` varchar(255) DEFAULT NULL,
  `CustID` varchar(255) NOT NULL,
  `CustName` varchar(255) DEFAULT NULL,
  `PaperCode` varchar(255) DEFAULT NULL,
  `PaperName` varchar(255) DEFAULT NULL,
  `FaxTel` varchar(255) DEFAULT NULL,
  `MobilePhone` varchar(255) DEFAULT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `PostCode` varchar(255) DEFAULT NULL,
  `EMail` varchar(255) DEFAULT NULL,
  `IsUnit` varchar(255) DEFAULT NULL,
  `Sex` varchar(255) DEFAULT NULL,
  `Nationality` varchar(255) DEFAULT NULL,
  `Birthday` varchar(255) DEFAULT NULL,
  `Job` varchar(255) DEFAULT NULL,
  `Hobbies` varchar(255) DEFAULT NULL,
  `custupdatetime` varchar(255) DEFAULT NULL,
  `IsDelete` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CustID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sync_wy_cust2house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_wy_cust2house`;
CREATE TABLE `cs_sync_wy_cust2house` (
  `LiveID` varchar(255) NOT NULL,
  `RoomID` varchar(255) DEFAULT NULL,
  `RoomSign` varchar(255) DEFAULT NULL,
  `CustID` varchar(255) DEFAULT NULL,
  `CustName` varchar(255) DEFAULT NULL,
  `PaperCode` varchar(255) DEFAULT NULL,
  `IsActive` varchar(255) DEFAULT NULL,
  `ChargeTime` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LiveID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sync_wy_house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sync_wy_house`;
CREATE TABLE `cs_sync_wy_house` (
  `OrganName` varchar(255) DEFAULT NULL,
  `City` varchar(255) DEFAULT NULL,
  `CommID` varchar(255) DEFAULT NULL,
  `CommName` varchar(255) DEFAULT NULL,
  `RoomID` varchar(255) NOT NULL,
  `RoomSign` varchar(255) DEFAULT NULL,
  `RoomName` varchar(255) DEFAULT NULL,
  `UnitSNum` varchar(255) DEFAULT NULL,
  `BuildName` varchar(255) DEFAULT NULL,
  `BuildSNum` varchar(255) DEFAULT NULL,
  `PropertyUses` varchar(255) DEFAULT NULL,
  `ContSubDate` varchar(255) DEFAULT NULL,
  `getHouseStartDate` varchar(255) DEFAULT NULL,
  `getHouseEndDate` varchar(255) DEFAULT NULL,
  `PayState` varchar(255) DEFAULT NULL,
  `BuildsRenovation` varchar(255) DEFAULT NULL,
  `ActualSubDate` varchar(255) DEFAULT NULL,
  `StayTime` varchar(255) DEFAULT NULL,
  `UpdateTime` varchar(255) DEFAULT NULL,
  `IsDelete` varchar(255) DEFAULT NULL,
  `UserName` varchar(255) DEFAULT NULL,
  `MobileTel` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`RoomID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cs_sys_my_cust
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_my_cust`;
CREATE TABLE `cs_sys_my_cust` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `cust_info_id` bigint(20) DEFAULT NULL COMMENT '客户信息ID(cs_cust_info里的ID)',
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `house_num` varchar(100) DEFAULT NULL COMMENT '房屋编号',
  `certificate_num` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) DEFAULT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(200) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '所属(个人/单位)',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_cs_sys_my_cust_housenum` (`house_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25081864 DEFAULT CHARSET=utf8 COMMENT='同步明源数据表';

-- ----------------------------
-- Table structure for cs_sys_my_house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_my_house`;
CREATE TABLE `cs_sys_my_house` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` varchar(100) NOT NULL COMMENT '项目ID',
  `house_id` varchar(100) NOT NULL COMMENT '房屋ID',
  `house_num` varchar(100) DEFAULT NULL COMMENT '房屋编号',
  `house_name` varchar(100) DEFAULT NULL COMMENT '房屋名称',
  `area` varchar(50) DEFAULT NULL COMMENT '区域',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `unit` varchar(50) DEFAULT NULL COMMENT '单元号',
  `building` varchar(50) DEFAULT NULL COMMENT '楼栋',
  `room_num` varchar(50) DEFAULT NULL COMMENT '房间号',
  `use_property` varchar(50) DEFAULT NULL COMMENT '使用性质',
  `obtain_time` varchar(50) DEFAULT NULL COMMENT '取证时间',
  `delivery_date` varchar(50) DEFAULT NULL COMMENT '合同交房时间',
  `sign_date` varchar(50) DEFAULT NULL COMMENT '签约时间',
  `fitment` varchar(11) DEFAULT NULL COMMENT '精装情况',
  `create_date` date DEFAULT NULL COMMENT '创建时间',
  `update_date` date DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `house_id` (`house_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20757685 DEFAULT CHARSET=utf8 COMMENT='同步明源房屋信息表';

-- ----------------------------
-- Table structure for cs_sys_timing_config
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_timing_config`;
CREATE TABLE `cs_sys_timing_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tim_id` varchar(100) NOT NULL COMMENT '定时ID',
  `tim_name` varchar(100) NOT NULL COMMENT '定时名称',
  `tim_describe` varchar(100) DEFAULT NULL COMMENT '定时描述',
  `tim_switch` varchar(50) DEFAULT NULL COMMENT '定时开关 on：开，off：关',
  `tim_ip` varchar(50) DEFAULT NULL COMMENT '允许执行IP（暂不用，预留功能）',
  `tim_last_date` datetime DEFAULT NULL COMMENT '最后执行时间',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1047 DEFAULT CHARSET=utf8 COMMENT='定时配置表';

-- ----------------------------
-- Table structure for cs_sys_wy_cust
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_wy_cust`;
CREATE TABLE `cs_sys_wy_cust` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cust_info_id` bigint(20) DEFAULT NULL COMMENT '客户信息ID(cs_cust_info里的ID)',
  `cust_id` varchar(100) NOT NULL COMMENT '客户ID',
  `cust_name` varchar(50) NOT NULL COMMENT '客户名称',
  `house_num` varchar(500) DEFAULT NULL COMMENT '房屋编号',
  `certificate_num` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `certificate_name` varchar(50) DEFAULT NULL COMMENT '证件名称',
  `fixed_telephone` varchar(50) DEFAULT NULL COMMENT '固定电话',
  `telephone` varchar(50) NOT NULL COMMENT '移动电话',
  `fax` varchar(50) DEFAULT NULL COMMENT '传真电话',
  `contact_address` varchar(500) DEFAULT NULL COMMENT '联系地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮件',
  `belong` varchar(50) DEFAULT NULL COMMENT '个人/单位',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `national` varchar(20) DEFAULT NULL COMMENT '国籍',
  `birthday` varchar(50) DEFAULT NULL COMMENT '出生年月日',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `profession` varchar(20) DEFAULT NULL COMMENT '职业',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  `other_board_member` varchar(10) DEFAULT NULL COMMENT '是否为其他版块会员',
  `special_customer` varchar(10) DEFAULT NULL COMMENT '是否为特殊客户',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_cust_id` (`cust_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=52014 DEFAULT CHARSET=utf8 COMMENT='同步物业客户信息';

-- ----------------------------
-- Table structure for cs_sys_wy_cust_family
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_wy_cust_family`;
CREATE TABLE `cs_sys_wy_cust_family` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_id` varchar(100) NOT NULL COMMENT '客户家庭成员唯一标识',
  `household_relation` varchar(20) DEFAULT NULL COMMENT '与户主关系',
  `certificate_name` varchar(20) DEFAULT NULL COMMENT '证件名称',
  `member_name` varchar(200) DEFAULT NULL COMMENT '成员名称',
  `id_number` varchar(30) DEFAULT NULL COMMENT '证件号码',
  `sex` varchar(100) DEFAULT NULL COMMENT '性别',
  `birthday` varchar(100) DEFAULT NULL COMMENT '出生日期',
  `nationality` varchar(100) DEFAULT NULL COMMENT '国籍',
  `mobile` varchar(200) DEFAULT NULL COMMENT '移动电话',
  `hobbies` varchar(100) DEFAULT NULL COMMENT '兴趣爱好',
  `other_board_member` int(11) DEFAULT NULL COMMENT '是否为其他版块会员',
  `special_customer` int(11) DEFAULT NULL COMMENT '是否为特殊客户',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `cust_id` varchar(100) DEFAULT NULL COMMENT '业主ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34677 DEFAULT CHARSET=utf8 COMMENT='同步物业客户家庭成员表';

-- ----------------------------
-- Table structure for cs_sys_wy_cust_house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_wy_cust_house`;
CREATE TABLE `cs_sys_wy_cust_house` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `cust_id` varchar(100) NOT NULL,
  `house_id` varchar(100) NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_cch_house_id` (`house_id`) USING BTREE,
  KEY `index_cch_cust_id` (`cust_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50034 DEFAULT CHARSET=utf8 COMMENT='同步物业客户房屋关系表';

-- ----------------------------
-- Table structure for cs_sys_wy_house
-- ----------------------------
DROP TABLE IF EXISTS `cs_sys_wy_house`;
CREATE TABLE `cs_sys_wy_house` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` varchar(100) NOT NULL COMMENT '项目ID',
  `house_id` varchar(100) NOT NULL COMMENT '房屋ID',
  `house_num` varchar(100) DEFAULT NULL COMMENT '房屋编号',
  `house_name` varchar(100) DEFAULT NULL COMMENT '房屋名称',
  `area` varchar(50) DEFAULT NULL COMMENT '区域',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `project` varchar(50) DEFAULT NULL COMMENT '项目',
  `unit` varchar(50) DEFAULT NULL COMMENT '单元号',
  `building` varchar(50) DEFAULT NULL COMMENT '楼栋',
  `room_num` varchar(50) DEFAULT NULL COMMENT '房间号',
  `use_property` varchar(50) DEFAULT NULL COMMENT '使用性质',
  `delivery_date` varchar(50) DEFAULT NULL COMMENT '合同交房时间',
  `sign_date` varchar(50) DEFAULT NULL COMMENT '签约时间',
  `focus_start_date` datetime DEFAULT NULL COMMENT '集中交房时间从',
  `focus_end_date` datetime DEFAULT NULL COMMENT '集中交房时间到',
  `delivery_status` int(11) DEFAULT NULL COMMENT '交付状态',
  `fitment` varchar(11) DEFAULT NULL COMMENT '精装情况',
  `actual_delivery_date` datetime DEFAULT NULL COMMENT '实际交房时间',
  `stay_time` datetime DEFAULT NULL COMMENT '入住时间',
  `off_aid_date` datetime DEFAULT NULL COMMENT '预计脱保时间',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `steward_name` varchar(20) DEFAULT NULL COMMENT '管家姓名',
  `steward_telephone` varchar(50) DEFAULT NULL COMMENT '管家电话',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_house_id` (`house_id`) USING BTREE,
  KEY `index_cs_sys_wy_house_num` (`house_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=406741 DEFAULT CHARSET=utf8 COMMENT='同步物业房屋信息表';
