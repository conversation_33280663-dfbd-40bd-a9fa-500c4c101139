# 客服一体化系统接口文档

---

## 1. 工单相关接口

### 1.1 分页查询工单
- **接口名称**：分页查询工单
- **路径**：`/api/csFormInst/info`
- **请求方式**：GET
- **功能描述**：分页获取工单主表信息

#### 入参
| 参数名   | 类型   | 必填 | 说明     |
|----------|--------|------|----------|
| pageNum  | int    | 否   | 页码，默认1 |
| pageSize | int    | 否   | 每页数量，默认20 |

#### 返回值示例
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "total": 100,
    "records": [
      {
        "id": 1,
        "formNo": "KF20240601001",
        "deptCode": "deptWY",
        "processCode": "processBZ",
        "firstSortCode": "coBX",
        "processStateCode": "handle",
        ...
      }
    ]
  }
}
```
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | int | 总记录数 |
| records | array | 工单列表，字段详见cs_form_inst表 |

#### SQL示例
```sql
SELECT * FROM cs_form_inst LIMIT #{(pageNum-1)*pageSize}, #{pageSize};
```

#### 典型业务场景
客服/管理人员在Web端工单列表页浏览、筛选、分页查看所有工单。

---

### 1.2 保存工单
- **接口名称**：保存工单
- **路径**：`/api/csFormInst/save`
- **请求方式**：POST
- **功能描述**：保存或更新工单信息，支持暂存/提交

#### 入参
| 参数名      | 类型   | 必填 | 说明         |
|-------------|--------|------|--------------|
| csFormInst  | object | 是   | 工单主表对象，字段详见cs_form_inst |
| operateType | string | 是   | 操作类型（draft/submit/update） |
| comment     | string | 否   | 备注         |

#### 返回值示例
```json
{
  "code": 0,
  "msg": "ok",
  "data": "123456" // 工单ID
}
```
| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | string | 工单ID |

#### SQL示例
```sql
INSERT INTO cs_form_inst (...) VALUES (...);
-- 或
UPDATE cs_form_inst SET ... WHERE id = #{id};
```

#### 典型业务场景
客服录入新工单或编辑已有工单，点击保存或提交。

---

### 1.3 获取工单编号
- **接口名称**：获取工单编号
- **路径**：`/api/csFormInst/getFormNo`
- **请求方式**：GET
- **功能描述**：根据一级分类生成工单编号

#### 入参
| 参数名    | 类型   | 必填 | 说明     |
|-----------|--------|------|----------|
| firstType | string | 是   | 一级分类 |

#### 返回值示例
```json
{
  "code": 0,
  "msg": "ok",
  "data": "KF20240601001"
}
```

#### SQL示例
```sql
SELECT MAX(form_no) FROM cs_form_inst WHERE first_sort_code = #{firstType};
```

#### 典型业务场景
新建工单时自动生成唯一编号。

---

## 2. 流程相关接口

### 2.1 工单提交/流转
- **接口名称**：工单提交/流转
- **路径**：`/api/process/submit`
- **请求方式**：POST
- **功能描述**：工单状态流转（提交、退回、分派等）

#### 入参
| 参数名        | 类型   | 必填 | 说明         |
|---------------|--------|------|--------------|
| formInstId    | string | 是   | 工单ID       |
| comment       | string | 否   | 备注         |
| assignUserId  | string | 否   | 分派人ID     |
| assignUserName| string | 否   | 分派人姓名   |
| mobile        | string | 否   | 分派人手机号 |
| operateType   | string | 是   | 操作类型（submit/reject等） |

#### 返回值示例
```json
{
  "code": 0,
  "msg": "提交成功",
  "data": null
}
```

#### SQL示例
```sql
UPDATE cs_form_inst SET process_state_code = #{nextState}, process_state_name = #{nextStateName}, ... WHERE id = #{formInstId};
INSERT INTO cs_process_workitem (form_inst_id, process_state_code, ...) VALUES (...);
```

#### 典型业务场景
工单从暂存状态提交，或在分派、处理、回访等节点流转。

---

## 3. 业主信息相关接口

### 3.1 分页查询业主信息
- **接口名称**：分页查询业主信息
- **路径**：`/api/csCustInfo/list`
- **请求方式**：GET
- **功能描述**：分页获取业主信息

#### 入参
| 参数名   | 类型   | 必填 | 说明     |
|----------|--------|------|----------|
| pageNum  | int    | 否   | 页码，默认1 |
| pageSize | int    | 否   | 每页数量，默认20 |
| ...      | ...    | 否   | 其他筛选条件，详见cs_cust_info表 |

#### 返回值示例
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "total": 100,
    "records": [
      {
        "id": 1,
        "custId": "CUST001",
        "custName": "张三",
        "telephone": "13800000000",
        ...
      }
    ]
  }
}
```

#### SQL示例
```sql
SELECT * FROM cs_cust_info WHERE ... LIMIT #{(pageNum-1)*pageSize}, #{pageSize};
```

#### 典型业务场景
客服/管理人员在Web端业主信息管理页面浏览、筛选、分页查看所有业主。

---

## 4. 项目信息相关接口

### 4.1 查询所有项目信息
- **接口名称**：查询所有项目信息
- **路径**：`/api/csProjectInfo/selectAll`
- **请求方式**：GET
- **功能描述**：获取所有项目信息

#### 入参
无

#### 返回值示例
```json
{
  "code": 0,
  "msg": "ok",
  "data": [
    {
      "id": 1,
      "regionCode": "R001",
      "region": "华东区",
      "cityCompanyCode": "CC001",
      "cityCompany": "上海公司",
      "cityCode": "C001",
      "city": "上海",
      "projectCode": "P001",
      "project": "泰禾广场",
      "projectSource": "1"
    }
  ]
}
```

#### SQL示例
```sql
SELECT * FROM cs_project_info;
```

#### 典型业务场景
工单录入、业主管理等页面选择项目时下拉列表。

---

## 5. 其他接口
（如需更多接口文档，可继续补充CsHouseInfoController、CsLabelController等） 